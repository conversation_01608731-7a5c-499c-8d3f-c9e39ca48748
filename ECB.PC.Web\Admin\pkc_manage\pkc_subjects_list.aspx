﻿<%@ Page Language="C#" AutoEventWireup="true" StylesheetTheme="Admin_Default" EnableEventValidation="false" CodeBehind="pkc_subjects_list.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_subjects_list" %>

<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>考试科目管理</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <asp:GridView ID="gvSubjectsList" runat="server" Width="100%" CellPadding="4" SkinID="gridviewSkin1" EmptyDataText="未找到信息"
                BorderWidth="1px" DataKeyNames="ID"
                AutoGenerateColumns="False" RowStyle-HorizontalAlign="Center">
                <Columns>
                    <asp:TemplateField HeaderText="选择">
                        <ItemTemplate>
                            <label class="chkItem">
                            <asp:CheckBox ID="chkItem1" runat="server" />
                            <asp:HiddenField ID="hidID" runat="server" Value='<%# Eval("ID") %>' />
                                </label>
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Center" Width="30" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="SubjectName" HeaderText="科目" SortExpression="SubjectName">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>

                    <asp:TemplateField HeaderText="科目性质">
                        <ItemTemplate>
                            <asp:Label ID="PropertyCode" runat="server" Text='<%#Eval("PropertyCode").ToString()=="1"?"文科":(Eval("PropertyCode").ToString()=="2"?"理科":"同科") %>'></asp:Label>
                        </ItemTemplate>
                        <HeaderStyle HorizontalAlign="Center" />
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="OrderNum" HeaderText="场次序号" SortExpression="OrderNum">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="场次序号">
                        <HeaderStyle HorizontalAlign="Center" />
                        <ItemTemplate>
                            <asp:TextBox ID="txtSort" runat="server" Text='<%#Eval("OrderNum") %>' Width="30px" MaxLength="3" AutoCompleteType="None"></asp:TextBox>
                        </ItemTemplate>
                        <ItemStyle />
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="开考时间">
                        <HeaderStyle HorizontalAlign="Center" />
                        <ItemTemplate>
                            <%--<asp:TextBox ID="txtBeginDate" runat="server" Text='<%#Eval("StartTime") %>' Width="200px" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" CssClass="Wdate" AutoCompleteType="None"></asp:TextBox>--%>
                            <asp:TextBox ID="txtBeginDate" runat="server" Text='<%#Eval("StartTime") %>' Width="200px" onfocus="SelectStartDate(this);" CssClass="Wdate date1" autocomplete="off" AutoCompleteType="None"></asp:TextBox>
                        </ItemTemplate>
                        <ItemStyle />
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="结束时间">
                        <HeaderStyle HorizontalAlign="Center" />
                        <ItemTemplate>
                            <%--                             <asp:TextBox ID="txtEndDate" runat="server" Text='<%#Eval("EndTime") %>' Width="200px" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" CssClass="Wdate" AutoCompleteType="None"></asp:TextBox>--%>
                            <asp:TextBox ID="txtEndDate" runat="server" Text='<%#Eval("EndTime") %>' Width="200px" onfocus="SelectDate(this);" CssClass="Wdate date2" autocomplete="off" AutoCompleteType="None"></asp:TextBox>
                        </ItemTemplate>
                        <ItemStyle />
                    </asp:TemplateField>
                    <asp:BoundField DataField="StartTime" HeaderText="开考时间" SortExpression="StartTime" DataFormatString="{0:yyyy-MM-dd HH:mm:ss}">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="EndTime" HeaderText="结束时间" SortExpression="EndTime" DataFormatString="{0:yyyy-MM-dd HH:mm:ss}">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                </Columns>
            </asp:GridView>
            <div id="pager">
                <div align="right" style="clear: both; line-height: 40px;">
                    <webdiyer:AspNetPager ID="AspNetPager1" runat="server" AlwaysShow="True" CustomInfoHTML="共有<font color=red>%RecordCount%</font>条记录,当前第<font color=red>%CurrentPageIndex%/%PageCount%</font>页,每页<font color=red>%PageSize%</font>条记录"
                        CustomInfoStyle="" CustomInfoTextAlign="left" FirstPageText="首页" HorizontalAlign="Right"
                        LastPageText="尾页" NavigationToolTipTextFormatString="{0}" NextPageText="下一页" PrevPageText="上一页" ShowCustomInfoSection="Left"
                        ShowNavigationToolTip="True" ShowPageIndexBox="Always" UrlPaging="false" OnPageChanged="AspNetPager1_PageChanged">
                    </webdiyer:AspNetPager>
                </div>
            </div>
            <asp:Panel ID="pnlButtons" runat="server">
                <asp:Panel ID="pnlBtn" runat="server" Style="display: inline">
                    <asp:CheckBox ID="chkAll1" Visible="false" runat="server" Text="全选" />
                    <asp:Button ID="btnSort" runat="server" CssClass="btnGreen" OnClick="btnSort_Click" Text="保存设置" />
                    <asp:Button ID="btnExport" runat="server" Text="导出科目表" CssClass="btnGreen" OnClick="btnExport_Click" />
                    <asp:Button ID="btnDel" runat="server" Text="删除" OnClientClick='return CheckDoAll("该操作将删除所有所选考试，不可恢复，是否删除?","btnDel")' CssClass="btnRed" OnClick="btnDelete_Click" />
                </asp:Panel>
                <asp:Button ID="btnBack" runat="server" Text="返回" CssClass="btnWhite" OnClick="btnBack_Click" />
            </asp:Panel>
            <asp:Panel ID="pnlMessage" runat="server">
                <asp:Label ID="lblShowMessage" runat="server" Text="" Style="color: red;"></asp:Label>
            </asp:Panel>
        </div>
        <asp:HiddenField ID="hidStartTime" runat="server" Value="" />
        <asp:HiddenField ID="hidEndTime" runat="server" Value="" />
    </form>
    <script type="text/javascript">

        function SelectDate(obj) {
            //    alert($(obj).parent().parent().find(".date1").val());
            //    var data1 = $(obj).attr("id") + "1";
            //var minValue = $(obj).parent().parent().find(".date1").val();
            var minValue = $('#hidStartTime').val();
            var maxValue = $('#hidEndTime').val();
            var selectValue = $(obj).parent().parent().find(".date1").val();
            if (selectValue != '') {
                minValue = selectValue;
            }
            //var minValue = $(obj).parent().parent().find(".date1").val();
            WdatePicker({ minDate: minValue, maxDate: maxValue, dateFmt: "yyyy/MM/dd HH:mm" });
        }

        function SelectStartDate(obj) {
            var minValue = $('#hidStartTime').val();
            var maxValue = $('#hidEndTime').val();
            var selectValue = $(obj).parent().parent().find(".date2").val();
            if (selectValue != '') {
                maxValue = selectValue;
            }
            //var maxValue = $(obj).parent().parent().find(".date2").val();
            WdatePicker({ minDate: $('#hidStartTime').val(), maxDate: maxValue, dateFmt: "yyyy/MM/dd HH:mm" });
        }
    </script>
</body>
</html>
