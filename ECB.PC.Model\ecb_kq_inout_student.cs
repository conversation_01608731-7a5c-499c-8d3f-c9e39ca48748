﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_kq_inout_student:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_kq_inout_student
    {
        public ecb_kq_inout_student()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private Guid _userid;
        private DateTime? _recorddate;
        private Guid _gradeid;
        private Guid _classid;
        private string _kqrecords;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? RecordDate
        {
            set { _recorddate = value; }
            get { return _recorddate; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string KQRecords
        {
            set { _kqrecords = value; }
            get { return _kqrecords; }
        }
        #endregion Model

    }
}

