﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_attendce_history
    /// </summary>
    public partial class ecb_attendce_history
    {
        public ecb_attendce_history()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_attendce_history");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_attendce_history model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_attendce_history(");
            strSql.Append("Id,ColumnId,ColumnPath,GradeId,ClassId,Normal,Lack,Leave,Late,ALL,RecordDate)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@GradeId,@ClassId,@Normal,@Lack,@Leave,@Late,@ALL,@RecordDate)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Normal", SqlDbType.Int,4),
                    new SqlParameter("@Lack", SqlDbType.Int,4),
                    new SqlParameter("@Leave", SqlDbType.Int,4),
                    new SqlParameter("@Late", SqlDbType.Int,4),
                    new SqlParameter("@ALL", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.GradeId;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.Normal;
            parameters[6].Value = model.Lack;
            parameters[7].Value = model.Leave;
            parameters[8].Value = model.Late;
            parameters[9].Value = model.ALL;
            parameters[10].Value = model.RecordDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_attendce_history model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_attendce_history set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("Normal=@Normal,");
            strSql.Append("Lack=@Lack,");
            strSql.Append("Leave=@Leave,");
            strSql.Append("Late=@Late,");
            strSql.Append("ALL=@ALL,");
            strSql.Append("RecordDate=@RecordDate");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Normal", SqlDbType.Int,4),
                    new SqlParameter("@Lack", SqlDbType.Int,4),
                    new SqlParameter("@Leave", SqlDbType.Int,4),
                    new SqlParameter("@Late", SqlDbType.Int,4),
                    new SqlParameter("@ALL", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.GradeId;
            parameters[3].Value = model.ClassId;
            parameters[4].Value = model.Normal;
            parameters[5].Value = model.Lack;
            parameters[6].Value = model.Leave;
            parameters[7].Value = model.Late;
            parameters[8].Value = model.ALL;
            parameters[9].Value = model.RecordDate;
            parameters[10].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_attendce_history ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_attendce_history ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_attendce_history GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,GradeId,ClassId,Normal,Lack,Leave,Late,ALL,RecordDate from ecb_attendce_history ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.ecb_attendce_history model = new Model.ecb_attendce_history();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_attendce_history DataRowToModel(DataRow row)
        {
            Model.ecb_attendce_history model = new Model.ecb_attendce_history();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["Normal"] != null && row["Normal"].ToString() != "")
                {
                    model.Normal = int.Parse(row["Normal"].ToString());
                }
                if (row["Lack"] != null && row["Lack"].ToString() != "")
                {
                    model.Lack = int.Parse(row["Lack"].ToString());
                }
                if (row["Leave"] != null && row["Leave"].ToString() != "")
                {
                    model.Leave = int.Parse(row["Leave"].ToString());
                }
                if (row["Late"] != null && row["Late"].ToString() != "")
                {
                    model.Late = int.Parse(row["Late"].ToString());
                }
                if (row["ALL"] != null && row["ALL"].ToString() != "")
                {
                    model.ALL = int.Parse(row["ALL"].ToString());
                }
                if (row["RecordDate"] != null && row["RecordDate"].ToString() != "")
                {
                    model.RecordDate = DateTime.Parse(row["RecordDate"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,GradeId,ClassId,Normal,Lack,Leave,Late,ALL,RecordDate ");
            strSql.Append(" FROM ecb_attendce_history ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,GradeId,ClassId,Normal,Lack,Leave,Late,ALL,RecordDate ");
            strSql.Append(" FROM ecb_attendce_history ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_attendce_history ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_attendce_history T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_attendce_history";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

