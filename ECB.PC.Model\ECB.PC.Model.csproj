﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5FC9B591-7551-4EA7-9800-94523417A60D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ECB.PC.Model</RootNamespace>
    <AssemblyName>ECB.PC.Model</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ai_config.cs" />
    <Compile Include="ai_dialogs.cs" />
    <Compile Include="ai_dialog_content.cs" />
    <Compile Include="ai_prompts.cs" />
    <Compile Include="api_kq_inout.cs" />
    <Compile Include="aspnet_Task.cs" />
    <Compile Include="aspnet_Welcome.cs" />
    <Compile Include="custom_Attendance.cs" />
    <Compile Include="custom_Attendance_record.cs" />
    <Compile Include="custom_Handling_record.cs" />
    <Compile Include="custom_Reward_record.cs" />
    <Compile Include="custom_Working_record.cs" />
    <Compile Include="DA_Catalog.cs" />
    <Compile Include="DA_XS_FeaturedActivities.cs" />
    <Compile Include="DA_XS_GrowAttachment.cs" />
    <Compile Include="DA_XS_StudentWorks.cs" />
    <Compile Include="ecb_abnormal_config.cs" />
    <Compile Include="ecb_Active_Class.cs" />
    <Compile Include="ecb_Active_School.cs" />
    <Compile Include="ecb_attendce_history.cs" />
    <Compile Include="ecb_AudioVolume.cs" />
    <Compile Include="ecb_classbrands.cs" />
    <Compile Include="ecb_ClassCadre.cs" />
    <Compile Include="ecb_ClassCadreConfig.cs" />
    <Compile Include="ecb_ClassGroup.cs" />
    <Compile Include="ecb_ClassGroupConfig.cs" />
    <Compile Include="ecb_classSubjectRepresent.cs" />
    <Compile Include="ecb_class_honor.cs" />
    <Compile Include="ecb_class_honor_attachement.cs" />
    <Compile Include="ecb_config.cs" />
    <Compile Include="ecb_Countdown.cs" />
    <Compile Include="ecb_custom_products.cs" />
    <Compile Include="ecb_Device.cs" />
    <Compile Include="ecb_Duty.cs" />
    <Compile Include="ecb_DutyItems.cs" />
    <Compile Include="ecb_Event.cs" />
    <Compile Include="ecb_FunctionRoom.cs" />
    <Compile Include="ecb_FunctionRoom_Audit_Records.cs" />
    <Compile Include="ecb_FunctionRoom_Reservation.cs" />
    <Compile Include="ecb_FunctionSubject.cs" />
    <Compile Include="ecb_GradeRedFlagConfig.cs" />
    <Compile Include="ecb_GuestBook.cs" />
    <Compile Include="ecb_HolidayInfo.cs" />
    <Compile Include="ecb_iccard_bind.cs" />
    <Compile Include="ecb_jishiyuan_timetable.cs" />
    <Compile Include="ecb_kq_inout_student.cs" />
    <Compile Include="ecb_kq_inout_student_config.cs" />
    <Compile Include="ecb_kq_inout_teacher.cs" />
    <Compile Include="ecb_kq_inout_teacher_config.cs" />
    <Compile Include="ecb_kq_inout_teacher_user.cs" />
    <Compile Include="ecb_kq_meeting.cs" />
    <Compile Include="ecb_kq_meeting_record.cs" />
    <Compile Include="ecb_kq_meeting_user.cs" />
    <Compile Include="ecb_kq_sh_config.cs" />
    <Compile Include="ecb_kq_temp.cs" />
    <Compile Include="ecb_kq_temp_record.cs" />
    <Compile Include="ecb_kq_temp_user.cs" />
    <Compile Include="ecb_loupai_info.cs" />
    <Compile Include="ecb_mailbox.cs" />
    <Compile Include="ecb_MenJin.cs" />
    <Compile Include="ecb_MenJin_Pusher.cs" />
    <Compile Include="ecb_Mode.cs" />
    <Compile Include="ecb_Mode_Config.cs" />
    <Compile Include="ecb_news.cs" />
    <Compile Include="ecb_news_category.cs" />
    <Compile Include="ecb_New_Duty.cs" />
    <Compile Include="ecb_officeStatus_OperationRecord.cs" />
    <Compile Include="ecb_place.cs" />
    <Compile Include="ecb_place_category.cs" />
    <Compile Include="ecb_Praise.cs" />
    <Compile Include="Ecb_PraiseType.cs" />
    <Compile Include="ecb_Program.cs" />
    <Compile Include="ecb_ProminentTeacher.cs" />
    <Compile Include="ecb_PushUser.cs" />
    <Compile Include="ecb_reboot_config.cs" />
    <Compile Include="ecb_reboot_once.cs" />
    <Compile Include="ecb_reboot_rule.cs" />
    <Compile Include="ecb_reboot_time.cs" />
    <Compile Include="ecb_Record.cs" />
    <Compile Include="ecb_RedFlag.cs" />
    <Compile Include="ecb_SchoolActivity.cs" />
    <Compile Include="ecb_SchoolStars.cs" />
    <Compile Include="ecb_SchoolStarsType.cs" />
    <Compile Include="ecb_SchoolVideo.cs" />
    <Compile Include="ecb_sc_week.cs" />
    <Compile Include="ecb_social_exam.cs" />
    <Compile Include="ecb_social_place.cs" />
    <Compile Include="ecb_teacher_works.cs" />
    <Compile Include="ecb_Teaching.cs" />
    <Compile Include="ecb_TimeTable.cs" />
    <Compile Include="ecb_TimeTable_stu.cs" />
    <Compile Include="ecb_time_config.cs" />
    <Compile Include="ecb_time_template.cs" />
    <Compile Include="ecb_ToDayInHistory.cs" />
    <Compile Include="ecb_Update.cs" />
    <Compile Include="ecb_user_wechats.cs" />
    <Compile Include="ecb_WebSorket.cs" />
    <Compile Include="ecb_welcome_message.cs" />
    <Compile Include="ecb_kq_class_config.cs" />
    <Compile Include="ecb_kq_class_student.cs" />
    <Compile Include="ecb_kq_class_teacher.cs" />
    <Compile Include="ecb_kq_exam.cs" />
    <Compile Include="Ecb_Xinli_Report.cs" />
    <Compile Include="ecb_XinLi_Reservation.cs" />
    <Compile Include="ecb_XinLi_SignIn.cs" />
    <Compile Include="Ecb_Xinli_StuCase.cs" />
    <Compile Include="Ecb_Xinli_StuCaseDetail.cs" />
    <Compile Include="ecb_XinLi_TeaLeave.cs" />
    <Compile Include="ecb_XinLi_timetable.cs" />
    <Compile Include="face_delete_log.cs" />
    <Compile Include="face_lib.cs" />
    <Compile Include="face_logs.cs" />
    <Compile Include="face_publish_version.cs" />
    <Compile Include="JC_ClassInfos.cs" />
    <Compile Include="JC_Department.cs" />
    <Compile Include="JC_GradeLeader.cs" />
    <Compile Include="JC_Homeworks.cs" />
    <Compile Include="JC_Homeworks_Check.cs" />
    <Compile Include="JC_Homework_Config.cs" />
    <Compile Include="JC_Homework_NoPost.cs" />
    <Compile Include="JC_Leaves.cs" />
    <Compile Include="JC_SchoolNotes.cs" />
    <Compile Include="JC_StudentEducation.cs" />
    <Compile Include="JC_Subjects.cs" />
    <Compile Include="JC_TeacherTimetable.cs" />
    <Compile Include="JC_TimetableConfig.cs" />
    <Compile Include="JsonResult.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="psych_activity.cs" />
    <Compile Include="psych_basicInfo.cs" />
    <Compile Include="psych_config.cs" />
    <Compile Include="psych_dict.cs" />
    <Compile Include="psych_dict_type.cs" />
    <Compile Include="psych_family_event.cs" />
    <Compile Include="psych_home_visit.cs" />
    <Compile Include="psych_interfere.cs" />
    <Compile Include="psych_interview.cs" />
    <Compile Include="psych_medical_history.cs" />
    <Compile Include="psych_test.cs" />
    <Compile Include="psych_test_result.cs" />
    <Compile Include="psych_warning_config.cs" />
    <Compile Include="psych_warning_record.cs" />
    <Compile Include="psych_week_diary_abnormal.cs" />
    <Compile Include="Site_Dictionary.cs" />
    <Compile Include="wpsl_flows.cs" />
    <Compile Include="wpsl_flow_checker.cs" />
    <Compile Include="wpsl_flow_detail.cs" />
    <Compile Include="xy_app_msg.cs" />
    <Compile Include="xy_app_msg_cate.cs" />
    <Compile Include="xy_app_msg_receive.cs" />
    <Compile Include="xy_app_version.cs" />
    <Compile Include="xy_Attachments.cs" />
    <Compile Include="xy_Feedback.cs" />
    <Compile Include="xy_scan.cs" />
    <Compile Include="zhsz_device_prize.cs" />
    <Compile Include="zhsz_prize.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>