﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// xy_app_msg:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class xy_app_msg
	{
		public xy_app_msg()
		{}
		#region Model
		private Guid _id;
		private string _cateid;
		private string _catepath;
		private string _businessid;
		private DateTime? _send_time;
		private Guid _send_userid;
		private int? _msg_type;
		private int? _receive_type;
		private int? _receive_num;
		private string _msg_title;
		private string _msg_content;
		private string _gt_result;
		private string _gt_taskid;
		private string _gt_status;
		/// <summary>
		/// 
		/// </summary>
		public Guid id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 消息类别Id
		/// </summary>
		public string cateId
		{
			set{ _cateid=value;}
			get{return _cateid;}
		}
		/// <summary>
		/// 消息类别路径
		/// </summary>
		public string catePath
		{
			set{ _catepath=value;}
			get{return _catepath;}
		}
		/// <summary>
		/// 业务Id
		/// </summary>
		public string businessId
		{
			set{ _businessid=value;}
			get{return _businessid;}
		}
		/// <summary>
		/// 发送时间
		/// </summary>
		public DateTime? send_time
		{
			set{ _send_time=value;}
			get{return _send_time;}
		}
		/// <summary>
		/// 发送者
		/// </summary>
		public Guid send_userId
		{
			set{ _send_userid=value;}
			get{return _send_userid;}
		}
		/// <summary>
		/// 消息类型（1系统对用户，2用户对用户）
		/// </summary>
		public int? msg_type
		{
			set{ _msg_type=value;}
			get{return _msg_type;}
		}
		/// <summary>
		/// 接收类型（1全体，2单用户，3多用户）
		/// </summary>
		public int? receive_type
		{
			set{ _receive_type=value;}
			get{return _receive_type;}
		}
		/// <summary>
		/// 接收者人数
		/// </summary>
		public int? receive_num
		{
			set{ _receive_num=value;}
			get{return _receive_num;}
		}
		/// <summary>
		/// 标题
		/// </summary>
		public string msg_title
		{
			set{ _msg_title=value;}
			get{return _msg_title;}
		}
		/// <summary>
		/// 消息内容
		/// </summary>
		public string msg_content
		{
			set{ _msg_content=value;}
			get{return _msg_content;}
		}
		/// <summary>
		/// 个推结果
		/// </summary>
		public string gt_result
		{
			set{ _gt_result=value;}
			get{return _gt_result;}
		}
		/// <summary>
		/// 个推任务id，result=ok才会有值
		/// </summary>
		public string gt_taskId
		{
			set{ _gt_taskid=value;}
			get{return _gt_taskid;}
		}
		/// <summary>
		/// 个推状态
		/// </summary>
		public string gt_status
		{
			set{ _gt_status=value;}
			get{return _gt_status;}
		}
		#endregion Model

	}
}

