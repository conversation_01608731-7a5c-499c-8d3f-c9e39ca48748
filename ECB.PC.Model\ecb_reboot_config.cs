﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_reboot_config:重启配置类
    /// </summary>
    [Serializable]
    public partial class ecb_reboot_config
    {
        public ecb_reboot_config()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private Guid _gradeid;
        private Guid _classid;
        private DateTime? _boottime;
        private DateTime? _offtime;
        private int _weekid;
        private int _isenable;
        private string _seriano;
        /// <summary>
        /// 主键id
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 年级id
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 班级id
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 开机时间
        /// </summary>
        public DateTime? BootTime
        {
            set { _boottime = value; }
            get { return _boottime; }
        }
        /// <summary>
        /// 关机时间
        /// </summary>
        public DateTime? OffTime
        {
            set { _offtime = value; }
            get { return _offtime; }
        }
        /// <summary>
        /// 星期几
        /// </summary>
        public int WeekId
        {
            set { _weekid = value; }
            get { return _weekid; }
        }
        /// <summary>
        /// 是否启用
        /// </summary>
        public int IsEnable
        {
            set { _isenable = value; }
            get { return _isenable; }
        }
        /// <summary>
        /// 班牌编号
        /// </summary>
        public string SeriaNo
        {
            set { _seriano = value; }
            get { return _seriano; }
        }

        #endregion Model

    }
}

