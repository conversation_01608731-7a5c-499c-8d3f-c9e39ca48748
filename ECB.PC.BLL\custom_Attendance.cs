﻿
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:custom_Attendance
	/// </summary>
	public partial class custom_Attendance
	{
		public custom_Attendance()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from custom_Attendance");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.custom_Attendance model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into custom_Attendance(");
			strSql.Append("ID,ColumnId,ColumnPath,Title,CreatorId,CreateTime)");
			strSql.Append(" values (");
			strSql.Append("@ID,@ColumnId,@ColumnPath,@Title,@CreatorId,@CreateTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@Title", SqlDbType.NVarChar,150),
					new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.Title;
			parameters[4].Value = model.CreatorId;
			parameters[5].Value = model.CreateTime;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.custom_Attendance model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update custom_Attendance set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("Title=@Title,");
			strSql.Append("CreatorId=@CreatorId,");
			strSql.Append("CreateTime=@CreateTime");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@Title", SqlDbType.NVarChar,150),
					new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.Title;
			parameters[3].Value = model.CreatorId;
			parameters[4].Value = model.CreateTime;
			parameters[5].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Attendance ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Attendance ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Attendance GetModel(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,ColumnId,ColumnPath,Title,CreatorId,CreateTime from custom_Attendance ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.custom_Attendance model=new ECB.PC.Model.custom_Attendance();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Attendance DataRowToModel(DataRow row)
		{
			ECB.PC.Model.custom_Attendance model=new ECB.PC.Model.custom_Attendance();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["Title"]!=null)
				{
					model.Title=row["Title"].ToString();
				}
				if(row["CreatorId"]!=null && row["CreatorId"].ToString()!="")
				{
					model.CreatorId= new Guid(row["CreatorId"].ToString());
				}
				if(row["CreateTime"]!=null && row["CreateTime"].ToString()!="")
				{
					model.CreateTime=DateTime.Parse(row["CreateTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,ColumnId,ColumnPath,Title,CreatorId,CreateTime ");
			strSql.Append(" FROM custom_Attendance ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,ColumnId,ColumnPath,Title,CreatorId,CreateTime ");
			strSql.Append(" FROM custom_Attendance ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM custom_Attendance ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from custom_Attendance T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "custom_Attendance";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 根据身份证号获取教师ID
		/// </summary>
		/// <param name="idCardNo">身份证号</param>
		/// <param name="columnId">地区ID</param>
		/// <returns>教师ID</returns>
		public Guid GetTeacherIdByIdCard(string idCardNo, int columnId)
		{
			string sql = @"SELECT TOP 1 u.UserID
                          FROM UserInfos u
                          INNER JOIN aspnet_Membership m ON u.UserID = m.UserId
                          WHERE m.IDCardNo = @IDCardNo AND u.ColumnId = @ColumnId
                          AND u.UserTypeID = 3 AND (u.IsDelete = 0 OR u.IsDelete IS NULL)";

			SqlParameter[] parameters = {
				new SqlParameter("@IDCardNo", SqlDbType.NVarChar, 50),
				new SqlParameter("@ColumnId", SqlDbType.Int)
			};
			parameters[0].Value = idCardNo;
			parameters[1].Value = columnId;

			object result = DbHelperSQL.GetSingle(sql, parameters);
			if (result != null && result != DBNull.Value)
			{
				return new Guid(result.ToString());
			}
			return Guid.Empty;
		}

		/// <summary>
		/// 添加考勤记录到事务列表
		/// </summary>
		/// <param name="model">考勤主记录模型</param>
		/// <param name="sqlList">SQL事务列表</param>
		public void Add(ECB.PC.Model.custom_Attendance model, ArrayList sqlList)
		{
			string sql = $@"INSERT INTO custom_Attendance(ID,ColumnId,ColumnPath,Title,CreatorId,CreateTime)
                           VALUES('{model.ID}',{model.ColumnId},'{model.ColumnPath}','{model.Title}','{model.CreatorId}','{model.CreateTime:yyyy-MM-dd HH:mm:ss}')";
			sqlList.Add(sql);
		}

		/// <summary>
		/// 执行SQL事务
		/// </summary>
		/// <param name="sqlList">SQL语句列表</param>
		/// <returns>执行结果</returns>
		public bool ExecuteSqlTran(ArrayList sqlList)
		{
			return DbHelperSQL.ExecuteSqlTran(sqlList) > 0;
		}

		#endregion  ExtensionMethod
	}
}

