﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_sc_week:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_sc_week
    {
        public ecb_sc_week()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _termid;
        private DateTime _stadate;
        private DateTime _enddate;
        private int _weeknum;
        private int _IsSingle;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 学期ID
        /// </summary>
        public Guid TermId
        {
            set { _termid = value; }
            get { return _termid; }
        }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StaDate
        {
            set { _stadate = value; }
            get { return _stadate; }
        }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate
        {
            set { _enddate = value; }
            get { return _enddate; }
        }
        /// <summary>
        /// 周次
        /// </summary>
        public int WeekNum
        {
            set { _weeknum = value; }
            get { return _weeknum; }
        }
        /// <summary>
        /// 单双周 是否双周
        /// </summary>
        public int IsSingle
        {
            set { _IsSingle = value; }
            get { return _IsSingle; }
        }
        #endregion Model

    }
}

