﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 校宣文化节目
	/// </summary>
	[Serializable]
	public partial class ecb_Program
	{
		public ecb_Program()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _programname;
		private int _isshow;
		private int _issue;
		private int _status;
		private Guid _createuserid;
		private DateTime _createtime;
		private string _contents;
		private string _resource;
		private int? _checkresult;
		private string _reason;
		private Guid _checkuserid;
		private DateTime? _checktime;
		private DateTime _startdate;
		private DateTime _enddate;
		private string _serializedjson;
		private long? _timestamp;
		private string _seriaNos;
		/// <summary>
		/// 发布对象是否横版班牌
		/// </summary>
		public string IsHorizontal { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 节目名称
		/// </summary>
		public string ProgramName
		{
			set{ _programname=value;}
			get{return _programname;}
		}
		/// <summary>
		/// 是否展示终端
		/// </summary>
		public int IsShow
		{
			set{ _isshow=value;}
			get{return _isshow;}
		}
		/// <summary>
		/// 是否发布
		/// </summary>
		public int IsSue
		{
			set{ _issue=value;}
			get{return _issue;}
		}
		/// <summary>
		/// 状态
		/// </summary>
		public int Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 内容
		/// </summary>
		public string Contents
		{
			set{ _contents=value;}
			get{return _contents;}
		}
		/// <summary>
		/// 资源包
		/// </summary>
		public string Resource
		{
			set{ _resource=value;}
			get{return _resource;}
		}
		/// <summary>
		/// 审核结果
		/// </summary>
		public int? CheckResult
		{
			set{ _checkresult=value;}
			get{return _checkresult;}
		}
		/// <summary>
		/// 驳回原因
		/// </summary>
		public string Reason
		{
			set{ _reason=value;}
			get{return _reason;}
		}
		/// <summary>
		/// 审核人
		/// </summary>
		public Guid CheckUserId
		{
			set{ _checkuserid=value;}
			get{return _checkuserid;}
		}
		/// <summary>
		/// 审核时间
		/// </summary>
		public DateTime? CheckTime
		{
			set{ _checktime=value;}
			get{return _checktime;}
		}
		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime StartDate
		{
			set { _startdate = value; }
			get { return _startdate; }
		}
		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime EndDate
		{
			set { _enddate = value; }
			get { return _enddate; }
		}
		/// <summary>
		/// 数据格式json
		/// </summary>
		public string SerializedJson
		{
			set { _serializedjson = value; }
			get { return _serializedjson; }
		}
		/// <summary>
		/// 最后一次打包时间戳
		/// </summary>
		public long? TimeStamp
		{
			set { _timestamp = value; }
			get { return _timestamp; }
		}
        /// <summary>
        /// 班牌编号
        /// </summary>
        public string SeriaNos
        {
            set { _seriaNos = value; }
            get { return _seriaNos; }
        }
        #endregion Model

    }
}

