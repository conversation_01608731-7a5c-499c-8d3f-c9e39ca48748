﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 学生在校经历
	/// </summary>
	[Serializable]
	public partial class JC_StudentEducation
	{
		public JC_StudentEducation()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _studentid;
		private string _schoolname;
		private DateTime _starttime;
		private DateTime _endtime;
		private string _gradename;
		private string _classname;
		private string _classtype;
		private string _headteachername;
		private string _roomno;
		private string _classcommitteetype;
		private string _likelevel;
		private Guid _lasteditby;
		private DateTime _lastedittime;
		/// <summary>
		/// 主键
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 学生Id
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 学校
		/// </summary>
		public string SchoolName
		{
			set{ _schoolname=value;}
			get{return _schoolname;}
		}
		/// <summary>
		/// 起始时间
		/// </summary>
		public DateTime StartTime
		{
			set{ _starttime=value;}
			get{return _starttime;}
		}
		/// <summary>
		/// 截止时间
		/// </summary>
		public DateTime EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 年级名称
		/// </summary>
		public string GradeName
		{
			set{ _gradename=value;}
			get{return _gradename;}
		}
		/// <summary>
		/// 班级名称
		/// </summary>
		public string ClassName
		{
			set{ _classname=value;}
			get{return _classname;}
		}
		/// <summary>
		/// 班级类型
		/// </summary>
		public string ClassType
		{
			set{ _classtype=value;}
			get{return _classtype;}
		}
		/// <summary>
		/// 班主任名称
		/// </summary>
		public string HeadTeacherName
		{
			set{ _headteachername=value;}
			get{return _headteachername;}
		}
		/// <summary>
		/// 住校房号
		/// </summary>
		public string RoomNo
		{
			set{ _roomno=value;}
			get{return _roomno;}
		}
		/// <summary>
		/// 班委类别
		/// </summary>
		public string ClassCommitteeType
		{
			set{ _classcommitteetype=value;}
			get{return _classcommitteetype;}
		}
		/// <summary>
		/// 对当时所处集体的喜爱程度
		/// </summary>
		public string LikeLevel
		{
			set{ _likelevel=value;}
			get{return _likelevel;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

