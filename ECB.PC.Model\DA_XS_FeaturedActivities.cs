﻿
using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 特色活动
    /// </summary>
	[Serializable]
	public partial class DA_XS_FeaturedActivities
	{
        public DA_XS_FeaturedActivities()
        { }
        #region Model
        private Guid _id;
        private int? _schoolcolumnid;
        private string _schoolcolumnpath;
        private string _schoolyear;
        private Guid _termid;
        private Guid _gradeid;
        private Guid _classid;
        private string _name;
        private string _description;
        private Guid _creatoruserid;
        private DateTime? _createtime;
        private string _creatorname;
        private int? _ispass;
        private Guid _ispassby;
        private DateTime? _ispassdate;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 学校ID
        /// </summary>
        public int? SchoolColumnId
        {
            set { _schoolcolumnid = value; }
            get { return _schoolcolumnid; }
        }
        /// <summary>
        /// 学校路径
        /// </summary>
        public string SchoolColumnPath
        {
            set { _schoolcolumnpath = value; }
            get { return _schoolcolumnpath; }
        }
        /// <summary>
        /// 学年
        /// </summary>
        public string SchoolYear
        {
            set { _schoolyear = value; }
            get { return _schoolyear; }
        }
        /// <summary>
        /// 学期
        /// </summary>
        public Guid TermId
        {
            set { _termid = value; }
            get { return _termid; }
        }
        /// <summary>
        /// 年级
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 班级
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 活动名称
        /// </summary>
        public string Name
        {
            set { _name = value; }
            get { return _name; }
        }
        /// <summary>
        /// 活动文字描述
        /// </summary>
        public string Description
        {
            set { _description = value; }
            get { return _description; }
        }
        /// <summary>
        /// 创建人
        /// </summary>
        public Guid CreatorUserId
        {
            set { _creatoruserid = value; }
            get { return _creatoruserid; }
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime
        {
            set { _createtime = value; }
            get { return _createtime; }
        }
        /// <summary>
        /// 创建人名称
        /// </summary>
        public string CreatorName
        {
            set { _creatorname = value; }
            get { return _creatorname; }
        }
        /// <summary>
		/// 是都通过审核
		/// </summary>
		public int? IsPass
        {
            set { _ispass = value; }
            get { return _ispass; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid IsPassBy
        {
            set { _ispassby = value; }
            get { return _ispassby; }
        }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? IsPassDate
        {
            set { _ispassdate = value; }
            get { return _ispassdate; }
        }
        #endregion Model

    }
}

