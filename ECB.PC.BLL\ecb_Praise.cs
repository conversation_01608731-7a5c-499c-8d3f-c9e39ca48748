﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_Praise
	/// </summary>
	public partial class ecb_Praise
	{
		public ecb_Praise()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_Praise");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_Praise model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_Praise(");
			strSql.Append("ID,ColumnId,ColumnPath,TypeId,PraiseLevel,StudentId,PraiseDesc,ImgPath,ClassId,GradeId,TermId,CreateUserId,CreateTime)");
			strSql.Append(" values (");
			strSql.Append("@ID,@ColumnId,@ColumnPath,@TypeId,@PraiseLevel,@StudentId,@PraiseDesc,@ImgPath,@ClassId,@GradeId,@TermId,@CreateUserId,@CreateTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@TypeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@PraiseLevel", SqlDbType.Int,4),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@PraiseDesc", SqlDbType.NVarChar,-1),
					new SqlParameter("@ImgPath", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime)};
			parameters[0].Value = model.ID;
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.TypeId;
			parameters[4].Value = model.PraiseLevel;
			parameters[5].Value = model.StudentId;
			parameters[6].Value = model.PraiseDesc;
			parameters[7].Value = model.ImgPath;
			parameters[8].Value = model.ClassId;
			parameters[9].Value = model.GradeId;
			parameters[10].Value = model.TermId;
			parameters[11].Value = model.CreateUserId;
			parameters[12].Value = model.CreateTime;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_Praise model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_Praise set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("TypeId=@TypeId,");
			strSql.Append("PraiseLevel=@PraiseLevel,");
			strSql.Append("StudentId=@StudentId,");
			strSql.Append("PraiseDesc=@PraiseDesc,");
			strSql.Append("ImgPath=@ImgPath,");
			strSql.Append("ClassId=@ClassId,");
			strSql.Append("GradeId=@GradeId,");
			strSql.Append("TermId=@TermId,");
			strSql.Append("CreateUserId=@CreateUserId,");
			strSql.Append("CreateTime=@CreateTime");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@TypeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@PraiseLevel", SqlDbType.Int,4),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@PraiseDesc", SqlDbType.NVarChar,-1),
					new SqlParameter("@ImgPath", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.TypeId;
			parameters[3].Value = model.PraiseLevel;
			parameters[4].Value = model.StudentId;
			parameters[5].Value = model.PraiseDesc;
			parameters[6].Value = model.ImgPath;
			parameters[7].Value = model.ClassId;
			parameters[8].Value = model.GradeId;
			parameters[9].Value = model.TermId;
			parameters[10].Value = model.CreateUserId;
			parameters[11].Value = model.CreateTime;
			parameters[12].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_Praise ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_Praise ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_Praise GetModel(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,ColumnId,ColumnPath,TypeId,PraiseLevel,StudentId,PraiseDesc,ImgPath,ClassId,GradeId,TermId,CreateUserId,CreateTime from ecb_Praise ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.ecb_Praise model=new ECB.PC.Model.ecb_Praise();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_Praise DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_Praise model=new ECB.PC.Model.ecb_Praise();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["TypeId"]!=null && row["TypeId"].ToString()!="")
				{
					model.TypeId= new Guid(row["TypeId"].ToString());
				}
				if(row["PraiseLevel"]!=null && row["PraiseLevel"].ToString()!="")
				{
					model.PraiseLevel=int.Parse(row["PraiseLevel"].ToString());
				}
				if(row["StudentId"]!=null && row["StudentId"].ToString()!="")
				{
					model.StudentId= new Guid(row["StudentId"].ToString());
				}
				if(row["PraiseDesc"]!=null)
				{
					model.PraiseDesc=row["PraiseDesc"].ToString();
				}
				if(row["ImgPath"]!=null)
				{
					model.ImgPath=row["ImgPath"].ToString();
				}
				if(row["ClassId"]!=null && row["ClassId"].ToString()!="")
				{
					model.ClassId= new Guid(row["ClassId"].ToString());
				}
				if(row["GradeId"]!=null && row["GradeId"].ToString()!="")
				{
					model.GradeId= new Guid(row["GradeId"].ToString());
				}
				if(row["TermId"]!=null && row["TermId"].ToString()!="")
				{
					model.TermId= new Guid(row["TermId"].ToString());
				}
				if(row["CreateUserId"]!=null && row["CreateUserId"].ToString()!="")
				{
					model.CreateUserId= new Guid(row["CreateUserId"].ToString());
				}
				if(row["CreateTime"]!=null && row["CreateTime"].ToString()!="")
				{
					model.CreateTime=DateTime.Parse(row["CreateTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,ColumnId,ColumnPath,TypeId,PraiseLevel,StudentId,PraiseDesc,ImgPath,ClassId,GradeId,TermId,CreateUserId,CreateTime ");
			strSql.Append(" FROM ecb_Praise ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,ColumnId,ColumnPath,TypeId,PraiseLevel,StudentId,PraiseDesc,ImgPath,ClassId,GradeId,TermId,CreateUserId,CreateTime ");
			strSql.Append(" FROM ecb_Praise ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_Praise ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string file,string table, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM (SELECT " + file + " from " + table + " ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" ) tt");
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string fileName, string tabName, string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by  " + orderby);
            }
            else
            {
                strSql.Append("order by  a.Id desc");
            }
            strSql.Append(" )AS Row, " + fileName + "  from " + tabName + "  ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Praise";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod

        /// <summary>
        /// 表扬统计列表
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetClassPraise(string timeWhere,string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT  ISNULL(CAST(((SELECT COUNT(StudentId) FROM dbo.ecb_Praise WHERE ClassId=a.id)*1.0 /NULLIF((SELECT COUNT(a.ID)  FROM dbo.JC_StudentInfos a LEFT JOIN dbo.JC_GradeInfos b ON a.GradeID=b.ID AND a.ColumnId=b.SchoolColumnId WHERE a.GradeID='2EF9A6ED-DAFF-43E1-8B26-83AE3A0C8391'),0) )*100 AS decimal(18, 2)),0) as pers ,");
            strSql.Append("a.ID,b.GradeName,a.ClassName+':'+CAST((SELECT COUNT(StudentId) FROM dbo.ecb_Praise WHERE ClassId=a.id) AS NVARCHAR(50)) +'人'  AS  name FROM dbo.JC_ClassInfos a LEFT JOIN dbo.JC_GradeInfos b ON a.GradeId=b.ID");
            if(strWhere.Trim()!="")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" GROUP BY a.ID,b.GradeName,a.ClassName ");
            return DbHelperSQL.Query(strSql.ToString());
        }

        //public DataSet GetGradePraise(string timeWhere,string strWhere)
        //{
        //    StringBuilder strSql = new StringBuilder();
        //    strSql.Append(" SELECT a.GradeName+':'+CAST((SELECT COUNT(StudentId) FROM dbo.ecb_Praise WHERE GradeId=a.ID " + timeWhere + ")AS NVARCHAR(50))+'人' name,");
        //    strSql.Append(" ISNULL(CAST(((SELECT COUNT(StudentId) FROM dbo.ecb_Praise WHERE GradeId=a.ID "+timeWhere+")*1.0 /");
        //    strSql.Append(" NULLIF((SELECT COUNT(a.ID)  FROM dbo.JC_StudentInfos a LEFT JOIN dbo.JC_GradeInfos b ON a.GradeID=b.ID AND a.ColumnId=b.SchoolColumnId WHERE "+strWhere+"),0))*100 AS decimal(18, 2)),0) as pers,");
        //    strSql.Append(" a.ID FROM dbo.JC_GradeInfos a ");
        //    if (strWhere.Trim() != "")
        //    {
        //        strSql.Append(" where " + strWhere);
        //    }
        //    strSql.Append(" GROUP BY a.ID,a.GradeName ");
        //    return DbHelperSQL.Query(strSql.ToString());
        //}
        /// <summary>
        /// 班级表扬统计
        /// </summary>
        /// <param name="timeWhere"></param>
        /// <param name="strWhere"></param>
        /// <param name="gradeId"></param>
        /// <returns></returns>
         public DataSet GetClassPraiseC(string timeWhere,string strWhere,Guid gradeId)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append("SELECT a.ClassName name,(SELECT COUNT( DISTINCT StudentId) FROM dbo.ecb_Praise WHERE ClassId=a.id " + timeWhere + ") stuNum,");
            strSql.Append(" (SELECT COUNT(StudentId) FROM dbo.ecb_Praise WHERE ClassId=a.id " + timeWhere + ") praiseNUm,");
            strSql.Append(" ISNULL(CAST(((SELECT COUNT(DISTINCT StudentId) FROM dbo.ecb_Praise WHERE ClassId=a.id " + timeWhere + ")*1.0 /");
            strSql.Append(" NULLIF((SELECT COUNT(ID)  FROM dbo.JC_StudentInfos where  ClassID=a.ID),0) )*100 AS decimal(18, 2)),0) as pers,");
            strSql.Append(" a.ID FROM dbo.JC_ClassInfos a LEFT JOIN dbo.JC_GradeInfos b ON a.GradeId=b.ID");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" GROUP BY a.ID,b.GradeName,a.ClassName,a.OrderId order by a.OrderId ");
            return DbHelperSQL.Query(strSql.ToString());
         }
             
        /// <summary>
        /// 年级表扬统计
        /// </summary>
        /// <param name="timeWhere"></param>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetGradePraiseC(string timeWhere, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT a.GradeName name,(SELECT COUNT(DISTINCT StudentId) FROM dbo.ecb_Praise WHERE GradeId=a.ID " + timeWhere + " ) AS stuNum,");//表扬人数
            strSql.Append(" (SELECT COUNT(StudentId) FROM dbo.ecb_Praise WHERE GradeId=a.ID " + timeWhere + ") praiseNum,"); ; //表扬次数
            strSql.Append(" ISNULL(CAST(((SELECT COUNT(DISTINCT StudentId) FROM dbo.ecb_Praise WHERE GradeId=a.ID " + timeWhere + ")*1.0 /");
            strSql.Append(" NULLIF((SELECT COUNT(ID)  FROM dbo.JC_StudentInfos where  GradeID=a.ID),0))*100 AS decimal(18, 2)),0) as pers,"); //表扬覆盖率 
            strSql.Append(" a.ID FROM dbo.JC_GradeInfos a ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" GROUP BY a.ID,a.GradeName,a.OrderId order by a.OrderId ");
            return DbHelperSQL.Query(strSql.ToString());
        }
	}
}

