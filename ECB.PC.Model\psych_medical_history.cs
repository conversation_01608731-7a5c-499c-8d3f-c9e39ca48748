﻿using System;

namespace ECB.PC.Model
{
    /// <summary>
    /// psych_medical_history:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class psych_medical_history
    {
        public psych_medical_history()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _studentid;
        private string _zdjb;
        private string _jwxljb;
        private DateTime _appointmenttime;
        private string _mzbl_photos;
        private string _memo;
        private Guid _lasteditby;
        private DateTime _lastedittime;
        /// <summary>
        /// 主键
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区id路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 学生Id
        /// </summary>
        public Guid StudentId
        {
            set { _studentid = value; }
            get { return _studentid; }
        }
        /// <summary>
        /// 重大或慢性疾病
        /// </summary>
        public string ZDJB
        {
            set { _zdjb = value; }
            get { return _zdjb; }
        }
        /// <summary>
        /// 既往心理疾病
        /// </summary>
        public string JWXLJB
        {
            set { _jwxljb = value; }
            get { return _jwxljb; }
        }
        /// <summary>
        /// 就诊时间
        /// </summary>
        public DateTime AppointmentTime
        {
            set { _appointmenttime = value; }
            get { return _appointmenttime; }
        }
        /// <summary>
        /// 门诊病历图片
        /// </summary>
        public string MZBL_Photos
        {
            set { _mzbl_photos = value; }
            get { return _mzbl_photos; }
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string Memo
        {
            set { _memo = value; }
            get { return _memo; }
        }
        /// <summary>
        /// 最后修改人
        /// </summary>
        public Guid LastEditBy
        {
            set { _lasteditby = value; }
            get { return _lasteditby; }
        }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        #endregion Model

    }
}
