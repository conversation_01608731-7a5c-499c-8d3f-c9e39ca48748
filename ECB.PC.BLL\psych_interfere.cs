﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_interfere
    /// </summary>
    public partial class psych_interfere
    {
        public psych_interfere()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_interfere");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.psych_interfere model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_interfere(");
            strSql.Append("Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,InterfereLevel,LastInterviewTime,LastInterviewMethod,LastInterviewTheme,ReferralSituation,HospitalDiagnosis,HospitalDiagnosisPic,Memo,CreatorId,CreateTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@SourceType,@SourceId,@InterfereLevel,@LastInterviewTime,@LastInterviewMethod,@LastInterviewTheme,@ReferralSituation,@HospitalDiagnosis,@HospitalDiagnosisPic,@Memo,@CreatorId,@CreateTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SourceType", SqlDbType.Int,4),
                    new SqlParameter("@SourceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@InterfereLevel", SqlDbType.NVarChar,4),
                    new SqlParameter("@LastInterviewTime", SqlDbType.DateTime),
                    new SqlParameter("@LastInterviewMethod", SqlDbType.NVarChar,50),
                    new SqlParameter("@LastInterviewTheme", SqlDbType.NVarChar,150),
                    new SqlParameter("@ReferralSituation", SqlDbType.NVarChar,4),
                    new SqlParameter("@HospitalDiagnosis", SqlDbType.NVarChar,500),
                    new SqlParameter("@HospitalDiagnosisPic", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Memo", SqlDbType.NVarChar,500),
                    new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.SourceType;
            parameters[5].Value = model.SourceId;
            parameters[6].Value = model.InterfereLevel;
            parameters[7].Value = model.LastInterviewTime;
            parameters[8].Value = model.LastInterviewMethod;
            parameters[9].Value = model.LastInterviewTheme;
            parameters[10].Value = model.ReferralSituation;
            parameters[11].Value = model.HospitalDiagnosis;
            parameters[12].Value = model.HospitalDiagnosisPic;
            parameters[13].Value = model.Memo;
            parameters[14].Value =model.CreatorId;
            parameters[15].Value = model.CreateTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.psych_interfere model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_interfere set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("SourceType=@SourceType,");
            strSql.Append("SourceId=@SourceId,");
            strSql.Append("InterfereLevel=@InterfereLevel,");
            strSql.Append("LastInterviewTime=@LastInterviewTime,");
            strSql.Append("LastInterviewMethod=@LastInterviewMethod,");
            strSql.Append("LastInterviewTheme=@LastInterviewTheme,");
            strSql.Append("ReferralSituation=@ReferralSituation,");
            strSql.Append("HospitalDiagnosis=@HospitalDiagnosis,");
            strSql.Append("HospitalDiagnosisPic=@HospitalDiagnosisPic,");
            strSql.Append("Memo=@Memo,");
            strSql.Append("CreatorId=@CreatorId,");
            strSql.Append("CreateTime=@CreateTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SourceType", SqlDbType.Int,4),
                    new SqlParameter("@SourceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@InterfereLevel", SqlDbType.NVarChar,4),
                    new SqlParameter("@LastInterviewTime", SqlDbType.DateTime),
                    new SqlParameter("@LastInterviewMethod", SqlDbType.NVarChar,50),
                    new SqlParameter("@LastInterviewTheme", SqlDbType.NVarChar,150),
                    new SqlParameter("@ReferralSituation", SqlDbType.NVarChar,4),
                    new SqlParameter("@HospitalDiagnosis", SqlDbType.NVarChar,500),
                    new SqlParameter("@HospitalDiagnosisPic", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Memo", SqlDbType.NVarChar,500),
                    new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.StudentId;
            parameters[3].Value = model.SourceType;
            parameters[4].Value = model.SourceId;
            parameters[5].Value = model.InterfereLevel;
            parameters[6].Value = model.LastInterviewTime;
            parameters[7].Value = model.LastInterviewMethod;
            parameters[8].Value = model.LastInterviewTheme;
            parameters[9].Value = model.ReferralSituation;
            parameters[10].Value = model.HospitalDiagnosis;
            parameters[11].Value = model.HospitalDiagnosisPic;
            parameters[12].Value = model.Memo;
            parameters[13].Value = model.CreatorId;
            parameters[14].Value = model.CreateTime;
            parameters[15].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_interfere ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_interfere ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_interfere GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,InterfereLevel,LastInterviewTime,LastInterviewMethod,LastInterviewTheme,ReferralSituation,HospitalDiagnosis,HospitalDiagnosisPic,Memo,CreatorId,CreateTime from psych_interfere ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.psych_interfere model = new ECB.PC.Model.psych_interfere();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_interfere DataRowToModel(DataRow row)
        {
            ECB.PC.Model.psych_interfere model = new ECB.PC.Model.psych_interfere();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["SourceType"] != null && row["SourceType"].ToString() != "")
                {
                    model.SourceType = int.Parse(row["SourceType"].ToString());
                }
                if (row["SourceId"] != null && row["SourceId"].ToString() != "")
                {
                    model.SourceId = new Guid(row["SourceId"].ToString());
                }
                if (row["InterfereLevel"] != null)
                {
                    model.InterfereLevel = row["InterfereLevel"].ToString();
                }
                if (row["LastInterviewTime"] != null && row["LastInterviewTime"].ToString() != "")
                {
                    model.LastInterviewTime = DateTime.Parse(row["LastInterviewTime"].ToString());
                }
                if (row["LastInterviewMethod"] != null)
                {
                    model.LastInterviewMethod = row["LastInterviewMethod"].ToString();
                }
                if (row["LastInterviewTheme"] != null)
                {
                    model.LastInterviewTheme = row["LastInterviewTheme"].ToString();
                }
                if (row["ReferralSituation"] != null)
                {
                    model.ReferralSituation = row["ReferralSituation"].ToString();
                }
                if (row["HospitalDiagnosis"] != null)
                {
                    model.HospitalDiagnosis = row["HospitalDiagnosis"].ToString();
                }
                if (row["HospitalDiagnosisPic"] != null)
                {
                    model.HospitalDiagnosisPic = row["HospitalDiagnosisPic"].ToString();
                }
                if (row["Memo"] != null)
                {
                    model.Memo = row["Memo"].ToString();
                }
                if (row["CreatorId"] != null && row["CreatorId"].ToString() != "")
                {
                    model.CreatorId = new Guid(row["CreatorId"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,InterfereLevel,LastInterviewTime,LastInterviewMethod,LastInterviewTheme,ReferralSituation,HospitalDiagnosis,HospitalDiagnosisPic,Memo,CreatorId,CreateTime ");
            strSql.Append(" FROM psych_interfere ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,InterfereLevel,LastInterviewTime,LastInterviewMethod,LastInterviewTheme,ReferralSituation,HospitalDiagnosis,HospitalDiagnosisPic,Memo,CreatorId,CreateTime ");
            strSql.Append(" FROM psych_interfere ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_interfere ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_interfere T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_interfere";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 获取干预记录统计数据
        /// 功能：统计干预记录，按来源类型和干预级别分组
        /// 关键逻辑：关联学生信息表进行权限过滤，处理访谈信息的特殊来源类型（3开头的都归为3）
        /// </summary>
        /// <param name="strWhereClause">where条件字符串，包含地区、年级、班级、时间等过滤条件</param>
        /// <returns>干预统计数据表，包含SourceType、InterfereLevel、Count字段</returns>
        public DataTable GetInterfereStatistics(string strWhereClause)
        {
            string strSql = @"
                SELECT
                    CASE
                        WHEN i.SourceType LIKE '3%' THEN 3
                        ELSE i.SourceType
                    END AS SourceType,
                    i.InterfereLevel,
                    COUNT(i.Id) AS Count
                FROM psych_interfere i
                INNER JOIN JC_StudentInfos s ON i.StudentId = s.ID
                WHERE " + strWhereClause + @"
                GROUP BY
                    CASE
                        WHEN i.SourceType LIKE '3%' THEN 3
                        ELSE i.SourceType
                    END,
                    i.InterfereLevel";

            DataSet ds = DbHelperSQL.Query(strSql);
            return ds != null && ds.Tables.Count > 0 ? ds.Tables[0] : new DataTable();
        }

        /// <summary>
        /// 获取干预统计
        /// </summary>
        /// <param name="columnId"></param>
        /// <param name="gradeId"></param>
        /// <param name="classId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public DataSet GetInterfereSta(int? columnId = null, Guid? gradeId = null, Guid? classId = null, DateTime? startTime = null, DateTime? endTime = null)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"SELECT 
            COUNT(DISTINCT PI.CreatorId) AS 参与教师,
            COUNT(DISTINCT PI.StudentId) AS 干预人数,
            COUNT(1) AS 干预人次,
            COUNT(DISTINCT pe.StudentId) AS 转介人数
             FROM psych_interfere PI LEFT JOIN psych_interfere pe ON PI.Id=pe.Id AND PI.StudentId = pe.StudentId AND pe.ReferralSituation IN(1,2)
            LEFT JOIN JC_StudentInfos stu on PI.StudentId = stu.ID ");

            sql.AppendLine(" WHERE 1=1");
            List<SqlParameter> parameters = new List<SqlParameter>();

            if (startTime.HasValue)
            {
                sql.AppendLine(" AND PI.CreateTime >= @StartTime");
                parameters.Add(new SqlParameter("@StartTime", SqlDbType.DateTime) { Value = startTime.Value });
            }

            if (endTime.HasValue)
            {
                sql.AppendLine(" AND PI.CreateTime <= @EndTime");
                parameters.Add(new SqlParameter("@EndTime", SqlDbType.DateTime) { Value = endTime.Value });
            }

            if (columnId.HasValue)
            {
                sql.AppendLine(" AND stu.ColumnId = @ColumnId");
                parameters.Add(new SqlParameter("@ColumnId", SqlDbType.Int) { Value = columnId.Value });
            }

            if (gradeId.HasValue)
            {
                sql.AppendLine(" AND stu.GradeID = @GradeId");
                parameters.Add(new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier) { Value = gradeId.Value });
            }

            if (classId.HasValue)
            {
                sql.AppendLine(" AND stu.ClassID = @ClassId");
                parameters.Add(new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier) { Value = classId.Value });
            }

            DataSet result = DbHelperSQL.Query(sql.ToString(), parameters.ToArray());
            return result;
        }

        public int GetCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_interfere t");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        public DataTable GetAbnormalStat(int ColumnId, string ColumnPath, string IsSchool,Guid termId)
        {
            string strSql = @"
                SELECT p.AbnormalStatus , COUNT(p.Id) AS Count
                FROM psych_basicInfo p
                INNER JOIN JC_StudentInfos s ON p.StudentId = s.ID
                WHERE ";
            strSql += @" p.ColumnPath+'|' like '"+ColumnPath+"|%'";
            if (!string.IsNullOrEmpty(IsSchool) && IsSchool == "1")
            {
                strSql += @" and p.ColumnID="+ColumnId+"";
            }
            if(termId!=Guid.Empty)
            {
                strSql += @" and s.SchoolYear IN (SELECT JC_TermInfos.SchoolYear FROM JC_TermInfos WHERE ID='"+ termId + "')";
            }
            strSql += @"
                AND p.AbnormalStatus IS NOT NULL AND p.AbnormalStatus <> ''
                GROUP BY p.AbnormalStatus";

            DataSet ds = DbHelperSQL.Query(strSql);
            return ds != null && ds.Tables.Count > 0 ? ds.Tables[0] : new DataTable();
        }
        #endregion  ExtensionMethod
    }
}