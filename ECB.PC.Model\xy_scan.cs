﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// xy_scan:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class xy_scan
	{
		public xy_scan()
		{}
		#region Model
		private Guid _sid;
		private Guid _user_id;
		private string _token;
		private string _stype;
		private DateTime? _s_time;
		private DateTime? _c_time;
		private int? _isok;
		private string _memo;
		/// <summary>
		/// 扫码唯一标识
		/// </summary>
		public Guid sid
		{
			set{ _sid=value;}
			get{return _sid;}
		}
		/// <summary>
		/// 扫码人
		/// </summary>
		public Guid user_id
		{
			set{ _user_id=value;}
			get{return _user_id;}
		}
		/// <summary>
		/// 扫码人token(验证身份，可能不会使用)
		/// </summary>
		public string token
		{
			set{ _token=value;}
			get{return _token;}
		}
		/// <summary>
		/// 扫码类型
		/// </summary>
		public string stype
		{
			set{ _stype=value;}
			get{return _stype;}
		}
		/// <summary>
		/// 扫码时间
		/// </summary>
		public DateTime? s_time
		{
			set{ _s_time=value;}
			get{return _s_time;}
		}
		/// <summary>
		/// 确认时间
		/// </summary>
		public DateTime? c_time
		{
			set{ _c_time=value;}
			get{return _c_time;}
		}
		/// <summary>
		/// 状态(默认0，确认1)
		/// </summary>
		public int? isok
		{
			set{ _isok=value;}
			get{return _isok;}
		}
		/// <summary>
		/// 说明
		/// </summary>
		public string memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		#endregion Model

	}
}

