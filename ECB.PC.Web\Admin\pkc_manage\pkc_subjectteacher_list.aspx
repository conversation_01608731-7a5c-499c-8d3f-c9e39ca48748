﻿<%@ Page Language="C#" AutoEventWireup="true" StylesheetTheme="Admin_Default" EnableEventValidation="false" CodeBehind="pkc_subjectteacher_list.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_subjectteacher_list" %>

<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>监考老师信息管理</title>
    <script src="../../js/jquery-1.8.3.min.js"></script>
    <link href="/js/layer/skin/default/layer.css" rel="stylesheet" />
    <script src="../../js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <asp:Panel ID="pnlButtons" runat="server" CssClass="search-bar">
                   显示：<asp:DropDownList ID="dorpShowSize" runat="server">
                    <asp:ListItem Value="10">10</asp:ListItem>
                    <asp:ListItem Value="15">15</asp:ListItem>
                    <asp:ListItem Value="20">20</asp:ListItem>
                    <asp:ListItem Value="50">50</asp:ListItem>
                    <asp:ListItem Value="80">80</asp:ListItem>
                    <asp:ListItem Value="100">100</asp:ListItem>
                </asp:DropDownList>
                <%--  考场：<asp:DropDownList ID="ddlRoom" runat="server" CssClass="input"></asp:DropDownList>--%>
                科目：<asp:DropDownList ID="ddlsubject" runat="server">
                </asp:DropDownList>
                <%--       <asp:DropDownList ID="ddlartormath" runat="server" OnSelectedIndexChanged="ddlartormath_SelectedIndexChanged" AutoPostBack="true">
                    <asp:ListItem Selected="True" Value="1">文科</asp:ListItem>
                    <asp:ListItem Value="2">理科</asp:ListItem>
                </asp:DropDownList>--%>            
                查询：<asp:DropDownList ID="dropType" runat="server">
                    <asp:ListItem Value="0">=请选择=</asp:ListItem>
                    <asp:ListItem Value="1" Selected="True">老师姓名</asp:ListItem>
                </asp:DropDownList>
                <asp:TextBox ID="inputName" runat="server"></asp:TextBox>
                <asp:Button ID="btnSearch" runat="server" Text="查 找" CssClass="btn" OnClick="btnSearch_Click" ValidationGroup="1" />
                <%--                <asp:Label ID="lblShowTime" runat="server" Text=""></asp:Label>--%>
               
            </asp:Panel>
            <asp:GridView ID="gvTestTeachersList" runat="server" Width="100%" CellPadding="4" SkinID="gridviewSkin1" EmptyDataText="未找到信息"
                BorderWidth="1px" DataKeyNames="ID"
                AutoGenerateColumns="False" RowStyle-HorizontalAlign="Center">
                <Columns>
                    <asp:TemplateField HeaderText="选择">
                        <ItemTemplate>
                            <label class="chkItem">
                            <asp:CheckBox ID="chkItem1" runat="server" />
                            <asp:HiddenField ID="hidID" runat="server" Value='<%# Eval("ID") %>' />
                                </label>
                        </ItemTemplate>
                        <HeaderStyle HorizontalAlign="Center" Width="40px" />
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="InvigilatorTeacher" HeaderText="监考老师" SortExpression="InvigilatorTeacher">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="SubjectName" HeaderText="考试科目" SortExpression="SubjectName">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="StartTime" HeaderText="开考时间" SortExpression="StartTime" DataFormatString="{0:d}">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="EndTime" HeaderText="结束时间" SortExpression="EndTime" DataFormatString="{0:d}">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="RoomNum" HeaderText="考场号" SortExpression="RoomNum">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="RoomLocation" HeaderText="考场所在位置" SortExpression="RoomLocation">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="InvigilatorNum" HeaderText="监考人数" SortExpression="InvigilatorNum">
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                    </asp:BoundField>
                </Columns>
            </asp:GridView>
            <div id="pager" class="paging-bar">
                    <div class="l-btns">
                        <span>显示</span><asp:TextBox ID="txtPageNum" runat="server"
                            AutoPostBack="True"
                            CssClass="pagenum" onkeydown="return checkNumber(event);"
                            OnTextChanged="txtPageNum_TextChanged"></asp:TextBox><span>条/页</span>
                    </div>
                    <webdiyer:AspNetPager ID="AspNetPager1" runat="server" AlwaysShow="True" AlwaysShowFirstLastPageNumber="True" CssClass="paging-default" CurrentPageButtonClass="current" CurrentPageButtonPosition="Center" CustomInfoHTML="&lt;span&gt;共%RecordCount%条记录&lt;/span&gt;" CustomInfoSectionWidth="" CustomInfoStyle="" CustomInfoTextAlign="NotSet" FirstPageText="首页" LastPageText="尾页" NavigationToolTipTextFormatString="{0}" NextPageText="下一页" NumericButtonCount="7" OnPageChanged="AspNetPager1_PageChanged" PageIndexBoxType="TextBox" PageSize="10" PagingButtonSpacing="0px" PrevPageText="上一页" ShowCustomInfoSection="Left" ShowFirstLast="False" ShowNavigationToolTip="True" ShowPageIndexBox="Never" UrlPaging="false">
                    </webdiyer:AspNetPager>
                </div>
            <div class="footer-bar">
                <label for="chkAll" class="btnWhite light">
	                <input id="chkAll" name="chkAll" type="checkbox" />全选
                </label>
                 <asp:Button ID="btnPTeacher" runat="server" Text="方法1.随机监考编排" CssClass="btnGreen" OnClientClick="HidePanel()" OnClick="btnPTeacher_Click" />
                <asp:Button ID="btnPTeacher2" runat="server" Text="方法2.固定监考编排" CssClass="btnGreen" OnClientClick="HidePanel()" OnClick="btnPTeacher2_Click" />
                <asp:Button ID="btnExport" runat="server" Text="导出监考安排表" CssClass="btnGreen" OnClick="btnExport_Click" />
                <asp:Button ID="btnDelAll" runat="server" Text="全部删除" OnClientClick='return CheckDo("该操作将删除本次考试所有考生数据，不可恢复，是否删除?","btnDelAll")' CssClass="btnRed" OnClick="btnDelAll_Click" />
                <asp:Button ID="btnBack" runat="server" Text="返回" CssClass="btnWhite" OnClick="btnBack_Click" />
            </div>
        </div>
        <div style="display: none;" id="loading">
            加载中……
        </div>
    </form>
    <script type="text/javascript">
        function HidePanel() {
            $("#div1").hide().addClass();
            $("#loading").show().addClass();
        }
    </script>
</body>
</html>
