﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_WebSorket:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_WebSorket
	{
		public ecb_WebSorket()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private string _websorket;
		private int? _port;
		private int? _isenable;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string WebSorket
		{
			set{ _websorket=value;}
			get{return _websorket;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Port
		{
			set{ _port=value;}
			get{return _port;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? IsEnable
		{
			set{ _isenable=value;}
			get{return _isenable;}
		}
		#endregion Model

	}
}

