﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 门禁表
    /// </summary>
    [Serializable]
    public partial class ecb_MenJin
    {
        public ecb_MenJin()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _deviceid;
        private string _doorname;
        private string _doorno;
        private Guid _placeid;
        /// <summary>
        /// 主键
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 设备id
        /// </summary>
        public Guid DeviceId
        {
            set { _deviceid = value; }
            get { return _deviceid; }
        }
        /// <summary>
        /// 门禁名称
        /// </summary>
        public string DoorName
        {
            set { _doorname = value; }
            get { return _doorname; }
        }
        /// <summary>
        /// 门禁编号
        /// </summary>
        public string DoorNo
        {
            set { _doorno = value; }
            get { return _doorno; }
        }
        /// <summary>
        /// 地点id
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        public string DeviceState { get; set; }
        #endregion Model

    }
}

