﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 审核记录
	/// </summary>
	[Serializable]
	public partial class ecb_FunctionRoom_Audit_Records
	{
		public ecb_FunctionRoom_Audit_Records()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _reservationid;
		private Guid _flowid;
		private string _flowcode;
		private int? _checkresult;
		private string _reason;
		private Guid _checkuserid;
		private DateTime? _checktime;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校Id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校Id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 预约记录id
		/// </summary>
		public Guid ReservationId
		{
			set{ _reservationid=value;}
			get{return _reservationid;}
		}
		/// <summary>
		/// 流程id
		/// </summary>
		public Guid FlowId
		{
			set{ _flowid=value;}
			get{return _flowid;}
		}
		/// <summary>
		/// 流程节点
		/// </summary>
		public string FlowCode
		{
			set{ _flowcode=value;}
			get{return _flowcode;}
		}
		/// <summary>
		/// 审核结果
		/// </summary>
		public int? CheckResult
		{
			set{ _checkresult=value;}
			get{return _checkresult;}
		}
		/// <summary>
		/// 驳回原因
		/// </summary>
		public string Reason
		{
			set{ _reason=value;}
			get{return _reason;}
		}
		/// <summary>
		/// 审核人
		/// </summary>
		public Guid CheckUserId
		{
			set{ _checkuserid=value;}
			get{return _checkuserid;}
		}
		/// <summary>
		/// 审核时间
		/// </summary>
		public DateTime? CheckTime
		{
			set{ _checktime=value;}
			get{return _checktime;}
		}
		#endregion Model

	}
}

