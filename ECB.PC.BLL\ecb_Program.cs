﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_Program
	/// </summary>
	public partial class ecb_Program
	{
		public ecb_Program()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_Program");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_Program model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_Program(");
			strSql.Append("Id,ColumnId,ColumnPath,ProgramName,IsShow,IsSue,Status,CreateUserId,CreateTime,Contents,Resource,CheckResult,Reason,CheckUserId,CheckTime,StartDate,EndDate,SerializedJson,TimeStamp,SeriaNos,IsHorizontal)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@ProgramName,@IsShow,@IsSue,@Status,@CreateUserId,@CreateTime,@Contents,@Resource,@CheckResult,@Reason,@CheckUserId,@CheckTime,@StartDate,@EndDate,@SerializedJson,@TimeStamp,@SeriaNos,@IsHorizontal)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@ProgramName", SqlDbType.NVarChar,50),
					new SqlParameter("@IsShow", SqlDbType.Int,4),
					new SqlParameter("@IsSue", SqlDbType.Int,4),
					new SqlParameter("@Status", SqlDbType.Int,4),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@Contents", SqlDbType.NVarChar,-1),
					new SqlParameter("@Resource", SqlDbType.NVarChar,255),
					new SqlParameter("@CheckResult", SqlDbType.Int,4),
					new SqlParameter("@Reason", SqlDbType.NVarChar,255),
					new SqlParameter("@CheckUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CheckTime", SqlDbType.DateTime),
					new SqlParameter("@StartDate", SqlDbType.DateTime),
					new SqlParameter("@EndDate", SqlDbType.DateTime),
					new SqlParameter("@SerializedJson", SqlDbType.NVarChar,-1),
					new SqlParameter("@TimeStamp", SqlDbType.BigInt,8),
                    new SqlParameter("@SeriaNos", SqlDbType.NVarChar,-1),
                    new SqlParameter("@IsHorizontal", SqlDbType.NVarChar,2)};
			parameters[0].Value = model.Id;
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.ProgramName;
			parameters[4].Value = model.IsShow;
			parameters[5].Value = model.IsSue;
			parameters[6].Value = model.Status;
			parameters[7].Value = model.CreateUserId;
			parameters[8].Value = model.CreateTime;
			parameters[9].Value = model.Contents;
			parameters[10].Value = model.Resource;
			parameters[11].Value = model.CheckResult;
			parameters[12].Value = model.Reason;
			parameters[13].Value = model.CreateUserId;
			parameters[14].Value = model.CheckTime;
			parameters[15].Value = model.StartDate;
			parameters[16].Value = model.EndDate;
			parameters[17].Value = model.SerializedJson;
			parameters[18].Value = model.TimeStamp;
			parameters[19].Value = model.SeriaNos;
            parameters[20].Value = model.IsHorizontal;
            int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_Program model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_Program set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("ProgramName=@ProgramName,");
			strSql.Append("IsShow=@IsShow,");
			strSql.Append("IsSue=@IsSue,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreateUserId=@CreateUserId,");
			strSql.Append("CreateTime=@CreateTime,");
			strSql.Append("Contents=@Contents,");
			strSql.Append("Resource=@Resource,");
			strSql.Append("CheckResult=@CheckResult,");
			strSql.Append("Reason=@Reason,");
			strSql.Append("CheckUserId=@CheckUserId,");
			strSql.Append("CheckTime=@CheckTime,");
			strSql.Append("StartDate=@StartDate,");
			strSql.Append("EndDate=@EndDate,");
			strSql.Append("SerializedJson=@SerializedJson,");
			strSql.Append("TimeStamp=@TimeStamp,");
            strSql.Append("SeriaNos=@SeriaNos,");
            strSql.Append("IsHorizontal=@IsHorizontal");
            strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@ProgramName", SqlDbType.NVarChar,50),
					new SqlParameter("@IsShow", SqlDbType.Int,4),
					new SqlParameter("@IsSue", SqlDbType.Int,4),
					new SqlParameter("@Status", SqlDbType.Int,4),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@Contents", SqlDbType.NVarChar,-1),
					new SqlParameter("@Resource", SqlDbType.NVarChar,255),
					new SqlParameter("@CheckResult", SqlDbType.Int,4),
					new SqlParameter("@Reason", SqlDbType.NVarChar,255),
					new SqlParameter("@CheckUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CheckTime", SqlDbType.DateTime),
					new SqlParameter("@StartDate", SqlDbType.DateTime),
					new SqlParameter("@EndDate", SqlDbType.DateTime),
					new SqlParameter("@SerializedJson", SqlDbType.NVarChar,-1),
					new SqlParameter("@TimeStamp", SqlDbType.BigInt,8),
                    new SqlParameter("@SeriaNos", SqlDbType.NVarChar,-1),
                    new SqlParameter("@IsHorizontal", SqlDbType.NVarChar,2),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.ProgramName;
			parameters[3].Value = model.IsShow;
			parameters[4].Value = model.IsSue;
			parameters[5].Value = model.Status;
			parameters[6].Value = model.CreateUserId;
			parameters[7].Value = model.CreateTime;
			parameters[8].Value = model.Contents;
			parameters[9].Value = model.Resource;
			parameters[10].Value = model.CheckResult;
			parameters[11].Value = model.Reason;
			parameters[12].Value = model.CheckUserId;
			parameters[13].Value = model.CheckTime;
			parameters[14].Value = model.StartDate;
			parameters[15].Value = model.EndDate;
			parameters[16].Value = model.SerializedJson;
			parameters[17].Value = model.TimeStamp;
			parameters[18].Value = model.SeriaNos;
            parameters[19].Value = model.IsHorizontal;
            parameters[20].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_Program ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_Program ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_Program GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 * from ecb_Program ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_Program model=new ECB.PC.Model.ecb_Program();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_Program DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_Program model=new ECB.PC.Model.ecb_Program();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["ProgramName"]!=null)
				{
					model.ProgramName=row["ProgramName"].ToString();
				}
				if(row["IsShow"]!=null && row["IsShow"].ToString()!="")
				{
					model.IsShow=int.Parse(row["IsShow"].ToString());
				}
				if(row["IsSue"]!=null && row["IsSue"].ToString()!="")
				{
					model.IsSue=int.Parse(row["IsSue"].ToString());
				}
				if(row["Status"]!=null && row["Status"].ToString()!="")
				{
					model.Status=int.Parse(row["Status"].ToString());
				}
				if(row["CreateUserId"]!=null && row["CreateUserId"].ToString()!="")
				{
					model.CreateUserId= new Guid(row["CreateUserId"].ToString());
				}
				if(row["CreateTime"]!=null && row["CreateTime"].ToString()!="")
				{
					model.CreateTime=DateTime.Parse(row["CreateTime"].ToString());
				}
				if(row["Contents"]!=null)
				{
					model.Contents=row["Contents"].ToString();
				}
				if(row["Resource"]!=null)
				{
					model.Resource=row["Resource"].ToString();
				}
				if(row["CheckResult"]!=null && row["CheckResult"].ToString()!="")
				{
					model.CheckResult=int.Parse(row["CheckResult"].ToString());
				}
				if(row["Reason"]!=null)
				{
					model.Reason=row["Reason"].ToString();
				}
				if(row["CheckUserId"]!=null && row["CheckUserId"].ToString()!="")
				{
					model.CheckUserId= new Guid(row["CheckUserId"].ToString());
				}
				if(row["CheckTime"]!=null && row["CheckTime"].ToString()!="")
				{
					model.CheckTime=DateTime.Parse(row["CheckTime"].ToString());
				}
				if (row["StartDate"] != null && row["StartDate"].ToString() != "")
				{
					model.StartDate = DateTime.Parse(row["StartDate"].ToString());
				}
				if (row["EndDate"] != null && row["EndDate"].ToString() != "")
				{
					model.EndDate = DateTime.Parse(row["EndDate"].ToString());
				}
				if (row["SerializedJson"] != null)
				{
					model.SerializedJson = row["SerializedJson"].ToString();
				}
				if (row["TimeStamp"] != null && row["TimeStamp"].ToString() != "")
				{
					model.TimeStamp = long.Parse(row["TimeStamp"].ToString());
				}
                if (row["SeriaNos"] != null)
                {
                    model.SeriaNos = row["SeriaNos"].ToString();
                }
                if (row["IsHorizontal"] != null)
                {
                    model.IsHorizontal = row["IsHorizontal"].ToString();
                }
            }
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select * ");
			strSql.Append(" FROM ecb_Program ");
			if (strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" * ");
			strSql.Append(" FROM ecb_Program ");
			if (strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_Program ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_Program T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Program";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

