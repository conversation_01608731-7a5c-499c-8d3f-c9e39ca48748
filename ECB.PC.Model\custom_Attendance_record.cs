﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// custom_Attendance_record:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class custom_Attendance_record
	{
		public custom_Attendance_record()
		{}
		#region Model
		private Guid _id;
		private Guid _userid;
		private string _attendancetype;
		private DateTime? _attendancetime;
		private string _reason;
		private Guid _attendanceid;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string AttendanceType
		{
			set{ _attendancetype=value;}
			get{return _attendancetype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? AttendanceTime
		{
			set{ _attendancetime=value;}
			get{return _attendancetime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Reason
		{
			set{ _reason=value;}
			get{return _reason;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid AttendanceId
		{
			set{ _attendanceid=value;}
			get{return _attendanceid;}
		}
		#endregion Model

	}
}

