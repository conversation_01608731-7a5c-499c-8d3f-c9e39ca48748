﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:DA_XS_FeaturedActivities
    /// </summary>
    public partial class DA_XS_FeaturedActivities
    {
        public DA_XS_FeaturedActivities()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from DA_XS_FeaturedActivities");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.DA_XS_FeaturedActivities model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into DA_XS_FeaturedActivities(");
            strSql.Append("ID,SchoolColumnId,SchoolColumnPath,SchoolYear,TermId,GradeId,ClassId,Name,Description,CreatorUserId,CreateTime,CreatorName,IsPass,IsPassBy,IsPassDate)");
            strSql.Append(" values (");
            strSql.Append("@ID,@SchoolColumnId,@SchoolColumnPath,@SchoolYear,@TermId,@GradeId,@ClassId,@Name,@Description,@CreatorUserId,@CreateTime,@CreatorName,@IsPass,@IsPassBy,@IsPassDate)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@SchoolYear", SqlDbType.NChar,10),
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,100),
                    new SqlParameter("@Description", SqlDbType.NVarChar,-1),
                    new SqlParameter("@CreatorUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@CreatorName", SqlDbType.NVarChar,50),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.SchoolColumnId;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.SchoolYear;
            parameters[4].Value = model.TermId;
            parameters[5].Value = model.GradeId;
            parameters[6].Value = model.ClassId;
            parameters[7].Value = model.Name;
            parameters[8].Value = model.Description;
            parameters[9].Value = model.CreatorUserId;
            parameters[10].Value = model.CreateTime;
            parameters[11].Value = model.CreatorName;
            parameters[12].Value = model.IsPass;
            parameters[13].Value = model.IsPassBy;
            parameters[14].Value = model.IsPassDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.DA_XS_FeaturedActivities model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update DA_XS_FeaturedActivities set ");
            strSql.Append("SchoolColumnId=@SchoolColumnId,");
            strSql.Append("SchoolColumnPath=@SchoolColumnPath,");
            strSql.Append("SchoolYear=@SchoolYear,");
            strSql.Append("TermId=@TermId,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("Name=@Name,");
            strSql.Append("Description=@Description,");
            strSql.Append("CreatorUserId=@CreatorUserId,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("CreatorName=@CreatorName,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("IsPassDate=@IsPassDate");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@SchoolYear", SqlDbType.NChar,10),
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,100),
                    new SqlParameter("@Description", SqlDbType.NVarChar,-1),
                    new SqlParameter("@CreatorUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@CreatorName", SqlDbType.NVarChar,50),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.SchoolColumnId;
            parameters[1].Value = model.SchoolColumnPath;
            parameters[2].Value = model.SchoolYear;
            parameters[3].Value = model.TermId;
            parameters[4].Value = model.GradeId;
            parameters[5].Value = model.ClassId;
            parameters[6].Value = model.Name;
            parameters[7].Value = model.Description;
            parameters[8].Value = model.CreatorUserId;
            parameters[9].Value = model.CreateTime;
            parameters[10].Value = model.CreatorName;
            parameters[11].Value = model.IsPass;
            parameters[12].Value = model.IsPassBy;
            parameters[13].Value = model.IsPassDate;
            parameters[14].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from DA_XS_FeaturedActivities ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from DA_XS_FeaturedActivities ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_XS_FeaturedActivities GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,SchoolColumnId,SchoolColumnPath,SchoolYear,TermId,GradeId,ClassId,Name,Description,CreatorUserId,CreateTime,CreatorName,IsPass,IsPassBy,IsPassDate from DA_XS_FeaturedActivities ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.DA_XS_FeaturedActivities model = new ECB.PC.Model.DA_XS_FeaturedActivities();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_XS_FeaturedActivities DataRowToModel(DataRow row)
        {
            ECB.PC.Model.DA_XS_FeaturedActivities model = new ECB.PC.Model.DA_XS_FeaturedActivities();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["SchoolColumnId"] != null && row["SchoolColumnId"].ToString() != "")
                {
                    model.SchoolColumnId = int.Parse(row["SchoolColumnId"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
                if (row["TermId"] != null && row["TermId"].ToString() != "")
                {
                    model.TermId = new Guid(row["TermId"].ToString());
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["Description"] != null)
                {
                    model.Description = row["Description"].ToString();
                }
                if (row["CreatorUserId"] != null && row["CreatorUserId"].ToString() != "")
                {
                    model.CreatorUserId = new Guid(row["CreatorUserId"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["CreatorName"] != null)
                {
                    model.CreatorName = row["CreatorName"].ToString();
                }
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    model.IsPass = int.Parse(row["IsPass"].ToString());
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,SchoolColumnId,SchoolColumnPath,SchoolYear,TermId,GradeId,ClassId,Name,Description,CreatorUserId,CreateTime,CreatorName,IsPass,IsPassBy,IsPassDate ");
            strSql.Append(" FROM DA_XS_FeaturedActivities ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,SchoolColumnId,SchoolColumnPath,SchoolYear,TermId,GradeId,ClassId,Name,Description,CreatorUserId,CreateTime,CreatorName,IsPass,IsPassBy,IsPassDate ");
            strSql.Append(" FROM DA_XS_FeaturedActivities ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM DA_XS_FeaturedActivities ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from DA_XS_FeaturedActivities T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 根据"TermId"和"ClassId"获得两个数据列表的集合
        /// </summary>
        public DataSet GetListById(string TermId, string ClassId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select FilePath,FileName,Name,Description from");
            strSql.Append(" DA_XS_FeaturedActivities a left join DA_XS_GrowAttachment b on a.ID=b.RelationId where 1=1 ");

            if (TermId.Trim() != "")
            {
                strSql.Append(" and TermId = '" + TermId + "'");
            }
            if (ClassId.Trim() != "")
            {
                strSql.Append(" and ClassId = '" + ClassId + "'");
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DA_XS_FeaturedActivities";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 自定义字段
        /// </summary>
        public DataSet GetList(string strWhere, string field, string tabName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (!string.IsNullOrEmpty(field))
            {
                strSql.Append(field);
            }
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

