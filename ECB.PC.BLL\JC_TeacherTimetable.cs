﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:JC_TeacherTimetable
	/// </summary>
	public partial class JC_TeacherTimetable
	{
		public JC_TeacherTimetable()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from JC_TeacherTimetable");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.JC_TeacherTimetable model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into JC_TeacherTimetable(");
			strSql.Append("ID,ColumnID,ColumnPath,TeacherNo,ClassID,SchoolYear,TermID,Weekday,Class,SubjectCode)");
			strSql.Append(" values (");
			strSql.Append("@ID,@ColumnID,@ColumnPath,@TeacherNo,@ClassID,@SchoolYear,@TermID,@Weekday,@Class,@SubjectCode)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnID", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@TeacherNo", SqlDbType.NVarChar,18),
					new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Weekday", SqlDbType.Int,4),
					new SqlParameter("@Class", SqlDbType.Int,4),
					new SqlParameter("@SubjectCode", SqlDbType.NVarChar,10)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnID;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.TeacherNo;
			parameters[4].Value = model.ClassID;
            parameters[5].Value = model.SchoolYear;
			parameters[6].Value = model.TermID;
            parameters[7].Value = model.Weekday;
			parameters[8].Value = model.Class;
			parameters[9].Value = model.SubjectCode;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.JC_TeacherTimetable model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update JC_TeacherTimetable set ");
			strSql.Append("ColumnID=@ColumnID,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("TeacherNo=@TeacherNo,");
			strSql.Append("ClassID=@ClassID,");
			strSql.Append("SchoolYear=@SchoolYear,");
			strSql.Append("TermID=@TermID,");
			strSql.Append("Weekday=@Weekday,");
			strSql.Append("Class=@Class,");
			strSql.Append("SubjectCode=@SubjectCode");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnID", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@TeacherNo", SqlDbType.NVarChar,18),
					new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Weekday", SqlDbType.Int,4),
					new SqlParameter("@Class", SqlDbType.Int,4),
					new SqlParameter("@SubjectCode", SqlDbType.NVarChar,10),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnID;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.TeacherNo;
			parameters[3].Value = model.ClassID;
			parameters[4].Value = model.SchoolYear;
			parameters[5].Value = model.TermID;
			parameters[6].Value = model.Weekday;
			parameters[7].Value = model.Class;
			parameters[8].Value = model.SubjectCode;
			parameters[9].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from JC_TeacherTimetable ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from JC_TeacherTimetable ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.JC_TeacherTimetable GetModel(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,ColumnID,ColumnPath,TeacherNo,ClassID,SchoolYear,TermID,Weekday,Class,SubjectCode from JC_TeacherTimetable ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.JC_TeacherTimetable model=new ECB.PC.Model.JC_TeacherTimetable();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.JC_TeacherTimetable DataRowToModel(DataRow row)
		{
			ECB.PC.Model.JC_TeacherTimetable model=new ECB.PC.Model.JC_TeacherTimetable();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["ColumnID"]!=null && row["ColumnID"].ToString()!="")
				{
					model.ColumnID=int.Parse(row["ColumnID"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["TeacherNo"]!=null)
				{
					model.TeacherNo=row["TeacherNo"].ToString();
				}
				if(row["ClassID"]!=null && row["ClassID"].ToString()!="")
				{
					model.ClassID= new Guid(row["ClassID"].ToString());
				}
				if(row["SchoolYear"]!=null)
				{
					model.SchoolYear=row["SchoolYear"].ToString();
				}
				if(row["TermID"]!=null && row["TermID"].ToString()!="")
				{
					model.TermID= new Guid(row["TermID"].ToString());
				}
				if(row["Weekday"]!=null && row["Weekday"].ToString()!="")
				{
					model.Weekday=int.Parse(row["Weekday"].ToString());
				}
				if(row["Class"]!=null && row["Class"].ToString()!="")
				{
					model.Class=int.Parse(row["Class"].ToString());
				}
				if(row["SubjectCode"]!=null)
				{
					model.SubjectCode=row["SubjectCode"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,ColumnID,ColumnPath,TeacherNo,ClassID,SchoolYear,TermID,Weekday,Class,SubjectCode ");
			strSql.Append(" FROM JC_TeacherTimetable ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,ColumnID,ColumnPath,TeacherNo,ClassID,SchoolYear,TermID,Weekday,Class,SubjectCode ");
			strSql.Append(" FROM JC_TeacherTimetable ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM JC_TeacherTimetable ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from JC_TeacherTimetable T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "JC_TeacherTimetable";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

