﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// face_lib:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class face_lib
    {
        public face_lib()
        { }
        #region Model
        private Guid _userid;
        private int? _columnid;
        private string _columnpath;
        private string _picpath;
        private DateTime? _createdate;
        private DateTime? _lasteditdate;
        private long? _timestamp;
        private int? _status;
        /// <summary>
        /// 
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 人脸图像路径（带域名）
        /// </summary>
        public string PicPath
        {
            set { _picpath = value; }
            get { return _picpath; }
        }
        /// <summary>
        /// 第一次录入时间
        /// </summary>
        public DateTime? CreateDate
        {
            set { _createdate = value; }
            get { return _createdate; }
        }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? LastEditDate
        {
            set { _lasteditdate = value; }
            get { return _lasteditdate; }
        }
        /// <summary>
        /// 下发时间戳，用于比对校验是否下发成功
        /// 年月日时分秒
        /// 全局变量
        /// </summary>
        public long? timestamp
        {
            set { _timestamp = value; }
            get { return _timestamp; }
        }
        /// <summary>
        /// 状态 0删除 1正常
        /// </summary>
        public int? status
        {
            set { _status = value; }
            get { return _status; }
        }
        /// <summary>
        /// 操作类型
        /// 1更新 2删除
        /// </summary>
        public int OperationType { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public Guid? Operator { get; set; }

        #endregion Model
    }
}

