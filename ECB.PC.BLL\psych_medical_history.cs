﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using YunEdu.DBUtility;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_medical_history
    /// </summary>
    public partial class psych_medical_history
    {
        public psych_medical_history()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_medical_history");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.psych_medical_history model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_medical_history(");
            strSql.Append("Id,ColumnId,ColumnPath,StudentId,ZDJB,JWXLJB,AppointmentTime,MZBL_Photos,Memo,LastEditBy,LastEditTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@ZDJB,@JWXLJB,@AppointmentTime,@MZBL_Photos,@Memo,@LastEditBy,@LastEditTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ZDJB", SqlDbType.NVarChar,500),
                    new SqlParameter("@JWXLJB", SqlDbType.NVarChar,500),
                    new SqlParameter("@AppointmentTime", SqlDbType.DateTime),
                    new SqlParameter("@MZBL_Photos", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Memo", SqlDbType.NVarChar,500),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.ZDJB;
            parameters[5].Value = model.JWXLJB;
            parameters[6].Value = model.AppointmentTime;
            parameters[7].Value = model.MZBL_Photos;
            parameters[8].Value = model.Memo;
            parameters[9].Value = model.LastEditBy;
            parameters[10].Value = model.LastEditTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.psych_medical_history model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_medical_history set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("ZDJB=@ZDJB,");
            strSql.Append("JWXLJB=@JWXLJB,");
            strSql.Append("AppointmentTime=@AppointmentTime,");
            strSql.Append("MZBL_Photos=@MZBL_Photos,");
            strSql.Append("Memo=@Memo,");
            strSql.Append("LastEditBy=@LastEditBy,");
            strSql.Append("LastEditTime=@LastEditTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ZDJB", SqlDbType.NVarChar,500),
                    new SqlParameter("@JWXLJB", SqlDbType.NVarChar,500),
                    new SqlParameter("@AppointmentTime", SqlDbType.DateTime),
                    new SqlParameter("@MZBL_Photos", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Memo", SqlDbType.NVarChar,500),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.StudentId;
            parameters[3].Value = model.ZDJB;
            parameters[4].Value = model.JWXLJB;
            parameters[5].Value = model.AppointmentTime;
            parameters[6].Value = model.MZBL_Photos;
            parameters[7].Value = model.Memo;
            parameters[8].Value = model.LastEditBy;
            parameters[9].Value = model.LastEditTime;
            parameters[10].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_medical_history ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_medical_history ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.psych_medical_history GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,ZDJB,JWXLJB,AppointmentTime,MZBL_Photos,Memo,LastEditBy,LastEditTime from psych_medical_history ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.psych_medical_history model = new Model.psych_medical_history();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.psych_medical_history DataRowToModel(DataRow row)
        {
            Model.psych_medical_history model = new Model.psych_medical_history();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["ZDJB"] != null)
                {
                    model.ZDJB = row["ZDJB"].ToString();
                }
                if (row["JWXLJB"] != null)
                {
                    model.JWXLJB = row["JWXLJB"].ToString();
                }
                if (row["AppointmentTime"] != null && row["AppointmentTime"].ToString() != "")
                {
                    model.AppointmentTime = DateTime.Parse(row["AppointmentTime"].ToString());
                }
                if (row["MZBL_Photos"] != null)
                {
                    model.MZBL_Photos = row["MZBL_Photos"].ToString();
                }
                if (row["Memo"] != null)
                {
                    model.Memo = row["Memo"].ToString();
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,StudentId,ZDJB,JWXLJB,AppointmentTime,MZBL_Photos,Memo,LastEditBy,LastEditTime ");
            strSql.Append(" FROM psych_medical_history ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,StudentId,ZDJB,JWXLJB,AppointmentTime,MZBL_Photos,Memo,LastEditBy,LastEditTime ");
            strSql.Append(" FROM psych_medical_history ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_medical_history ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_medical_history T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_medical_history";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 获得数据列表
        /// </summary>
        /// <param name="studentId">学生id</param>
        /// <returns></returns>
        public DataSet GetList(Guid studentId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,StudentId,ZDJB,JWXLJB,AppointmentTime,MZBL_Photos,Memo,LastEditBy,LastEditTime from psych_medical_history ");
            strSql.Append(" where StudentId=@StudentId order by AppointmentTime desc");
            SqlParameter[] parameters = {
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = studentId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        #endregion  ExtensionMethod
    }
}
