﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_test_result
    /// </summary>
    public partial class psych_test_result
    {
        public psych_test_result()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_test_result");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.psych_test_result model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_test_result(");
            strSql.Append("Id,ColumnId,ColumnPath,StudentId,ClassId,Contents,TotalScore,WarningStatus)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@ClassId,@Contents,@TotalScore,@WarningStatus)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Contents", SqlDbType.NVarChar,-1),
                    new SqlParameter("@TotalScore", SqlDbType.Float,8),
                    new SqlParameter("@WarningStatus", SqlDbType.NVarChar,2)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.Contents;
            parameters[6].Value = model.TotalScore;
            parameters[7].Value = model.WarningStatus;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 增加一条数据（使用事务）
        /// </summary>
        public bool Add(ECB.PC.Model.psych_test_result model, ArrayList sqlList)
        {
            // 使用字符串插值格式构建SQL语句
            string sql = $@"INSERT INTO psych_test_result(Id,ColumnId,ColumnPath,StudentId,ClassId,Contents,TotalScore,WarningStatus) 
                           VALUES('{model.Id}',{model.ColumnId},
                           '{model.ColumnPath}','{model.StudentId}',
                           '{model.ClassId}','{YunEdu.Common.DataSecurity.FilteSQLStrAll(model.Contents)}',
                           {model.TotalScore},'{model.WarningStatus}')";

            // 将SQL语句添加到事务列表中
            sqlList.Add(sql);

            return true;
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.psych_test_result model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_test_result set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("Contents=@Contents,");
            strSql.Append("TotalScore=@TotalScore,");
            strSql.Append("WarningStatus=@WarningStatus");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Contents", SqlDbType.NVarChar,-1),
                    new SqlParameter("@TotalScore", SqlDbType.Float,8),
                    new SqlParameter("@WarningStatus", SqlDbType.NVarChar,2),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.StudentId;
            parameters[3].Value = model.ClassId;
            parameters[4].Value = model.Contents;
            parameters[5].Value = model.TotalScore;
            parameters[6].Value = model.WarningStatus;
            parameters[7].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_test_result ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_test_result ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_test_result GetModel(Guid Id, Guid studentId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,ClassId,Contents,TotalScore,WarningStatus from psych_test_result ");
            strSql.Append(" where Id=@Id and StudentId=@StudentId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = Id;
            parameters[1].Value = studentId;
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_test_result DataRowToModel(DataRow row)
        {
            ECB.PC.Model.psych_test_result model = new ECB.PC.Model.psych_test_result();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["Contents"] != null)
                {
                    model.Contents = row["Contents"].ToString();
                }
                if (row["TotalScore"] != null && row["TotalScore"].ToString() != "")
                {
                    model.TotalScore = decimal.Parse(row["TotalScore"].ToString());
                }
                if (row["WarningStatus"] != null)
                {
                    model.WarningStatus = row["WarningStatus"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,StudentId,ClassId,Contents,TotalScore,WarningStatus ");
            strSql.Append(" FROM psych_test_result ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,StudentId,ClassId,Contents,TotalScore,WarningStatus ");
            strSql.Append(" FROM psych_test_result ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_test_result ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_test_result T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_test_result";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList_class(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.*,b.classname,c.studentname");
            strSql.Append(" FROM psych_test_result a left join jc_classinfos b on a.classid=b.id left join jc_studentinfos c on a.studentid=c.id");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }

            strSql.Append(" order by [TotalScore] desc");
            return DbHelperSQL.Query(strSql.ToString());
        }

        public DataTable GetListbyStudent(string studentId)
        {
            string testQuery = $@"SELECT t.Title AS TestName, tr.Id, tr.StudentId, tr.Contents, tr.TotalScore, t.CreateTime
                                    FROM psych_test_result tr 
                                    INNER JOIN psych_test t ON tr.Id = t.Id
                                    WHERE tr.StudentId = '{studentId}'
                                    ORDER BY t.CreateTime";

            return DbHelperSQL.Query(testQuery).Tables[0];

        }

        /// <summary>
        /// 执行事务
        /// </summary>
        public bool ExecuteSqlTran(ArrayList list)
        {
            int rows = DbHelperSQL.ExecuteSqlTran(list);
            if (rows > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        /// <param name="studentId">学生id</param>
        /// <returns></returns>
        public DataSet GetList(Guid studentId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.Contents,a.TotalScore,a.WarningStatus,b.Title,b.CreateTime,c.WarningCode,c.WarningName,c.Color ");
            strSql.Append(" FROM psych_test_result a left join psych_test b on a.Id=b.Id left join psych_warning_config c on a.WarningStatus=c.WarningCode and a.ColumnId=c.ColumnId");
            strSql.Append(" where StudentId=@StudentId");
            SqlParameter[] parameters = {
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = studentId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        #endregion  ExtensionMethod
    }
}