﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_news
    /// </summary>
    public partial class ecb_news
    {
        public ecb_news()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_news");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_news model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_news(");
            strSql.Append("Id,ColumnId,ColumnPath,CategoryId,ImgUrl,Title,ZhaiYao,Content,Author,AddTime,UpdateUser,UpdateTime,Sort,IsPublish,IsTop,IsSlide,IsShowECB,IsPass,IsPassBy,IsPassDate,CallName)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@CategoryId,@ImgUrl,@Title,@ZhaiYao,@Content,@Author,@AddTime,@UpdateUser,@UpdateTime,@Sort,@IsPublish,@IsTop,@IsSlide,@IsShowECB,@IsPass,@IsPassBy,@IsPassDate,@CallName)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@CategoryId", SqlDbType.Int,4),
                    new SqlParameter("@ImgUrl", SqlDbType.NVarChar,255),
                    new SqlParameter("@Title", SqlDbType.NVarChar,255),
                    new SqlParameter("@ZhaiYao", SqlDbType.NVarChar,255),
                    new SqlParameter("@Content", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Author", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@AddTime", SqlDbType.DateTime),
                    new SqlParameter("@UpdateUser", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UpdateTime", SqlDbType.DateTime),
                    new SqlParameter("@Sort", SqlDbType.Int,4),
                    new SqlParameter("@IsPublish", SqlDbType.Int,4),
                    new SqlParameter("@IsTop", SqlDbType.Int,4),
                    new SqlParameter("@IsSlide", SqlDbType.Int,4),
                    new SqlParameter("@IsShowECB", SqlDbType.Int,4),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@CallName", SqlDbType.NVarChar,50)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.CategoryId;
            parameters[4].Value = model.ImgUrl;
            parameters[5].Value = model.Title;
            parameters[6].Value = model.ZhaiYao;
            parameters[7].Value = model.Content;
            parameters[8].Value = model.Author;
            parameters[9].Value = model.AddTime;
            parameters[10].Value = model.UpdateUser;
            parameters[11].Value = model.UpdateTime;
            parameters[12].Value = model.Sort;
            parameters[13].Value = model.IsPublish;
            parameters[14].Value = model.IsTop;
            parameters[15].Value = model.IsSlide;
            parameters[16].Value = model.IsShowECB;
            parameters[17].Value = model.IsPass;
            parameters[18].Value = model.IsPassBy;
            parameters[19].Value = model.IsPassDate;
            parameters[20].Value = model.CallName;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_news model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_news set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("CategoryId=@CategoryId,");
            strSql.Append("ImgUrl=@ImgUrl,");
            strSql.Append("Title=@Title,");
            strSql.Append("ZhaiYao=@ZhaiYao,");
            strSql.Append("Content=@Content,");
            strSql.Append("Author=@Author,");
            strSql.Append("AddTime=@AddTime,");
            strSql.Append("UpdateUser=@UpdateUser,");
            strSql.Append("UpdateTime=@UpdateTime,");
            strSql.Append("Sort=@Sort,");
            strSql.Append("IsPublish=@IsPublish,");
            strSql.Append("IsTop=@IsTop,");
            strSql.Append("IsSlide=@IsSlide,");
            strSql.Append("IsShowECB=@IsShowECB,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("CallName=@CallName,");
            strSql.Append("IsPassDate=@IsPassDate");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@CategoryId", SqlDbType.Int,4),
                    new SqlParameter("@ImgUrl", SqlDbType.NVarChar,255),
                    new SqlParameter("@Title", SqlDbType.NVarChar,255),
                    new SqlParameter("@ZhaiYao", SqlDbType.NVarChar,255),
                    new SqlParameter("@Content", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Author", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@AddTime", SqlDbType.DateTime),
                    new SqlParameter("@UpdateUser", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UpdateTime", SqlDbType.DateTime),
                    new SqlParameter("@Sort", SqlDbType.Int,4),
                    new SqlParameter("@IsPublish", SqlDbType.Int,4),
                    new SqlParameter("@IsTop", SqlDbType.Int,4),
                    new SqlParameter("@IsSlide", SqlDbType.Int,4),
                    new SqlParameter("@IsShowECB", SqlDbType.Int,4),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@CallName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.CategoryId;
            parameters[3].Value = model.ImgUrl;
            parameters[4].Value = model.Title;
            parameters[5].Value = model.ZhaiYao;
            parameters[6].Value = model.Content;
            parameters[7].Value = model.Author;
            parameters[8].Value = model.AddTime;
            parameters[9].Value = model.UpdateUser;
            parameters[10].Value = model.UpdateTime;
            parameters[11].Value = model.Sort;
            parameters[12].Value = model.IsPublish;
            parameters[13].Value = model.IsTop;
            parameters[14].Value = model.IsSlide;
            parameters[15].Value = model.IsShowECB;
            parameters[16].Value = model.IsPass;
            parameters[17].Value = model.IsPassBy;
            parameters[18].Value = model.IsPassDate;
            parameters[19].Value = model.CallName;
            parameters[20].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_news ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_news ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_news GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,CategoryId,ImgUrl,Title,ZhaiYao,Content,Author,AddTime,UpdateUser,UpdateTime,Sort,IsPublish,IsTop,IsSlide,IsShowECB,IsPass,IsPassBy,IsPassDate,CallName from ecb_news ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_news model = new ECB.PC.Model.ecb_news();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_news DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_news model = new ECB.PC.Model.ecb_news();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["CategoryId"] != null && row["CategoryId"].ToString() != "")
                {
                    model.CategoryId = int.Parse(row["CategoryId"].ToString());
                }
                if (row["ImgUrl"] != null)
                {
                    model.ImgUrl = row["ImgUrl"].ToString();
                }
                if (row["Title"] != null)
                {
                    model.Title = row["Title"].ToString();
                }
                if (row["ZhaiYao"] != null)
                {
                    model.ZhaiYao = row["ZhaiYao"].ToString();
                }
                if (row["Content"] != null)
                {
                    model.Content = row["Content"].ToString();
                }
                if (row["Author"] != null && row["Author"].ToString() != "")
                {
                    model.Author = new Guid(row["Author"].ToString());
                }
                if (row["AddTime"] != null && row["AddTime"].ToString() != "")
                {
                    model.AddTime = DateTime.Parse(row["AddTime"].ToString());
                }
                if (row["UpdateUser"] != null && row["UpdateUser"].ToString() != "")
                {
                    model.UpdateUser = new Guid(row["UpdateUser"].ToString());
                }
                if (row["UpdateTime"] != null && row["UpdateTime"].ToString() != "")
                {
                    model.UpdateTime = DateTime.Parse(row["UpdateTime"].ToString());
                }
                if (row["Sort"] != null && row["Sort"].ToString() != "")
                {
                    model.Sort = int.Parse(row["Sort"].ToString());
                }
                if (row["IsPublish"] != null && row["IsPublish"].ToString() != "")
                {
                    model.IsPublish = int.Parse(row["IsPublish"].ToString());
                }
                if (row["IsTop"] != null && row["IsTop"].ToString() != "")
                {
                    model.IsTop = int.Parse(row["IsTop"].ToString());
                }
                if (row["IsSlide"] != null && row["IsSlide"].ToString() != "")
                {
                    model.IsSlide = int.Parse(row["IsSlide"].ToString());
                }
                if (row["IsShowECB"] != null && row["IsShowECB"].ToString() != "")
                {
                    model.IsShowECB = int.Parse(row["IsShowECB"].ToString());
                }
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    model.IsPass = int.Parse(row["IsPass"].ToString());
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
                if (row["CallName"] != null)
                {
                    model.CallName = row["CallName"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,CategoryId,ImgUrl,Title,ZhaiYao,Content,Author,AddTime,UpdateUser,UpdateTime,Sort,IsPublish,IsTop,IsSlide,IsShowECB,IsPass,IsPassBy,IsPassDate,CallName ");
            strSql.Append(" FROM ecb_news ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,CategoryId,ImgUrl,Title,ZhaiYao,Content,Author,AddTime,UpdateUser,UpdateTime,Sort,IsPublish,IsTop,IsSlide,IsShowECB,IsPass,IsPassBy,IsPassDate,CallName ");
            strSql.Append(" FROM ecb_news ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere,string[] ConnStr=null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_news ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), ConnStr);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_news T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_news";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

