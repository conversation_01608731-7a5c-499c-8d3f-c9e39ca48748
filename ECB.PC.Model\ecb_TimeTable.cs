﻿
using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 课表
    /// </summary>
    [Serializable]
    public partial class ecb_TimeTable
    {
        public ecb_TimeTable()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _termid;
        private Guid _gradeid;
        private Guid _classid;
        private string _lessontime;
        private int _weekday;
        private int _number;
        private string _subjectcode;
        private Guid _teacherid;
        private Guid _placeid;
        private bool _iscover;
        private string _weektype;
        private Guid _placeid1;
        private string _subjectcode1;
        private Guid _teacherid1;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 学校Id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 学校路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 学期Id
        /// </summary>
        public Guid TermID
        {
            set { _termid = value; }
            get { return _termid; }
        }
        /// <summary>
        /// 年级Id
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 班级Id
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 课时
        /// </summary>
        public string LessonTime
        {
            set { _lessontime = value; }
            get { return _lessontime; }
        }
        /// <summary>
        /// 星期几
        /// </summary>
        public int Weekday
        {
            set { _weekday = value; }
            get { return _weekday; }
        }
        /// <summary>
        /// 节次
        /// </summary>
        public int Number
        {
            set { _number = value; }
            get { return _number; }
        }
        /// <summary>
        /// 课程Code
        /// </summary>
        public string SubjectCode
        {
            set { _subjectcode = value; }
            get { return _subjectcode; }
        }
        /// <summary>
        /// 任课教师
        /// </summary>
        public Guid TeacherId
        {
            set { _teacherid = value; }
            get { return _teacherid; }
        }
        /// <summary>
        /// 场地
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        /// <summary>
        /// 手动排课设置标识
        /// </summary>
        public bool IsCover
        {
            set { _iscover = value; }
            get { return _iscover; }
        }
        /// <summary>
        /// 单双周课
        /// </summary>
        public string WeekType
        {
            set { _weektype = value; }
            get { return _weektype; }
        }
        /// <summary>
		/// 
		/// </summary>
		public Guid PlaceId1
        {
            set { _placeid1 = value; }
            get { return _placeid1; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string SubjectCode1
        {
            set { _subjectcode1 = value; }
            get { return _subjectcode1; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid TeacherId1
        {
            set { _teacherid1 = value; }
            get { return _teacherid1; }
        }
        #endregion Model

    }
}

