﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_Active_School
    /// </summary>
    public partial class ecb_Active_School
    {
        public ecb_Active_School()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_Active_School");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_Active_School model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_Active_School(");
            strSql.Append("Id,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@ScoreType,@Score,@ContinueDays,@RecordDate)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ScoreType", SqlDbType.Int,4),
                    new SqlParameter("@Score", SqlDbType.Decimal,5),
                    new SqlParameter("@ContinueDays", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ScoreType;
            parameters[4].Value = model.Score;
            parameters[5].Value = model.ContinueDays;
            parameters[6].Value = model.RecordDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_Active_School model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Active_School set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ScoreType=@ScoreType,");
            strSql.Append("Score=@Score,");
            strSql.Append("ContinueDays=@ContinueDays,");
            strSql.Append("RecordDate=@RecordDate");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ScoreType", SqlDbType.Int,4),
                    new SqlParameter("@Score", SqlDbType.Decimal,5),
                    new SqlParameter("@ContinueDays", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.ScoreType;
            parameters[3].Value = model.Score;
            parameters[4].Value = model.ContinueDays;
            parameters[5].Value = model.RecordDate;
            parameters[6].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Active_School ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Active_School ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_Active_School GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate from ecb_Active_School ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.ecb_Active_School model = new Model.ecb_Active_School();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_Active_School DataRowToModel(DataRow row)
        {
            Model.ecb_Active_School model = new Model.ecb_Active_School();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ScoreType"] != null && row["ScoreType"].ToString() != "")
                {
                    model.ScoreType = int.Parse(row["ScoreType"].ToString());
                }
                if (row["Score"] != null && row["Score"].ToString() != "")
                {
                    model.Score = decimal.Parse(row["Score"].ToString());
                }
                if (row["ContinueDays"] != null && row["ContinueDays"].ToString() != "")
                {
                    model.ContinueDays = int.Parse(row["ContinueDays"].ToString());
                }
                if (row["RecordDate"] != null && row["RecordDate"].ToString() != "")
                {
                    model.RecordDate = DateTime.Parse(row["RecordDate"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate ");
            strSql.Append(" FROM ecb_Active_School ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate ");
            strSql.Append(" FROM ecb_Active_School ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Active_School ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_Active_School T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Active_School";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 班牌活跃指数
        /// </summary>
        /// <param name="model"></param>
        public void addActiveScore(Model.ecb_Active_School model)
        {
            //获取今日活跃情况
            Model.ecb_Active_School model_active_now = GetModelByType(int.Parse(model.ColumnId.ToString()), int.Parse(model.ScoreType.ToString()), Convert.ToDateTime(model.RecordDate));
            //获取昨天活跃情况
            Model.ecb_Active_School model_active_pre = GetModelByType(int.Parse(model.ColumnId.ToString()), int.Parse(model.ScoreType.ToString()), Convert.ToDateTime(model.RecordDate).AddDays(-1));

            //判断该地区今天是否有得分--没有则添加
            if (model_active_now == null)
            {
                //判断昨天是否有得分(没有--直接加分)
                if (model_active_pre == null)
                {
                    Add(model);
                }
                //判断昨天是否有得分（有--加连续奖励分）
                else
                {
                    //连续天数大于7天则连续奖励分一直为7
                    if (model_active_pre.ContinueDays < 7)
                    {
                        model.ContinueDays = model_active_pre.ContinueDays + 1;
                    }
                    else
                    {
                        model.ContinueDays = 7;
                    }
                    model.Score = model.ContinueDays + 1;
                    Add(model);
                }
            }
        }
        /// <summary>
        /// 根据学校Id和类型编号得到一个对象实体
        /// </summary>
        public Model.ecb_Active_School GetModelByType(int ColumnId, int ScoreType, DateTime RecordDate)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate from ecb_Active_School ");
            strSql.Append(" where ColumnId=@ColumnId and ScoreType=@ScoreType and RecordDate=@RecordDate");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ScoreType", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = ColumnId;
            parameters[1].Value = ScoreType;
            parameters[2].Value = RecordDate;

            Model.ecb_Active_School model = new Model.ecb_Active_School();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取活跃指数排行
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet getactiveRank(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("WITH[SchoolActiveScore] as( SELECT SUM(a.Score) as totalscore,b.AreaName as SchoolName,a.ColumnId FROM ecb_Active_School a ");
            strSql.Append(" LEFT JOIN BM_Areas b ON a.ColumnId =b.ColumnID");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" GROUP BY b.AreaName,a.ColumnId  )");
            strSql.AppendLine(" SELECT SchoolName,totalscore,RANK() OVER(ORDER BY totalscore DESC) sort,ColumnId FROM [SchoolActiveScore]");
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取当月活跃内容排行
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet getcontentRank(string schoolIds)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(count) as total,table1.ColumnId,b.AreaName as SchoolName FROM ");
            strSql.Append(" (SELECT COUNT(*) as count,ColumnId FROM ecb_SchoolActivity WHERE ColumnId IN (" + schoolIds + ")  GROUP BY ColumnId");
            strSql.Append(" UNION ALL SELECT COUNT(*) as count,SchoolColumnId as ColumnId FROM DA_XS_FeaturedActivities WHERE SchoolColumnId IN (" + schoolIds + ")  GROUP BY SchoolColumnId");
            strSql.Append(" UNION ALL SELECT COUNT(*) as count,ColumnId FROM JC_TeacherHonors WHERE ColumnId IN (" + schoolIds + ")  GROUP BY ColumnId");
            strSql.Append(" UNION ALL SELECT COUNT(*) as count,ColumnId from JC_StudentHonors WHERE ColumnId IN (" + schoolIds + ")  GROUP BY ColumnId");
            strSql.Append(" UNION ALL SELECT COUNT(*) as count,ColumnId FROM ecb_class_honor WHERE ColumnId IN (" + schoolIds + ")  GROUP BY ColumnId");
            strSql.Append(" UNION ALL SELECT COUNT(*) as count,ColumnId FROM ecb_teacher_works WHERE ColumnId IN (" + schoolIds + ")  GROUP BY ColumnId");
            strSql.Append(" UNION ALL SELECT COUNT(*) as count,SchoolColumnId as ColumnId FROM DA_XS_StudentWorks WHERE SchoolColumnId IN (" + schoolIds + ")  GROUP BY SchoolColumnId");
            strSql.Append(" UNION ALL SELECT count(*) as count,ColumnId from ecb_Praise WHERE ColumnId IN (" + schoolIds + ") GROUP BY ColumnId) as table1 ");
            strSql.Append(" LEFT JOIN BM_Areas b ON table1.ColumnId =b.ColumnID");
            strSql.Append(" GROUP BY table1.ColumnId,b.AreaName");
            strSql.Append(" ORDER by total desc");

            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取App使用情况(激活人数)
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetAppUseCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT *,t.parentCount+t.teacherCount TotalCount FROM (");
            strSql.Append("  SELECT b.AreaName as SchoolName,COUNT(1) useCount,");
            strSql.Append(" (SELECT COUNT(1) FROM aspnet_Membership WHERE ColumnID=a.ColumnId AND  UserId IN (SELECT UserId FROM aspnet_UsersInRoles WHERE ColumnDepth=8) AND AppVersion is not null) parentCount, ");
            strSql.Append(" (SELECT COUNT(1) FROM aspnet_Membership WHERE ColumnID=a.ColumnId AND  UserId IN (SELECT UserId FROM aspnet_UsersInRoles WHERE ColumnDepth=6) AND AppVersion is not null ) teacherCount ");
            strSql.Append(" FROM dbo.aspnet_Membership a LEFT JOIN dbo.BM_Areas b ON a.ColumnID=b.ColumnID where ");
            strSql.Append(" a.UserId IN (SELECT UserId FROM aspnet_UsersInRoles WHERE  ColumnDepth=6 or ColumnDepth=8)");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" GROUP BY a.ColumnID ,b.AreaName");
            strSql.Append(" ) t ORDER BY TotalCount desc");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

