﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_time_template
	/// </summary>
	public partial class ecb_time_template
	{
		public ecb_time_template()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_time_template");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_time_template model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_time_template(");
			strSql.Append("Id,ColumnID,ColumnPath,TName,Days,EarlyCount,MorningCount,AfternoonCount,NightCount,CourseLength,RecessLength)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnID,@ColumnPath,@TName,@Days,@EarlyCount,@MorningCount,@AfternoonCount,@NightCount,@CourseLength,@RecessLength)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnID", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@TName", SqlDbType.NVarChar,50),
					new SqlParameter("@Days", SqlDbType.Int,4),
					new SqlParameter("@EarlyCount", SqlDbType.Int,4),
					new SqlParameter("@MorningCount", SqlDbType.Int,4),
					new SqlParameter("@AfternoonCount", SqlDbType.Int,4),
					new SqlParameter("@NightCount", SqlDbType.Int,4),
					new SqlParameter("@CourseLength", SqlDbType.Int,4),
					new SqlParameter("@RecessLength", SqlDbType.Int,4)};
			parameters[0].Value = model.Id;
			parameters[1].Value = model.ColumnID;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.TName;
			parameters[4].Value = model.Days;
			parameters[5].Value = model.EarlyCount;
			parameters[6].Value = model.MorningCount;
			parameters[7].Value = model.AfternoonCount;
			parameters[8].Value = model.NightCount;
			parameters[9].Value = model.CourseLength;
			parameters[10].Value = model.RecessLength;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_time_template model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_time_template set ");
			strSql.Append("ColumnID=@ColumnID,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("TName=@TName,");
			strSql.Append("Days=@Days,");
			strSql.Append("EarlyCount=@EarlyCount,");
			strSql.Append("MorningCount=@MorningCount,");
			strSql.Append("AfternoonCount=@AfternoonCount,");
			strSql.Append("NightCount=@NightCount,");
			strSql.Append("CourseLength=@CourseLength,");
			strSql.Append("RecessLength=@RecessLength");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnID", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@TName", SqlDbType.NVarChar,50),
					new SqlParameter("@Days", SqlDbType.Int,4),
					new SqlParameter("@EarlyCount", SqlDbType.Int,4),
					new SqlParameter("@MorningCount", SqlDbType.Int,4),
					new SqlParameter("@AfternoonCount", SqlDbType.Int,4),
					new SqlParameter("@NightCount", SqlDbType.Int,4),
					new SqlParameter("@CourseLength", SqlDbType.Int,4),
					new SqlParameter("@RecessLength", SqlDbType.Int,4),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnID;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.TName;
			parameters[3].Value = model.Days;
			parameters[4].Value = model.EarlyCount;
			parameters[5].Value = model.MorningCount;
			parameters[6].Value = model.AfternoonCount;
			parameters[7].Value = model.NightCount;
			parameters[8].Value = model.CourseLength;
			parameters[9].Value = model.RecessLength;
			parameters[10].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_time_template ");
			strSql.Append(" where Id=@Id ");
            strSql.Append(" delete from ecb_time_config ");
            strSql.Append(" where TemplateId=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_time_template ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_time_template GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnID,ColumnPath,TName,Days,EarlyCount,MorningCount,AfternoonCount,NightCount,CourseLength,RecessLength from ecb_time_template ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_time_template model=new ECB.PC.Model.ecb_time_template();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_time_template DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_time_template model=new ECB.PC.Model.ecb_time_template();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnID"]!=null && row["ColumnID"].ToString()!="")
				{
					model.ColumnID=int.Parse(row["ColumnID"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["TName"]!=null)
				{
					model.TName=row["TName"].ToString();
				}
				if(row["Days"]!=null && row["Days"].ToString()!="")
				{
					model.Days=int.Parse(row["Days"].ToString());
				}
				if(row["EarlyCount"]!=null && row["EarlyCount"].ToString()!="")
				{
					model.EarlyCount=int.Parse(row["EarlyCount"].ToString());
				}
				if(row["MorningCount"]!=null && row["MorningCount"].ToString()!="")
				{
					model.MorningCount=int.Parse(row["MorningCount"].ToString());
				}
				if(row["AfternoonCount"]!=null && row["AfternoonCount"].ToString()!="")
				{
					model.AfternoonCount=int.Parse(row["AfternoonCount"].ToString());
				}
				if(row["NightCount"]!=null && row["NightCount"].ToString()!="")
				{
					model.NightCount=int.Parse(row["NightCount"].ToString());
				}
				if(row["CourseLength"]!=null && row["CourseLength"].ToString()!="")
				{
					model.CourseLength=int.Parse(row["CourseLength"].ToString());
				}
				if(row["RecessLength"]!=null && row["RecessLength"].ToString()!="")
				{
					model.RecessLength=int.Parse(row["RecessLength"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnID,ColumnPath,TName,Days,EarlyCount,MorningCount,AfternoonCount,NightCount,CourseLength,RecessLength ");
			strSql.Append(" FROM ecb_time_template ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnID,ColumnPath,TName,Days,EarlyCount,MorningCount,AfternoonCount,NightCount,CourseLength,RecessLength ");
			strSql.Append(" FROM ecb_time_template ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_time_template ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_time_template T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_time_template";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

