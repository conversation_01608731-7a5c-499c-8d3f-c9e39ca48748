﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:Site_Dictionary
	/// </summary>
	public partial class Site_Dictionary
	{
		public Site_Dictionary()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return DbHelperSQL.GetMaxID("DictTypeId", "Site_Dictionary"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int DictTypeId,int DictionaryId)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Site_Dictionary");
			strSql.Append(" where DictTypeId=@DictTypeId and DictionaryId=@DictionaryId ");
			SqlParameter[] parameters = {
					new SqlParameter("@DictTypeId", SqlDbType.Int,4),
					new SqlParameter("@DictionaryId", SqlDbType.Int,4)			};
			parameters[0].Value = DictTypeId;
			parameters[1].Value = DictionaryId;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(ECB.PC.Model.Site_Dictionary model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Site_Dictionary(");
			strSql.Append("DictTypeId,DictText,DictValue,DictTextOrder,DictParentValue,DictDepth,ColumnId,ColumnPath)");
			strSql.Append(" values (");
			strSql.Append("@DictTypeId,@DictText,@DictValue,@DictTextOrder,@DictParentValue,@DictDepth,@ColumnId,@ColumnPath)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@DictTypeId", SqlDbType.Int,4),
					new SqlParameter("@DictText", SqlDbType.NVarChar,100),
					new SqlParameter("@DictValue", SqlDbType.NVarChar,10),
					new SqlParameter("@DictTextOrder", SqlDbType.Int,4),
					new SqlParameter("@DictParentValue", SqlDbType.Int,4),
					new SqlParameter("@DictDepth", SqlDbType.Int,4),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50)};
			parameters[0].Value = model.DictTypeId;
			parameters[1].Value = model.DictText;
			parameters[2].Value = model.DictValue;
			parameters[3].Value = model.DictTextOrder;
			parameters[4].Value = model.DictParentValue;
			parameters[5].Value = model.DictDepth;
			parameters[6].Value = model.ColumnId;
			parameters[7].Value = model.ColumnPath;

			object obj = DbHelperSQL.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.Site_Dictionary model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Site_Dictionary set ");
			strSql.Append("DictText=@DictText,");
			strSql.Append("DictValue=@DictValue,");
			strSql.Append("DictTextOrder=@DictTextOrder,");
			strSql.Append("DictParentValue=@DictParentValue,");
			strSql.Append("DictDepth=@DictDepth,");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath");
			strSql.Append(" where DictionaryId=@DictionaryId");
			SqlParameter[] parameters = {
					new SqlParameter("@DictText", SqlDbType.NVarChar,100),
					new SqlParameter("@DictValue", SqlDbType.NVarChar,10),
					new SqlParameter("@DictTextOrder", SqlDbType.Int,4),
					new SqlParameter("@DictParentValue", SqlDbType.Int,4),
					new SqlParameter("@DictDepth", SqlDbType.Int,4),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@DictionaryId", SqlDbType.Int,4),
					new SqlParameter("@DictTypeId", SqlDbType.Int,4)};
			parameters[0].Value = model.DictText;
			parameters[1].Value = model.DictValue;
			parameters[2].Value = model.DictTextOrder;
			parameters[3].Value = model.DictParentValue;
			parameters[4].Value = model.DictDepth;
			parameters[5].Value = model.ColumnId;
			parameters[6].Value = model.ColumnPath;
			parameters[7].Value = model.DictionaryId;
			parameters[8].Value = model.DictTypeId;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int DictionaryId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Site_Dictionary ");
			strSql.Append(" where DictionaryId=@DictionaryId");
			SqlParameter[] parameters = {
					new SqlParameter("@DictionaryId", SqlDbType.Int,4)
			};
			parameters[0].Value = DictionaryId;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int DictTypeId,int DictionaryId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Site_Dictionary ");
			strSql.Append(" where DictTypeId=@DictTypeId and DictionaryId=@DictionaryId ");
			SqlParameter[] parameters = {
					new SqlParameter("@DictTypeId", SqlDbType.Int,4),
					new SqlParameter("@DictionaryId", SqlDbType.Int,4)			};
			parameters[0].Value = DictTypeId;
			parameters[1].Value = DictionaryId;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string DictionaryIdlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Site_Dictionary ");
			strSql.Append(" where DictionaryId in ("+DictionaryIdlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.Site_Dictionary GetModel(int DictionaryId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 DictionaryId,DictTypeId,DictText,DictValue,DictTextOrder,DictParentValue,DictDepth,ColumnId,ColumnPath from Site_Dictionary ");
			strSql.Append(" where DictionaryId=@DictionaryId");
			SqlParameter[] parameters = {
					new SqlParameter("@DictionaryId", SqlDbType.Int,4)
			};
			parameters[0].Value = DictionaryId;

			ECB.PC.Model.Site_Dictionary model=new ECB.PC.Model.Site_Dictionary();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.Site_Dictionary DataRowToModel(DataRow row)
		{
			ECB.PC.Model.Site_Dictionary model=new ECB.PC.Model.Site_Dictionary();
			if (row != null)
			{
				if(row["DictionaryId"]!=null && row["DictionaryId"].ToString()!="")
				{
					model.DictionaryId=int.Parse(row["DictionaryId"].ToString());
				}
				if(row["DictTypeId"]!=null && row["DictTypeId"].ToString()!="")
				{
					model.DictTypeId=int.Parse(row["DictTypeId"].ToString());
				}
				if(row["DictText"]!=null)
				{
					model.DictText=row["DictText"].ToString();
				}
				if(row["DictValue"]!=null)
				{
					model.DictValue=row["DictValue"].ToString();
				}
				if(row["DictTextOrder"]!=null && row["DictTextOrder"].ToString()!="")
				{
					model.DictTextOrder=int.Parse(row["DictTextOrder"].ToString());
				}
				if(row["DictParentValue"]!=null && row["DictParentValue"].ToString()!="")
				{
					model.DictParentValue=int.Parse(row["DictParentValue"].ToString());
				}
				if(row["DictDepth"]!=null && row["DictDepth"].ToString()!="")
				{
					model.DictDepth=int.Parse(row["DictDepth"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select DictionaryId,DictTypeId,DictText,DictValue,DictTextOrder,DictParentValue,DictDepth,ColumnId,ColumnPath ");
			strSql.Append(" FROM Site_Dictionary ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" DictionaryId,DictTypeId,DictText,DictValue,DictTextOrder,DictParentValue,DictDepth,ColumnId,ColumnPath ");
			strSql.Append(" FROM Site_Dictionary ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Site_Dictionary ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.DictionaryId desc");
			}
			strSql.Append(")AS Row, T.*  from Site_Dictionary T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Site_Dictionary";
			parameters[1].Value = "DictionaryId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 是否存在文本值
        /// DictionaryId默认为0
        /// </summary>
        public bool ExistsName(int DictTypeId, string DictText,int ColumnId, int DictionaryId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Site_Dictionary");
            strSql.Append(" where DictTypeId=@DictTypeId and DictText=@DictText and ColumnId in (0,@ColumnId) and  DictionaryId !=@DictionaryId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@DictTypeId", SqlDbType.Int,4),
                    new SqlParameter("@DictText", SqlDbType.NVarChar,100),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@DictionaryId", SqlDbType.Int,4)          };
            parameters[0].Value = DictTypeId;
            parameters[1].Value = DictText;
            parameters[2].Value = ColumnId;
            parameters[3].Value = DictionaryId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 是否存在取值
        /// DictionaryId默认为0
        /// </summary>
        public bool ExistsValue(int DictTypeId, string DictValue, int ColumnId, int DictionaryId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Site_Dictionary");
            strSql.Append(" where DictTypeId=@DictTypeId and DictValue=@DictValue and ColumnId in (0,@ColumnId) and  DictionaryId !=@DictionaryId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@DictTypeId", SqlDbType.Int,4),
                    new SqlParameter("@DictValue", SqlDbType.NVarChar,10),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@DictionaryId", SqlDbType.Int,4)          };
            parameters[0].Value = DictTypeId;
            parameters[1].Value = DictValue;
            parameters[2].Value = ColumnId;
            parameters[3].Value = DictionaryId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        public int GetMaxValue(int DictTypeId, int ColumnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select max(convert(int,DictValue)) +1  from Site_Dictionary");
            strSql.Append(" where DictTypeId=@DictTypeId and ColumnId in (0,@ColumnId)");
            SqlParameter[] parameters = {
                    new SqlParameter("@DictTypeId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)};
            parameters[0].Value = DictTypeId;
            parameters[1].Value = ColumnId;
            object single = DbHelperSQL.GetSingle(strSql.ToString(), parameters, (string[])null);
            if (single == null)
            {
                return 1;
            }
            return int.Parse(single.ToString());
        }

        /// <summary>
        /// 获取字典数据并按正确顺序排序，优先使用DictTextOrder，其次使用DictValue数值排序
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <returns>排序后的字典数据</returns>
        public DataTable GetListWithProperOrder(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT DictionaryId,DictTypeId,DictText,DictValue,DictTextOrder,DictParentValue,DictDepth,ColumnId,ColumnPath ");
            strSql.Append("FROM Site_Dictionary ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append("WHERE " + strWhere + " ");
            }
            strSql.Append("ORDER BY ");
            strSql.Append("CASE WHEN DictTextOrder IS NOT NULL THEN DictTextOrder ELSE 999999 END, ");
            strSql.Append("CASE WHEN ISNUMERIC(DictValue) = 1 THEN CAST(DictValue AS INT) ELSE 999999 END, ");
            strSql.Append("DictValue");

            return DbHelperSQL.Query(strSql.ToString()).Tables[0];
        }
        #endregion  ExtensionMethod
    }
}

