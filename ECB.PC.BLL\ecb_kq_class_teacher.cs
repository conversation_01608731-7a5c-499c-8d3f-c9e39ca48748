﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_kq_class_teacher
    /// </summary>
    public partial class ecb_kq_class_teacher
    {
        public ecb_kq_class_teacher()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_kq_class_teacher");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_kq_class_teacher model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_kq_class_teacher(");
            strSql.Append("Id,ColumnId,ColumnPath,UserId,SignTime,ClassId,SubjectCode,Status,IsSubstitute,ClassNum,Photo,PlaceId)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@UserId,@SignTime,@ClassId,@SubjectCode,@Status,@IsSubstitute,@ClassNum,@Photo,@PlaceId)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SignTime", SqlDbType.DateTime),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,50),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@IsSubstitute", SqlDbType.Int,4),
                    new SqlParameter("@Photo", SqlDbType.NVarChar,255),
                    new SqlParameter("@ClassNum", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.UserId;
            parameters[4].Value = model.SignTime;
            parameters[5].Value = model.ClassId;
            parameters[6].Value = model.SubjectCode;
            parameters[7].Value = model.Status;
            parameters[8].Value = model.IsSubstitute;
            parameters[9].Value = model.Photo;
            parameters[10].Value = model.ClassNum;
            parameters[11].Value = model.PlaceId;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_kq_class_teacher model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_kq_class_teacher set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("SignTime=@SignTime,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("SubjectCode=@SubjectCode,");
            strSql.Append("Status=@Status,");
            strSql.Append("IsSubstitute=@IsSubstitute,");
            strSql.Append("Photo=@Photo,");
            strSql.Append("ClassNum=@ClassNum,");
            strSql.Append("PlaceId=@PlaceId");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SignTime", SqlDbType.DateTime),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,50),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@IsSubstitute", SqlDbType.Int,4),
                    new SqlParameter("@ClassNum", SqlDbType.Int,4),
                    new SqlParameter("@Photo", SqlDbType.NVarChar,255),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.UserId;
            parameters[3].Value = model.SignTime;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.SubjectCode;
            parameters[6].Value = model.Status;
            parameters[7].Value = model.IsSubstitute;
            parameters[8].Value = model.ClassNum;
            parameters[9].Value = model.Photo;
            parameters[10].Value = model.PlaceId;
            parameters[11].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_class_teacher ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_class_teacher ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_class_teacher GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,UserId,SignTime,ClassId,SubjectCode,Status,IsSubstitute,ClassNum,Photo,PlaceId from ecb_kq_class_teacher ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_kq_class_teacher model = new ECB.PC.Model.ecb_kq_class_teacher();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_class_teacher DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_kq_class_teacher model = new ECB.PC.Model.ecb_kq_class_teacher();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["SignTime"] != null && row["SignTime"].ToString() != "")
                {
                    model.SignTime = DateTime.Parse(row["SignTime"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["SubjectCode"] != null)
                {
                    model.SubjectCode = row["SubjectCode"].ToString();
                }
                if (row["Status"] != null && row["Status"].ToString() != "")
                {
                    model.Status = int.Parse(row["Status"].ToString());
                }
                if (row["IsSubstitute"] != null && row["IsSubstitute"].ToString() != "")
                {
                    model.IsSubstitute = int.Parse(row["IsSubstitute"].ToString());
                }
                if (row["ClassNum"] != null && row["ClassNum"].ToString() != "")
                {
                    model.ClassNum = int.Parse(row["ClassNum"].ToString());
                }
                if (row["Photo"] != null)
                {
                    model.Photo = row["Photo"].ToString();
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,UserId,SignTime,ClassId,SubjectCode,Status,IsSubstitute,ClassNum,Photo,PlaceId ");
            strSql.Append(" FROM ecb_kq_class_teacher ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,UserId,SignTime,ClassId,SubjectCode,Status,IsSubstitute,ClassNum,Photo,PlaceId ");
            strSql.Append(" FROM ecb_kq_class_teacher ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_kq_class_teacher ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_kq_class_teacher T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_kq_class_teacher";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 教师个人考勤-打卡(希沃无地点判断)
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <param name="userId">用户id</param>
        /// <param name="ClassId">班级Id</param>
        /// <returns></returns>
        public DataSet TeacherSubmitAttendance(int columnId, Guid userId, string Photo, int isSubstitute, DateTime SignTime)
        {
            SqlParameter[] parameters = {
                new SqlParameter("@UserId",SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@columnId",SqlDbType.Int),
                new SqlParameter("@Photo",SqlDbType.NVarChar,500),
                new SqlParameter("@isSubstitute",SqlDbType.Int),
                 new SqlParameter("@SignTime",SqlDbType.DateTime)
            };
            parameters[0].Value = userId;
            parameters[1].Value = columnId;
            parameters[2].Value = Photo;
            parameters[3].Value = isSubstitute;
            parameters[4].Value = SignTime;
            return DbHelperSQL.RunProcedureReturnMoreDateTable("UP_TeacherAttendance_Seewo", parameters);
        }

        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ClassNum '节次' ,SUM(CASE WHEN b.[Status] = 1 THEN 1 ELSE 0 END) '正常',SUM(CASE WHEN b.[Status] = 2 THEN 1 ELSE 0 END) '迟到',SUM(CASE WHEN b.[Status] = 3 THEN 1 ELSE 0 END) '缺卡',SUM(CASE WHEN b.[IsSubstitute] = 1 THEN 1 ELSE 0 END) '代课',CAST(Round(convert(float,SUM(case when  b.[Status]=3 then 1 else 0 end))/convert(float,COUNT( b.[Status]))*100,2) as nvarchar)+'%' as '缺卡率'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher_history) a cross apply openjson(a.KQRecords) with(IsSubstitute int,ClassId uniqueidentifier,[Status] INT,ClassNum INT,SignTime DATETIME,BeginTime DATETIME) as b LEFT JOIN JC_ClassInfos D ON b.ClassId=D.ID where BeginTime<=GETDATE()  ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }

            strSql.Append(" group by ClassNum  order by ClassNum");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendInfo(string strWhere, int IsSubstitute)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select c.UserId as ID,CName 'UserName',COUNT(1) total");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher_history) a cross apply openjson(a.KQRecords) with(IsSubstitute int,SubstituteUserId uniqueidentifier ,ClassId uniqueidentifier,[Status] INT,ClassNum INT,SignTime DATETIME,BeginTime DATETIME) as b left join aspnet_Membership c on (case when " + IsSubstitute + "=1 then SubstituteUserId else a.UserId end)=c.UserId  LEFT JOIN JC_ClassInfos D ON b.ClassId=D.ID  where BeginTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" group by c.UserId,CName order by total desc");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 导出教师班级课堂考勤统计
        /// </summary>
        public DataSet ExecelOut(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select CAST( ClassNum as nvarchar) '节次' ,SUM(CASE WHEN b.[Status] = 1 THEN 1 ELSE 0 END) '正常(人)',SUM(CASE WHEN b.[Status] = 2 THEN 1 ELSE 0 END) '迟到(人)',SUM(CASE WHEN b.[Status] = 3 THEN 1 ELSE 0 END) '缺卡(人)',SUM(CASE WHEN b.[IsSubstitute] = 1 THEN 1 ELSE 0 END) '代课(次)',CAST(Round(convert(float,SUM(case when  b.[Status]=3 then 1 else 0 end))/convert(float,COUNT( b.[Status]))*100,2) as nvarchar)+'%' as '缺卡率'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher_history) a cross apply openjson(a.KQRecords) with(IsSubstitute int,ClassId uniqueidentifier,[Status] INT,ClassNum INT,SignTime DATETIME,BeginTime DATETIME) as b LEFT JOIN JC_ClassInfos D ON b.ClassId=D.ID  where BeginTime<=GETDATE() ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }

            strSql.Append(" group by ClassNum  order by ClassNum");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 导出教师班级课堂考勤统计
        /// </summary>
        public DataSet ExecelOutteaInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select c.UserId ,CName  '姓名',SUM(CASE WHEN b.[Status] = 2 THEN 1 ELSE 0 END) '迟到(次)',SUM(CASE WHEN b.[Status] = 3 THEN 1 ELSE 0 END) '缺卡(次)'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher_history) a cross apply openjson(a.KQRecords) with(ClassId uniqueidentifier,[Status] INT,SignTime DATETIME,BeginTime DATETIME) as b left join aspnet_Membership c on a.UserId=c.UserId  LEFT JOIN JC_ClassInfos D ON b.ClassId=D.ID  where BeginTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" group by c.UserId,CName order by CName");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 导出教师班级课堂考勤统计
        /// </summary>
        public DataSet ExecelDetail(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ClassId,GradeName '年级',ClassName '班级',CAST(Round(convert(float,SUM(case when  b.[Status]=3 then 1 else 0 end))/convert(float,COUNT( b.[Status]))*100,2) as nvarchar)+'%' as '缺卡率',SUM(case when  b.[Status]=1 then 1 else 0 end) '正常(人)',SUM(case when  b.[Status]=2 then 1 else 0 end) '迟到(人)',SUM(case when  b.[Status]=3 then 1 else 0 end) '缺卡(人)'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher_history) a cross apply openjson(a.KQRecords) with(ClassId uniqueidentifier,[Status] INT,SignTime DATETIME,BeginTime DATETIME) as b LEFT JOIN JC_ClassInfos D ON b.ClassId=D.ID left join JC_GradeInfos g on d.GradeId=g.ID  where BeginTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" group by g.OrderId,d.OrderId, ClassId,ClassName ,GradeName order by g.OrderId,d.OrderId,ClassId,ClassName");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 导出教师班级课堂考勤统计
        /// </summary>
        public DataSet ExecelDetailInfo(string strWhere, string excelType)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select CName '姓名',CONVERT(varchar(100),SignTime, 23) as '日期',ClassName '任教班级', DictText '任教科目',ClassNum '课节',case when Status = 2  then '迟到' when Status = 3 then '缺卡' when Status = 1 then '正常' else '' end '考勤状态',CASE WHEN Status=3 THEN '' ELSE CONVERT(varchar(100),SignTime, 120) END  AS '考勤时间'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher_history ) a cross apply openjson(KQRecords) with(ClassId UNIQUEIDENTIFIER,[Status] INT,ClassNum INT,SubjectCode nvarchar(10),SignTime DATETIME,BeginTime DATETIME) as b LEFT JOIN aspnet_Membership C ON a.UserId=C.UserId left join JC_ClassInfos d on b.ClassId=d.ID left join Site_Dictionary e on b.SubjectCode=e.DictValue and e.DictTypeId=28 AND e.ColumnId IN (0,a.ColumnId) where BeginTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            if (excelType != "1")
            {
                strSql.Append(" AND Status<>1 ");
            }
            strSql.Append(" ORDER BY RecordDate,ClassNum,OrderId, CName,SignTime");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 导出教师代课课堂考勤统计
        /// </summary>
        public DataSet ExecelDaikeDetailInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select c.CName '姓名',CONVERT(varchar(100),SignTime, 23) as '日期',ClassName '任教班级', DictText '任教科目',ClassNum '课节',  mem.CName '代课', case when Status = 2  then '迟到' when Status = 3 then '缺卡' else '' end '考勤状态',CASE WHEN Status=3 THEN '' ELSE CONVERT(varchar(100),SignTime, 120) END  AS '考勤时间'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_teacher_history ) a cross apply openjson(KQRecords) with(IsSubstitute INT,SubstituteUserId UNIQUEIDENTIFIER,ClassId UNIQUEIDENTIFIER,[Status] INT,ClassNum INT,SubjectCode nvarchar(10),SignTime DATETIME,BeginTime DATETIME) as b LEFT JOIN aspnet_Membership C ON a.UserId=C.UserId LEFT JOIN aspnet_Membership mem ON mem.UserId=b.SubstituteUserId  left join JC_ClassInfos d on b.ClassId=d.ID left join Site_Dictionary e on b.SubjectCode=e.DictValue and e.DictTypeId=28 AND e.ColumnId IN (0,a.ColumnId) where BeginTime<=GETDATE() AND  IsSubstitute=1");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" ORDER BY RecordDate,ClassNum,OrderId, mem.CName,SignTime");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 删除指定日期之后的到离校考勤,不包括历史数据
        /// </summary>
        /// <param name="UserId">学生id</param>
        /// <param name="RecordDate">日期</param>
        /// <returns></returns>
        public bool Delete(Guid UserId, DateTime RecordDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_class_teacher");
            strSql.Append(" where UserId=@UserId AND RecordDate>@RecordDate");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16) ,
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = UserId;
            parameters[1].Value = RecordDate;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

