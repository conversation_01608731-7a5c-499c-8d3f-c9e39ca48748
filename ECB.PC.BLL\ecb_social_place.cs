﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_social_place
	/// </summary>
	public partial class ecb_social_place
	{
		public ecb_social_place()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_social_place");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_social_place model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_social_place(");
			strSql.Append("Id,ColumnID,ColumnPath,ExamId,PlaceNo,PlaceId,TestNumber,OrderId)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnID,@ColumnPath,@ExamId,@PlaceNo,@PlaceId,@TestNumber,@OrderId)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnID", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@ExamId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@PlaceNo", SqlDbType.NVarChar,50),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TestNumber", SqlDbType.NVarChar,255),
                    new SqlParameter("@OrderId", SqlDbType.Int,4)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnID;
			parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ExamId;
            parameters[4].Value = model.PlaceNo;
			parameters[5].Value = model.PlaceId;
            parameters[6].Value = model.TestNumber;
            parameters[7].Value = model.OrderId;

            int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_social_place model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_social_place set ");
			strSql.Append("ColumnID=@ColumnID,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("ExamId=@ExamId,");
			strSql.Append("PlaceNo=@PlaceNo,");
			strSql.Append("PlaceId=@PlaceId,");
			strSql.Append("TestNumber=@TestNumber,");
            strSql.Append("OrderId=@OrderId");
            strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnID", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@ExamId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@PlaceNo", SqlDbType.NVarChar,50),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TestNumber", SqlDbType.NVarChar,255),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnID;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.ExamId;
			parameters[3].Value = model.PlaceNo;
			parameters[4].Value = model.PlaceId;
			parameters[5].Value = model.TestNumber;
            parameters[6].Value = model.OrderId;
            parameters[7].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_social_place ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_social_place ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_social_place GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnID,ColumnPath,ExamId,PlaceNo,PlaceId,TestNumber,OrderId from ecb_social_place ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_social_place model=new ECB.PC.Model.ecb_social_place();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_social_place DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_social_place model=new ECB.PC.Model.ecb_social_place();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnID"]!=null && row["ColumnID"].ToString()!="")
				{
					model.ColumnID=int.Parse(row["ColumnID"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["ExamId"]!=null && row["ExamId"].ToString()!="")
				{
					model.ExamId= new Guid(row["ExamId"].ToString());
				}
				if(row["PlaceNo"]!=null)
				{
					model.PlaceNo=row["PlaceNo"].ToString();
				}
				if(row["PlaceId"]!=null && row["PlaceId"].ToString()!="")
				{
					model.PlaceId= new Guid(row["PlaceId"].ToString());
				}
				if(row["TestNumber"]!=null)
				{
					model.TestNumber=row["TestNumber"].ToString();
				}
                if (row["OrderId"] != null && row["OrderId"].ToString() != "")
                {
                    model.OrderId = int.Parse(row["OrderId"].ToString());
                }
            }
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnID,ColumnPath,ExamId,PlaceNo,PlaceId,TestNumber,OrderId ");
			strSql.Append(" FROM ecb_social_place ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnID,ColumnPath,ExamId,PlaceNo,PlaceId,TestNumber,OrderId ");
			strSql.Append(" FROM ecb_social_place ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_social_place ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_social_place T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_social_place";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool DeletebyExam(Guid ExamId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_social_place ");
            strSql.Append(" where ExamId=@ExamId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ExamId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ExamId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ExamId,Guid PlaceId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_social_place ");
            strSql.Append(" where  ExamId=@ExamId AND PlaceId=@PlaceId");
            SqlParameter[] parameters = {
                    new SqlParameter("@ExamId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = ExamId;
            parameters[0].Value = PlaceId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

