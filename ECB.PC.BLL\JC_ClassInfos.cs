﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:JC_ClassInfos
	/// </summary>
	public partial class JC_ClassInfos
	{
		public JC_ClassInfos()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from JC_ClassInfos");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.JC_ClassInfos model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into JC_ClassInfos(");
			strSql.Append("ID,ClassName,AliasName,ClassTypeCode,ClassPropertyCode,SchoolId,GradeId,HeadTeacherName,ClassDesc,ClassMotto,ClassroomAddress,OrderId,TeacherNo,SchoolColumnPath,Namerule,SchoolYear,NewClassId,PlaceId)");
			strSql.Append(" values (");
			strSql.Append("@ID,@ClassName,@AliasName,@ClassTypeCode,@ClassPropertyCode,@SchoolId,@GradeId,@HeadTeacherName,@ClassDesc,@ClassMotto,@ClassroomAddress,@OrderId,@TeacherNo,@SchoolColumnPath,@Namerule,@SchoolYear,@NewClassId,@PlaceId)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassName", SqlDbType.NVarChar,20),
					new SqlParameter("@AliasName", SqlDbType.NVarChar,20),
					new SqlParameter("@ClassTypeCode", SqlDbType.NVarChar,10),
					new SqlParameter("@ClassPropertyCode", SqlDbType.NVarChar,10),
					new SqlParameter("@SchoolId", SqlDbType.NVarChar,20),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@HeadTeacherName", SqlDbType.NVarChar,10),
					new SqlParameter("@ClassDesc", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassMotto", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassroomAddress", SqlDbType.NVarChar,50),
					new SqlParameter("@OrderId", SqlDbType.Int,4),
					new SqlParameter("@TeacherNo", SqlDbType.NVarChar,18),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,500),
					new SqlParameter("@Namerule", SqlDbType.NVarChar,10),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@NewClassId", SqlDbType.VarBinary,16),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ClassName;
			parameters[2].Value = model.AliasName;
			parameters[3].Value = model.ClassTypeCode;
			parameters[4].Value = model.ClassPropertyCode;
			parameters[5].Value = model.SchoolId;
			parameters[6].Value = model.GradeId;
			parameters[7].Value = model.HeadTeacherName;
			parameters[8].Value = model.ClassDesc;
			parameters[9].Value = model.ClassMotto;
			parameters[10].Value = model.ClassroomAddress;
			parameters[11].Value = model.OrderId;
			parameters[12].Value = model.TeacherNo;
			parameters[13].Value = model.SchoolColumnPath;
			parameters[14].Value = model.Namerule;
			parameters[15].Value = model.SchoolYear;
			parameters[16].Value = model.NewClassId;
			parameters[17].Value = model.PlaceId;
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.JC_ClassInfos model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update JC_ClassInfos set ");
			strSql.Append("ClassName=@ClassName,");
			strSql.Append("AliasName=@AliasName,");
			strSql.Append("ClassTypeCode=@ClassTypeCode,");
			strSql.Append("ClassPropertyCode=@ClassPropertyCode,");
			strSql.Append("SchoolId=@SchoolId,");
			strSql.Append("GradeId=@GradeId,");
			strSql.Append("HeadTeacherName=@HeadTeacherName,");
			strSql.Append("ClassDesc=@ClassDesc,");
			strSql.Append("ClassMotto=@ClassMotto,");
			strSql.Append("ClassroomAddress=@ClassroomAddress,");
			strSql.Append("OrderId=@OrderId,");
			strSql.Append("TeacherNo=@TeacherNo,");
			strSql.Append("SchoolColumnPath=@SchoolColumnPath,");
			strSql.Append("Namerule=@Namerule,");
			strSql.Append("SchoolYear=@SchoolYear,");
			strSql.Append("NewClassId=@NewClassId,");
			strSql.Append("PlaceId=@PlaceId");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ClassName", SqlDbType.NVarChar,20),
					new SqlParameter("@AliasName", SqlDbType.NVarChar,20),
					new SqlParameter("@ClassTypeCode", SqlDbType.NVarChar,10),
					new SqlParameter("@ClassPropertyCode", SqlDbType.NVarChar,10),
					new SqlParameter("@SchoolId", SqlDbType.NVarChar,20),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@HeadTeacherName", SqlDbType.NVarChar,10),
					new SqlParameter("@ClassDesc", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassMotto", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassroomAddress", SqlDbType.NVarChar,50),
					new SqlParameter("@OrderId", SqlDbType.Int,4),
					new SqlParameter("@TeacherNo", SqlDbType.NVarChar,18),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,500),
					new SqlParameter("@Namerule", SqlDbType.NVarChar,10),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@NewClassId", SqlDbType.VarBinary,16),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ClassName;
			parameters[1].Value = model.AliasName;
			parameters[2].Value = model.ClassTypeCode;
			parameters[3].Value = model.ClassPropertyCode;
			parameters[4].Value = model.SchoolId;
			parameters[5].Value = model.GradeId;
			parameters[6].Value = model.HeadTeacherName;
			parameters[7].Value = model.ClassDesc;
			parameters[8].Value = model.ClassMotto;
			parameters[9].Value = model.ClassroomAddress;
			parameters[10].Value = model.OrderId;
			parameters[11].Value = model.TeacherNo;
			parameters[12].Value = model.SchoolColumnPath;
			parameters[13].Value = model.Namerule;
			parameters[14].Value = model.SchoolYear;
			parameters[15].Value = model.NewClassId;
			parameters[16].Value = model.PlaceId;
			parameters[17].Value = model.ID;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from JC_ClassInfos ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = ID;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from JC_ClassInfos ");
			strSql.Append(" where ID in (" + IDlist + ")  ");
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.JC_ClassInfos GetModel(Guid ID)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 * from JC_ClassInfos ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = ID;

			ECB.PC.Model.JC_ClassInfos model = new ECB.PC.Model.JC_ClassInfos();
			DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}



		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.JC_ClassInfos DataRowToModel(DataRow row)
		{
			ECB.PC.Model.JC_ClassInfos model = new ECB.PC.Model.JC_ClassInfos();
			if (row != null)
			{
				if (row["ID"] != null && row["ID"].ToString() != "")
				{
					model.ID = new Guid(row["ID"].ToString());
				}
				if (row["ClassName"] != null)
				{
					model.ClassName = row["ClassName"].ToString();
				}
				if (row["AliasName"] != null)
				{
					model.AliasName = row["AliasName"].ToString();
				}
				if (row["ClassTypeCode"] != null)
				{
					model.ClassTypeCode = row["ClassTypeCode"].ToString();
				}
				if (row["ClassPropertyCode"] != null)
				{
					model.ClassPropertyCode = row["ClassPropertyCode"].ToString();
				}
				if (row["SchoolId"] != null)
				{
					model.SchoolId = row["SchoolId"].ToString();
				}
				if (row["GradeId"] != null && row["GradeId"].ToString() != "")
				{
					model.GradeId = new Guid(row["GradeId"].ToString());
				}
				if (row["HeadTeacherName"] != null)
				{
					model.HeadTeacherName = row["HeadTeacherName"].ToString();
				}
				if (row["ClassDesc"] != null)
				{
					model.ClassDesc = row["ClassDesc"].ToString();
				}
				if (row["ClassMotto"] != null)
				{
					model.ClassMotto = row["ClassMotto"].ToString();
				}
				if (row["ClassroomAddress"] != null)
				{
					model.ClassroomAddress = row["ClassroomAddress"].ToString();
				}
				if (row["OrderId"] != null && row["OrderId"].ToString() != "")
				{
					model.OrderId = int.Parse(row["OrderId"].ToString());
				}
				if (row["TeacherNo"] != null)
				{
					model.TeacherNo = row["TeacherNo"].ToString();
				}
				if (row["SchoolColumnPath"] != null)
				{
					model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
				}
				if (row["Namerule"] != null)
				{
					model.Namerule = row["Namerule"].ToString();
				}
				if (row["SchoolYear"] != null)
				{
					model.SchoolYear = row["SchoolYear"].ToString();
				}
				if (row["NewClassId"] != null && row["NewClassId"].ToString() != "")
				{
					model.NewClassId = (byte[])row["NewClassId"];
				}
				if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
				{
					model.PlaceId = new Guid(row["PlaceId"].ToString());
				}
			}
			return model;
		}

		public bool DeleteAll(Guid ClassID)
		{
			StringBuilder strSql = new StringBuilder();
			//任课老师
			strSql.AppendFormat("DELETE FROM [JC_Subjects] WHERE ClassID = '{0}';", ClassID);
			//班级公告
			strSql.AppendFormat("DELETE FROM [JC_ClassNotes] WHERE ClassID = '{0}';", ClassID);
			//考试信息
			strSql.AppendFormat("DELETE FROM [JC_Tests] WHERE ClassID = '{0}';", ClassID);
			//成绩信息
			strSql.AppendFormat("DELETE FROM [JC_TestScores] WHERE ClassID = '{0}';", ClassID);
			//班务日志
			strSql.AppendFormat("DELETE FROM [JC_ClassLogs] WHERE ClassID = '{0}';", ClassID);
			////第三方映射表
			//strSql.AppendFormat("DELETE FROM [Interface_Other] WHERE YunEdu_TableID = '{0}' AND YunEdu_TableName='JC_ClassInfos';", ClassID.ToString());
			//班级课表
			strSql.AppendFormat("DELETE FROM [ecb_TimeTable] WHERE ClassId = '{0}';", ClassID);
			//学生课表
			strSql.AppendFormat("DELETE FROM [ecb_TimeTable_stu] WHERE ClassId = '{0}';", ClassID);
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select * ");
			strSql.Append(" FROM JC_ClassInfos ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" * ");
			strSql.Append(" FROM JC_ClassInfos ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere, string[] ConnStr = null)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM JC_ClassInfos ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString(), ConnStr);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from JC_ClassInfos T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "JC_ClassInfos";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		/// <summary>
		/// 根据班级名称，学校Id得到一个对象实体
		/// </summary>
		public ECB.PC.Model.JC_ClassInfos GetModel(string className, string schoolId, string schoolYear, Guid GradeId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 * from JC_ClassInfos ");
			strSql.Append(" where ClassName=@ClassName AND SchoolId=@SchoolId and  SchoolYear=@SchoolYear");
			if (GradeId != Guid.Empty)
			{
				strSql.Append("  AND GradeId='" + GradeId + "'");
			}
			SqlParameter[] parameters = {
					new SqlParameter("@ClassName", SqlDbType.NVarChar,20),
					new SqlParameter("@SchoolId", SqlDbType.NVarChar,20),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10)};
			parameters[0].Value = className;
			parameters[1].Value = schoolId;
			parameters[2].Value = schoolYear;

			ECB.PC.Model.JC_ClassInfos model = new ECB.PC.Model.JC_ClassInfos();
			DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		/// 获取最多班级数
		/// </summary>
		/// <param name="strWhere"></param>
		/// <returns></returns>
		public int GetClassMax(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select MAX(t._count) from (");
			strSql.Append("select count(1) _count FROM JC_ClassInfos ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" ) t");
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod

		/// <summary>
		/// 获取班级信息及学期
		/// </summary>
		/// <param name="strWhere"></param>
		/// <returns></returns>
		public DataSet GetClassList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select a.*,(SELECT TermID FROM dbo.JC_GradeInfos WHERE ID=a.GradeId) termId");
			strSql.Append(" FROM JC_ClassInfos a");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}
		/// <summary>
		/// 获取班级信息通过学生ID
		/// </summary>
		/// <param name="strWhere"></param>
		/// <returns></returns>
		public Model.JC_ClassInfos GetClassByStudentId(Guid StudentId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select a.*");
			strSql.Append(" FROM JC_ClassInfos a");

			strSql.AppendFormat(" where Id IN(select ClassId FROM jc_studentinfos where Id='{0}')", StudentId);
			DataSet ds = DbHelperSQL.Query(strSql.ToString());
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}

		}


		/// <summary>
		/// 学生信息
		/// </summary>
		/// <param name="strWhere"></param>
		/// <returns></returns>
		public DataSet GetStudentList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append(" select a.StudentName, ");
			strSql.Append(" (select v.Photo from aspnet_Membership v where v.UserId=a.ID) as Photo,(select v.UserName from aspnet_Membership v where v.UserId=a.ID) as UserName,(select v.DPassword from aspnet_Membership v where v.UserId=a.ID) as DPassword, ");
			strSql.Append(" cast((select d.EnterSchoolYear from JC_GradeInfos d where d.ID=a.GradeID) as nvarchar(10))+'级'+substring(b.ClassName,CHARINDEX('（',b.ClassName)+1,CHARINDEX('）',b.ClassName)-CHARINDEX('（',b.ClassName)-1)+'班' as ClassName, ");
			strSql.Append(" c.DictText from JC_StudentInfos a ");
			strSql.Append(" left join JC_ClassInfos b on a.ClassID=b.ID ");
			strSql.Append(" left join JC_GradeInfos d on a.GradeID=d.ID ");
			strSql.Append(" left join Site_Dictionary c on c.DictTypeId=39 and d.EduStageCode=c.DictValue ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		public DataTable GetClassList(int ColumnId)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@SchoolId", SqlDbType.Int)};
			parameters[0].Value = ColumnId;
			string strSql = "select * from JC_ClassInfos where  SchoolId=@SchoolId and SchoolYear in(SELECT SchoolYear FROM JC_TermInfos WHERE SchoolColumnId=@SchoolId and IsCurrentTerm=1 )";
			DataSet ds = DbHelperSQL.Query(strSql, parameters);
			return ds.Tables[0];
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.JC_ClassInfos GetModelByPlaceId(Guid PlaceId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 * from JC_ClassInfos ");
			strSql.Append(" where PlaceId=@PlaceId and SchoolYear in (select SchoolYear from JC_TermInfos where SchoolColumnId=SchoolId and IsCurrentTerm=1)");
			strSql.Append(" ORDER BY ClassTypeCode");
			SqlParameter[] parameters = {
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = PlaceId;

			ECB.PC.Model.JC_ClassInfos model = new ECB.PC.Model.JC_ClassInfos();
			DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetListForJson(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ID,ClassName ");
			strSql.Append(" FROM JC_ClassInfos ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string fileName, string strWhere, string tabName, string order)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  " + fileName);
			strSql.Append(" FROM  " + tabName);
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			if (order.Trim() != "")
			{
				strSql.Append(" ORDER BY " + order);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}
		/// <summary>
		///修改班级信息
		/// </summary>
		public bool UpdateClass(string where, string setName)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update JC_ClassInfos set  " + setName);
			strSql.Append(" where " + where);
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 根据年级id获取班级列表
		/// 字段：ID,ClassName,PlaceId
		/// </summary>
		/// <param name="gradeId">年级id</param>
		/// <returns>班级列表</returns>
		public DataSet GetList(Guid gradeId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("  select a.ID,a.ClassName,a.PlaceId from JC_ClassInfos a where a.GradeId=@GradeId and exists(select 1 from ecb_classbrands b where b.ClassId=a.ID) ");
			strSql.Append(" order by a.OrderId");
			SqlParameter[] parameters = {
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16)
			};
			parameters[0].Value = gradeId;
			return DbHelperSQL.Query(strSql.ToString(), parameters);
		}

		/// <summary>
		/// 获取班级地点信息
		/// </summary>
		/// <param name="currentColumnId"></param>
		/// <returns></returns>
		public DataTable GetClassPlace(int currentColumnId)
		{
			string sql = $@"select a.ClassName 班级,b.Name 现地点,'' 新地点
			from JC_ClassInfos a left join ecb_place b on a.PlaceId=b.Id where a.SchoolId={currentColumnId} and a.SchoolYear in(select SchoolYear from JC_TermInfos where SchoolColumnId={currentColumnId} and IsCurrentTerm=1)  ORDER BY  ClassName COLLATE Chinese_PRC_Stroke_CS_AS";
			return DbHelperSQL.Query(sql).Tables[0];
		}
	}

}

