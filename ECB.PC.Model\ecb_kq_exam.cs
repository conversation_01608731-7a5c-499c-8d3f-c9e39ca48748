﻿
using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 考试考勤记录表
    /// </summary>
    [Serializable]
    public partial class ecb_kq_exam
    {
        public ecb_kq_exam()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _testid;
        private Guid _placeid;
        private string _subjectcode;
        private Guid _userid;
        private DateTime? _signintime;
        private DateTime? _signouttime;
        private int _status;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 考试id
        /// </summary>
        public Guid TestId
        {
            set { _testid = value; }
            get { return _testid; }
        }
        /// <summary>
        /// 考场id
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        /// <summary>
        /// 考试科目
        /// </summary>
        public string SubjectCode
        {
            set { _subjectcode = value; }
            get { return _subjectcode; }
        }
        /// <summary>
        /// 考勤人
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 进场时间
        /// </summary>
        public DateTime? SignInTime
        {
            set { _signintime = value; }
            get { return _signintime; }
        }
        /// <summary>
        /// 离场时间
        /// </summary>
        public DateTime? SignOutTime
        {
            set { _signouttime = value; }
            get { return _signouttime; }
        }
        /// <summary>
        /// 考勤状态
        /// </summary>
        public int Status
        {
            set { _status = value; }
            get { return _status; }
        }
        #endregion Model

    }
}

