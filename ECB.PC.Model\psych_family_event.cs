﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 重大生活事件
	/// </summary>
	[Serializable]
	public partial class psych_family_event
	{
		public psych_family_event()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _studentid;
		private string _eventcode;
		private string _eventcontent;
		private DateTime _eventtime;
		private string _teachername;
		private int _sortid;
		private Guid _lasteditby;
		private DateTime _lastedittime;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 学生Id
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 事件类别
		/// </summary>
		public string EventCode
		{
			set{ _eventcode=value;}
			get{return _eventcode;}
		}
		/// <summary>
		/// 事件内容
		/// </summary>
		public string EventContent
		{
			set{ _eventcontent=value;}
			get{return _eventcontent;}
		}
		/// <summary>
		/// 事件时间
		/// </summary>
		public DateTime EventTime
		{
			set{ _eventtime=value;}
			get{return _eventtime;}
		}
		/// <summary>
		/// 教师
		/// </summary>
		public string TeacherName
		{
			set{ _teachername=value;}
			get{return _teachername;}
		}
		/// <summary>
		/// 排序
		/// </summary>
		public int SortId
		{
			set{ _sortid=value;}
			get{return _sortid;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}