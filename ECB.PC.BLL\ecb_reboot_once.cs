﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_reboot_once
    /// </summary>
    public partial class ecb_reboot_once
    {
        public ecb_reboot_once()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_reboot_once");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_reboot_once model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_reboot_once(");
            strSql.Append("id,ColumnID,ColumnPath,RuleName,BeginDate,EndDate,Ids,LastEditTime,LastEditor,OnceType)");
            strSql.Append(" values (");
            strSql.Append("@id,@ColumnID,@ColumnPath,@RuleName,@BeginDate,@EndDate,@Ids,@LastEditTime,@LastEditor,@OnceType)");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@RuleName", SqlDbType.NVarChar,255),
                    new SqlParameter("@BeginDate", SqlDbType.DateTime),
                    new SqlParameter("@EndDate", SqlDbType.DateTime),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@OnceType", SqlDbType.Int,4)};
            parameters[0].Value = model.id;
            parameters[1].Value = model.ColumnID;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.RuleName;
            parameters[4].Value = model.BeginDate;
            parameters[5].Value = model.EndDate;
            parameters[6].Value = model.Ids;
            parameters[7].Value = model.LastEditTime;
            parameters[8].Value = model.LastEditor;
            parameters[9].Value = model.OnceType;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_reboot_once model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_reboot_once set ");
            strSql.Append("ColumnID=@ColumnID,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("RuleName=@RuleName,");
            strSql.Append("BeginDate=@BeginDate,");
            strSql.Append("EndDate=@EndDate,");
            strSql.Append("Ids=@Ids,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("LastEditor=@LastEditor,");
            strSql.Append("OnceType=@OnceType");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@RuleName", SqlDbType.NVarChar,255),
                    new SqlParameter("@BeginDate", SqlDbType.DateTime),
                    new SqlParameter("@EndDate", SqlDbType.DateTime),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@OnceType", SqlDbType.Int,4),
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnID;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.RuleName;
            parameters[3].Value = model.BeginDate;
            parameters[4].Value = model.EndDate;
            parameters[5].Value = model.Ids;
            parameters[6].Value = model.LastEditTime;
            parameters[7].Value = model.LastEditor;
            parameters[8].Value = model.OnceType;
            parameters[9].Value = model.id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_once ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_once ");
            strSql.Append(" where id in (" + idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_reboot_once GetModel(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 id,ColumnID,ColumnPath,RuleName,BeginDate,EndDate,Ids,LastEditTime,LastEditor,OnceType from ecb_reboot_once ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            ECB.PC.Model.ecb_reboot_once model = new ECB.PC.Model.ecb_reboot_once();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_reboot_once DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_reboot_once model = new ECB.PC.Model.ecb_reboot_once();
            if (row != null)
            {
                if (row["id"] != null && row["id"].ToString() != "")
                {
                    model.id = new Guid(row["id"].ToString());
                }
                if (row["ColumnID"] != null && row["ColumnID"].ToString() != "")
                {
                    model.ColumnID = int.Parse(row["ColumnID"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["RuleName"] != null)
                {
                    model.RuleName = row["RuleName"].ToString();
                }
                if (row["BeginDate"] != null && row["BeginDate"].ToString() != "")
                {
                    model.BeginDate = DateTime.Parse(row["BeginDate"].ToString());
                }
                if (row["EndDate"] != null && row["EndDate"].ToString() != "")
                {
                    model.EndDate = DateTime.Parse(row["EndDate"].ToString());
                }
                if (row["Ids"] != null)
                {
                    model.Ids = row["Ids"].ToString();
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
                if (row["LastEditor"] != null && row["LastEditor"].ToString() != "")
                {
                    model.LastEditor = new Guid(row["LastEditor"].ToString());
                }
                if (row["OnceType"] != null && row["OnceType"].ToString() != "")
                {
                    model.OnceType = int.Parse(row["OnceType"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select id,ColumnID,ColumnPath,RuleName,BeginDate,EndDate,Ids,LastEditTime,LastEditor,OnceType ");
            strSql.Append(" FROM ecb_reboot_once ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" id,ColumnID,ColumnPath,RuleName,BeginDate,EndDate,Ids,LastEditTime,LastEditor,OnceType ");
            strSql.Append(" FROM ecb_reboot_once ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere, string[] ConnStr=null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_reboot_once ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString(),ConnStr);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_reboot_once T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_reboot_once";
			parameters[1].Value = "id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetAllList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select * from ( ");
            strSql.Append(" select id,ColumnID,ColumnPath,RuleName,Ids,CONVERT(nvarchar(100),BeginDate,23) as BeginDate ,CONVERT(nvarchar(100),EndDate,23) EndDate,LastEditTime from ecb_reboot_once ");
            strSql.Append(" union ");
            strSql.Append(" select  id,ColumnID,ColumnPath,RuleName,Ids,'' BeginDate,'' EndDate,LastEditTime from  ecb_reboot_rule ");
            strSql.Append(" ) tt");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetAllListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_reboot_once T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得指定日期的临时开关机
        /// </summary>
        public DataSet GetRebootByDay(DateTime date, Guid ClassId, int BrandsType, int columnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select MIN(REPLACE(CONVERT(char(24),BootTime,21),CONVERT(char(10),BootTime,23),CONVERT(char(10),BeginDate,23))) as BootTime, ");
            strSql.Append(" MAX(REPLACE(CONVERT(char(24),OffTime,21),CONVERT(char(10),OffTime,23),CONVERT(char(10),EndDate,23))) as OffTime from ecb_reboot_rule a  ");
            strSql.Append(" left join ecb_reboot_once b on a.Id=b.RuleId ");
            strSql.Append(" where BeginDate<='" + date + "' and '" + date + "'<=EndDate and a.ColumnId='" + columnId + "' AND ");
            strSql.Append(" (CHARINDEX(cast('" + ClassId + "' as varchar(36)),ids)>0 or CHARINDEX('\"'+cast(" + BrandsType + " as varchar(10))+'\":\"0\"',ids)>0 or Ids='{0}') ");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

