﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理访谈信息
	/// </summary>
	[Serializable]
	public partial class psych_test
	{
		public psych_test()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _title;
		private Guid _creatorid;
		private DateTime _createtime;
		private int _isconfirm = 0;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 标题
		/// </summary>
		public string Title
		{
			set{ _title=value;}
			get{return _title;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid CreatorId
		{
			set{ _creatorid=value;}
			get{return _creatorid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 是否确认 0:未确认 1:已确认
		/// </summary>
		public int IsConfirm
		{
			set{ _isconfirm=value;}
			get{return _isconfirm;}
		}
		#endregion Model

	}
}