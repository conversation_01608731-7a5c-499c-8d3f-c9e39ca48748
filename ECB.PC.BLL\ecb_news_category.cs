﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_news_category
    /// </summary>
    public partial class ecb_news_category
    {
        public ecb_news_category()
        { }
        #region  BasicMethod
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_news_category");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.Int,4)            };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_news_category model)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
                     new SqlParameter("@Id",SqlDbType.Int,4),
                    new SqlParameter("@TypeName", SqlDbType.NVarChar,150),
                    new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@CallName", SqlDbType.NVarChar,50)};
            parameters[0].Value = ParameterDirection.Output;
            parameters[1].Value = model.Name;
            parameters[2].Value = model.ParentId;
            parameters[3].Value = model.ColumnId;
            parameters[4].Value = model.ColumnPath;
            parameters[5].Value = model.CallName;
            object obj = DbHelperSQL.RunProcedure("UP_ecb_news_category_ADD", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_news_category model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_news_category set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Name=@Name,");
            strSql.Append("ParentId=@ParentId,");
            strSql.Append("CategoryPath=@CategoryPath,");
            strSql.Append("Depth=@Depth,");
            strSql.Append("CallName=@CallName,");
            strSql.Append("SortId=@SortId");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@Name", SqlDbType.NVarChar,200),
                    new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@CategoryPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@Depth", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.Int,4),
                    new SqlParameter("@SortId", SqlDbType.Int,4),
                    new SqlParameter("@CallName", SqlDbType.NVarChar,50)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.Name;
            parameters[3].Value = model.ParentId;
            parameters[4].Value = model.CategoryPath;
            parameters[5].Value = model.Depth;
            parameters[6].Value = model.Id;
            parameters[7].Value = model.SortId;
            parameters[8].Value = model.CallName;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_news_category ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.Int,4)            };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string SortIdlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_news_category ");
            strSql.Append(" where SortId in (" + SortIdlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_news_category GetModel(int Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Name,ParentId,CategoryPath,Depth,SortId,CallName from ecb_news_category ");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.Int,4)
            };
            parameters[0].Value = Id;

            Model.ecb_news_category model = new Model.ecb_news_category();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_news_category DataRowToModel(DataRow row)
        {
            Model.ecb_news_category model = new Model.ecb_news_category();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = int.Parse(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["ParentId"] != null && row["ParentId"].ToString() != "")
                {
                    model.ParentId = int.Parse(row["ParentId"].ToString());
                }
                if (row["CategoryPath"] != null)
                {
                    model.CategoryPath = row["CategoryPath"].ToString();
                }
                if (row["Depth"] != null && row["Depth"].ToString() != "")
                {
                    model.Depth = int.Parse(row["Depth"].ToString());
                }
                if (row["SortId"] != null && row["SortId"].ToString() != "")
                {
                    model.SortId = int.Parse(row["SortId"].ToString());
                }
                if (row["CallName"] != null)
                {
                    model.CallName = row["CallName"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Name,ParentId,CategoryPath,Depth,SortId,CallName ");
            strSql.Append(" FROM ecb_news_category ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,Name,ParentId,CategoryPath,Depth,SortId,CallName ");
            strSql.Append(" FROM ecb_news_category ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_news_category ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.SortId desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_news_category T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_news_category";
			parameters[1].Value = "SortId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        //上移
        public object MoveUp(int Id)
        {
            SqlParameter[] parameter = { new SqlParameter("@Id", Id) };
            return DbHelperSQL.RunProcedure("UP_ecb_news_category_moveup", parameter);
        }
        //下移
        public object MoveDown(int Id)
        {
            SqlParameter[] parameter = { new SqlParameter("@Id", Id) };
            return DbHelperSQL.RunProcedure("UP_ecb_news_category_movedown", parameter);
        }
        /// <summary>
        /// 根据深度查找类型集合
        /// </summary>
        /// <param name="ColumnPath"></param>
        /// <param name="categoryDepth"></param>
        /// <returns></returns>
        public DataSet GetTypesByDepth(string ColumnPath, int Depth)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select CategoryPath,Name,ColumnId FROM ecb_news_category where ColumnPath like '' + @ColumnPath + '%' and Depth=@Depth order by ColumnId,SortId");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@Depth", SqlDbType.Int,4)};
            parameters[0].Value = ColumnPath;
            parameters[1].Value = Depth;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsbyBname(int columnId, string Name, int Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_news_category");
            strSql.Append(" where ColumnId=@ColumnId and Name=@Name and Id!=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@Name",SqlDbType.NVarChar,150),
                    new SqlParameter("@Id",SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = Name;
            parameters[2].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters,DbHelperSQL.ConnMain);
        }
        #endregion  ExtensionMethod
    }
}
