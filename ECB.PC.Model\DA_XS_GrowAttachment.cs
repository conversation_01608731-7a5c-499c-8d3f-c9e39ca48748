﻿
using System;
namespace ECB.PC.Model
{

    /// <summary>
    /// 学生成长档案附件
    /// </summary>
    [Serializable]
    public partial class DA_XS_GrowAttachment
    {
        public DA_XS_GrowAttachment()
        { }
        #region Model
        private Guid _id;
        private Guid _relationid;
        private int? _schoolcolumnid;
        private string _schoolcolumnpath;
        private string _filepath;
        private string _filename;
        private DateTime? _uploadtime;
        private int? _filetype;
        private string _schoolyear;
        private Guid _creatoruserid;
        private string _creatorname;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 关联ID。外键，关联其他表主键
        /// </summary>
        public Guid RelationId
        {
            set { _relationid = value; }
            get { return _relationid; }
        }
        /// <summary>
        /// 学校ID
        /// </summary>
        public int? SchoolColumnId
        {
            set { _schoolcolumnid = value; }
            get { return _schoolcolumnid; }
        }
        /// <summary>
        /// 学校路径
        /// </summary>
        public string SchoolColumnPath
        {
            set { _schoolcolumnpath = value; }
            get { return _schoolcolumnpath; }
        }
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath
        {
            set { _filepath = value; }
            get { return _filepath; }
        }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName
        {
            set { _filename = value; }
            get { return _filename; }
        }
        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime? UploadTime
        {
            set { _uploadtime = value; }
            get { return _uploadtime; }
        }
        /// <summary>
        /// 附件类型。枚举字典
        /// </summary>
        public int? FileType
        {
            set { _filetype = value; }
            get { return _filetype; }
        }
        /// <summary>
        /// 学年
        /// </summary>
        public string SchoolYear
        {
            set { _schoolyear = value; }
            get { return _schoolyear; }
        }
        /// <summary>
        /// 上传人
        /// </summary>
        public Guid CreatorUserId
        {
            set { _creatoruserid = value; }
            get { return _creatoruserid; }
        }
        /// <summary>
        /// 上传人名称
        /// </summary>
        public string CreatorName
        {
            set { _creatorname = value; }
            get { return _creatorname; }
        }
        #endregion Model

    }

    public class File
    {
        private string _name;
        /// <summary>
        /// 名称
        /// </summary>
        public string Name
        {
            get { return _name; }
            set { _name = value; }
        }
        private string _format;
        /// <summary>
        /// 类型
        /// </summary>
        public string Format
        {
            get { return _format; }
            set { _format = value; }
        }
        private string _path;
        /// <summary>
        /// 路径
        /// </summary>
        public string Path
        {
            get { return _path; }
            set { _path = value; }
        }
    }
}

