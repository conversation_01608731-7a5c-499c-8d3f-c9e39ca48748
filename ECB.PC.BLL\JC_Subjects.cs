﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:JC_Subjects
    /// </summary>
    public partial class JC_Subjects
    {
        public JC_Subjects()
        {
        }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from JC_Subjects");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.JC_Subjects model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into JC_Subjects(");
            strSql.Append("ID,TeacherNo,ColumnId,ColumnPath,TermID,GradeID,ClassID,SubjectCode,StatusCode,CreateDate,LastEditDate,LastEditBy,TName,SchoolYear,TeacherId)");
            strSql.Append(" values (");
            strSql.Append("@ID,@TeacherNo,@ColumnId,@ColumnPath,@TermID,@GradeID,@ClassID,@SubjectCode,@StatusCode,@CreateDate,@LastEditDate,@LastEditBy,@TName,@SchoolYear,@TeacherId)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TeacherNo", SqlDbType.NVarChar,18),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@GradeID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@StatusCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TName", SqlDbType.NVarChar,10),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
            new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ID = Guid.NewGuid();
            parameters[1].Value = model.TeacherNo;
            parameters[2].Value = model.ColumnId;
            parameters[3].Value = model.ColumnPath;
            parameters[4].Value = model.TermID;
            parameters[5].Value = model.GradeID;
            parameters[6].Value = model.ClassID;
            parameters[7].Value = model.SubjectCode;
            parameters[8].Value = model.StatusCode;
            parameters[9].Value = model.CreateDate;
            parameters[10].Value = model.LastEditDate;
            parameters[11].Value = model.LastEditBy;
            parameters[12].Value = model.TName;
            parameters[13].Value = model.SchoolYear;
            parameters[14].Value = model.TeacherId;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.JC_Subjects model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update JC_Subjects set ");
            strSql.Append("TeacherNo=@TeacherNo,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("TermID=@TermID,");
            strSql.Append("GradeID=@GradeID,");
            strSql.Append("ClassID=@ClassID,");
            strSql.Append("SubjectCode=@SubjectCode,");
            strSql.Append("StatusCode=@StatusCode,");
            strSql.Append("CreateDate=@CreateDate,");
            strSql.Append("LastEditDate=@LastEditDate,");
            strSql.Append("LastEditBy=@LastEditBy,");
            strSql.Append("TName=@TName,");
            strSql.Append("TeacherId=@TeacherId,");
            strSql.Append("SchoolYear=@SchoolYear");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@TeacherNo", SqlDbType.NVarChar,18),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@GradeID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@StatusCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TName", SqlDbType.NVarChar,10),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.TeacherNo;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.TermID;
            parameters[4].Value = model.GradeID;
            parameters[5].Value = model.ClassID;
            parameters[6].Value = model.SubjectCode;
            parameters[7].Value = model.StatusCode;
            parameters[8].Value = model.CreateDate;
            parameters[9].Value = model.LastEditDate;
            parameters[10].Value = model.LastEditBy;
            parameters[11].Value = model.TName;
            parameters[12].Value = model.SchoolYear;
            parameters[13].Value = model.ID;
            parameters[14].Value = model.TeacherId;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Subjects ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public bool DeleteByClassID(Guid ClassID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Subjects ");
            strSql.Append(" where ClassID=@ClassID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ClassID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Subjects ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Subjects GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,TeacherNo,ColumnId,ColumnPath,TermID,GradeID,ClassID,SubjectCode,StatusCode,CreateDate,LastEditDate,LastEditBy,TName,SchoolYear,TeacherId from JC_Subjects ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.JC_Subjects model = new ECB.PC.Model.JC_Subjects();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Subjects DataRowToModel(DataRow row)
        {
            ECB.PC.Model.JC_Subjects model = new ECB.PC.Model.JC_Subjects();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["TeacherNo"] != null)
                {
                    model.TeacherNo = row["TeacherNo"].ToString();
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["TermID"] != null && row["TermID"].ToString() != "")
                {
                    model.TermID = new Guid(row["TermID"].ToString());
                }
                if (row["GradeID"] != null && row["GradeID"].ToString() != "")
                {
                    model.GradeID = new Guid(row["GradeID"].ToString());
                }
                if (row["ClassID"] != null && row["ClassID"].ToString() != "")
                {
                    model.ClassID = new Guid(row["ClassID"].ToString());
                }
                if (row["SubjectCode"] != null)
                {
                    model.SubjectCode = row["SubjectCode"].ToString();
                }
                if (row["StatusCode"] != null)
                {
                    model.StatusCode = row["StatusCode"].ToString();
                }
                if (row["CreateDate"] != null && row["CreateDate"].ToString() != "")
                {
                    model.CreateDate = DateTime.Parse(row["CreateDate"].ToString());
                }
                if (row["LastEditDate"] != null && row["LastEditDate"].ToString() != "")
                {
                    model.LastEditDate = DateTime.Parse(row["LastEditDate"].ToString());
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["TName"] != null)
                {
                    model.TName = row["TName"].ToString();
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
                if (row["TeacherId"] != null && row["TeacherId"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["TeacherId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,TeacherNo,ColumnId,ColumnPath,TermID,GradeID,ClassID,SubjectCode,StatusCode,CreateDate,LastEditDate,LastEditBy,TName,SchoolYear,TeacherId ");
            strSql.Append(" FROM JC_Subjects ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,TeacherNo,ColumnId,ColumnPath,TermID,GradeID,ClassID,SubjectCode,StatusCode,CreateDate,LastEditDate,LastEditBy,TName,SchoolYear,TeacherId ");
            strSql.Append(" FROM JC_Subjects ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_Subjects ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_Subjects T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "JC_Subjects";
            parameters[1].Value = "ID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod

        /// <summary>
        /// 获取任课教师
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetTeacher(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.TeacherNo,b.UserID FROM dbo.JC_Subjects a");
            strSql.Append(" LEFT JOIN dbo.UserInfos b ON a.TeacherNo=b.UserNo");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where StatusCode=1 and " + strWhere);
            }
            strSql.Append(" GROUP BY a.TeacherNo,b.UserID");
            return DbHelperSQL.Query(strSql.ToString());
        }

        // <summary>
        /// 获取教师,班主任所带班级的所有年级
        /// </summary>
        public DataSet GetGradeList(string SchoolYear, string TeacherNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select t.*,a.GradeName from (  ");
            strSql.Append(" SELECT GradeId 'ID',TeacherNo FROM dbo.JC_ClassInfos UNION ");
            strSql.Append(" SELECT GradeID 'ID',TeacherNo FROM dbo.JC_Subjects WHERE StatusCode=1");
            strSql.Append(" ) t LEFT JOIN dbo.JC_GradeInfos a ON t.ID=a.ID ");
            strSql.Append("WHERE TeacherNo='" + TeacherNo + "' AND SchoolYear='" + SchoolYear + "'");
            strSql.Append(" ORDER BY a.OrderId");

            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 根据年级获取教师，班主任所带班级
        /// </summary>
        public DataSet GetClassList(string GradeId, string TeacherNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT t.* from ( ");
            strSql.Append(" SELECT ID ,TeacherNo,ClassName,GradeId FROM dbo.JC_ClassInfos ");
            strSql.Append(" UNION SELECT ClassID 'ID',a.TeacherNo,b.ClassName,a.GradeID FROM dbo.JC_Subjects a");
            strSql.Append(" LEFT JOIN dbo.JC_ClassInfos b ON a.ClassID=b.ID WHERE  StatusCode=1) t");
            strSql.Append(" where TeacherNo='" + TeacherNo + "' and GradeID='" + GradeId + "'");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取任课教师
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetSubject(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select DictText,SubjectCode FROM JC_Subjects a LEFT JOIN dbo.Site_Dictionary b ON a.SubjectCode=b.DictValue AND DictTypeId=28 and b.ColumnId in (0,a.ColumnId)");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where  " + strWhere);
            }
            strSql.Append(" GROUP BY DictText, SubjectCode,DictTextOrder ORDER BY DictTextOrder");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取 班级任课教师 以及任课课程
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetClassTeacher(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.TeacherNo,b.UserID,a.TName,SubjectCode,ClassID,DictText FROM dbo.JC_Subjects a");
            strSql.Append(" LEFT JOIN dbo.UserInfos b ON a.TeacherNo=b.UserNo LEFT JOIN dbo.Site_Dictionary c ON a.SubjectCode=c.DictValue AND DictTypeId=28 and c.ColumnId in (0,a.ColumnId)");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where StatusCode=1 and " + strWhere);
            }
            strSql.Append(" GROUP BY a.TeacherNo,b.UserID,a.TName,SubjectCode,ClassID,DictText,c.DictTextOrder ORDER BY c.DictTextOrder");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Subjects GetModel(Guid ClassID, string SubjectCode)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,TeacherNo,ColumnId,ColumnPath,TermID,GradeID,ClassID,SubjectCode,StatusCode,CreateDate,LastEditDate,LastEditBy,TName,SchoolYear from JC_Subjects ");
            strSql.Append(" where ClassID=@ClassID AND SubjectCode=@SubjectCode and StatusCode=1");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16) ,new SqlParameter("@SubjectCode", SqlDbType.NVarChar,2)          };
            parameters[0].Value = ClassID;
            parameters[1].Value = SubjectCode;

            ECB.PC.Model.JC_Subjects model = new ECB.PC.Model.JC_Subjects();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 批量更新任课教师
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="subjectCode"></param>
        /// <param name="teacherId"></param>
        /// <returns></returns>
        public bool UpdateSubjectTeacher(Guid classId, string subjectCode, Guid teacherId, Guid TermID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" update ecb_TimeTable set TeacherId=(case when SubjectCode=@SubjectCode then @TeacherId else TeacherId end ) ,TeacherId1=(case when SubjectCode1=@SubjectCode then @TeacherId else TeacherId1 end )  where ClassId = @ClassId AND TermID= @TermID AND (SubjectCode= @SubjectCode OR SubjectCode1= @SubjectCode)");
            strSql.Append(" update ecb_TimeTable_stu set TeacherId=(case when SubjectNo=@SubjectCode then @TeacherId else TeacherId end ) ,TeacherId1=(case when SubjectNo1=@SubjectCode then @TeacherId else TeacherId1 end )  where ClassId = @ClassId AND TermID= @TermID AND (SubjectNo= @SubjectCode OR  SubjectNo1= @SubjectCode) ");
            SqlParameter[] parameters = {
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = teacherId;
            parameters[1].Value = classId;
            parameters[2].Value = subjectCode;
            parameters[3].Value = TermID;
            int effects = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effects > 0;
        }


        /// <summary>
        /// 查询任课教师表
        /// </summary>
        /// <param name="columnId"></param>
        /// <param name="gradeId"></param>
        /// <param name="classId"></param>
        /// <returns></returns>
        public DataSet GetSubjects(int columnId, Guid gradeId, Guid classId)
        {
            StringBuilder _sbSql = new StringBuilder();
            _sbSql.Append(@"select a.ID,d.TName,a.SubjectCode,b.DictText StatusText,c.DictText SubjectName from JC_Subjects a ");
            _sbSql.Append(" left join Site_Dictionary b on b.DictTypeId=41 and a.StatusCode=b.DictValue ");
            _sbSql.Append(@" left join Site_Dictionary c on c.DictTypeId=28 and c.ColumnId in (0,a.ColumnId) and a.SubjectCode=c.DictValue " +
                " left join UserInfos d on a.TeacherId=d.UserID");
            _sbSql.AppendFormat(" where a.ColumnId={0} and a.GradeID='{1}' and a.ClassID='{2}' and a.StatusCode='1'and SchoolYear in(select SchoolYear from JC_TermInfos where SchoolColumnId={0} and IsCurrentTerm=1) order by CONVERT(int, a.SubjectCode)", columnId, gradeId, classId);
            return DbHelperSQL.Query(_sbSql.ToString());
        }

        /// <summary>
        /// 根据年级获取教师，班主任所带班级
        /// </summary>
        /// <param name="gradeId">年级Id</param>
        /// <param name="teacherNo">班级id</param>
        /// <returns>包含ID,TeacherNo,ClassName,GradeId,PlaceId字段的列表</returns>
        public DataSet GetClassList(Guid gradeId, string teacherNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT t.* from ( ");
            strSql.Append(" SELECT ID,TeacherNo,ClassName,GradeId,PlaceId FROM dbo.JC_ClassInfos ");
            strSql.Append(" UNION SELECT ClassID 'ID',a.TeacherNo,b.ClassName,a.GradeID,b.PlaceId FROM dbo.JC_Subjects a");
            strSql.Append(" LEFT JOIN dbo.JC_ClassInfos b ON a.ClassID=b.ID WHERE  StatusCode=1) t");
            strSql.Append(" where GradeID=@GradeID and TeacherNo=@TeacherNo and exists(select 1 from ecb_classbrands c where c.ClassId=t.ID)");
            SqlParameter[] parameters = {
                new SqlParameter("@GradeID", SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@TeacherNo", SqlDbType.NVarChar,18)
            };
            parameters[0].Value = gradeId;
            parameters[1].Value = teacherNo;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 查询班级任课教师
        /// </summary>
        /// <param name="classId"></param>
        public DataSet GetTeacherID(Guid ClassId)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = ClassId;
            StringBuilder _sql = new StringBuilder();
            _sql.AppendLine("SELECT DISTINCT b.UserId,a.* FROM JC_Subjects a LEFT JOIN aspnet_Membership b ON a.TeacherNo = b.TeacherNo");
            _sql.AppendLine(" where a.ClassID=@ClassId AND StatusCode = 1 ");
            _sql.AppendLine("ORDER BY a.TeacherNo");
            DataSet _dsGrades = DbHelperSQL.Query(_sql.ToString(), parameters);
            return _dsGrades;
        }

        /// <summary>
        /// 查询班级任课教师
        /// </summary>
        /// <param name="classId"></param>
        public List<string> GetTeacherIdsbyClassId(Guid ClassId)
        {
            List<string> _idList = new List<string>();
            DataSet _ds = GetTeacherID(ClassId);
            if (_ds != null && _ds.Tables.Count > 0 && _ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow item in _ds.Tables[0].Rows)
                {
                    if (item["UserId"].ToString() != "")
                    {
                        _idList.Add("" + item["UserId"].ToString().ToLower() + "");
                    }
                }
            }
            if (_idList.Count > 0)
            {
                HashSet<string> hs = new HashSet<string>(_idList);
                _idList.Clear();
                _idList.AddRange(hs);
                return _idList;
            }
            else
            {
                return new List<string>();
            }
        }
    }
}

