﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:JC_TimetableConfig
    /// </summary>
    public partial class JC_TimetableConfig
    {
        public JC_TimetableConfig()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from JC_TimetableConfig");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.JC_TimetableConfig model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into JC_TimetableConfig(");
            strSql.Append("ID,SchoolColumnID,SchoolColumnPath,TermID,Weekday,Number,StartTime,EndTime,LessonType,PlaceId,ClassId)");
            strSql.Append(" values (");
            strSql.Append("@ID,@SchoolColumnID,@SchoolColumnPath,@TermID,@Weekday,@Number,@StartTime,@EndTime,@LessonType,@PlaceId,@ClassId)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Weekday", SqlDbType.NVarChar,10),
                    new SqlParameter("@Number", SqlDbType.Int,4),
                    new SqlParameter("@StartTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@LessonType", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.SchoolColumnID;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.TermID;
            parameters[4].Value = model.Weekday;
            parameters[5].Value = model.Number;
            parameters[6].Value = model.StartTime;
            parameters[7].Value = model.EndTime;
            parameters[8].Value = model.LessonType;
            parameters[9].Value = model.PlaceId;
            parameters[10].Value = model.ClassId;


            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.JC_TimetableConfig model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update JC_TimetableConfig set ");
            strSql.Append("SchoolColumnID=@SchoolColumnID,");
            strSql.Append("SchoolColumnPath=@SchoolColumnPath,");
            strSql.Append("TermID=@TermID,");
            strSql.Append("Weekday=@Weekday,");
            strSql.Append("Number=@Number,");
            strSql.Append("StartTime=@StartTime,");
            strSql.Append("EndTime=@EndTime,");
            strSql.Append("LessonType=@LessonType,");
            strSql.Append("PlaceId=@PlaceId,");
            strSql.Append("ClassId=@ClassId");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Weekday", SqlDbType.NVarChar,10),
                    new SqlParameter("@Number", SqlDbType.Int,4),
                    new SqlParameter("@StartTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@LessonType", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.SchoolColumnID;
            parameters[1].Value = model.SchoolColumnPath;
            parameters[2].Value = model.TermID;
            parameters[3].Value = model.Weekday;
            parameters[4].Value = model.Number;
            parameters[5].Value = model.StartTime;
            parameters[6].Value = model.EndTime;
            parameters[7].Value = model.LessonType;
            parameters[8].Value = model.PlaceId;
            parameters[9].Value = model.ClassId;
            parameters[10].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_TimetableConfig ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_TimetableConfig ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_TimetableConfig GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,SchoolColumnID,SchoolColumnPath,TermID,Weekday,Number,StartTime,EndTime,LessonType,PlaceId,ClassId from JC_TimetableConfig ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.JC_TimetableConfig model = new ECB.PC.Model.JC_TimetableConfig();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_TimetableConfig DataRowToModel(DataRow row)
        {
            ECB.PC.Model.JC_TimetableConfig model = new ECB.PC.Model.JC_TimetableConfig();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["SchoolColumnID"] != null && row["SchoolColumnID"].ToString() != "")
                {
                    model.SchoolColumnID = int.Parse(row["SchoolColumnID"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["TermID"] != null && row["TermID"].ToString() != "")
                {
                    model.TermID = new Guid(row["TermID"].ToString());
                }
                if (row["Weekday"] != null)
                {
                    model.Weekday = row["Weekday"].ToString();
                }
                if (row["Number"] != null && row["Number"].ToString() != "")
                {
                    model.Number = int.Parse(row["Number"].ToString());
                }
                if (row["StartTime"] != null && row["StartTime"].ToString() != "")
                {
                    model.StartTime = DateTime.Parse(row["StartTime"].ToString());
                }
                if (row["EndTime"] != null && row["EndTime"].ToString() != "")
                {
                    model.EndTime = DateTime.Parse(row["EndTime"].ToString());
                }
                if (row["LessonType"] != null && row["LessonType"].ToString() != "")
                {
                    model.LessonType = int.Parse(row["LessonType"].ToString());
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,SchoolColumnID,SchoolColumnPath,TermID,Weekday,Number,StartTime,EndTime,LessonType,PlaceId,ClassId ");
            strSql.Append(" FROM JC_TimetableConfig ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,SchoolColumnID,SchoolColumnPath,TermID,Weekday,Number,StartTime,EndTime,LessonType,PlaceId,ClassId  ");
            strSql.Append(" FROM JC_TimetableConfig ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere, string[] ConnStr=null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_TimetableConfig ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), ConnStr);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_TimetableConfig T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "JC_TimetableConfig";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得课节总数
        /// </summary>
        public DataSet GetLessonType(Guid TermId, int ColumnId, Guid classId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT  LessonType,COUNT(1) Count FROM dbo.JC_TimetableConfig ");
            strSql.Append(" WHERE TermID='" + TermId + "' AND SchoolColumnID='" + ColumnId + "' and (ClassId='" + classId + "' OR PlaceId='" + classId + "') and  Weekday=(select top 1 Weekday from JC_TimetableConfig where TermID='" + TermId + "'  and (ClassId='" + classId + "' OR PlaceId='" + classId + "')  GROUP BY Weekday HAVING COUNT(*)>0 ORDER BY MAX(Number) desc,COUNT(*) DESC )  GROUP BY LessonType ORDER BY LessonType");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 删除一个学期的所有数据
        /// </summary>
        public bool DeleteByTermId(Guid TermID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_TimetableConfig ");
            strSql.Append(" where TermID=@TermID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = TermID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除一个班级的所有数据
        /// </summary>
        public bool DeleteByClass(Guid ClassId, Guid TermID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_TimetableConfig ");
            strSql.Append(" where  TermID=@TermID AND ClassId=@ClassId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = ClassId;
            parameters[1].Value = TermID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 赋值一个学期的数据到另一个学期
        /// </summary>
        public int CopyConfig(Guid TermID, Guid ToTermId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" insert JC_TimetableConfig ");

            strSql.Append(" select NEWID(), SchoolColumnID, SchoolColumnPath, '" + ToTermId + "', Weekday, Number, StartTime, EndTime,LessonType,PlaceId,ClassId  from JC_TimetableConfig");
            strSql.Append(" where TermID = '" + TermID + "' ");

            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }


        /// <summary>
        /// 获得请假时间数据列表
        /// </summary>
        public DataSet GetLeaveTimeList(Guid TermID, Guid ClassId, int Weekday, DateTime newDate,int IsSingle,Guid StudentId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from (select a.Weekday,a.Number,cast(CONVERT(nvarchar(100), CONVERT(datetime, '" + newDate + "'), 23) + ' ' + CONVERT(nvarchar(100), StartTime, 24) as datetime) as StartTime,cast(CONVERT(nvarchar(100), CONVERT(datetime, '" + newDate + "'), 23) + ' ' + CONVERT(nvarchar(100), EndTime, 24) as datetime) as EndTime, CASE  WHEN "+ IsSingle + " = 1 THEN SubjectNo ELSE SubjectNo1 end SubjectCode ");
            strSql.Append(" FROM JC_TimetableConfig a left join ecb_TimeTable_stu b on a.TermID=b.TermID and a.ClassId=b.ClassId and a.Weekday=b.Weekday and a.Number=b.Number ");
            strSql.Append(" where a.TermID='" + TermID + "' AND a.ClassId='" + ClassId + "' AND a.Weekday='" + Weekday + "' AND  b.StudentId='"+ StudentId + "' ) tt ORDER BY Number ");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod

    }
}

