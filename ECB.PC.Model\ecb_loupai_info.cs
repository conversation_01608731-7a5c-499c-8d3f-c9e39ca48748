﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_loupai_info:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_loupai_info
    {
        public ecb_loupai_info()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private string _name;
        private string _address;
        private Guid _placeid;
        public string ImgUrl { get; set; }
		public string RoomDesc { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Name
        {
            set { _name = value; }
            get { return _name; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Address
        {
            set { _address = value; }
            get { return _address; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        #endregion Model

    }
}

