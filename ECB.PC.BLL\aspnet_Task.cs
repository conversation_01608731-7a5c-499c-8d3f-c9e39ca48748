﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:aspnet_Task
    /// </summary>
    public partial class aspnet_Task
    {
        public aspnet_Task()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from aspnet_Task");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.aspnet_Task model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into aspnet_Task(");
            strSql.Append("Id,ColumnId,ColumnPath,BatchId,TaskType,BusinessType,TaskContent,ExcuteTime,StartTime,FinishedTime,Status,ErrorMessage,TaskResult,CreateTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@BatchId,@TaskType,@BusinessType,@TaskContent,@ExcuteTime,@StartTime,@FinishedTime,@Status,@ErrorMessage,@TaskResult,@CreateTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@BatchId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TaskType", SqlDbType.Int,4),
                    new SqlParameter("@BusinessType", SqlDbType.Int,4),
                    new SqlParameter("@TaskContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ExcuteTime", SqlDbType.DateTime),
                    new SqlParameter("@StartTime", SqlDbType.DateTime),
                    new SqlParameter("@FinishedTime", SqlDbType.DateTime),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@ErrorMessage", SqlDbType.NVarChar,-1),
                    new SqlParameter("@TaskResult", SqlDbType.NVarChar,-1),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.BatchId;
            parameters[4].Value = model.TaskType;
            parameters[5].Value = model.BusinessType;
            parameters[6].Value = model.TaskContent;
            parameters[7].Value = model.ExcuteTime;
            parameters[8].Value = model.StartTime;
            parameters[9].Value = model.FinishedTime;
            parameters[10].Value = model.Status;
            parameters[11].Value = model.ErrorMessage;
            parameters[12].Value = model.TaskResult;
            parameters[13].Value = model.CreateTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.aspnet_Task model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update aspnet_Task set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("BatchId=@BatchId,");
            strSql.Append("TaskType=@TaskType,");
            strSql.Append("BusinessType=@BusinessType,");
            strSql.Append("TaskContent=@TaskContent,");
            strSql.Append("ExcuteTime=@ExcuteTime,");
            strSql.Append("StartTime=@StartTime,");
            strSql.Append("FinishedTime=@FinishedTime,");
            strSql.Append("Status=@Status,");
            strSql.Append("ErrorMessage=@ErrorMessage,");
            strSql.Append("TaskResult=@TaskResult,");
            strSql.Append("CreateTime=@CreateTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@BatchId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TaskType", SqlDbType.Int,4),
                    new SqlParameter("@BusinessType", SqlDbType.Int,4),
                    new SqlParameter("@TaskContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ExcuteTime", SqlDbType.DateTime),
                    new SqlParameter("@StartTime", SqlDbType.DateTime),
                    new SqlParameter("@FinishedTime", SqlDbType.DateTime),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@ErrorMessage", SqlDbType.NVarChar,-1),
                    new SqlParameter("@TaskResult", SqlDbType.NVarChar,-1),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.BatchId;
            parameters[3].Value = model.TaskType;
            parameters[4].Value = model.BusinessType;
            parameters[5].Value = model.TaskContent;
            parameters[6].Value = model.ExcuteTime;
            parameters[7].Value = model.StartTime;
            parameters[8].Value = model.FinishedTime;
            parameters[9].Value = model.Status;
            parameters[10].Value = model.ErrorMessage;
            parameters[11].Value = model.TaskResult;
            parameters[12].Value = model.CreateTime;
            parameters[13].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from aspnet_Task ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from aspnet_Task ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.aspnet_Task GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 *");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.aspnet_Task model = new ECB.PC.Model.aspnet_Task();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.aspnet_Task DataRowToModel(DataRow row)
        {
            ECB.PC.Model.aspnet_Task model = new ECB.PC.Model.aspnet_Task();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["BatchId"] != null && row["BatchId"].ToString() != "")
                {
                    model.BatchId = new Guid(row["BatchId"].ToString());
                }
                if (row["TaskType"] != null && row["TaskType"].ToString() != "")
                {
                    model.TaskType = int.Parse(row["TaskType"].ToString());
                }
                if (row["BusinessType"] != null && row["BusinessType"].ToString() != "")
                {
                    model.BusinessType = int.Parse(row["BusinessType"].ToString());
                }
                if (row["TaskContent"] != null)
                {
                    model.TaskContent = row["TaskContent"].ToString();
                }
                if (row["ExcuteTime"] != null && row["ExcuteTime"].ToString() != "")
                {
                    model.ExcuteTime = DateTime.Parse(row["ExcuteTime"].ToString());
                }
                if (row["StartTime"] != null && row["StartTime"].ToString() != "")
                {
                    model.StartTime = DateTime.Parse(row["StartTime"].ToString());
                }
                if (row["FinishedTime"] != null && row["FinishedTime"].ToString() != "")
                {
                    model.FinishedTime = DateTime.Parse(row["FinishedTime"].ToString());
                }
                if (row["Status"] != null && row["Status"].ToString() != "")
                {
                    model.Status = int.Parse(row["Status"].ToString());
                }
                if (row["ErrorMessage"] != null)
                {
                    model.ErrorMessage = row["ErrorMessage"].ToString();
                }
                if (row["TaskResult"] != null)
                {
                    model.TaskResult = row["TaskResult"].ToString();
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM aspnet_Task ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" * ");
            strSql.Append(" FROM aspnet_Task ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM aspnet_Task ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from aspnet_Task T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "aspnet_Task";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 得到一个对象实体，通过批次和时间，请假用
        /// </summary>
        public ECB.PC.Model.aspnet_Task GetLeaveModel(Guid BatchId, DateTime ExcuteTime)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 * from aspnet_Task ");
            strSql.Append(" where BatchId=@BatchId and ExcuteTime=@ExcuteTime ");
            SqlParameter[] parameters = {
                    new SqlParameter("@BatchId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ExcuteTime", SqlDbType.DateTime)};
            parameters[0].Value = BatchId;
            parameters[1].Value = ExcuteTime;
            ECB.PC.Model.aspnet_Task model = new ECB.PC.Model.aspnet_Task();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteByBatchId(Guid BatchId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from aspnet_Task ");
            strSql.Append(" where BatchId=@BatchId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@BatchId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = BatchId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

