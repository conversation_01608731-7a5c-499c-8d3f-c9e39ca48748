﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 教学计划
	/// </summary>
	[Serializable]
	public partial class ecb_Teaching
	{
		public ecb_Teaching()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _subjectcode;
		private Guid _teacherid;
		private Guid _placeid;
		private int? _weekclass;
		private int? _weekevenclassnum;
		private int? _alwayevenclassnum;
		private int? _wxweekclass;
		private string _message;
		private Guid _gradeid;
		private Guid _classid;
        private string _weektype;
        /// <summary>
        /// 职教教学Id
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 课程Id
		/// </summary>
		public string SubjectCode
		{
			set{ _subjectcode=value;}
			get{return _subjectcode;}
		}
		/// <summary>
		/// 教师Id
		/// </summary>
		public Guid TeacherId
		{
			set{ _teacherid=value;}
			get{return _teacherid;}
		}
		/// <summary>
		/// 场地Id
		/// </summary>
		public Guid PlaceId
		{
			set{ _placeid=value;}
			get{return _placeid;}
		}
		/// <summary>
		/// 周课时
		/// </summary>
		public int? WeekClass
		{
			set{ _weekclass=value;}
			get{return _weekclass;}
		}
		/// <summary>
		/// 每周连课次数
		/// </summary>
		public int? WeekEvenClassNum
		{
			set{ _weekevenclassnum=value;}
			get{return _weekevenclassnum;}
		}
		/// <summary>
		/// 每次连课节数
		/// </summary>
		public int? AlwayEvenClassNum
		{
			set{ _alwayevenclassnum=value;}
			get{return _alwayevenclassnum;}
		}
		/// <summary>
		/// 晚修周课时
		/// </summary>
		public int? WXWeekClass
		{
			set{ _wxweekclass=value;}
			get{return _wxweekclass;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Message
		{
			set{ _message=value;}
			get{return _message;}
		}
		/// <summary>
		/// 年级
		/// </summary>
		public Guid GradeId
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 班级
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
        /// <summary>
        /// 单双周课
        /// </summary>
        public string WeekType
        {
            set { _weektype = value; }
            get { return _weektype; }
        }

        #endregion Model

    }
}

