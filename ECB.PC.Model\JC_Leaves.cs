﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// JC_Leaves:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class JC_Leaves
	{
		public JC_Leaves()
		{}
		#region Model
		private Guid _id;
		private int? _schoolcolumnid;
		private string _schoolcolumnpath;
		private Guid _gradeid;
		private Guid _classid;
		private Guid _termid;
		private Guid _userid;
		private string _usertypecode;
		private string _leavetypecode;
		private string _leavereason;
		private DateTime? _begintime;
		private DateTime? _endtime;
		private bool _ispass;
		private Guid _ispassby;
		private DateTime? _ispassdate;
		private bool _issmsnote;
		private bool _isinnernote;
		private string _schoolyear;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校ID
		/// </summary>
		public int? SchoolColumnID
		{
			set{ _schoolcolumnid=value;}
			get{return _schoolcolumnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string SchoolColumnPath
		{
			set{ _schoolcolumnpath=value;}
			get{return _schoolcolumnpath;}
		}
		/// <summary>
		/// 年级编号
		/// </summary>
		public Guid GradeID
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 班级编号
		/// </summary>
		public Guid ClassID
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学期编号
		/// </summary>
		public Guid TermID
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 用户ID。学生或老师
		/// </summary>
		public Guid UserID
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 用户类型。字典：学生、教师等
		/// </summary>
		public string UserTypeCode
		{
			set{ _usertypecode=value;}
			get{return _usertypecode;}
		}
		/// <summary>
		/// 请假类型。字典：事假、病假等
		/// </summary>
		public string LeaveTypeCode
		{
			set{ _leavetypecode=value;}
			get{return _leavetypecode;}
		}
		/// <summary>
		/// 请假理由
		/// </summary>
		public string LeaveReason
		{
			set{ _leavereason=value;}
			get{return _leavereason;}
		}
		/// <summary>
		/// 请假时间（开始）
		/// </summary>
		public DateTime? BeginTime
		{
			set{ _begintime=value;}
			get{return _begintime;}
		}
		/// <summary>
		/// 请假时间（结束）
		/// </summary>
		public DateTime? EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 审批
		/// </summary>
		public bool IsPass
		{
			set{ _ispass=value;}
			get{return _ispass;}
		}
		/// <summary>
		/// 审批人
		/// </summary>
		public Guid IsPassBy
		{
			set{ _ispassby=value;}
			get{return _ispassby;}
		}
		/// <summary>
		/// 审批时间
		/// </summary>
		public DateTime? IsPassDate
		{
			set{ _ispassdate=value;}
			get{return _ispassdate;}
		}
		/// <summary>
		/// 提醒家长：手机短信通知
		/// </summary>
		public bool IsSmsNote
		{
			set{ _issmsnote=value;}
			get{return _issmsnote;}
		}
		/// <summary>
		/// 提醒家长：内部短信通知
		/// </summary>
		public bool IsInnerNote
		{
			set{ _isinnernote=value;}
			get{return _isinnernote;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SchoolYear
		{
			set{ _schoolyear=value;}
			get{return _schoolyear;}
		}
		#endregion Model

	}
}

