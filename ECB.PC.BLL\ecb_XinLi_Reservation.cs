﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_XinLi_Reservation
	/// </summary>
	public partial class ecb_XinLi_Reservation
	{
		public ecb_XinLi_Reservation()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_XinLi_Reservation");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_XinLi_Reservation model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_XinLi_Reservation(");
			strSql.Append("Id,ColumnId,ColumnPath,CreateUserId,CreateTime,StartTime,EndTime,TeacherId,Number,ReservationNo,Status,CheckUserId,CheckTime)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@CreateUserId,@CreateTime,@StartTime,@EndTime,@TeacherId,@Number,@ReservationNo,@Status,@CheckUserId,@CheckTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@StartTime", SqlDbType.DateTime),
					new SqlParameter("@EndTime", SqlDbType.DateTime),
					new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Number", SqlDbType.Int,4),
					new SqlParameter("@ReservationNo", SqlDbType.NVarChar,10),
					new SqlParameter("@Status", SqlDbType.Int,4),
					new SqlParameter("@CheckUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CheckTime", SqlDbType.DateTime)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = Guid.NewGuid();
			parameters[4].Value = model.CreateTime;
			parameters[5].Value = model.StartTime;
			parameters[6].Value = model.EndTime;
			parameters[7].Value = Guid.NewGuid();
			parameters[8].Value = model.Number;
			parameters[9].Value = model.ReservationNo;
			parameters[10].Value = model.Status;
			parameters[11].Value = Guid.NewGuid();
			parameters[12].Value = model.CheckTime;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_XinLi_Reservation model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_XinLi_Reservation set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("CreateUserId=@CreateUserId,");
			strSql.Append("CreateTime=@CreateTime,");
			strSql.Append("StartTime=@StartTime,");
			strSql.Append("EndTime=@EndTime,");
			strSql.Append("TeacherId=@TeacherId,");
			strSql.Append("Number=@Number,");
			strSql.Append("ReservationNo=@ReservationNo,");
			strSql.Append("Status=@Status,");
			strSql.Append("CheckUserId=@CheckUserId,");
			strSql.Append("CheckTime=@CheckTime");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@StartTime", SqlDbType.DateTime),
					new SqlParameter("@EndTime", SqlDbType.DateTime),
					new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Number", SqlDbType.Int,4),
					new SqlParameter("@ReservationNo", SqlDbType.NVarChar,10),
					new SqlParameter("@Status", SqlDbType.Int,4),
					new SqlParameter("@CheckUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CheckTime", SqlDbType.DateTime),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.CreateUserId;
			parameters[3].Value = model.CreateTime;
			parameters[4].Value = model.StartTime;
			parameters[5].Value = model.EndTime;
			parameters[6].Value = model.TeacherId;
			parameters[7].Value = model.Number;
			parameters[8].Value = model.ReservationNo;
			parameters[9].Value = model.Status;
			parameters[10].Value = model.CheckUserId;
			parameters[11].Value = model.CheckTime;
			parameters[12].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_XinLi_Reservation ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_XinLi_Reservation ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_XinLi_Reservation GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,CreateUserId,CreateTime,StartTime,EndTime,TeacherId,Number,ReservationNo,Status,CheckUserId,CheckTime from ecb_XinLi_Reservation ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_XinLi_Reservation model=new ECB.PC.Model.ecb_XinLi_Reservation();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_XinLi_Reservation DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_XinLi_Reservation model=new ECB.PC.Model.ecb_XinLi_Reservation();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["CreateUserId"]!=null && row["CreateUserId"].ToString()!="")
				{
					model.CreateUserId= new Guid(row["CreateUserId"].ToString());
				}
				if(row["CreateTime"]!=null && row["CreateTime"].ToString()!="")
				{
					model.CreateTime=DateTime.Parse(row["CreateTime"].ToString());
				}
				if(row["StartTime"]!=null && row["StartTime"].ToString()!="")
				{
					model.StartTime=DateTime.Parse(row["StartTime"].ToString());
				}
				if(row["EndTime"]!=null && row["EndTime"].ToString()!="")
				{
					model.EndTime=DateTime.Parse(row["EndTime"].ToString());
				}
				if(row["TeacherId"]!=null && row["TeacherId"].ToString()!="")
				{
					model.TeacherId= new Guid(row["TeacherId"].ToString());
				}
				if(row["Number"]!=null && row["Number"].ToString()!="")
				{
					model.Number=int.Parse(row["Number"].ToString());
				}
				if(row["ReservationNo"]!=null)
				{
					model.ReservationNo=row["ReservationNo"].ToString();
				}
				if(row["Status"]!=null && row["Status"].ToString()!="")
				{
					model.Status=int.Parse(row["Status"].ToString());
				}
				if(row["CheckUserId"]!=null && row["CheckUserId"].ToString()!="")
				{
					model.CheckUserId= new Guid(row["CheckUserId"].ToString());
				}
				if(row["CheckTime"]!=null && row["CheckTime"].ToString()!="")
				{
					model.CheckTime=DateTime.Parse(row["CheckTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere, string[] ConnStr)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,CreateUserId,CreateTime,StartTime,EndTime,TeacherId,Number,ReservationNo,Status,CheckUserId,CheckTime ");
			strSql.Append(" FROM ecb_XinLi_Reservation ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString(), ConnStr);
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,CreateUserId,CreateTime,StartTime,EndTime,TeacherId,Number,ReservationNo,Status,CheckUserId,CheckTime ");
			strSql.Append(" FROM ecb_XinLi_Reservation ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_XinLi_Reservation ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_XinLi_Reservation T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_XinLi_Reservation";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

