﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 功能室预约记录
	/// </summary>
	[Serializable]
	public partial class ecb_FunctionRoom_Reservation
	{
		public ecb_FunctionRoom_Reservation()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _functionroomid;
		private Guid _flowid;
		private string _flowcode;
		private int _reservationtype;
		private string _classids;
		private DateTime? _reservationdate;
		private int? _classnum;
		private DateTime? _begintime;
		private DateTime? _endtime;
		private string _subjectname;
		private string _equipment;
		private string _reason;
		private string _description;
		private Guid _createuserid;
		private DateTime? _createtime;
		private int _status;
		private Guid _lastedituserid;
		private DateTime? _lastedittime;
		private Guid _teacherid;
		private string _subjectcode;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校Id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校Id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 功能室id
		/// </summary>
		public Guid FunctionRoomId
		{
			set{ _functionroomid=value;}
			get{return _functionroomid;}
		}
		/// <summary>
		/// 流程id
		/// </summary>
		public Guid FlowId
		{
			set{ _flowid=value;}
			get{return _flowid;}
		}
		/// <summary>
		/// 当前流程节点
		/// </summary>
		public string FlowCode
		{
			set{ _flowcode=value;}
			get{return _flowcode;}
		}
		/// <summary>
		/// 枚举 1班级预约 2个人预约
		/// </summary>
		public int ReservationType
		{
			set{ _reservationtype=value;}
			get{return _reservationtype;}
		}
		/// <summary>
		/// 选择的班级id集合，竖线分割
		/// </summary>
		public string ClassIds
		{
			set{ _classids=value;}
			get{return _classids;}
		}
		/// <summary>
		/// 预约日期
		/// </summary>
		public DateTime? ReservationDate
		{
			set{ _reservationdate=value;}
			get{return _reservationdate;}
		}
		/// <summary>
		/// 预约节次
		/// </summary>
		public int? ClassNum
		{
			set{ _classnum=value;}
			get{return _classnum;}
		}
		/// <summary>
		/// 预约开始时间
		/// </summary>
		public DateTime? BeginTime
		{
			set{ _begintime=value;}
			get{return _begintime;}
		}
		/// <summary>
		/// 预约结束时间
		/// </summary>
		public DateTime? EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 预约主题
		/// </summary>
		public string SubjectName
		{
			set{ _subjectname=value;}
			get{return _subjectname;}
		}
		/// <summary>
		/// 所需设备
		/// </summary>
		public string Equipment
		{
			set{ _equipment=value;}
			get{return _equipment;}
		}
		/// <summary>
		/// 申请原因
		/// </summary>
		public string Reason
		{
			set{ _reason=value;}
			get{return _reason;}
		}
		/// <summary>
		/// 其他说明
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 预约人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 预约创建时间
		/// </summary>
		public DateTime? CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 状态枚举 0待审核 1通过 2驳回 9取消
		/// </summary>
		public int Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 最后操作的人
		/// </summary>
		public Guid LastEditUserId
		{
			set{ _lastedituserid=value;}
			get{return _lastedituserid;}
		}
		/// <summary>
		/// 最后更新时间
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		/// <summary>
		/// 任课教师Id
		/// </summary>
		public Guid TeacherId
		{
			set{ _teacherid=value;}
			get{return _teacherid;}
		}
		/// <summary>
		/// 任课科目
		/// </summary>
		public string SubjectCode
		{
			set{ _subjectcode=value;}
			get{return _subjectcode;}
		}
		#endregion Model

	}
}

