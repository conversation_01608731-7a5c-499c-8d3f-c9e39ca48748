﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:face_logs
    /// </summary>
	public partial class face_logs
    {
        public face_logs()
        { }

        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from face_logs");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.face_logs model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into face_logs(");
            strSql.Append("Id,ColumnId,ColumnPath,VersionId,OperationType,SeriaNo,UserId,Status,CreateDate,Creator,Comment)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@VersionId,@OperationType,@SeriaNo,@UserId,@Status,@CreateDate,@Creator,@Comment)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@VersionId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@OperationType", SqlDbType.Int,4),
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,255),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Comment", SqlDbType.NVarChar,-1)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.VersionId;
            parameters[4].Value = model.OperationType;
            parameters[5].Value = model.SeriaNo;
            parameters[6].Value = model.UserId;
            parameters[7].Value = model.Status;
            parameters[8].Value = model.CreateDate;
            parameters[9].Value = model.Creator;
            parameters[10].Value = model.Comment;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.face_logs model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update face_logs set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("VersionId=@VersionId,");
            strSql.Append("OperationType=@OperationType,");
            strSql.Append("SeriaNo=@SeriaNo,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("Status=@Status,");
            strSql.Append("CreateDate=@CreateDate,");
            strSql.Append("Creator=@Creator,");
            strSql.Append("Comment=@Comment");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@VersionId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@OperationType", SqlDbType.Int,4),
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,255),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Comment", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.VersionId;
            parameters[3].Value = model.OperationType;
            parameters[4].Value = model.SeriaNo;
            parameters[5].Value = model.UserId;
            parameters[6].Value = model.Status;
            parameters[7].Value = model.CreateDate;
            parameters[8].Value = model.Creator;
            parameters[9].Value = model.Comment;
            parameters[10].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from face_logs ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from face_logs ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.face_logs GetModel(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,VersionId,OperationType,SeriaNo,UserId,Status,CreateDate,Creator,Comment from face_logs ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.face_logs DataRowToModel(DataRow row)
        {
            ECB.PC.Model.face_logs model = new ECB.PC.Model.face_logs();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["VersionId"] != null && row["VersionId"].ToString() != "")
                {
                    model.VersionId = new Guid(row["VersionId"].ToString());
                }
                if (row["OperationType"] != null && row["OperationType"].ToString() != "")
                {
                    model.OperationType = int.Parse(row["OperationType"].ToString());
                }
                if (row["SeriaNo"] != null)
                {
                    model.SeriaNo = row["SeriaNo"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["Status"] != null && row["Status"].ToString() != "")
                {
                    model.Status = int.Parse(row["Status"].ToString());
                }
                if (row["CreateDate"] != null && row["CreateDate"].ToString() != "")
                {
                    model.CreateDate = DateTime.Parse(row["CreateDate"].ToString());
                }
                if (row["Creator"] != null && row["Creator"].ToString() != "")
                {
                    model.Creator = new Guid(row["Creator"].ToString());
                }
                if (row["Comment"] != null)
                {
                    model.Comment = row["Comment"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,VersionId,OperationType,SeriaNo,UserId,Status,CreateDate,Creator,Comment ");
            strSql.Append(" FROM face_logs ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,VersionId,OperationType,SeriaNo,UserId,Status,CreateDate,Creator,Comment ");
            strSql.Append(" FROM face_logs ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM face_logs ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from face_logs T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "face_logs";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod

        /// <summary>
        /// 是否存在下发记录
        /// </summary>
        /// <param name="versionId">版本号id</param>
        /// <returns>true存在，false不存在</returns>
        public bool ExistsFaceLogs(Guid versionId)
        {
            string strSql = "select count(1) FROM face_logs where VersionId=@VersionId";
            SqlParameter[] parameters = {
                    new SqlParameter("@VersionId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = versionId;
            object obj = DbHelperSQL.GetSingle(strSql, parameters);
            if (obj == null)
            {
                return false;
            }
            else
            {
                return Convert.ToInt32(obj) > 0;
            }
        }

        /// <summary>
        /// 分页获取设备下发日志统计
        /// </summary>      
        /// <param name="versionId">人脸库版本号id</param>
        /// <param name="seriaNo">设备编号</param>
        /// <returns>记录数量</returns>
        public int GetPageCount(Guid versionId, string seriaNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ( ");
            strSql.Append("select a.SeriaNo from face_logs a inner join ecb_classbrands b on a.SeriaNo=b.SeriaNo and a.ColumnId=b.ColumnId ");
            strSql.Append("where a.VersionId=@VersionId ");
            // 判断是否有筛选设备
            if (!string.IsNullOrEmpty(seriaNo))
            {
                strSql.Append($" and a.SeriaNo='{seriaNo}'");
            }
            strSql.Append(" group by a.VersionId,a.SeriaNo,b.Name) T  ");
            SqlParameter[] parameters = {
                new SqlParameter("@VersionId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = versionId;
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取设备下发日志统计
        /// </summary>     
        /// <param name="versionId">人脸库版本号id</param>
        /// <param name="pageSize">分页大小</param>
        /// <param name="pageIndex">分页页码</param>
        /// <returns>下发日志统计</returns>
        public DataSet GetPageList(Guid versionId, int pageSize, int pageIndex, string seriaNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select T.VersionId,T.SeriaNo,t.Name,T.SuccessNum,T.FailNum from ( ");
            strSql.Append("select a.VersionId,a.SeriaNo,b.Name,sum(case when a.[Status]=1 then 1 else 0 end) SuccessNum,sum(case when a.[Status]=0 then 1 else 0 end) FailNum,ROW_NUMBER() OVER (ORDER BY a.SeriaNo) AS RowNum ");
            strSql.Append("from face_logs a inner join ecb_classbrands b on a.SeriaNo=b.SeriaNo and a.ColumnId=b.ColumnId ");
            strSql.Append("where a.VersionId=@VersionId ");
            // 判断是否有筛选设备
            if (!string.IsNullOrEmpty(seriaNo))
            {
                strSql.Append($" and a.SeriaNo='{seriaNo}'");
            }
            strSql.Append(" group by a.VersionId,a.SeriaNo,b.Name) T where T.RowNum between @BeginIndex and @EndIndex order by T.Name");
            SqlParameter[] parameters = {
                new SqlParameter("@VersionId", SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@BeginIndex",SqlDbType.Int,4),
                new SqlParameter("@EndIndex",SqlDbType.Int,4),
            };
            if (pageSize == 0)
            {
                pageSize = 10;
            }
            if (pageIndex == 0)
            {
                pageIndex = 1;
            }
            parameters[0].Value = versionId;
            parameters[1].Value = (pageIndex - 1) * pageSize + 1;
            parameters[2].Value = pageIndex * pageSize;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 获取设备列表
        /// </summary>
        /// <param name="versionId"></param>
        /// <returns></returns>
        public DataSet GetDeviceList(int columnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select distinct a.SeriaNo ");
            strSql.Append(" FROM ecb_classbrands a where a.ColumnId=@ColumnId");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)};
            parameters[0].Value = columnId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 删除下发日志
        /// </summary>
        /// <param name="versionId">人脸版本号</param>
        /// <param name="seriaNo">设备号</param>
        /// <returns></returns>
        public bool DeleteFaceLog(Guid versionId, string seriaNo)
        {
            string strSql = "delete from face_logs where VersionId=@VersionId and SeriaNo=@SeriaNo";
            SqlParameter[] parameters = {
                    new SqlParameter("@VersionId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SeriaNo",SqlDbType.NVarChar,255)
            };
            parameters[0].Value = versionId;
            parameters[1].Value = seriaNo;
            object obj = DbHelperSQL.GetSingle(strSql, parameters);
            if (obj == null)
            {
                return false;
            }
            else
            {
                return Convert.ToInt32(obj) > 0;
            }
        }

        #endregion  ExtensionMethod
    }
}

