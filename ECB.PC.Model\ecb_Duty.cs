﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_Duty:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_Duty
	{
		public ecb_Duty()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _gradeid;
		private Guid _classid;
		private Guid _studentid;
		private DateTime _dutydate;
		private Guid _itemid;
		private string _mark;
		private int? _status;
		private Guid _termid;
		private Guid _lasteditor;
		private DateTime? _lastedittime;
        /// <summary>
        /// 主键Id
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 路径ID
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 年级
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 班级
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 学生
        /// </summary>
        public Guid StudentId
        {
            set { _studentid = value; }
            get { return _studentid; }
        }
        /// <summary>
        /// 值日时间
        /// </summary>
        public DateTime DutyDate
        {
            set { _dutydate = value; }
            get { return _dutydate; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid ItemId
        {
            set { _itemid = value; }
            get { return _itemid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Mark
        {
            set { _mark = value; }
            get { return _mark; }
        }
        /// <summary>
        /// 0:不安排 1正常 2管理员
        /// </summary>
        public int? Status
        {
            set { _status = value; }
            get { return _status; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid TermId
        {
            set { _termid = value; }
            get { return _termid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid LastEditor
        {
            set { _lasteditor = value; }
            get { return _lasteditor; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        #endregion Model

    }
}

