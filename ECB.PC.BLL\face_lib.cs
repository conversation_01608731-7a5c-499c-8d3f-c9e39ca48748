﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
using YunEdu.Common;
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:face_lib
    /// </summary>
    public partial class face_lib
    {
        public face_lib()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid UserId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from face_lib");
            strSql.Append(" where UserId=@UserId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = UserId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.face_lib model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into face_lib(");
            strSql.Append("UserId,ColumnId,ColumnPath,PicPath,CreateDate,LastEditDate,timestamp,status,OperationType,Operator)");
            strSql.Append(" values (");
            strSql.Append("@UserId,@ColumnId,@ColumnPath,@PicPath,@CreateDate,@LastEditDate,@timestamp,@status,@OperationType,@Operator)");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@PicPath", SqlDbType.NVarChar,-1),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@timestamp", SqlDbType.BigInt,8),
                    new SqlParameter("@status", SqlDbType.Int,4),
                    new SqlParameter("@OperationType",SqlDbType.Int,4),
                    new SqlParameter("@Operator",SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = model.UserId;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.PicPath;
            parameters[4].Value = model.CreateDate;
            parameters[5].Value = model.LastEditDate;
            parameters[6].Value = model.timestamp;
            parameters[7].Value = model.status;
            parameters[8].Value = model.OperationType;
            if (model.Operator == null)
            {
                parameters[9].Value = DBNull.Value;
            }
            else
            {
                parameters[9].Value = model.Operator.Value;
            }

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.face_lib model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update face_lib set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("PicPath=@PicPath,");
            strSql.Append("CreateDate=@CreateDate,");
            strSql.Append("LastEditDate=@LastEditDate,");
            strSql.Append("timestamp=@timestamp,");
            strSql.Append("status=@status,");
            strSql.Append("OperationType=@OperationType,");
            strSql.Append("Operator=@Operator");
            strSql.Append(" where UserId=@UserId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@PicPath", SqlDbType.NVarChar,-1),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@timestamp", SqlDbType.BigInt,8),
                    new SqlParameter("@status", SqlDbType.Int,4),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@OperationType",SqlDbType.Int,4),
                    new SqlParameter("@Operator",SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.PicPath;
            parameters[3].Value = model.CreateDate;
            parameters[4].Value = model.LastEditDate;
            parameters[5].Value = model.timestamp;
            parameters[6].Value = model.status;
            parameters[7].Value = model.UserId;
            parameters[8].Value = model.OperationType;
            if (model.Operator == null)
            {
                parameters[9].Value = DBNull.Value;
            }
            else
            {
                parameters[9].Value = model.Operator.Value;
            }

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid UserId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from face_lib ");
            strSql.Append(" where UserId=@UserId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = UserId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string UserIdlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from face_lib ");
            strSql.Append(" where UserId in (" + UserIdlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.face_lib GetModel(Guid UserId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 UserId,ColumnId,ColumnPath,PicPath,CreateDate,LastEditDate,timestamp,status,OperationType,Operator from face_lib ");
            strSql.Append(" where UserId=@UserId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = UserId;

            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.face_lib DataRowToModel(DataRow row)
        {
            ECB.PC.Model.face_lib model = new ECB.PC.Model.face_lib();
            if (row != null)
            {
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["PicPath"] != null)
                {
                    model.PicPath = row["PicPath"].ToString();
                }
                if (row["CreateDate"] != null && row["CreateDate"].ToString() != "")
                {
                    model.CreateDate = DateTime.Parse(row["CreateDate"].ToString());
                }
                if (row["LastEditDate"] != null && row["LastEditDate"].ToString() != "")
                {
                    model.LastEditDate = DateTime.Parse(row["LastEditDate"].ToString());
                }
                if (row["timestamp"] != null && row["timestamp"].ToString() != "")
                {
                    model.timestamp = long.Parse(row["timestamp"].ToString());
                }
                if (row["status"] != null && row["status"].ToString() != "")
                {
                    model.status = int.Parse(row["status"].ToString());
                }
                if (row["OperationType"] != null && row["OperationType"].ToString() != "")
                {
                    model.OperationType = int.Parse(row["OperationType"].ToString());
                }
                if (row["Operator"] != null && row["Operator"].ToString() != "")
                {
                    model.Operator = new Guid(row["Operator"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select UserId,ColumnId,ColumnPath,PicPath,CreateDate,LastEditDate,timestamp,status,OperationType,Operator ");
            strSql.Append(" FROM face_lib ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" UserId,ColumnId,ColumnPath,PicPath,CreateDate,LastEditDate,timestamp,status,OperationType,Operator ");
            strSql.Append(" FROM face_lib ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM face_lib ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.UserId desc");
            }
            strSql.Append(")AS Row, T.*  from face_lib T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "face_lib";
			parameters[1].Value = "UserId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 返回存储图片路径
        /// </summary>
        /// <param name="columnID"></param>
        /// <param name="status">教师还是学生</param>
        /// <param name="type">临时还是正式(off)</param>
        /// <returns></returns>
        public string GetImgPath(string columnID, string status, string type)
        {
            string imgPath = "";
            if (type == "off")//正式路径
            {
                if (!string.IsNullOrEmpty(status))
                {
                    imgPath = string.Format("/userfiles/{0}/{1}/{2}/", CodeTable.FileType.images, "BP_"+ columnID.ToString(), status);
                }

            }
            else//临时路径
            {
                imgPath = string.Format("/userfiles/{0}/{1}/temporary/", CodeTable.FileType.images, columnID);
            }
            return imgPath;
        }

        /// <summary>
        /// 删除指令
        /// </summary>
        public bool DeleteCmdByBusiness(string businessId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("delete from tbl_fkcmd_trans_cmd_param where exists(select trans_id from tbl_fkcmd_trans a where a.business_id=@business_id and a.trans_id=tbl_fkcmd_trans_cmd_param.trans_id)");
            strSql.AppendLine("delete from tbl_fkcmd_trans_cmd_result where exists(select trans_id from tbl_fkcmd_trans a where a.business_id=@business_id and a.trans_id=tbl_fkcmd_trans_cmd_result.trans_id)");
            strSql.AppendLine("delete from tbl_fkcmd_trans where business_id=@business_id");
            SqlParameter[] parameters = {
                    new SqlParameter("@business_id", SqlDbType.NVarChar,50)
            };
            parameters[0].Value = businessId;
            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 删除之前用户的指令
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="userAutoId"></param>
        /// <returns></returns>
        public bool DeleteCmdByBusiness(string businessId, string userAutoId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("delete from tbl_fkcmd_trans_cmd_param where exists(select trans_id from tbl_fkcmd_trans a where a.business_id=@business_id and a.user_id=@user_id and a.trans_id=tbl_fkcmd_trans_cmd_param.trans_id)");
            strSql.AppendLine("delete from tbl_fkcmd_trans_cmd_result where exists(select trans_id from tbl_fkcmd_trans a where a.business_id=@business_id and a.user_id=@user_id and a.trans_id=tbl_fkcmd_trans_cmd_result.trans_id)");
            strSql.AppendLine("delete from tbl_fkcmd_trans where business_id=@business_id and user_id=@user_id");
            SqlParameter[] parameters = {
                    new SqlParameter("@business_id", SqlDbType.NVarChar,50),
                    new SqlParameter("@user_id",SqlDbType.NVarChar,16)
            };
            parameters[0].Value = businessId;
            parameters[1].Value = userAutoId;
            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 添加指令
        /// </summary>
        /// <param name="Cmd">指令字符串</param>
        /// <param name="mDevId">设备id</param>
        /// <param name="mParam">指纹或者人脸二进制参数</param>
        /// <returns></returns>
        public string AddCmd(string Cmd, string mDevId, string userId, long timestamp, byte[] mParam, string businessId = "")
        {
            string sSql;

            string mTransID;
            mTransID = GetNewTransId();

            switch (Cmd)
            {
                case "GET_USER_ID_LIST": break;

            }

            sSql = "delete from tbl_fkcmd_trans_cmd_param where trans_id = '" + mTransID + "'";
            DbHelperSQL.ExecuteSql(sSql);
            sSql = "delete from tbl_fkcmd_trans_cmd_result where trans_id = '" + mTransID + "'";
            DbHelperSQL.ExecuteSql(sSql);

            if (mParam != null && mParam.Length > 0)
            {
                sSql = "insert into tbl_fkcmd_trans_cmd_param";
                sSql += "(trans_id, device_id, cmd_param)";
                sSql += "values(@trans_id, @device_id, @cmd_param)";

                SqlParameter[] cmdParamParameters = new SqlParameter[] {
                    new SqlParameter("@trans_id",SqlDbType.VarChar,16),
                    new SqlParameter("@device_id",SqlDbType.VarChar,24),
                    new SqlParameter("@cmd_param",SqlDbType.VarBinary)
                };

                cmdParamParameters[0].Value = mTransID;
                cmdParamParameters[1].Value = mDevId;
                cmdParamParameters[2].Direction = ParameterDirection.Input;
                cmdParamParameters[2].Size = mParam.Length;
                cmdParamParameters[2].Value = mParam;

                DbHelperSQL.ExecuteSql(sSql, cmdParamParameters);
            }
            sSql = "insert into tbl_fkcmd_trans (trans_id,device_id,cmd_code,status,update_time,user_id,time_stamp,business_id) values(@trans_id,@device_id,@cmd_code,'WAIT',@update_time,@user_id,@time_stamp,@business_id)";

            SqlParameter[] parameters = new SqlParameter[] {
                new SqlParameter("@trans_id",SqlDbType.VarChar,16),
                new SqlParameter("@device_id",SqlDbType.VarChar,24),
                new SqlParameter("@cmd_code",SqlDbType.VarChar,32),
                new SqlParameter("@update_time",SqlDbType.VarChar),
                new SqlParameter("@user_id",SqlDbType.NVarChar,16),
                new SqlParameter ("@time_stamp",SqlDbType.BigInt,8),
                new SqlParameter("@business_id",SqlDbType.NVarChar,50)
            };
            parameters[0].Value = mTransID;
            parameters[1].Value = mDevId;
            parameters[2].Value = Cmd;
            parameters[3].Value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            parameters[4].Value = userId;
            parameters[5].Value = timestamp;
            parameters[6].Value = businessId;
            DbHelperSQL.ExecuteSql(sSql, parameters);

            return mTransID;
        }

        /// <summary>
        /// 获取最新的id
        /// </summary>
        /// <returns></returns>
        public string GetNewTransId()
        {
            int nTransId;
            string sSql = "select max(trans_id) from tbl_fkcmd_trans";
            object obj = DbHelperSQL.GetSingle(sSql);

            if (obj == null)
            {
                nTransId = 200;
            }
            else
            {
                nTransId = int.Parse(obj.ToString());
            }
            return Convert.ToString(nTransId + 1);
        }

        #endregion  ExtensionMethod
    }
}

