﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 假期详细表
	/// </summary>
	[Serializable]
	public partial class ecb_HolidayInfo
	{
		public ecb_HolidayInfo()
		{}
		#region Model
		private Guid _id;
		private int _years;
		private DateTime _holiday;
		private int _type;
		private string _remark;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 假期年度
		/// </summary>
		public int Years
		{
			set{ _years=value;}
			get{return _years;}
		}
		/// <summary>
		/// 假期日期
		/// </summary>
		public DateTime Holiday
		{
			set{ _holiday=value;}
			get{return _holiday;}
		}
		/// <summary>
		/// 假期类型  1放假 2调休 3其他
		/// </summary>
		public int Type
		{
			set{ _type=value;}
			get{return _type;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Remark
		{
			set{ _remark=value;}
			get{return _remark;}
		}
		#endregion Model

	}
}

