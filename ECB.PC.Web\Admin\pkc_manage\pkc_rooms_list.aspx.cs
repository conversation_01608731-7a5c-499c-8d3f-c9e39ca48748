﻿using NPOI.HSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.Common.DEncrypt;
using YunEdu.DBUtility;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_rooms_list : YunEdu.Authority.AdminCommonJC
    {
        string id;
        string tp;
        YunEdu.BLL.PKC_Rooms bllrooms = new YunEdu.BLL.PKC_Rooms();
        YunEdu.Model.PKC_Rooms modelrooms = new YunEdu.Model.PKC_Rooms();

        YunEdu.BLL.PKC_Rooms bllrooms2 = new YunEdu.BLL.PKC_Rooms();
        YunEdu.Model.PKC_Rooms modelrooms2 = new YunEdu.Model.PKC_Rooms();

        YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();
        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();
        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();

        protected void Page_Load(object sender, EventArgs e)
        {
            GetArea();

            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
                tp = Request.QueryString["tp"].ToString();
                if (!IsPostBack)
                {
                    InitControls();
                    BindData();
                }
            }
        }

        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }

        //返回
        protected void btnBack_Click(object sender, EventArgs e)
        {
            Response.Redirect("pkc_tests_list.aspx?MenuId=" + Request.QueryString["MenuId"]);
        }

        /// <summary>
        /// 获取查询条件
        /// </summary>
        /// <returns></returns>
        private string GetWhere()
        {
            StringBuilder sbWhere = new StringBuilder();

            string strColumnId = string.Empty;
            string strColumnPath = string.Empty;
            // 第一次加载
            if (!IsPostBack)
            {
                strColumnId = modelAreaUser.ColumnID.ToString();
                strColumnPath = modelAreaUser.ColumnPath;
            }
            else
            {
                // 是否选择附属学校
                if (!string.IsNullOrEmpty(hidChildSchool.Value))
                {
                    modelArea = bllArea.GetModel(int.Parse(hidChildSchool.Value));
                }
                else if (!string.IsNullOrEmpty(hidSchoolId.Value))
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                }
                if (modelArea != null && modelArea.ColumnID != 0)
                {
                    strColumnId = modelArea.ColumnID.ToString();
                    strColumnPath = modelArea.ColumnPath;
                }
            }
            sbWhere.Append("(SchoolId=" + strColumnId + " or SchoolPath like '" + strColumnPath + "|%')");
            //if (!string.IsNullOrEmpty(hidTermId.Value))
            //{
            //    if (sbWhere.Length > 0)
            //    {
            //        sbWhere.Append(" and ");
            //    }
            //    sbWhere.Append("a.TermId='" + hidTermId.Value + "'");
            //}
            if (!string.IsNullOrEmpty(hidGradeId.Value))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append(" a.GradeId='" + hidGradeId.Value + "'");
            }
            if (!string.IsNullOrEmpty(hidClassId.Value))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append(" a.ClassId='" + hidClassId.Value + "'");
            }
            //if (!string.IsNullOrEmpty(this.ddlRoom.SelectedValue) && !this.ddlRoom.SelectedValue.ToString().Equals("0"))
            //{
            //    if (sbWhere.Length > 0)
            //    {
            //        sbWhere.Append(" and ");
            //    }
            //    sbWhere.Append(" a.RoomId='" + this.ddlRoom.SelectedValue.ToString() + "'");
            //}
            if (!string.IsNullOrEmpty(this.ddlRoomNum.SelectedValue) && !this.ddlRoomNum.SelectedValue.ToString().Equals("0"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append(" a.RoomId='" + this.ddlRoomNum.SelectedValue.ToString() + "'");
            }
            if (!string.IsNullOrEmpty(this.ddltype.SelectedValue) && !this.ddltype.SelectedValue.ToString().Equals("0"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append(" a.Roomtype='" + this.ddltype.SelectedValue.ToString() + "'");
            }
            sbWhere.Append(" and a.TestId='" + id + "'");
            if (!string.IsNullOrEmpty(this.dorpShowSize.SelectedValue) && !this.dorpShowSize.SelectedValue.ToString().Equals("0"))
            {
                AspNetPager1.PageSize = int.Parse(this.dorpShowSize.SelectedValue);
            }
            if (!string.IsNullOrEmpty(this.dropType.SelectedValue) && !this.dropType.SelectedValue.ToString().Equals("0"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                string _text = YunEdu.Common.StringPlus.Filter(this.inputName.Text, "html");
                switch (this.dropType.SelectedValue)
                {
                    case "1":
                        sbWhere.Append("RoomTitle like '%" + this.inputName.Text + "%'");
                        break;
                    case "2":
                        sbWhere.Append("RoomLocation like '%" + this.inputName.Text + "%'");
                        break;
                    default:
                        break;
                }
            }

            return sbWhere.ToString();
        }
        /// <summary>
        /// 获取数据并绑定
        /// </summary>
        private void BindData(bool isMain = false)
        {
            
            //过去考场可以安排的人数
            int amount = 0;
            int capacityamount = 0;
            foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "RoomLayout,RoomCapacity", "TestId='" + id + "' and IsEnabled = 1", "RoomNum asc", isMain ? DbHelperSQL.ConnMain : null).Tables[0].Rows)
            {
                try
                {
                    //考场布局安排的总人数
                    string layout = item["RoomLayout"].ToString();
                    string[] _tempLayouts = layout.Split('|');
                    int[] a = new int[_tempLayouts.Length];
                    for (int i = 0; i < _tempLayouts.Length; i++)
                    {
                        a[i] = Convert.ToInt32(_tempLayouts[i]);
                    }
                    for (int i = 0; i < a.Length; i++)
                    {
                        amount += a[i];
                    }
                    //考场总容量
                    capacityamount += int.Parse(item["RoomCapacity"].ToString());
                }
                catch (Exception)
                {
                    continue;
                }
            }
            this.txtPageNum.Text = this.AspNetPager1.PageSize.ToString();
            string testusers = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "'").ToString();
            modeltests = blltests.GetModel(new Guid(id));
            YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Tests", "TestId,TestTitle", "TestTitle", "TestId", "ColumnId=" + modeltests.ColumnId, "TestAddDate desc", ddl_TestName, "0", true, "选择考试");        
            string strWhere = GetWhere();
            AspNetPager1.RecordCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms a left join PKC_Tests b on a.TestId=b.TestId left join BM_Areas c on a.SchoolId=c.ColumnID", strWhere, isMain ? DbHelperSQL.ConnMain : null);
            AspNetPager1.CustomInfoHTML = "共有<font color=red>" + AspNetPager1.RecordCount + "</font>条记录,可安排<font color=red>" + amount + "</font>人，最多可安排<font color=red>" + capacityamount + "</font>人。当前第<font color=red>" + AspNetPager1.CurrentPageIndex + "/" + AspNetPager1.PageCount + "</font>页,每页<font color=red>" + AspNetPager1.PageSize + "</font>条记录&nbsp &nbsp本次考试考生<font color=red>" + testusers + "</font>人";
            DataSet dsData = YunEdu.Common.GetRecordByPageOrder.GetList("PKC_Rooms a left join PKC_Tests b on a.TestId=b.TestId left join BM_Areas c on a.SchoolId=c.ColumnID", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "a.*,b.TestTitle,c.AreaName", strWhere, "RoomNum asc", isMain ? DbHelperSQL.ConnMain : null);
            //switch (tp)
            //{
            //    case "all":
            //        btnBack.Visible = false;
            //        pnlRooms.Visible = false;
            //        gvRoomList.Columns[2].Visible = false;
            //        gvRoomList.Columns[9].Visible = false;
            //        break;
            //    case "select":
            //        pnlSet.Visible = false;
            //        btnConfirm.Visible = true;
            //        pnlRooms.Visible = false;
            //        pnlRoomLayout.Visible = false;
            //        gvRoomList.Columns[2].Visible = false;
            //        gvRoomList.Columns[8].Visible = false;
            //        gvRoomList.Columns[9].Visible = false;
            //        gvRoomList.Columns[10].Visible = false;
            //        break;

            //}
            gvRoomList.DataSource = dsData;
            gvRoomList.DataBind();

        }

        /// <summary>
        /// 根据学校获取学校ID，并绑定信息
        /// </summary>
        /// <param name="schoolId">学校ID</param>
        /// <param name="selectedValue"></param>
        private void BindTermInfos(string schoolId, string selectedValue = "")
        {
            YunEdu.BLL.JC_TermInfos bllTerm = new YunEdu.BLL.JC_TermInfos();
            DataSet dsTerms = bllTerm.GetList("SchoolColumnId=" + schoolId);
            if (dsTerms != null && dsTerms.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlTerm, dsTerms.Tables[0], "", "FullTermName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    ddlTerm.SelectedValue = selectedValue;
                    hidTermId.Value = selectedValue;
                    BindGradeInfos(new Guid(selectedValue), hidGradeId.Value);
                }
            }
        }

        /// <summary>
        /// 绑定年级信息
        /// </summary>
        /// <param name="schoolId">学校ID</param>
        /// <param name="selectedValue"></param>
        private void BindGradeInfos(Guid termId, string selectedValue = "")
        {
            YunEdu.BLL.JC_GradeInfos bllGrade = new YunEdu.BLL.JC_GradeInfos();
            DataSet dsGradeInfos = new DataSet();
            // 管理员
            if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()) ||
              RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
              RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
              RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()))
            {
                dsGradeInfos = bllGrade.GetList(0, "TermID='" + termId + "'", "OrderId");

            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsGradeInfos = GetRecordByPageOrder.GetModel(string.Format(@"JC_Subjects a join JC_GradeInfos b on a.GradeID=b.ID and a.TeacherNo='{0}'", UserName), " distinct b.ID,b.GradeName", "");
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModel(new Guid(UserId));
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsGradeInfos = bllGrade.GetList(0, "TermID='" + termId + "'", "OrderId");
            }
            // 绑定年级信息列表
            if (dsGradeInfos != null && dsGradeInfos.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlGrade, dsGradeInfos.Tables[0], "", "GradeName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    ddlGrade.SelectedValue = selectedValue;
                    hidGradeId.Value = selectedValue;
                    BindClassInfos(new Guid(selectedValue), hidClassId.Value);
                }
            }
        }

        /// <summary>
        /// 绑定班级信息
        /// </summary>
        /// <param name="gradeId"></param>
        /// <param name="selectedValue"></param>
        private void BindClassInfos(Guid gradeId, string selectedValue = "")
        {
            YunEdu.BLL.JC_ClassInfos bllClass = new YunEdu.BLL.JC_ClassInfos();
            DataSet dsClass = new DataSet();
            if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()) ||
              RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
              RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
              RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()))
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", gradeId.ToString()), "OrderId");
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsClass = GetRecordByPageOrder.GetModel(string.Format(@"JC_Subjects a join JC_ClassInfos b on a.ClassID=b.ID and a.TeacherNo='{0}' and a.GradeID='{1}'",
                    UserName, gradeId), "b.ID,b.ClassName", "");
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }

            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(UserName);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", gradeId.ToString()), "OrderId");
            }
            if (dsClass != null && dsClass.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlClass, dsClass.Tables[0], "", "ClassName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    hidClassId.Value = selectedValue;
                    ddlClass.SelectedValue = selectedValue;
                }
            }
        }

        #region 加载区域信息

        private void GetArea()
        {
            //设区市，绑定，同时，根据用户所处级别，选定当前选择项
            //任何级别的用户都要显示市级
            //hidSchoolId.value默认显示用户所在县区学校
            string strColumnPath = "";

            if (!IsPostBack)
            {
                strColumnPath = modelAreaUser.ColumnPath;
                hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
            }
            else
            {
                if (string.IsNullOrEmpty(hidSchoolId.Value))
                {
                    strColumnPath = modelAreaUser.ColumnPath;
                    hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
                }
                else
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                    if (modelArea == null)
                    {
                        return;
                    }
                    else
                    {
                        strColumnPath = modelArea.ColumnPath;
                    }
                }
            }
            if (string.IsNullOrEmpty(strColumnPath))
            {
                return;
            }
            string[] place = strColumnPath.Split('|');
            bllArea.BindAreaDropDown(S1, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S2, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S3, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(ddlChildSchool, "", IsPostBack, "", "请选择市");
            GetRecordByPageOrder.BindDropDownList(ddlTerm, null, "", "GradeName", "ID", "请选择学期");
            GetRecordByPageOrder.BindDropDownList(ddlGrade, null, "", "GradeName", "ID", "请选择年级");
            GetRecordByPageOrder.BindDropDownList(ddlClass, null, "", "ClassName", "ID", "请选择班级");
            switch (place.Length)
            {
                case 1:
                    // 通过省查处市级
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, "", "请选择市");
                    break;
                case 2:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, "", "请选择县区");
                    break;
                case 3:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, "", "请选择学校");
                    break;
                case 4:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, "", "请选择学校");
                    if (ddlChildSchool.Items.Count > 1)
                    {
                        ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                        if (!string.IsNullOrEmpty(hidChildSchool.Value))
                        {
                            ddlChildSchool.SelectedValue = hidChildSchool.Value;
                        }
                    }
                    BindTermInfos(place[3], hidTermId.Value);
                    break;
                case 5:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    pnlSchool.Visible = false;
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, place[4], "请选择附属学校");
                    ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                    BindTermInfos(place[4], hidTermId.Value);
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    break;
                default:
                    break;
            }
        }

        #endregion

        //绑定
        private void InitControls()
        {
            //YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Rooms", "RoomId,RoomTitle", "RoomTitle", "RoomId", "TestId='" + id + "'", "RoomNum asc", ddlRoom, "0", true, "请选择考场");
            YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Rooms", "RoomId,RoomNum", "RoomNum", "RoomId", "TestId='" + id + "'", "RoomNum asc", ddlRoomNum, "0", true, "考场号");
        }
        /// <summary>
        /// 批量修改布局
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        protected void btnRoomLayout_Click(object sender, EventArgs e)
        {
            string _RoomLayout = this.txtRoomLayout.Value;
            string _RoomType = sltRoomType.Value;
            #region 判断用户输入的格式是否正确

            int amount = 0;

            string[] _tempLayout = _RoomLayout.Split('|');
            int[] a = new int[_tempLayout.Length];
            int num;
            for (int i = 0; i < _tempLayout.Length; i++)
            {
                if (int.TryParse(_tempLayout[i], out num))
                {
                    if (int.Parse(_tempLayout[i]) <= 20)
                    {
                        a[i] = Convert.ToInt32(_tempLayout[i]);
                    }
                    else
                    {
                        MessageBox.ResponseScript(this, "layer.msg('每列长度不可以超过20个人！请重新输入');");
                        return;
                    }
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('请输入正确的格式和数字');");
                    return;
                }
            }
            for (int i = 0; i < a.Length; i++)
            {
                amount += a[i];
            }
            

            //判断s型的考场布局是否正确
            if (_RoomType.Equals("1"))
            {
                for (int i = 0; i < _tempLayout.Length; i++)
                {
                    if (_tempLayout[0].Equals(_tempLayout[i]))
                    {

                    }
                    else
                    {
                        MessageBox.ResponseScript(this, "layer.msg('考场类型要与考场布局相匹配，考场类型为S的必须列数相等');");
                        return;
                    }
                }
            }
            #endregion
           
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvRoomList.Rows)
            {

                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        iCount++;
                        modelrooms = bllrooms.GetModel(new Guid(gvRoomList.DataKeys[myItem.RowIndex][0].ToString()));

                        if (amount <= modelrooms.RoomCapacity)
                        {
                            modelrooms.RoomType = int.Parse(_RoomType);
                            modelrooms.RoomLayout = txtRoomLayout.Value;
                            bllrooms.Update(modelrooms);
                        }
                        else
                        {
                            iError++;
                        }                    
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要修改的记录');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('修改成功');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('批量修改失败！存在考场布局编排的人数大于考场容量的考场');");
            }
            BindData(true);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnDelete_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvRoomList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        iCount++;
                        if (!bllrooms.Delete(new Guid(gvRoomList.DataKeys[myItem.RowIndex][0].ToString())))
                        {
                            iError++;
                        }
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要删除的记录!');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('删除失败!');");
            }
            BindData(true);
        }
        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            BindData();
        }
        //设置分页显示数量
        protected void txtPageNum_TextChanged(object sender, EventArgs e)
        {
            int _pagesize;
            if (int.TryParse(txtPageNum.Text.Trim(), out _pagesize))
            {
                this.AspNetPager1.PageSize = _pagesize;
                this.AspNetPager1.CurrentPageIndex = 1;
                BindData();
            }
        }
        /// <summary>
        /// 保存考场号
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnSort_Click(object sender, EventArgs e)
        {
            if (gvRoomList.Rows.Count < 1)
            {
                return;
            }
            bool flag = false;
            int iError = 0;
            foreach (GridViewRow myItem in gvRoomList.Rows)
            {
                string roomid = gvRoomList.DataKeys[myItem.RowIndex][0].ToString();
                modelrooms = bllrooms.GetModel(new Guid(roomid));
                int sortId;
                if (((TextBox)myItem.FindControl("txtSort")).Text.Trim() == string.Empty)
                {
                    sortId = 0;
                }
                else if (int.TryParse(((TextBox)myItem.FindControl("txtSort")).Text.Trim(), out sortId))
                {
                    int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "' and " + "RoomNum='" + sortId + "'");
                    if (count == 0)
                    {

                        modelrooms.RoomNum = sortId;
                        flag = bllrooms.Update(modelrooms);
                        
                    }
                    else
                    {
                        foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_Rooms", "RoomId", "TestId='" + id + "' and " + "RoomNum='" + sortId + "'").Tables[0].Rows)
                        {
                            modelrooms2 = bllrooms2.GetModel(new Guid (item["RoomId"].ToString()));
                            modelrooms2.RoomNum = 0;
                            bllrooms2.Update(modelrooms2);
                        }
                        modelrooms.RoomNum = sortId;
                        flag = bllrooms.Update(modelrooms);
                    }
                }
                else
                {
                    //格式错误
                    iError++;
                }
                
            }
            if (iError == 0)
            {
                if (flag)
                {
                    MessageBox.ResponseScript(this, "layer.msg('保存考场号成功!');");
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('保存考场号失败!');");
                }
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('保存考场号失败！存在考场号不符合规范，请输入纯数字，并且考场号不能重复!');");
            }
           
            BindData(true);
        }
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            BindData(!string.IsNullOrEmpty(Request["__EVENTARGUMENT"]));
        }
        //复用考场
        protected void btnReuseRooms_Click(object sender, EventArgs e)
        {
            string testid = this.ddl_TestName.SelectedValue.ToString();
            if (!string.IsNullOrWhiteSpace(testid) && !testid.Equals("0"))
            {
                foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "*", "TestId='" + testid + "'", "CreateDate desc").Tables[0].Rows)
                {
                    try
                    {
                        modelrooms = bllrooms.GetModel(new Guid(item["RoomId"].ToString()));
                        if (modelrooms != null)
                        {
                            string roomtitle = modelrooms.RoomTitle;
                            int roomnum = (int)modelrooms.RoomNum;
                            string roomlocation = modelrooms.RoomLocation;
                            string roomlayout = modelrooms.RoomLayout;
                            int roomtype = (int)modelrooms.RoomType;
                            bool isenabled = modelrooms.IsEnabled;
                            int schoolid = (int)modelrooms.SchoolId;
                            string schoolpath = modelrooms.SchoolPath;
                            string roominstruction = modelrooms.RoomInstruction;
                            int roomcapacity = (int)modelrooms.RoomCapacity;

                            int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "' and " + "RoomNum='" + roomnum + "'");
                            if (count == 0)
                            {
                                modelrooms = new YunEdu.Model.PKC_Rooms();
                                modelrooms.TestId = new Guid(id);
                                modelrooms.RoomTitle = roomtitle;
                                modelrooms.RoomType = roomtype;
                                modelrooms.RoomNum = roomnum;
                                modelrooms.RoomLocation = roomlocation;
                                modelrooms.RoomLayout = roomlayout;
                                modelrooms.IsEnabled = isenabled;
                                modelrooms.CreateDate = DateTime.Now;
                                modelrooms.SchoolId = schoolid;
                                modelrooms.SchoolPath = schoolpath;
                                modelrooms.RoomCapacity = roomcapacity;
                                modelrooms.RoomInstruction = roominstruction;
                                modeltests = blltests.GetModel(new Guid(id));
                                modelrooms.SchoolYear = modeltests.SchoolYear;
                                bllrooms.Add(modelrooms);
                            }
                        }
                    }
                    catch (Exception)
                    {
                        continue;
                    }
                }
                BindData();
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择考试!');");
            }
        }
        #region 导入考场
        protected void btnImpotRooms_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.fupScore.FileName.Length == 0)
                {
                    lblMessage.Text = "你没有选择导入的Excel文件！";
                    return;
                }
                else if (System.IO.Path.GetExtension(this.fupScore.FileName) != ".xls" && System.IO.Path.GetExtension(this.fupScore.FileName) != ".xlsx")
                {
                    lblMessage.Text = "请选择Excel格式“*.xls,*.xlsx”的文件！";
                    return;
                }
                else
                {
                    UpLoad _upload = new UpLoad();
                    UpLoad.UploadFileInfo _fileInfo = _upload.UpLoadFileTemp(fupScore, modelAreaUser.ColumnPath, UserName, CodeTable.FileType.files);
                    DataTable dt = GetExcelData(_fileInfo.RelativePath);//获取Excel表数据 文件名+*.格式
                    ImportData(dt);//导入
                }
            }
            catch (Exception exception)
            {
                MessageBox.ResponseScript(this, "layer.msg('出现错误：" + exception.Message+"');");
            }
            BindData();
        }
        /// <summary>
        /// 返回 导入数据
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private DataTable GetExcelData(string fileName)
        {
            string path = Server.MapPath(fileName);
            HSSFWorkbook hssfworkbook;
            using (FileStream file = new FileStream(path, FileMode.Open, FileAccess.Read))
                hssfworkbook = new HSSFWorkbook(file);

            HSSFSheet sheet = (HSSFSheet)hssfworkbook.GetSheetAt(0);
            System.Collections.IEnumerator rows = sheet.GetRowEnumerator();
            DataTable dt = new DataTable();
            //移动的标题行，若不存在提示用户
            if (rows.MoveNext())
            {
                HSSFRow rowTitle = (HSSFRow)rows.Current;
                for (int i = 0; i < rowTitle.LastCellNum; i++)
                {
                    HSSFCell cell = (HSSFCell)rowTitle.GetCell(i);
                    if (cell != null)
                        dt.Columns.Add(cell.ToString().Trim());
                }
                while (rows.MoveNext())
                {

                    HSSFRow row = (HSSFRow)rows.Current;
                    DataRow dr = dt.NewRow();
                    for (int i = 0; i < row.LastCellNum; i++)
                    {
                        HSSFCell cell = (HSSFCell)row.GetCell(i);
                        if (cell != null) dr[i] = cell.ToString().Trim();
                        else dr[i] = null;
                    }
                    dt.Rows.Add(dr);
                }
            }
            else throw new Exception("无效格式");
            return dt;
        }
        /// <summary>
        /// 导入数据库
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private void ImportData(DataTable dt)
        {
            YunEdu.BLL.JC_ClassInfos bllClass = new YunEdu.BLL.JC_ClassInfos();
            try
            {
                StringBuilder sb = new StringBuilder();
                if (!CheckExcel(dt))
                {
                    MessageBox.ResponseScript(this, "layer.msg('导入失败');");
                    return;
                }
                else
                {
                    int count = 0;
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (!string.IsNullOrEmpty(dt.Rows[i]["考场名称"].ToString()))
                        {
                            int amount = 0;
                            string[] _tempLayouts = dt.Rows[i]["考场布局"].ToString().Split('|');
                            int[] a = new int[_tempLayouts.Length];
                            for (int k = 0; k < _tempLayouts.Length; k++)
                            {
                                a[k] = Convert.ToInt32(_tempLayouts[k]);
                            }
                            for (int j = 0; j < a.Length; j++)
                            {
                                amount += a[j];
                            }

                            if (amount <= int.Parse(dt.Rows[i]["考场容量"].ToString()))
                            {
                                int countroom = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "' and " + "RoomNum='" + dt.Rows[i]["考场号"].ToString() + "'");
                                if (countroom == 0)
                                {
                                    YunEdu.Model.PKC_Rooms model = new YunEdu.Model.PKC_Rooms();
                                    model.TestId = new Guid(id);
                                    model.SchoolId = modelAreaUser.ColumnID;
                                    model.SchoolPath = modelAreaUser.ColumnPath;
                                    model.CreateDate = DateTime.Now;
                                    model.RoomTitle = dt.Rows[i]["考场名称"].ToString();
                                    if (!string.IsNullOrEmpty(dt.Rows[i]["考场号"].ToString()))
                                    {
                                        model.RoomNum = int.Parse(dt.Rows[i]["考场号"].ToString());
                                    }
                                    model.RoomLocation = dt.Rows[i]["考场所在位置"].ToString();
                                    model.RoomLayout = dt.Rows[i]["考场布局"].ToString();
                                    model.RoomInstruction = dt.Rows[i]["考场说明"].ToString();
                                    model.RoomCapacity = int.Parse(dt.Rows[i]["考场容量"].ToString());
                                    if (dt.Rows[i]["是否可用"].ToString().Equals("是"))
                                    {
                                        model.IsEnabled = true;
                                    }
                                    else
                                    {
                                        model.IsEnabled = false;
                                    }
                                    if (dt.Rows[i]["考场类型"].ToString().Equals("川型"))
                                    {
                                        model.RoomType = 2;
                                    }
                                    else
                                    {
                                        model.RoomType = 1;
                                    }
                                    if (!string.IsNullOrEmpty(dt.Rows[i]["关联班级"].ToString()))
                                    {
                                        YunEdu.Model.JC_ClassInfos modelClass = bllClass.GetModel(dt.Rows[i]["关联班级"].ToString(), modelAreaUser.ColumnID.ToString());
                                        if (modelClass != null)
                                        {
                                            model.ClassId = modelClass.ID;
                                        }
                                        else
                                        {
                                            sb.Append("<li style=\"color: Black;display:inline;\">导入失败!关联班级 " + dt.Rows[i]["关联班级"].ToString() + "不存在！</li>");
                                            continue;
                                        }
                                    }
                                    //string ResultStr = string.Empty;
                                    //DateTime datetime;
                                    //if (DateTime.TryParse(dt.Rows[i]["获奖日期"].ToString(), out datetime))
                                    //{
                                    //    model.AwardsDate = DateTime.Parse(dt.Rows[i]["获奖日期"].ToString());
                                    //}
                                    //if (!string.IsNullOrEmpty(dt.Rows[i]["TypeCode"].ToString()))
                                    //{
                                    //    model.TypeCode = dt.Rows[i]["TypeCode"].ToString();
                                    //}
                                    //if (!string.IsNullOrEmpty(dt.Rows[i]["LeveCode"].ToString()))
                                    //{
                                    //    model.LeveCode = dt.Rows[i]["LeveCode"].ToString();
                                    //}
                                    //if (!string.IsNullOrEmpty(dt.Rows[i]["RankCode"].ToString()))
                                    //{
                                    //    model.RankCode = dt.Rows[i]["RankCode"].ToString();
                                    //}
                                    //DataSet ds = new BLL.JC_SchoolHonors().GetList("AwardsName='" + dt.Rows[i]["荣誉名称"] + "' AND AwardsDate = '" + dt.Rows[i]["获奖日期"] + "' AND ToAwardsUnit='" + dt.Rows[i]["授奖单位"] + "' ");
                                    //if (ds.Tables[0].Rows.Count > 0)
                                    //{
                                    //    sb.Append("<li  style=\"color: Black;\">导入数据，Excel第" + (i + 2) + "行 学校荣誉：“" + dt.Rows[i]["荣誉名称"] + "” 数据库已存在数据！</li>");
                                    //}
                                    //else
                                    //{
                                    new YunEdu.BLL.PKC_Rooms().Add(model);
                                    count++;
                                }
                            }
                        }
                    }
                    sb.Append("<li style=\"color: Black;display:inline;\">成功导入 " + count + "个 考场！</li>");

                    this.lblMessage.Text += sb.ToString();
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        /// 验证Excel格式
        /// </summary>
        /// <returns></returns>
        private bool CheckExcel(DataTable dt)
        {
            bool result = false;
            #region 标题
            bool RoomTitle_Check = false, RoomNum_Check = false, RoomLocation_Check = false, RoomType_Check = false, IsEnabled_Check = false, RoomLayout_Check = false, RoomCapacity_Check = false, RoomInstruction_Check = false;
            //bool RoomTitle_Check = false, RoomNum_Check = false, RoomLocation_Check = false, RoomType_Check = false, IsEnabled_Check = false, RoomLayout_Check = false, Rank_Check = false;
            string str = string.Empty;

            if (dt.Columns.Contains("考场名称")) { RoomTitle_Check = true; }
            if (dt.Columns.Contains("是否可用")) { IsEnabled_Check = true; }
            if (dt.Columns.Contains("考场布局")) { RoomLayout_Check = true; }
            if (dt.Columns.Contains("考场容量")) { RoomCapacity_Check = true; }
            if (dt.Columns.Contains("考场说明")) { RoomInstruction_Check = true; }
            if (dt.Columns.Contains("考场号")) { RoomNum_Check = true; }
            if (dt.Columns.Contains("考场所在位置")) { RoomLocation_Check = true; }
            if (dt.Columns.Contains("考场类型")) { RoomType_Check = true; }

            if (!RoomTitle_Check) str += "<li>“考场名称”标题未找到</li>";
            if (!IsEnabled_Check) str += "<li>“是否可用”标题未找到</li>";
            if (!RoomLayout_Check) str += "<li>“考场布局”标题未找到</li>";
            if (!RoomCapacity_Check) str += "<li>“考场容量”标题未找到</li>";
            if (!RoomInstruction_Check) str += "<li>“考场说明”标题未找到</li>";
            if (!RoomNum_Check) str += "<li>“考场号”标题未找到</li>";
            if (!RoomLocation_Check) str += "<li>“考场所在位置”标题未找到</li>";
            if (!RoomType_Check) str += "<li>“考场类型”标题未找到</li>";

            if (RoomTitle_Check && RoomNum_Check && RoomLocation_Check && RoomType_Check && IsEnabled_Check && RoomLayout_Check && RoomCapacity_Check && RoomInstruction_Check) result = true;
            else str += "<li>导入文件的标题内容格式不正确！</li>";
            this.lblMessage.Text = str;
            #endregion

            //if (result)
            //{
            //    dt.Columns.Add("TypeCode", typeof(object));
            //    dt.Columns.Add("LeveCode", typeof(object));
            //    dt.Columns.Add("RankCode", typeof(object));

            //    DataSet dsDictionary = new BLL.Site_Dictionary().GetList(" DictTypeId in(20,22,23) ");
            //    string strResult = string.Empty;//返回错误信息
            //    for (int i = 0; i < dt.Rows.Count; i++)
            //    {
            //        dsDictionary.Tables[0].DefaultView.RowFilter = "DictTypeId=20 and DictText='" + dt.Rows[i]["获奖类别"] + "'";
            //        if (dsDictionary.Tables[0].DefaultView.ToTable().Rows.Count == 0)
            //        {
            //            strResult += "<li>导入数据，Excel第 " + (i + 2) + " 行的获奖类别：“" + dt.Rows[i]["获奖类别"] + "”不存在！</li>";
            //        }
            //        else
            //        {
            //            dt.Rows[i]["TypeCode"] = dsDictionary.Tables[0].DefaultView.ToTable().Rows[0]["DictValue"];
            //        }
            //        dsDictionary.Tables[0].DefaultView.RowFilter = "DictTypeId=22 and DictText='" + dt.Rows[i]["获奖级别"] + "'";
            //        if (dsDictionary.Tables[0].DefaultView.ToTable().Rows.Count == 0)
            //        {
            //            strResult += "<li>导入数据，Excel第 " + (i + 2) + " 行的获奖级别：“" + dt.Rows[i]["获奖级别"] + "”不存在！</li>";
            //        }
            //        else
            //        {
            //            dt.Rows[i]["LeveCode"] = dsDictionary.Tables[0].DefaultView.ToTable().Rows[0]["DictValue"];
            //        }
            //        //dsDictionary.Tables[0].DefaultView.RowFilter = "DictTypeId=23 and DictText='" + dt.Rows[i]["获奖等级"] + "'";
            //        //if (dsDictionary.Tables[0].DefaultView.ToTable().Rows.Count == 0)
            //        //{
            //        //    strResult += "<li>导入数据，Excel第 " + (i + 2) + " 行的获奖等级：“" + dt.Rows[i]["获奖等级"] + "”不存在！</li>";
            //        //}
            //        //else
            //        //{
            //        //    dt.Rows[i]["RankCode"] = dsDictionary.Tables[0].DefaultView.ToTable().Rows[0]["DictValue"];
            //        //}

            //    }
            //    if (strResult != string.Empty)
            //    { this.lblMessage.Text = strResult; }
            //}
            return result;
        }



        protected void btnDownloadRooms_Click(object sender, EventArgs e)
        {
            Response.Clear();
            Response.ContentType = "application/x-zip-compressed";
            Response.AddHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode("考场模板.xls", System.Text.Encoding.UTF8));
            string filename = Server.MapPath("/Admin/pkc_manage/userfiles/考场模板.xls");
            Response.TransmitFile(filename);
        }

        #endregion




    }
}