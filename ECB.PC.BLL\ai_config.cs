﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ai_config
    /// </summary>
    public partial class ai_config
    {
        public ai_config()
        { }

        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ai_config");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ai_config model)
        {
            StringBuilder strSql = new StringBuilder();
            // 判断是否启用当前添加的模型
            if (model.IsEnable == 1)
            {
                strSql.AppendLine("update ai_config set isEnable=0");
            }
            strSql.Append("insert into ai_config(");
            strSql.Append("Id,Model,ApiUrl,Token,ModelConfig,MaxDialogNum,DialogEndTime,HistoryDialogNum,isEnable,ModelName)");
            strSql.Append(" values (");
            strSql.Append("@Id,@Model,@ApiUrl,@Token,@ModelConfig,@MaxDialogNum,@DialogEndTime,@HistoryDialogNum,@isEnable,@ModelName)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Model", SqlDbType.NVarChar,100),
                    new SqlParameter("@ApiUrl", SqlDbType.NVarChar,255),
                    new SqlParameter("@Token", SqlDbType.NVarChar,255),
                    new SqlParameter("@ModelConfig", SqlDbType.NVarChar,255),
                    new SqlParameter("@MaxDialogNum", SqlDbType.Int,4),
                    new SqlParameter("@DialogEndTime", SqlDbType.Int,4),
                    new SqlParameter("@HistoryDialogNum", SqlDbType.Int,4),
                    new SqlParameter("@isEnable",SqlDbType.Int,4),
					 new SqlParameter("@ModelName", SqlDbType.NVarChar,100)
			};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.Model;
            parameters[2].Value = model.ApiUrl;
            parameters[3].Value = model.Token;
            parameters[4].Value = model.ModelConfig;
            parameters[5].Value = model.MaxDialogNum;
            parameters[6].Value = model.DialogEndTime;
            parameters[7].Value = model.HistoryDialogNum;
            parameters[8].Value = model.IsEnable;
			parameters[9].Value = model.ModelName;
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ai_config model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append("update ai_config set ");
            strSql.Append("Model=@Model,");
            strSql.Append("ApiUrl=@ApiUrl,");
            strSql.Append("Token=@Token,");
            strSql.Append("ModelConfig=@ModelConfig,");
            strSql.Append("MaxDialogNum=@MaxDialogNum,");
            strSql.Append("DialogEndTime=@DialogEndTime,");
            strSql.Append("HistoryDialogNum=@HistoryDialogNum,");
			strSql.Append("ModelName=@ModelName,");
			strSql.Append("isEnable=@isEnable");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Model", SqlDbType.NVarChar,100),
                    new SqlParameter("@ApiUrl", SqlDbType.NVarChar,255),
                    new SqlParameter("@Token", SqlDbType.NVarChar,255),
                    new SqlParameter("@ModelConfig", SqlDbType.NVarChar,255),
                    new SqlParameter("@MaxDialogNum", SqlDbType.Int,4),
                    new SqlParameter("@DialogEndTime", SqlDbType.Int,4),
                    new SqlParameter("@HistoryDialogNum", SqlDbType.Int,4),
                    new SqlParameter("@isEnable",SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
			        new SqlParameter("@ModelName", SqlDbType.NVarChar,100)};
            parameters[0].Value = model.Model;
            parameters[1].Value = model.ApiUrl;
            parameters[2].Value = model.Token;
            parameters[3].Value = model.ModelConfig;
            parameters[4].Value = model.MaxDialogNum;
            parameters[5].Value = model.DialogEndTime;
            parameters[6].Value = model.HistoryDialogNum;
            parameters[7].Value = model.IsEnable;
            parameters[8].Value = model.Id;
			parameters[9].Value = model.ModelName;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ai_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ai_config ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ai_config GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,Model,ApiUrl,Token,ModelConfig,MaxDialogNum,DialogEndTime,HistoryDialogNum,isEnable,ModelName from ai_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ai_config model = new ECB.PC.Model.ai_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ai_config DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ai_config model = new ECB.PC.Model.ai_config();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["Model"] != null)
                {
                    model.Model = row["Model"].ToString();
                }
                if (row["ApiUrl"] != null)
                {
                    model.ApiUrl = row["ApiUrl"].ToString();
                }
                if (row["Token"] != null)
                {
                    model.Token = row["Token"].ToString();
                }
                if (row["ModelConfig"] != null)
                {
                    model.ModelConfig = row["ModelConfig"].ToString();
                }
                if (row["MaxDialogNum"] != null && row["MaxDialogNum"].ToString() != "")
                {
                    model.MaxDialogNum = int.Parse(row["MaxDialogNum"].ToString());
                }
                if (row["DialogEndTime"] != null && row["DialogEndTime"].ToString() != "")
                {
                    model.DialogEndTime = int.Parse(row["DialogEndTime"].ToString());
                }
                if (row["HistoryDialogNum"] != null && row["HistoryDialogNum"].ToString() != "")
                {
                    model.HistoryDialogNum = int.Parse(row["HistoryDialogNum"].ToString());
                }
                if (row["isEnable"] != null && row["isEnable"].ToString() != "")
                {
                    model.IsEnable = int.Parse(row["isEnable"].ToString());
				}
				if (row["ModelName"] != null)
				{
					model.ModelName = row["ModelName"].ToString();
				}
			}
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,Model,ApiUrl,Token,ModelConfig,MaxDialogNum,DialogEndTime,HistoryDialogNum,isEnable,ModelName ");
            strSql.Append(" FROM ai_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,Model,ApiUrl,Token,ModelConfig,MaxDialogNum,DialogEndTime,HistoryDialogNum,isEnable,ModelName ");
            strSql.Append(" FROM ai_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ai_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ai_config T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ai_config";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetAiModelList()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Model FROM ai_config");
            return DbHelperSQL.Query(strSql.ToString());
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ai_config GetModel(string modelName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,Model,ApiUrl,Token,ModelConfig,MaxDialogNum,DialogEndTime,HistoryDialogNum,isEnable,ModelName from ai_config ");
            strSql.Append(" where Model=@Model ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Model", SqlDbType.NVarChar,100)
            };
            parameters[0].Value = modelName;
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        public ECB.PC.Model.ai_config GetCurrentModel()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,Model,ApiUrl,Token,ModelConfig,MaxDialogNum,DialogEndTime,HistoryDialogNum,isEnable,ModelName from ai_config ");
            strSql.Append(" where isEnable=1 ");

            ECB.PC.Model.ai_config model = new ECB.PC.Model.ai_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString());
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool ChangeEnable(Guid id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("update ai_config set isEnable=0");
            strSql.AppendLine("update ai_config set isEnable=1 where Id=@Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = id;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

