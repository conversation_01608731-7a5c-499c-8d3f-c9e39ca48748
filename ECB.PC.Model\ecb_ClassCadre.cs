﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 班干部字典表
	/// </summary>
	[Serializable]
	public partial class ecb_ClassCadre
	{
		public ecb_ClassCadre()
		{}
		#region Model
		private Guid _id;
		private string _cadrename;
		private string _remark;
        private Guid _classid;
        private int _orderid;
		private DateTime _createdate;
		private Guid _createuserid;
		private int _columnid;
		private string _columnpath;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string CadreName
		{
			set{ _cadrename=value;}
			get{return _cadrename;}
		}
		/// <summary>
		/// 说明
		/// </summary>
		public string Remark
		{
			set{ _remark=value;}
			get{return _remark;}
		}
        /// <summary>
        /// 所属班级
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 排序ID
        /// </summary>
        public int OrderId
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateDate
		{
			set{ _createdate=value;}
			get{return _createdate;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		#endregion Model

	}
}

