﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理系统字典类型表
	/// </summary>
	[Serializable]
	public partial class psych_dict_type
	{
		public psych_dict_type()
		{}
		#region Model
		private int _id;
		private int _columnid;
		private string _columnpath;
		private string _dicttypename;
		/// <summary>
		/// 主键
		/// </summary>
		public int Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 类型名称
		/// </summary>
		public string DictTypeName
		{
			set{ _dicttypename=value;}
			get{return _dicttypename;}
		}
		#endregion Model

	}
}