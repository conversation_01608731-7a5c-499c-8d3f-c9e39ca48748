﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理预警配置
	/// </summary>
	[Serializable]
	public partial class psych_warning_record
	{
		public psych_warning_record()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _studentid;
		private int? _sourcetype;
		private Guid _sourceid;
		private string _warningstatus;
		private DateTime? _createtime;
		private Guid _creator;
		public int IsInterfere;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 预警来源类型枚举 
		/// </summary>
		public int? SourceType
		{
			set{ _sourcetype=value;}
			get{return _sourcetype;}
		}
		/// <summary>
		/// 预警来源业务id
		/// </summary>
		public Guid SourceId
		{
			set{ _sourceid=value;}
			get{return _sourceid;}
		}
		/// <summary>
		/// 预警状态
		/// </summary>
		public string WarningStatus
		{
			set{ _warningstatus=value;}
			get{return _warningstatus;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		#endregion Model

	}
}