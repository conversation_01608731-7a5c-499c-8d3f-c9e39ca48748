﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// AI对话记录
	/// </summary>
	[Serializable]
	public partial class ai_dialogs
	{
		public ai_dialogs()
		{}
		#region Model
		private Guid _id;
		private string _model;
		private Guid _userid;
		private string _title;
		private DateTime _createtime;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 模型 唯一
		/// </summary>
		public string Model
		{
			set{ _model=value;}
			get{return _model;}
		}
		/// <summary>
		/// 用户Id
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 标题 取用户的第一条信息
		/// </summary>
		public string Title
		{
			set{ _title=value;}
			get{return _title;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}

