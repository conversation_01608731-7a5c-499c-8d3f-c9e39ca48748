﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_kq_sh_config:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_kq_sh_config
	{
		public ecb_kq_sh_config()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private int? _week;
		private string _beginkqtime;
		private string _normalkqtime;
		private string _endkqtime;
		private Guid _lastedituserid;
		private DateTime? _lastedittime;
		private int? _KQType;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Week
		{
			set{ _week=value;}
			get{return _week;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string BeginKQTime
		{
			set{ _beginkqtime=value;}
			get{return _beginkqtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string NormalKQTime
		{
			set{ _normalkqtime=value;}
			get{return _normalkqtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string EndKQTime
		{
			set{ _endkqtime=value;}
			get{return _endkqtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid LastEditUserId
		{
			set{ _lastedituserid=value;}
			get{return _lastedituserid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		/// <summary>
		/// 0:未知1：出寝 2：归寝
		/// </summary>
		public int? KQType
		{
			set{ _KQType=value;}
			get{return _KQType;}
		}
		#endregion Model

	}
}

