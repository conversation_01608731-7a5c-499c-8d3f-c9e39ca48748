{"RootPath": "F:\\workstation\\ECB\\ECB-PC\\sourcecode\\ECB.PC\\ECB.PC.BLL", "ProjectFileName": "ECB.PC.BLL.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "ai_config.cs"}, {"SourceFile": "ai_dialogs.cs"}, {"SourceFile": "ai_dialog_content.cs"}, {"SourceFile": "ai_prompts.cs"}, {"SourceFile": "api_kq_inout.cs"}, {"SourceFile": "aspnet_Task.cs"}, {"SourceFile": "aspnet_Welcome.cs"}, {"SourceFile": "CommonEnum.cs"}, {"SourceFile": "DA_Catalog.cs"}, {"SourceFile": "DA_XS_FeaturedActivities.cs"}, {"SourceFile": "DA_XS_GrowAttachment.cs"}, {"SourceFile": "DA_XS_StudentWorks.cs"}, {"SourceFile": "ecb_abnormal_config.cs"}, {"SourceFile": "ecb_Active_Class.cs"}, {"SourceFile": "ecb_Active_School.cs"}, {"SourceFile": "ecb_attendce_history.cs"}, {"SourceFile": "ecb_AudioVolume.cs"}, {"SourceFile": "ecb_classbrands.cs"}, {"SourceFile": "ecb_ClassCadre.cs"}, {"SourceFile": "ecb_ClassCadreConfig.cs"}, {"SourceFile": "ecb_ClassGroup.cs"}, {"SourceFile": "ecb_ClassGroupConfig.cs"}, {"SourceFile": "ecb_classSubjectRepresent.cs"}, {"SourceFile": "ecb_class_honor.cs"}, {"SourceFile": "ecb_class_honor_attachement.cs"}, {"SourceFile": "ecb_config.cs"}, {"SourceFile": "ecb_Countdown.cs"}, {"SourceFile": "ecb_custom_products.cs"}, {"SourceFile": "ecb_Device.cs"}, {"SourceFile": "ecb_Duty.cs"}, {"SourceFile": "ecb_DutyItems.cs"}, {"SourceFile": "ecb_Event.cs"}, {"SourceFile": "ecb_FunctionRoom.cs"}, {"SourceFile": "ecb_FunctionRoom_Audit_Records.cs"}, {"SourceFile": "ecb_FunctionRoom_Reservation.cs"}, {"SourceFile": "ecb_FunctionSubject.cs"}, {"SourceFile": "ecb_GradeRedFlagConfig.cs"}, {"SourceFile": "ecb_GuestBook.cs"}, {"SourceFile": "ecb_HolidayInfo.cs"}, {"SourceFile": "ecb_iccard_bind.cs"}, {"SourceFile": "ecb_jishiyuan_timetable.cs"}, {"SourceFile": "ecb_kq_inout_student.cs"}, {"SourceFile": "ecb_kq_inout_student_config.cs"}, {"SourceFile": "ecb_kq_inout_teacher.cs"}, {"SourceFile": "ecb_kq_inout_teacher_config.cs"}, {"SourceFile": "ecb_kq_inout_teacher_user.cs"}, {"SourceFile": "ecb_kq_meeting.cs"}, {"SourceFile": "ecb_kq_meeting_record.cs"}, {"SourceFile": "ecb_kq_meeting_user.cs"}, {"SourceFile": "ecb_kq_sh_config.cs"}, {"SourceFile": "ecb_kq_temp.cs"}, {"SourceFile": "ecb_kq_temp_record.cs"}, {"SourceFile": "ecb_kq_temp_user.cs"}, {"SourceFile": "ecb_loupai_info.cs"}, {"SourceFile": "ecb_mailbox.cs"}, {"SourceFile": "ecb_MenJin.cs"}, {"SourceFile": "ecb_<PERSON><PERSON>in_Pusher.cs"}, {"SourceFile": "ecb_Mode.cs"}, {"SourceFile": "ecb_Mode_Config.cs"}, {"SourceFile": "ecb_news.cs"}, {"SourceFile": "ecb_news_category.cs"}, {"SourceFile": "ecb_New_Duty.cs"}, {"SourceFile": "ecb_officeStatus_OperationRecord.cs"}, {"SourceFile": "ecb_place.cs"}, {"SourceFile": "ecb_place_category.cs"}, {"SourceFile": "ecb_Praise.cs"}, {"SourceFile": "Ecb_PraiseType.cs"}, {"SourceFile": "ecb_Program.cs"}, {"SourceFile": "ecb_ProminentTeacher.cs"}, {"SourceFile": "ecb_PushUser.cs"}, {"SourceFile": "ecb_reboot_config.cs"}, {"SourceFile": "ecb_reboot_once.cs"}, {"SourceFile": "ecb_reboot_rule.cs"}, {"SourceFile": "ecb_reboot_time.cs"}, {"SourceFile": "ecb_Record.cs"}, {"SourceFile": "ecb_RedFlag.cs"}, {"SourceFile": "ecb_SchoolActivity.cs"}, {"SourceFile": "ecb_SchoolStars.cs"}, {"SourceFile": "ecb_SchoolStarsType.cs"}, {"SourceFile": "ecb_SchoolVideo.cs"}, {"SourceFile": "ecb_sc_week.cs"}, {"SourceFile": "ecb_social_exam.cs"}, {"SourceFile": "ecb_social_place.cs"}, {"SourceFile": "ecb_teacher_works.cs"}, {"SourceFile": "ecb_Teaching.cs"}, {"SourceFile": "ecb_TimeTable.cs"}, {"SourceFile": "ecb_TimeTable_stu.cs"}, {"SourceFile": "ecb_time_config.cs"}, {"SourceFile": "ecb_time_template.cs"}, {"SourceFile": "ecb_ToDayInHistory.cs"}, {"SourceFile": "ecb_Update.cs"}, {"SourceFile": "ecb_user_wechats.cs"}, {"SourceFile": "ecb_WebSorket.cs"}, {"SourceFile": "ecb_welcome_message.cs"}, {"SourceFile": "ecb_kq_class_config.cs"}, {"SourceFile": "ecb_kq_class_student.cs"}, {"SourceFile": "ecb_kq_class_teacher.cs"}, {"SourceFile": "ecb_kq_exam.cs"}, {"SourceFile": "Ecb_Xinli_Report.cs"}, {"SourceFile": "ecb_XinLi_Reservation.cs"}, {"SourceFile": "ecb_XinLi_SignIn.cs"}, {"SourceFile": "Ecb_Xinli_StuCase.cs"}, {"SourceFile": "Ecb_Xinli_StuCaseDetail.cs"}, {"SourceFile": "ecb_XinLi_TeaLeave.cs"}, {"SourceFile": "ecb_XinLi_timetable.cs"}, {"SourceFile": "face_delete_log.cs"}, {"SourceFile": "face_lib.cs"}, {"SourceFile": "face_logs.cs"}, {"SourceFile": "face_publish_version.cs"}, {"SourceFile": "JC_ClassInfos.cs"}, {"SourceFile": "JC_Department.cs"}, {"SourceFile": "JC_GradeLeader.cs"}, {"SourceFile": "JC_Homeworks.cs"}, {"SourceFile": "JC_Homeworks_Check.cs"}, {"SourceFile": "JC_Homework_Config.cs"}, {"SourceFile": "JC_Homework_NoPost.cs"}, {"SourceFile": "JC_Leaves.cs"}, {"SourceFile": "JC_SchoolNotes.cs"}, {"SourceFile": "JC_Subjects.cs"}, {"SourceFile": "JC_TeacherTimetable.cs"}, {"SourceFile": "JC_TimetableConfig.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Site_Dictionary.cs"}, {"SourceFile": "wpsl_flows.cs"}, {"SourceFile": "wpsl_flow_checker.cs"}, {"SourceFile": "wpsl_flow_detail.cs"}, {"SourceFile": "xy_app_msg.cs"}, {"SourceFile": "xy_app_msg_cate.cs"}, {"SourceFile": "xy_app_msg_receive.cs"}, {"SourceFile": "xy_app_version.cs"}, {"SourceFile": "xy_Attachments.cs"}, {"SourceFile": "xy_Feedback.cs"}, {"SourceFile": "xy_scan.cs"}, {"SourceFile": "zhsz_device_prize.cs"}, {"SourceFile": "zhsz_prize.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.0.AssemblyAttributes.cs"}], "References": [{"Reference": "F:\\workstation\\ECB\\ECB-PC\\sourcecode\\ECB.PC\\ECB.PC.Model\\bin\\Debug\\ECB.PC.Model.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "F:\\workstation\\ECB\\ECB-PC\\sourcecode\\ECB.PC\\ECB.PC.Model\\bin\\Debug\\ECB.PC.Model.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\workstation\\ECB\\ECB-PC\\sourcecode\\ECB.PC\\lib\\Newtonsoft.Json.DLL", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.0\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\workstation\\ECB\\ECB-PC\\sourcecode\\ECB.PC\\lib\\YunEdu.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\workstation\\ECB\\ECB-PC\\bak\\YunEdu.DBUtility.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\workstation\\ECB\\ECB-PC\\sourcecode\\ECB.PC\\lib\\YunEdu.Model.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "F:\\workstation\\ECB\\ECB-PC\\sourcecode\\ECB.PC\\ECB.PC.BLL\\bin\\Debug\\ECB.PC.BLL.dll", "OutputItemRelativePath": "ECB.PC.BLL.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}