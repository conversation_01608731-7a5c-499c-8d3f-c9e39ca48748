﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:xy_scan
	/// </summary>
	public partial class xy_scan
	{
		public xy_scan()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid sid)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from xy_scan");
			strSql.Append(" where sid=@sid ");
			SqlParameter[] parameters = {
					new SqlParameter("@sid", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = sid;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.xy_scan model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into xy_scan(");
			strSql.Append("sid,user_id,token,stype,s_time,c_time,isok,memo)");
			strSql.Append(" values (");
			strSql.Append("@sid,@user_id,@token,@stype,@s_time,@c_time,@isok,@memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@sid", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@user_id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@token", SqlDbType.NVarChar,50),
					new SqlParameter("@stype", SqlDbType.NVarChar,50),
					new SqlParameter("@s_time", SqlDbType.DateTime),
					new SqlParameter("@c_time", SqlDbType.DateTime),
					new SqlParameter("@isok", SqlDbType.Int,4),
					new SqlParameter("@memo", SqlDbType.NVarChar,-1)};
			parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.user_id;
			parameters[2].Value = model.token;
			parameters[3].Value = model.stype;
			parameters[4].Value = model.s_time;
			parameters[5].Value = model.c_time;
			parameters[6].Value = model.isok;
			parameters[7].Value = model.memo;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.xy_scan model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update xy_scan set ");
			strSql.Append("user_id=@user_id,");
			strSql.Append("token=@token,");
			strSql.Append("stype=@stype,");
			strSql.Append("s_time=@s_time,");
			strSql.Append("c_time=@c_time,");
			strSql.Append("isok=@isok,");
			strSql.Append("memo=@memo");
			strSql.Append(" where sid=@sid ");
			SqlParameter[] parameters = {
					new SqlParameter("@user_id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@token", SqlDbType.NVarChar,50),
					new SqlParameter("@stype", SqlDbType.NVarChar,50),
					new SqlParameter("@s_time", SqlDbType.DateTime),
					new SqlParameter("@c_time", SqlDbType.DateTime),
					new SqlParameter("@isok", SqlDbType.Int,4),
					new SqlParameter("@memo", SqlDbType.NVarChar,-1),
					new SqlParameter("@sid", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.user_id;
			parameters[1].Value = model.token;
			parameters[2].Value = model.stype;
			parameters[3].Value = model.s_time;
			parameters[4].Value = model.c_time;
			parameters[5].Value = model.isok;
			parameters[6].Value = model.memo;
			parameters[7].Value = model.sid;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid sid)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from xy_scan ");
			strSql.Append(" where sid=@sid ");
			SqlParameter[] parameters = {
					new SqlParameter("@sid", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = sid;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string sidlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from xy_scan ");
			strSql.Append(" where sid in ("+sidlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.xy_scan GetModel(Guid sid)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 sid,user_id,token,stype,s_time,c_time,isok,memo from xy_scan ");
			strSql.Append(" where sid=@sid ");
			SqlParameter[] parameters = {
					new SqlParameter("@sid", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = sid;

			ECB.PC.Model.xy_scan model=new ECB.PC.Model.xy_scan();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.xy_scan DataRowToModel(DataRow row)
		{
			ECB.PC.Model.xy_scan model=new ECB.PC.Model.xy_scan();
			if (row != null)
			{
				if(row["sid"]!=null && row["sid"].ToString()!="")
				{
					model.sid= new Guid(row["sid"].ToString());
				}
				if(row["user_id"]!=null && row["user_id"].ToString()!="")
				{
					model.user_id= new Guid(row["user_id"].ToString());
				}
				if(row["token"]!=null)
				{
					model.token=row["token"].ToString();
				}
				if(row["stype"]!=null)
				{
					model.stype=row["stype"].ToString();
				}
				if(row["s_time"]!=null && row["s_time"].ToString()!="")
				{
					model.s_time=DateTime.Parse(row["s_time"].ToString());
				}
				if(row["c_time"]!=null && row["c_time"].ToString()!="")
				{
					model.c_time=DateTime.Parse(row["c_time"].ToString());
				}
				if(row["isok"]!=null && row["isok"].ToString()!="")
				{
					model.isok=int.Parse(row["isok"].ToString());
				}
				if(row["memo"]!=null)
				{
					model.memo=row["memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select sid,user_id,token,stype,s_time,c_time,isok,memo ");
			strSql.Append(" FROM xy_scan ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" sid,user_id,token,stype,s_time,c_time,isok,memo ");
			strSql.Append(" FROM xy_scan ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM xy_scan ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.sid desc");
			}
			strSql.Append(")AS Row, T.*  from xy_scan T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "xy_scan";
			parameters[1].Value = "sid";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

