﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_kq_inout_student_config
    /// </summary>
    public partial class ecb_kq_inout_student_config
    {
        public ecb_kq_inout_student_config()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_kq_inout_student_config");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_kq_inout_student_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_kq_inout_student_config(");
            strSql.Append("Id,ClassId,StartWeek,EndWeek,KQStage,BeginTime,EndTime,ColumnId,ColumnPath,KQType)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ClassId,@StartWeek,@EndWeek,@KQStage,@BeginTime,@EndTime,@ColumnId,@ColumnPath,@KQType)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StartWeek", SqlDbType.Int,4),
                    new SqlParameter("@EndWeek", SqlDbType.Int,4),
                    new SqlParameter("@KQStage", SqlDbType.Int,4),
                    new SqlParameter("@BeginTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@KQType",SqlDbType.Int,4)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ClassId;
            parameters[2].Value = model.StartWeek;
            parameters[3].Value = model.EndWeek;
            parameters[4].Value = model.KQStage;
            parameters[5].Value = model.BeginTime;
            parameters[6].Value = model.EndTime;
            parameters[7].Value = model.ColumnId;
            parameters[8].Value = model.ColumnPath;
            parameters[9].Value = model.KQType;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_kq_inout_student_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_kq_inout_student_config set ");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("StartWeek=@StartWeek,");
            strSql.Append("EndWeek=@EndWeek,");
            strSql.Append("KQStage=@KQStage,");
            strSql.Append("BeginTime=@BeginTime,");
            strSql.Append("EndTime=@EndTime,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("KQType=@Inout ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StartWeek", SqlDbType.Int,4),
                    new SqlParameter("@EndWeek", SqlDbType.Int,4),
                    new SqlParameter("@KQStage", SqlDbType.Int,4),
                    new SqlParameter("@BeginTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@KQType",SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ClassId;
            parameters[1].Value = model.StartWeek;
            parameters[2].Value = model.EndWeek;
            parameters[3].Value = model.KQStage;
            parameters[4].Value = model.BeginTime;
            parameters[5].Value = model.EndTime;
            parameters[6].Value = model.ColumnId;
            parameters[7].Value = model.ColumnPath;
            parameters[8].Value = model.KQType;
            parameters[9].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student_config ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_inout_student_config GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ClassId,StartWeek,EndWeek,KQStage,BeginTime,EndTime,ColumnId,ColumnPath,KQType from ecb_kq_inout_student_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_kq_inout_student_config model = new ECB.PC.Model.ecb_kq_inout_student_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_inout_student_config DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_kq_inout_student_config model = new ECB.PC.Model.ecb_kq_inout_student_config();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["StartWeek"] != null && row["StartWeek"].ToString() != "")
                {
                    model.StartWeek = int.Parse(row["StartWeek"].ToString());
                }
                if (row["EndWeek"] != null && row["EndWeek"].ToString() != "")
                {
                    model.EndWeek = int.Parse(row["EndWeek"].ToString());
                }
                if (row["KQStage"] != null && row["KQStage"].ToString() != "")
                {
                    model.KQStage = int.Parse(row["KQStage"].ToString());
                }
                if (row["KQType"] != null && row["KQType"].ToString() != "")
                {
                    model.KQType = int.Parse(row["KQType"].ToString());
                }
                if (row["BeginTime"] != null && row["BeginTime"].ToString() != "")
                {
                    model.BeginTime = DateTime.Parse(row["BeginTime"].ToString());
                }
                if (row["EndTime"] != null && row["EndTime"].ToString() != "")
                {
                    model.EndTime = DateTime.Parse(row["EndTime"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ClassId,StartWeek,EndWeek,KQStage,BeginTime,EndTime,ColumnId,ColumnPath,KQType ");
            strSql.Append(" FROM ecb_kq_inout_student_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ClassId,StartWeek,EndWeek,KQStage,BeginTime,EndTime,ColumnId,ColumnPath,KQType ");
            strSql.Append(" FROM ecb_kq_inout_student_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_kq_inout_student_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_kq_inout_student_config T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_inout_student_config";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DataSet GetModelbySchool(int ColumnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"declare @txtBeginDate_amat datetime
                                    declare @txtEndDate_amat datetime
                                    declare @txtBeginDate_amle datetime
                                    declare @txtEndDate_amle datetime
                                    declare @txtBeginDate_pmat datetime
                                    declare @txtEndDate_pmat datetime
                                    declare @txtBeginDate_pmle datetime
                                    declare @txtEndDate_pmle datetime
                                    declare @txtBeginDate_ntat datetime
                                    declare @txtEndDate_ntat datetime
                                    declare @txtBeginDate_ntle datetime
                                    declare @txtEndDate_ntle datetime
                                    declare @startweek int
                                    declare @endweek int ");
            strSql.AppendLine(@"select @txtBeginDate_amat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ColumnId=@ColumnId and KQType=1 and KQStage=1
                                        select @txtEndDate_amat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ColumnId=@ColumnId and KQType=1 and KQStage=1
                                        select @txtBeginDate_amle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                         where ColumnId=@ColumnId and KQType=2 and KQStage=1
                                        select @txtEndDate_amle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                         where ColumnId=@ColumnId and KQType=2 and KQStage=1
                                        select @txtBeginDate_pmat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                         where ColumnId=@ColumnId and KQType=1 and KQStage=2
                                        select @txtEndDate_pmat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ColumnId=@ColumnId and KQType=1 and KQStage=2
                                        select @txtBeginDate_pmle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ColumnId=@ColumnId and KQType=2 and KQStage=2
                                        select @txtEndDate_pmle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                         where ColumnId=@ColumnId and KQType=2 and KQStage=2
                                        select @txtBeginDate_ntat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ColumnId=@ColumnId and KQType=1 and KQStage=3
                                        select @txtEndDate_ntat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                         where ColumnId=@ColumnId and KQType=1 and KQStage=3
                                        select @txtBeginDate_ntle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                         where ColumnId=@ColumnId and KQType=2 and KQStage=3
                                        select @txtEndDate_ntle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ColumnId=@ColumnId and KQType=2 and KQStage=3
                                        select @endweek = max(EndWeek), @startweek = min(StartWeek) from ecb_kq_inout_student_config
                                        where ColumnId=@ColumnId ");
            strSql.AppendLine("select @txtBeginDate_amat as txtBeginDate_amat,@txtEndDate_amat as txtEndDate_amat ,@txtBeginDate_amle as txtBeginDate_amle ,@txtEndDate_amle as txtEndDate_amle,@txtBeginDate_pmat as txtBeginDate_pmat,@txtEndDate_pmat as txtEndDate_pmat ," +
                "@txtBeginDate_pmle as txtBeginDate_pmle, @txtEndDate_pmle as txtEndDate_pmle, @txtBeginDate_ntat as txtBeginDate_ntat, @txtEndDate_ntat as txtEndDate_ntat," +
                "@txtBeginDate_ntle as txtBeginDate_ntle, @txtEndDate_ntle as txtEndDate_ntle," +
                "@startweek as startweek, @endweek as endweek");
            SqlParameter[] parameters = {
             new SqlParameter("@ColumnId",SqlDbType.Int,4)};
            parameters[0].Value = ColumnId;

            return DbHelperSQL.Query(strSql.ToString(), parameters);

        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DataSet GetModelbyGrade(string gradeIds)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"declare @txtBeginDate_amat datetime
                                    declare @txtEndDate_amat datetime
                                    declare @txtBeginDate_amle datetime
                                    declare @txtEndDate_amle datetime
                                    declare @txtBeginDate_pmat datetime
                                    declare @txtEndDate_pmat datetime
                                    declare @txtBeginDate_pmle datetime
                                    declare @txtEndDate_pmle datetime
                                    declare @txtBeginDate_ntat datetime
                                    declare @txtEndDate_ntat datetime
                                    declare @txtBeginDate_ntle datetime
                                    declare @txtEndDate_ntle datetime
                                    declare @startweek int
                                    declare @endweek int ");
            strSql.AppendFormat(@"select @txtBeginDate_amat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=1 and KQStage=1
                                        select @txtEndDate_amat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=1 and KQStage=1
                                        select @txtBeginDate_amle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=2 and KQStage=1
                                        select @txtEndDate_amle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=2 and KQStage=1
                                        select @txtBeginDate_pmat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                          where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=1 and KQStage=2
                                        select @txtEndDate_pmat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=1 and KQStage=2
                                        select @txtBeginDate_pmle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=2 and KQStage=2
                                        select @txtEndDate_pmle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                          where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=2 and KQStage=2
                                        select @txtBeginDate_ntat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=1 and KQStage=3
                                        select @txtEndDate_ntat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=1 and KQStage=3
                                        select @txtBeginDate_ntle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=2 and KQStage=3
                                        select @txtEndDate_ntle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) and KQType=2 and KQStage=3
                                        select @endweek = max(EndWeek), @startweek = min(StartWeek) from ecb_kq_inout_student_config
                                         where ClassId in (select ID from JC_ClassInfos where GradeId in ({0}) ) ", gradeIds);
            strSql.AppendLine("select @txtBeginDate_amat as txtBeginDate_amat,@txtEndDate_amat as txtEndDate_amat ,@txtBeginDate_amle as txtBeginDate_amle ,@txtEndDate_amle as txtEndDate_amle,@txtBeginDate_pmat as txtBeginDate_pmat,@txtEndDate_pmat as txtEndDate_pmat ," +
                "@txtBeginDate_pmle as txtBeginDate_pmle, @txtEndDate_pmle as txtEndDate_pmle, @txtBeginDate_ntat as txtBeginDate_ntat, @txtEndDate_ntat as txtEndDate_ntat," +
                "@txtBeginDate_ntle as txtBeginDate_ntle, @txtEndDate_ntle as txtEndDate_ntle," +
                "@startweek as startweek, @endweek as endweek");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DataSet GetModelbyClass(Guid ClassId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"declare @txtBeginDate_amat datetime
                                    declare @txtEndDate_amat datetime
                                    declare @txtBeginDate_amle datetime
                                    declare @txtEndDate_amle datetime
                                    declare @txtBeginDate_pmat datetime
                                    declare @txtEndDate_pmat datetime
                                    declare @txtBeginDate_pmle datetime
                                    declare @txtEndDate_pmle datetime
                                    declare @txtBeginDate_ntat datetime
                                    declare @txtEndDate_ntat datetime
                                    declare @txtBeginDate_ntle datetime
                                    declare @txtEndDate_ntle datetime
                                    declare @startweek int
                                    declare @endweek int ");
            strSql.AppendLine(@"select @txtBeginDate_amat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=1 and KQStage=1
                                        select @txtEndDate_amat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=1 and KQStage=1
                                        select @txtBeginDate_amle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=2 and KQStage=1
                                        select @txtEndDate_amle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=2 and KQStage=1
                                        select @txtBeginDate_pmat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=1 and KQStage=2
                                        select @txtEndDate_pmat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=1 and KQStage=2
                                        select @txtBeginDate_pmle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=2 and KQStage=2
                                        select @txtEndDate_pmle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=2 and KQStage=2
                                        select @txtBeginDate_ntat =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=1 and KQStage=3
                                        select @txtEndDate_ntat =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=1 and KQStage=3
                                        select @txtBeginDate_ntle =  CONVERT(nvarchar(100),BeginTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=2 and KQStage=3
                                        select @txtEndDate_ntle =  CONVERT(nvarchar(100),EndTime,24) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId and KQType=2 and KQStage=3
                                        select @endweek = max(EndWeek), @startweek = min(StartWeek) from ecb_kq_inout_student_config
                                        where ClassId =@ClassId ");
            strSql.AppendLine("select @txtBeginDate_amat as txtBeginDate_amat,@txtEndDate_amat as txtEndDate_amat ,@txtBeginDate_amle as txtBeginDate_amle ,@txtEndDate_amle as txtEndDate_amle,@txtBeginDate_pmat as txtBeginDate_pmat,@txtEndDate_pmat as txtEndDate_pmat ," +
                "@txtBeginDate_pmle as txtBeginDate_pmle, @txtEndDate_pmle as txtEndDate_pmle, @txtBeginDate_ntat as txtBeginDate_ntat, @txtEndDate_ntat as txtEndDate_ntat," +
                "@txtBeginDate_ntle as txtBeginDate_ntle, @txtEndDate_ntle as txtEndDate_ntle," +
                "@startweek as startweek, @endweek as endweek");
            SqlParameter[] parameters = {
             new SqlParameter("@ClassId",SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = ClassId;

            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool DeletebySchool(int ColumnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student_config ");
            strSql.Append(" where ColumnId =@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = ColumnId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool DeletebyGrade(string gradeIds, int ColumnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student_config ");
            strSql.AppendFormat(" where ColumnId={1} and  ClassId in (select ID from JC_ClassInfos where GradeId in ({0})) ", gradeIds, ColumnId);
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), gradeIds);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool DeletebyClass(int ColumnId, Guid ClassId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student_config ");
            strSql.Append(" where ColumnId =@ColumnId and ClassId=@ClassId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
            new SqlParameter("@ClassId",SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = ColumnId;
            parameters[1].Value = ClassId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public int AddTranList(ArrayList list)
        {
            return DbHelperSQL.ExecuteSqlTran(list);
        }
        /// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetLeaveTimeList(Guid ClassId, int Weekday, DateTime newDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from (select Id,ClassId,StartWeek,EndWeek,KQStage,cast(CONVERT(nvarchar(100), CONVERT(datetime, '" + newDate + "'), 23) + ' ' + CONVERT(nvarchar(100), BeginTime, 24) as datetime) as BeginTime,cast(CONVERT(nvarchar(100), CONVERT(datetime, '" + newDate + "'), 23) + ' ' + CONVERT(nvarchar(100), EndTime, 24) as datetime) as EndTime,ColumnId,ColumnPath,KQType ");
            strSql.Append(" FROM ecb_kq_inout_student_config ");
            strSql.Append(" where  ClassId='" + ClassId + "'  AND StartWeek<='" + Weekday + "' AND EndWeek>='" + Weekday + "'  ) tt ORDER BY KQStage,KQType");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string fileName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  " + fileName);
            strSql.Append(" FROM ecb_kq_inout_student_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取考勤的阶段和类型列表
        /// </summary>
        /// <param name="columnId">学校id</param>
        /// <returns></returns>
        public DataSet GetKqStageAndTypeList(int columnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select distinct KQStage,KQType from ecb_kq_inout_student_config where ColumnId=@ColumnId order by KQStage,KQType");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 获取考勤的阶段和类型列表
        /// </summary>
        /// <param name="columnId">年级id</param>
        /// <returns></returns>
        public DataSet GetKqStageAndTypeList(Guid gradeId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("select distinct KQStage,KQType from ecb_kq_inout_student_config a");
            strSql.AppendLine("left join JC_ClassInfos b on a.ClassId=b.ID where b.GradeId=@GradeId");
            strSql.Append("order by KQStage,KQType");
            SqlParameter[] parameters = {
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = gradeId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 获取考勤的阶段和类型列表
        /// </summary>
        /// <param name="columnId">班级id</param>
        /// <returns></returns>
        public DataSet GetKqStageAndTypeListByClassId(Guid classId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("select distinct KQStage,KQType from ecb_kq_inout_student_config a where a.ClassId=@ClassId");
            strSql.Append("order by KQStage,KQType");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = classId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        #endregion  ExtensionMethod
    }
}

