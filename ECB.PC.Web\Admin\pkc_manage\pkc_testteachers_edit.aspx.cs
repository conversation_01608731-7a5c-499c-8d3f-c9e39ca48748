﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_testteachers_edit : YunEdu.Authority.AdminCommonJC
    {
        YunEdu.BLL.PKC_TestTeachers blltestteachers = new YunEdu.BLL.PKC_TestTeachers();
        YunEdu.Model.PKC_TestTeachers modeltestteachers = new YunEdu.Model.PKC_TestTeachers();

        YunEdu.BLL.UserInfos blluserinfos = new YunEdu.BLL.UserInfos();
        YunEdu.Model.UserInfos modeluserinfos = new YunEdu.Model.UserInfos();

        BLL.Site_Dictionary bllDictionary = new BLL.Site_Dictionary();
        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();

        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();

        string id = "";
        string _testid = "";
        protected void Page_Load(object sender, EventArgs e)
        {

            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
            }
            if (Request.QueryString["testid"] != null)
            {
                _testid = Request.QueryString["testid"].ToString();
            }
            if (!Page.IsPostBack)
            {

                if (!IsPostBack)
                {
                    InitControls();
                    DefaultDataBound();
                }
            }
        }
        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }

        private void DefaultDataBound()
        {
            if (id != "")
            {
                this.pnlxuanze.Visible = false;
                modeltestteachers = blltestteachers.GetModel(new Guid(id));
                //modeluserinfos = blluserinfos.GetModel(modeltestteachers.UserId);
                txtTeachers.Text = YunEdu.Common.GetRecordByPageOrder.GetModelField("UserInfos", "TName", "UserId='" + modeltestteachers.UserId.ToString() + "'");
                ddlPosition.SelectedValue = modeltestteachers.Position.ToString();
                //txtJianKaoCount.Text = modeltestteachers.JianKaoCount.ToString();
                //ddlRoomNum.SelectedValue = modeltestteachers.RoomId.ToString();
                //ckbIsEnabled.Checked = modeltestteachers.IsEnabled;
            }

        }
        private void InitControls()
        {
            DataTable data = null;
            data = bllDictionary.GetList("DictTypeId=78").Tables[0];
            GetRecordByPageOrder.BindDropDownList(ddlPosition, data, 78);
        }
        protected void btnAddTeacher_Click(object sender, EventArgs e)
        {

            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + _testid + "' and Position=1 and JianKaoCount is not null") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，无法修改!');");
                return;
            }
            YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
            YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();
            modeltests = blltests.GetModel(new Guid(_testid));
            //string[,] s = new string[1, 21];
            Guid guidTeacherId = Guid.Empty;
            string t = "add";
            int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + _testid + "'and PropertyCode=" + 1);
            int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + _testid + "' and PropertyCode=" + 2);
            if (id != "" && id != null)
            {
                t = "edit";
                modeltestteachers = blltestteachers.GetModel(new Guid(id));
            }
            else
            {
                guidTeacherId = Guid.NewGuid();
                modeltestteachers = blltestteachers.GetModel(guidTeacherId);
            }
            string _Position = this.ddlPosition.SelectedValue;

            //string _roomid = ddlRoomNum.SelectedValue.ToString();
            //if(!_roomid.Equals("0"))
            //{
            //    modeltestteachers.RoomId = new Guid(_roomid);
            //}     
            //modeltestteachers.JianKaoCount = int.Parse(txtJianKaoCount.Text);
            modeltestteachers.Position = int.Parse(_Position);

            if (t == "add")
            {
                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + _testid + "' and Position=1 and JianKaoCount is not null") != 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，无法修改!');");
                    return;
                }
                modeltestteachers.CreateTime = DateTime.Now;
                modeltestteachers.ColumnId = modelAreaUser.ColumnID;
                modeltestteachers.ColumnPath = modelAreaUser.ColumnPath;

                modeltestteachers.TestId = new Guid(_testid);
                string userid = YunEdu.Common.GetRecordByPageOrder.GetModelField("UserInfos", "UserID", "UserNo='" + this.hfldTeacher.Value + "'");
                modeltestteachers.UserId = new Guid(userid);
                modeltestteachers.SchoolYear = modeltests.SchoolYear;
                //blltestteachers.Add(modeltestteachers);
                if (blltestteachers.Add(modeltestteachers))
                {
                    MessageBox.ResponseScript(this, "layer.msg('添加成功!');Reload();");
                }
            }
            else
            {
                if (blltestteachers.Update(modeltestteachers))
                {
                    MessageBox.ResponseScript(this, "layer.msg('编辑成功!');Reload();");
                }
            }
        }
        //private void InitControls()
        //{
        //    //YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Rooms", "RoomId,RoomNum", "RoomNum", "RoomId", "TestId='" + _testid + "'", "RoomNum asc", ddlRoomNum, "0", true, "考场号");
        //}
    }
}