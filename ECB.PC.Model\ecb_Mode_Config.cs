﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 留言表
	/// </summary>
	[Serializable]
	public partial class ecb_Mode_Config
	{
		public ecb_Mode_Config()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _modeid;
		private int _showtype;
		private string _weekdays;
		private DateTime _statime;
		private DateTime _endtime;
		private string _classids;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区Id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 所属模式
		/// </summary>
		public Guid ModeId
		{
			set{ _modeid=value;}
			get{return _modeid;}
		}
		/// <summary>
		/// 模式显示类型 
		/// </summary>
		public int ShowType
		{
			set{ _showtype=value;}
			get{return _showtype;}
		}
		/// <summary>
		/// 周次
		/// </summary>
		public string WeekDays
		{
			set{ _weekdays=value;}
			get{return _weekdays;}
		}
		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime StaTime
		{
			set{ _statime=value;}
			get{return _statime;}
		}
		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 班级ID |分割
		/// </summary>
		public string ClassIds
		{
			set{ _classids=value;}
			get{return _classids;}
		}
		#endregion Model

	}
}

