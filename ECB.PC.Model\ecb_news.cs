﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 班牌新闻表
    /// </summary>
    [Serializable]
    public partial class ecb_news
    {
        public ecb_news()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private int _categoryid;
        private string _imgurl;
        private string _title;
        private string _zhaiyao;
        private string _content;
        private Guid _author;
        private DateTime _addtime;
        private Guid _updateuser;
        private DateTime? _updatetime;
        private int _sort;
        private int _ispublish;
        private int _istop;
        private int _isslide;
        private int _isshowecb;
        private int? _ispass;
        private Guid _ispassby;
        private DateTime? _ispassdate;
        /// <summary>
        /// 调用别名
        /// </summary>
        public string CallName { set; get; }
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 类别
        /// </summary>
        public int CategoryId
        {
            set { _categoryid = value; }
            get { return _categoryid; }
        }
        /// <summary>
        /// 封面图片
        /// </summary>
        public string ImgUrl
        {
            set { _imgurl = value; }
            get { return _imgurl; }
        }
        /// <summary>
        /// 标题
        /// </summary>
        public string Title
        {
            set { _title = value; }
            get { return _title; }
        }
        /// <summary>
        /// 摘要
        /// </summary>
        public string ZhaiYao
        {
            set { _zhaiyao = value; }
            get { return _zhaiyao; }
        }
        /// <summary>
        /// 内容
        /// </summary>
        public string Content
        {
            set { _content = value; }
            get { return _content; }
        }
        /// <summary>
        /// 作者
        /// </summary>
        public Guid Author
        {
            set { _author = value; }
            get { return _author; }
        }
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddTime
        {
            set { _addtime = value; }
            get { return _addtime; }
        }
        /// <summary>
        /// 更新人
        /// </summary>
        public Guid UpdateUser
        {
            set { _updateuser = value; }
            get { return _updateuser; }
        }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime
        {
            set { _updatetime = value; }
            get { return _updatetime; }
        }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort
        {
            set { _sort = value; }
            get { return _sort; }
        }
        /// <summary>
        /// 是否发布
        /// </summary>
        public int IsPublish
        {
            set { _ispublish = value; }
            get { return _ispublish; }
        }
        /// <summary>
        /// 是否置顶
        /// </summary>
        public int IsTop
        {
            set { _istop = value; }
            get { return _istop; }
        }
        /// <summary>
        /// 是否轮播
        /// </summary>
        public int IsSlide
        {
            set { _isslide = value; }
            get { return _isslide; }
        }
        /// <summary>
        /// 是否展示在班牌
        /// </summary>
        public int IsShowECB
        {
            set { _isshowecb = value; }
            get { return _isshowecb; }
        }
        /// <summary>
        /// 是否通过审核
        /// </summary>
        public int? IsPass
        {
            set { _ispass = value; }
            get { return _ispass; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid IsPassBy
        {
            set { _ispassby = value; }
            get { return _ispassby; }
        }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? IsPassDate
        {
            set { _ispassdate = value; }
            get { return _ispassdate; }
        }
        #endregion Model

    }
}

