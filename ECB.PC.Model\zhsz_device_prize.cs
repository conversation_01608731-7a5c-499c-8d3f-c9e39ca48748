﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 设备商品表
	/// </summary>
	[Serializable]
	public partial class zhsz_device_prize
	{
		public zhsz_device_prize()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _prizeid;
		private Guid _deviceid;
		private int _stocknum;
		private Guid _lasteditby;
		private DateTime _lastedittime;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 奖品id
		/// </summary>
		public Guid PrizeId
		{
			set{ _prizeid=value;}
			get{return _prizeid;}
		}
		/// <summary>
		/// 设备id
		/// </summary>
		public Guid DeviceId
		{
			set{ _deviceid=value;}
			get{return _deviceid;}
		}
		/// <summary>
		/// 库存数量
		/// </summary>
		public int StockNum
		{
			set{ _stocknum=value;}
			get{return _stocknum;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

