﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;


using System.Collections.Generic;
using System.Linq;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_FunctionRoom_Reservation
    /// </summary>
    public partial class ecb_FunctionRoom_Reservation
    {
        public ecb_FunctionRoom_Reservation()
        { }

        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_FunctionRoom_Reservation");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_FunctionRoom_Reservation model, bool heban, out string errorCode, out string errorMsg)
        {

            errorCode = "0";
            errorMsg = "";
            int rows = 0;
            using (SqlConnection conn = new SqlConnection(DbHelperSQL.ConnMain[0]))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    string beginTime = model.BeginTime.Value.ToString("yyyy-MM-dd HH: mm");
                    string endTime = model.EndTime.Value.ToString("yyyy-MM-dd HH: mm");
                    StringBuilder strSql = new StringBuilder();
                    // 查询选择的功能室，是否在暂停时间内
                    strSql.AppendFormat("select count(1) from ecb_FunctionRoom where not('{0}'<=CloseBeginTime or '{1}'> CloseEndTime)  and Status=2 and ID='{2}'", endTime, beginTime,model.FunctionRoomId);
                    PrepareCommand(cmd, conn, null, strSql.ToString(), null);
                    object obj = cmd.ExecuteScalar();
                    if (obj != null && int.Parse(obj.ToString()) > 0)
                    {
                        errorCode = "0";
                        errorMsg = "当前时间，所选功能室暂停使用！";
                        return false;
                    }

                    strSql.Clear();
                    // 查询预约时间里所有功能室预约记录
                    strSql.Append($"select * from ecb_FunctionRoom_Reservation ");
                    strSql.AppendFormat(" where not('{0}'<=BeginTime or '{1}'> EndTime) and Status not in ({2},{3})", endTime, beginTime,(int)BLL.CommonEnum.ReservationStatus.cancel,(int)BLL.CommonEnum.ReservationStatus.react);
                    PrepareCommand(cmd, conn, null, strSql.ToString(), null);
                    DataSet dsReservation = new DataSet();
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        try
                        {
                            da.Fill(dsReservation, "ds");
                            cmd.Parameters.Clear();
                        }
                        catch (SqlException ex)
                        {
                        }
                    }
                    if (dsReservation.Tables.Count > 0 && dsReservation.Tables[0].Rows.Count > 0)
                    {
                        // 有预约记录
                        // 1.判断是否有预约其他功能室
                        DataRow[] records = dsReservation.Tables[0].Select($"FunctionRoomId<>'{model.FunctionRoomId}'");
                        if (records.Length > 0)
                        {
                            if (model.ReservationType == 1)
                            {
                                // 之前预约的班级
                                List<string> recordClassIdList = new List<string>();
                                foreach (var item in records)
                                {
                                    recordClassIdList.AddRange(item["ClassIds"].ToString().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries));
                                }
                                // 当前预约的班级 
                                string[] currentClassIdList = model.ClassIds.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
                                // 取两个的交集
                                IEnumerable<string> intersectList = recordClassIdList.Intersect(currentClassIdList);
                                if (intersectList.Count() > 0)
                                {
                                    errorCode = "1";
                                    errorMsg = "当前时间，所选班级已预约了其他功能室！";
                                    return false;
                                }
                            }
                            else
                            {
                                //个人
                                records = dsReservation.Tables[0].Select($"FunctionRoomId<>'{model.FunctionRoomId}' and CreateUserId='{model.CreateUserId}'");
                                if (records.Length > 0)
                                {
                                    errorCode = "1";
                                    errorMsg = "当前时间，你已预约了其他功能室！";
                                    return false;
                                }
                            }
                        }
                        // 2.找到当前预约功能室的记录，时间不一致的
                        records = dsReservation.Tables[0].Select($"FunctionRoomId='{model.FunctionRoomId}' and ('{beginTime}'<>BeginTime or '{endTime}'<>EndTime)");
                        if (records.Length > 0)
                        {
                            errorCode = "2";
                            errorMsg = "当前时间已有其他预约记录(时间不符)，请重新预约！";
                            return false;
                        }
                        else
                        {
                            // 找到当前预约功能室的记录，时间一致的,课程不一样的
                            records = dsReservation.Tables[0].Select($"FunctionRoomId='{model.FunctionRoomId}' and BeginTime='{beginTime}' and EndTime='{endTime}'", "ReservationType");
                            if (records.Length > 0)
                            {
                                // 个人预约，不能重复预约
                                if (model.ReservationType == 2)
                                {
                                    errorCode = "2";
                                    errorMsg = "当前时间已有其他预约记录，请勿重复预约！";
                                    return false;
                                }
                                //班级预约，判断课程及教师信息是否一致
                                string ReservationType = records[0]["ReservationType"].ToString();
                                if (ReservationType == "1" && (records[0]["SubjectCode"].ToString() != model.SubjectCode || records[0]["TeacherId"].ToString() != model.TeacherId.ToString())) // 判断课程、教师是否一致
                                {
                                    // 课程、教师不一致返回
                                    errorCode = "2";
                                    errorMsg = "当前时间已有其他预约记录(课程或教师信息不符)，请重新预约！";
                                    return false;
                                }
                                else
                                {
                                    // 之前预约的班级
                                    List<string> recordClassIdList = new List<string>();
                                    foreach (var item in records)
                                    {
                                        recordClassIdList.AddRange(item["ClassIds"].ToString().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries));
                                    }
                                    // 当前预约的班级 
                                    string[] currentClassIdList = model.ClassIds.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
                                    // 取两个的交集
                                    IEnumerable<string> intersectList = recordClassIdList.Intersect(currentClassIdList);
                                    if (intersectList.Count() > 0)
                                    {
                                        errorCode = "2";
                                        errorMsg = "所选班级已有预约记录，请勿重复预约！";
                                        return false;
                                    }
                                    //课程、教师一致,合班
                                    if (!heban)
                                    {
                                        // 进行合并提示
                                        errorCode = "3";
                                        errorMsg = "当前功能室已有其他预约记录，是否进行合班上课？";
                                        return false;
                                    }
                                }
                            }
                        }
                    }


                    strSql.Clear();
                    strSql.Append("insert into ecb_FunctionRoom_Reservation(");
                    strSql.Append("Id,ColumnId,ColumnPath,FunctionRoomId,FlowId,FlowCode,ReservationType,ClassIds,ReservationDate,ClassNum,BeginTime,EndTime,SubjectName,Equipment,Reason,Description,CreateUserId,CreateTime,Status,LastEditUserId,LastEditTime,TeacherId,SubjectCode)");
                    strSql.Append(" values (");
                    strSql.Append("@Id,@ColumnId,@ColumnPath,@FunctionRoomId,@FlowId,@FlowCode,@ReservationType,@ClassIds,@ReservationDate,@ClassNum,@BeginTime,@EndTime,@SubjectName,@Equipment,@Reason,@Description,@CreateUserId,@CreateTime,@Status,@LastEditUserId,@LastEditTime,@TeacherId,@SubjectCode)");
                    SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@FunctionRoomId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@ReservationType", SqlDbType.Int,4),
                    new SqlParameter("@ClassIds", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ReservationDate", SqlDbType.Date,3),
                    new SqlParameter("@ClassNum", SqlDbType.Int,4),
                    new SqlParameter("@BeginTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@SubjectName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Equipment", SqlDbType.NVarChar,200),
                    new SqlParameter("@Reason", SqlDbType.NVarChar,200),
                    new SqlParameter("@Description", SqlDbType.NVarChar,200),
                    new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@LastEditUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,50)};
                    parameters[0].Value = model.Id;
                    parameters[1].Value = model.ColumnId;
                    parameters[2].Value = model.ColumnPath;
                    parameters[3].Value = model.FunctionRoomId;
                    parameters[4].Value = model.FlowId;
                    parameters[5].Value = model.FlowCode;
                    parameters[6].Value = model.ReservationType;
                    parameters[7].Value = model.ClassIds;
                    parameters[8].Value = model.ReservationDate;
                    parameters[9].Value = model.ClassNum;
                    parameters[10].Value = model.BeginTime;
                    parameters[11].Value = model.EndTime;
                    parameters[12].Value = model.SubjectName;
                    parameters[13].Value = model.Equipment;
                    parameters[14].Value = model.Reason;
                    parameters[15].Value = model.Description;
                    parameters[16].Value = model.CreateUserId;
                    parameters[17].Value = model.CreateTime;
                    parameters[18].Value = model.Status;
                    parameters[19].Value = model.LastEditUserId;
                    parameters[20].Value = model.LastEditTime;
                    parameters[21].Value = model.TeacherId;
                    parameters[22].Value = model.SubjectCode;

                    PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
                    rows = cmd.ExecuteNonQuery();
                    cmd.Parameters.Clear();
                    tx.Commit();
                }
                catch (SqlException)
                {
                    errorCode = "9";
                    errorMsg = "提交保存失败，请稍后重试！";
                    rows = 0;
                    tx.Rollback();
                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                }
            }
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public bool Update(ECB.PC.Model.ecb_FunctionRoom_Reservation model, bool heban, out string errorCode, out string errorMsg)
        {

            errorCode = "0";
            errorMsg = "";
            int rows = 0;
            using (SqlConnection conn = new SqlConnection(DbHelperSQL.ConnMain[0]))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    string beginTime = model.BeginTime.Value.ToString("yyyy-MM-dd HH: mm");
                    string endTime = model.EndTime.Value.ToString("yyyy-MM-dd HH: mm");
                    StringBuilder strSql = new StringBuilder();
                    // 查询选择的功能室，是否在暂停时间内
                    strSql.AppendFormat("select count(1) from ecb_FunctionRoom where not('{0}'<=CloseBeginTime or '{1}'> CloseEndTime)  and Status=2 and ID='{2}'", endTime, beginTime, model.FunctionRoomId);
                    PrepareCommand(cmd, conn, null, strSql.ToString(), null);
                    object obj = cmd.ExecuteScalar();
                    if (obj != null && int.Parse(obj.ToString()) > 0)
                    {
                        errorCode = "0";
                        errorMsg = "当前时间，所选功能室暂停使用！";
                        return false;
                    }

                    strSql.Clear();
                    // 查询预约时间里所有功能室预约记录
                    strSql.Append($"select * from ecb_FunctionRoom_Reservation ");
                    strSql.AppendFormat(" where not('{0}'<=BeginTime or '{1}'> EndTime) and Status not in ({2},{3}) and Id<>'{4}'", endTime, beginTime, (int)BLL.CommonEnum.ReservationStatus.cancel, (int)BLL.CommonEnum.ReservationStatus.react, model.Id);
                    PrepareCommand(cmd, conn, null, strSql.ToString(), null);
                    DataSet dsReservation = new DataSet();
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        try
                        {
                            da.Fill(dsReservation, "ds");
                            cmd.Parameters.Clear();
                        }
                        catch (SqlException ex)
                        {
                        }
                    }
                    if (dsReservation.Tables.Count > 0 && dsReservation.Tables[0].Rows.Count > 0)
                    {
                        // 有预约记录
                        // 1.判断是否有预约其他功能室
                        DataRow[] records = dsReservation.Tables[0].Select($"FunctionRoomId<>'{model.FunctionRoomId}'");
                        if (records.Length > 0)
                        {
                            if (model.ReservationType == 1)
                            {
                                // 之前预约的班级
                                List<string> recordClassIdList = new List<string>();
                                foreach (var item in records)
                                {
                                    recordClassIdList.AddRange(item["ClassIds"].ToString().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries));
                                }
                                // 当前预约的班级 
                                string[] currentClassIdList = model.ClassIds.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
                                // 取两个的交集
                                IEnumerable<string> intersectList = recordClassIdList.Intersect(currentClassIdList);
                                if (intersectList.Count() > 0)
                                {
                                    errorCode = "1";
                                    errorMsg = "当前时间，所选班级已预约了其他功能室！";
                                    return false;
                                }
                            }
                            else
                            {
                                //个人
                                records = dsReservation.Tables[0].Select($"FunctionRoomId<>'{model.FunctionRoomId}' and CreateUserId='{model.CreateUserId}'");
                                if (records.Length > 0)
                                {
                                    errorCode = "1";
                                    errorMsg = "当前时间，你已预约了其他功能室！";
                                    return false;
                                }
                            }
                        }
                        //else
                        //{
                        // 找到当前预约功能室的记录，时间不一致的
                        records = dsReservation.Tables[0].Select($"FunctionRoomId='{model.FunctionRoomId}' and ('{beginTime}'<>BeginTime or '{endTime}'<>EndTime)");
                        if (records.Length > 0)
                        {
                            errorCode = "2";
                            errorMsg = "当前时间已有其他预约记录(时间不符)，请重新预约！";
                            return false;
                        }
                        else
                        {
                            // 找到当前预约功能室的记录，时间一致的,课程不一样的
                            records = dsReservation.Tables[0].Select($"FunctionRoomId='{model.FunctionRoomId}' and BeginTime='{beginTime}' and EndTime='{endTime}'", "ReservationType");
                            if (records.Length > 0)
                            {
                                // 个人预约，不能重复预约
                                if (model.ReservationType == 2)
                                {
                                    errorCode = "2";
                                    errorMsg = "当前时间已有其他预约记录，请勿重复预约！";
                                    return false;
                                }
                                //班级预约，判断课程及教师信息是否一致
                                string ReservationType = records[0]["ReservationType"].ToString();
                                if (ReservationType == "1" && (records[0]["SubjectCode"].ToString() != model.SubjectCode || records[0]["TeacherId"].ToString() != model.TeacherId.ToString())) // 判断课程、教师是否一致
                                {
                                    // 课程、教师不一致返回
                                    errorCode = "2";
                                    errorMsg = "当前时间已有其他预约记录(课程或教师信息不符)，请重新预约！";
                                    return false;
                                }
                                else
                                {
                                    // 之前预约的班级
                                    List<string> recordClassIdList = new List<string>();
                                    foreach (var item in records)
                                    {
                                        recordClassIdList.AddRange(item["ClassIds"].ToString().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries));
                                    }
                                    // 当前预约的班级 
                                    string[] currentClassIdList = model.ClassIds.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
                                    // 取两个的交集
                                    IEnumerable<string> intersectList = recordClassIdList.Intersect(currentClassIdList);
                                    if (intersectList.Count() > 0)
                                    {
                                        errorCode = "2";
                                        errorMsg = "所选班级已有预约记录，请勿重复预约！";
                                        return false;
                                    }
                                    //课程、教师一致,合班
                                    if (!heban)
                                    {
                                        // 进行合并提示
                                        errorCode = "3";
                                        errorMsg = "当前功能室已有其他预约记录，是否进行合班上课？";
                                        return false;
                                    }
                                }
                            }
                        }
                    }

                    strSql.Clear();
                    strSql.Append("update ecb_FunctionRoom_Reservation set ");
                    strSql.Append("ColumnId=@ColumnId,");
                    strSql.Append("ColumnPath=@ColumnPath,");
                    strSql.Append("FunctionRoomId=@FunctionRoomId,");
                    strSql.Append("FlowId=@FlowId,");
                    strSql.Append("FlowCode=@FlowCode,");
                    strSql.Append("ReservationType=@ReservationType,");
                    strSql.Append("ClassIds=@ClassIds,");
                    strSql.Append("ReservationDate=@ReservationDate,");
                    strSql.Append("ClassNum=@ClassNum,");
                    strSql.Append("BeginTime=@BeginTime,");
                    strSql.Append("EndTime=@EndTime,");
                    strSql.Append("SubjectName=@SubjectName,");
                    strSql.Append("Equipment=@Equipment,");
                    strSql.Append("Reason=@Reason,");
                    strSql.Append("Description=@Description,");
                    strSql.Append("CreateUserId=@CreateUserId,");
                    strSql.Append("CreateTime=@CreateTime,");
                    strSql.Append("Status=@Status,");
                    strSql.Append("LastEditUserId=@LastEditUserId,");
                    strSql.Append("LastEditTime=@LastEditTime,");
                    strSql.Append("TeacherId=@TeacherId,");
                    strSql.Append("SubjectCode=@SubjectCode");
                    strSql.Append(" where Id=@Id ");
                    SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@FunctionRoomId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@ReservationType", SqlDbType.Int,4),
                    new SqlParameter("@ClassIds", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ReservationDate", SqlDbType.Date,3),
                    new SqlParameter("@ClassNum", SqlDbType.Int,4),
                    new SqlParameter("@BeginTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@SubjectName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Equipment", SqlDbType.NVarChar,200),
                    new SqlParameter("@Reason", SqlDbType.NVarChar,200),
                    new SqlParameter("@Description", SqlDbType.NVarChar,200),
                    new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@LastEditUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,50),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
                    parameters[0].Value = model.ColumnId;
                    parameters[1].Value = model.ColumnPath;
                    parameters[2].Value = model.FunctionRoomId;
                    parameters[3].Value = model.FlowId;
                    parameters[4].Value = model.FlowCode;
                    parameters[5].Value = model.ReservationType;
                    parameters[6].Value = model.ClassIds;
                    parameters[7].Value = model.ReservationDate;
                    parameters[8].Value = model.ClassNum;
                    parameters[9].Value = model.BeginTime;
                    parameters[10].Value = model.EndTime;
                    parameters[11].Value = model.SubjectName;
                    parameters[12].Value = model.Equipment;
                    parameters[13].Value = model.Reason;
                    parameters[14].Value = model.Description;
                    parameters[15].Value = model.CreateUserId;
                    parameters[16].Value = model.CreateTime;
                    parameters[17].Value = model.Status;
                    parameters[18].Value = model.LastEditUserId;
                    parameters[19].Value = model.LastEditTime;
                    parameters[20].Value = model.TeacherId;
                    parameters[21].Value = model.SubjectCode;
                    parameters[22].Value = model.Id;


                    PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
                    rows = cmd.ExecuteNonQuery();
                    cmd.Parameters.Clear();
                    tx.Commit();
                }
                catch (SqlException)
                {
                    errorCode = "9";
                    errorMsg = "修改失败，请稍后重试！";
                    rows = 0;
                    tx.Rollback();
                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                }
            }
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_FunctionRoom_Reservation model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_FunctionRoom_Reservation set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("FunctionRoomId=@FunctionRoomId,");
            strSql.Append("FlowId=@FlowId,");
            strSql.Append("FlowCode=@FlowCode,");
            strSql.Append("ReservationType=@ReservationType,");
            strSql.Append("ClassIds=@ClassIds,");
            strSql.Append("ReservationDate=@ReservationDate,");
            strSql.Append("ClassNum=@ClassNum,");
            strSql.Append("BeginTime=@BeginTime,");
            strSql.Append("EndTime=@EndTime,");
            strSql.Append("SubjectName=@SubjectName,");
            strSql.Append("Equipment=@Equipment,");
            strSql.Append("Reason=@Reason,");
            strSql.Append("Description=@Description,");
            strSql.Append("CreateUserId=@CreateUserId,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("Status=@Status,");
            strSql.Append("LastEditUserId=@LastEditUserId,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("TeacherId=@TeacherId,");
            strSql.Append("SubjectCode=@SubjectCode");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@FunctionRoomId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@ReservationType", SqlDbType.Int,4),
                    new SqlParameter("@ClassIds", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ReservationDate", SqlDbType.Date,3),
                    new SqlParameter("@ClassNum", SqlDbType.Int,4),
                    new SqlParameter("@BeginTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@SubjectName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Equipment", SqlDbType.NVarChar,200),
                    new SqlParameter("@Reason", SqlDbType.NVarChar,200),
                    new SqlParameter("@Description", SqlDbType.NVarChar,200),
                    new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@LastEditUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,50),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.FunctionRoomId;
            parameters[3].Value = model.FlowId;
            parameters[4].Value = model.FlowCode;
            parameters[5].Value = model.ReservationType;
            parameters[6].Value = model.ClassIds;
            parameters[7].Value = model.ReservationDate;
            parameters[8].Value = model.ClassNum;
            parameters[9].Value = model.BeginTime;
            parameters[10].Value = model.EndTime;
            parameters[11].Value = model.SubjectName;
            parameters[12].Value = model.Equipment;
            parameters[13].Value = model.Reason;
            parameters[14].Value = model.Description;
            parameters[15].Value = model.CreateUserId;
            parameters[16].Value = model.CreateTime;
            parameters[17].Value = model.Status;
            parameters[18].Value = model.LastEditUserId;
            parameters[19].Value = model.LastEditTime;
            parameters[20].Value = model.TeacherId;
            parameters[21].Value = model.SubjectCode;
            parameters[22].Value = model.Id;
            

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_FunctionRoom_Reservation ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_FunctionRoom_Reservation ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_FunctionRoom_Reservation GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,FunctionRoomId,FlowId,FlowCode,ReservationType,ClassIds,ReservationDate,ClassNum,BeginTime,EndTime,SubjectName,Equipment,Reason,Description,CreateUserId,CreateTime,Status,LastEditUserId,LastEditTime,TeacherId,SubjectCode from ecb_FunctionRoom_Reservation ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_FunctionRoom_Reservation model = new ECB.PC.Model.ecb_FunctionRoom_Reservation();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_FunctionRoom_Reservation DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_FunctionRoom_Reservation model = new ECB.PC.Model.ecb_FunctionRoom_Reservation();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["FunctionRoomId"] != null && row["FunctionRoomId"].ToString() != "")
                {
                    model.FunctionRoomId = new Guid(row["FunctionRoomId"].ToString());
                }
                if (row["FlowId"] != null && row["FlowId"].ToString() != "")
                {
                    model.FlowId = new Guid(row["FlowId"].ToString());
                }
                if (row["FlowCode"] != null)
                {
                    model.FlowCode = row["FlowCode"].ToString();
                }
                if (row["ReservationType"] != null && row["ReservationType"].ToString() != "")
                {
                    model.ReservationType = int.Parse(row["ReservationType"].ToString());
                }
                if (row["ClassIds"] != null)
                {
                    model.ClassIds = row["ClassIds"].ToString();
                }
                if (row["ReservationDate"] != null && row["ReservationDate"].ToString() != "")
                {
                    model.ReservationDate = DateTime.Parse(row["ReservationDate"].ToString());
                }
                if (row["ClassNum"] != null && row["ClassNum"].ToString() != "")
                {
                    model.ClassNum = int.Parse(row["ClassNum"].ToString());
                }
                if (row["BeginTime"] != null && row["BeginTime"].ToString() != "")
                {
                    model.BeginTime = DateTime.Parse(row["BeginTime"].ToString());
                }
                if (row["EndTime"] != null && row["EndTime"].ToString() != "")
                {
                    model.EndTime = DateTime.Parse(row["EndTime"].ToString());
                }
                if (row["SubjectName"] != null)
                {
                    model.SubjectName = row["SubjectName"].ToString();
                }
                if (row["Equipment"] != null)
                {
                    model.Equipment = row["Equipment"].ToString();
                }
                if (row["Reason"] != null)
                {
                    model.Reason = row["Reason"].ToString();
                }
                if (row["Description"] != null)
                {
                    model.Description = row["Description"].ToString();
                }
                if (row["CreateUserId"] != null && row["CreateUserId"].ToString() != "")
                {
                    model.CreateUserId = new Guid(row["CreateUserId"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["Status"] != null && row["Status"].ToString() != "")
                {
                    model.Status = int.Parse(row["Status"].ToString());
                }
                if (row["LastEditUserId"] != null && row["LastEditUserId"].ToString() != "")
                {
                    model.LastEditUserId = new Guid(row["LastEditUserId"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
                if (row["TeacherId"] != null && row["TeacherId"].ToString() != "")
                {
                    model.TeacherId = new Guid(row["TeacherId"].ToString());
                }
                if (row["SubjectCode"] != null)
                {
                    model.SubjectCode = row["SubjectCode"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,FunctionRoomId,FlowId,FlowCode,ReservationType,ClassIds,ReservationDate,ClassNum,BeginTime,EndTime,SubjectName,Equipment,Reason,Description,CreateUserId,CreateTime,Status,LastEditUserId,LastEditTime,TeacherId,SubjectCode ");
            strSql.Append(" FROM ecb_FunctionRoom_Reservation ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string filed, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            if (filed.Trim() != "")
            {
                strSql.Append(" "+filed);
            }
            else {
                strSql.Append(" Id,ColumnId,ColumnPath,FunctionRoomId,FlowId,FlowCode,ReservationType,ClassIds,ReservationDate,ClassNum,BeginTime,EndTime,SubjectName,Equipment,Reason,Description,CreateUserId,CreateTime,Status,LastEditUserId,LastEditTime,TeacherId,SubjectCode ");
            }
            strSql.Append(" FROM ecb_FunctionRoom_Reservation ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_FunctionRoom_Reservation ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_FunctionRoom_Reservation T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_FunctionRoom_Reservation";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod
        /// <summary>
        /// 通过更新状态
        /// </summary>
        public bool UpdateReservation(ECB.PC.Model.ecb_FunctionRoom_Reservation model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_FunctionRoom_Reservation set ");
            strSql.Append("Status=@Status");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.Status;
            parameters[1].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                int rowsAffected = 0;
                SqlParameter[] parameters1 = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
                parameters1[0].Value = model.Id;

                DbHelperSQL.RunProcedure("up_attendance_function_pre", parameters1, out rowsAffected);
                if (rowsAffected > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        private static void PrepareCommand(SqlCommand cmd, SqlConnection conn, SqlTransaction trans, string cmdText, SqlParameter[] cmdParms)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            cmd.Connection = conn;
            cmd.CommandText = cmdText;
            if (trans != null)
                cmd.Transaction = trans;
            cmd.CommandType = CommandType.Text;//cmdType;
            if (cmdParms != null)
            {
                foreach (SqlParameter parameter in cmdParms)
                {
                    if ((parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Input) &&
                        (parameter.Value == null))
                    {
                        parameter.Value = DBNull.Value;
                    }
                    cmd.Parameters.Add(parameter);
                }
            }
        }
        /// <summary>
        /// 获得预约信息
        /// </summary>
        public DataSet GetResInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.Id,b.RoomName,convert(nvarchar(10),a.ReservationDate,23) ReservationDate,case when ReservationType=1 then (convert(nvarchar(5),BeginTime,8)+'-'+convert(nvarchar(5),EndTime,8)+'（第'+CAST( a.ClassNum as nvarchar)+'节）') else convert(nvarchar(5), BeginTime, 8) + ' - ' + convert(nvarchar(5), EndTime, 8) end time,c.CName,c.MobilePhone,a.CreateUserId FROM ecb_FunctionRoom_Reservation a left join ecb_FunctionRoom b on a.FunctionRoomId = b.ID left join aspnet_Membership c on a.CreateUserId = c.UserId ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 年级使用次数统计
        /// </summary>
        /// <param name="functionRoomId">功能室id</param>
        /// <param name="beginDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public DataSet GetGradeUseStatistics(Guid functionRoomId, DateTime beginDate, DateTime endDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select c.ID,c.GradeName name,count(1) value from ecb_FunctionRoom_Reservation a ");
            strSql.Append("left join JC_ClassInfos b on charindex(cast(b.ID as nvarchar(256)),a.ClassIds)>0 ");
            strSql.Append("left join JC_GradeInfos c on b.GradeId=c.ID ");
            strSql.Append("where a.ReservationType=1");
            if (functionRoomId != Guid.Empty)
            {
                strSql.Append(" and FunctionRoomId=@FunctionRoomId");
            }
            strSql.Append($" and BeginTime>=@BeginDate and EndTime<@EndDate and Status=1 group by c.ID,c.GradeName");
            SqlParameter[] parameters = {
                    new SqlParameter("@FunctionRoomId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@BeginDate",SqlDbType.Date),
                    new SqlParameter("@EndDate",SqlDbType.Date)
            };
            parameters[0].Value = functionRoomId;
            parameters[1].Value = beginDate.Date;
            parameters[2].Value = endDate.AddDays(1).Date;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 各功能室，学生使用人次（考勤次数）
        /// </summary>
        /// <param name="functionRoomId">功能室id</param>
        /// <param name="beginDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public DataSet GetStudentUseStatistics(int columnId, Guid functionRoomId, DateTime beginDate, DateTime endDate)
        {
            StringBuilder strSql = new StringBuilder();
            if (endDate.Date >= DateTime.Now.Date)
            {
                strSql.AppendLine("SELECT c.ID,c.RoomName name,count(1) value");
                strSql.AppendLine("from ecb_kq_class_student a");
                strSql.AppendLine("cross apply openjson(a.KQRecords) with(ClassId uniqueidentifier, ClassNum INT,[Status] INT, BeginTime datetime,PlaceId uniqueidentifier) as b");
                strSql.AppendLine("inner join ecb_FunctionRoom c on b.PlaceId=c.PlaceId");
                strSql.Append("where a.ColumnId=@ColumnId");
                if (functionRoomId != Guid.Empty)
                {
                    strSql.Append(" and c.ID=@FunctionRoomId");
                }
                strSql.Append(" and a.RecordDate='" + DateTime.Now.Date.ToString("yyyy-MM-dd") + "'");
                strSql.AppendLine(" and c.IsReservation=1 and b.[Status]<=2");
                strSql.AppendLine("GROUP BY c.ID,c.RoomName ORDER BY c.ID,c.RoomName;");
            }
            strSql.AppendLine("SELECT c.ID,c.RoomName name,count(1) value");
            strSql.AppendLine("from ecb_kq_class_student_history a");
            strSql.AppendLine("cross apply openjson(a.KQRecords) with(ClassId uniqueidentifier, ClassNum INT,[Status] INT, BeginTime datetime,PlaceId uniqueidentifier) as b");
            strSql.AppendLine("inner join ecb_FunctionRoom c on b.PlaceId=c.PlaceId");
            strSql.Append("where a.ColumnId=@ColumnId");
            if (functionRoomId != Guid.Empty)
            {
                strSql.Append(" and c.ID=@FunctionRoomId");
            }
            strSql.Append(" and a.RecordDate>=@BeginDate and a.RecordDate<@EndDate");
            strSql.AppendLine(" and c.IsReservation=1 and b.[Status]<=2");
            strSql.AppendLine("GROUP BY c.ID,c.RoomName ORDER BY c.ID,c.RoomName");
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int,4),
                new SqlParameter("@FunctionRoomId", SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@BeginDate",SqlDbType.Date),
                new SqlParameter("@EndDate",SqlDbType.Date)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = functionRoomId;
            parameters[2].Value = beginDate.Date;
            parameters[3].Value = endDate.AddDays(1).Date;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        #endregion  ExtensionMethod
    }
}

