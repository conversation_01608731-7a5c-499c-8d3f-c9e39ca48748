﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 会议考勤
	/// </summary>
	[Serializable]
	public partial class ecb_kq_meeting
	{
		public ecb_kq_meeting()
		{ }
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _title;
		private DateTime _starttime;
		private DateTime _overtime;
		private DateTime _begintime;
		private DateTime _endtime;
		private string _deviceno;
		/// <summary>
		/// 主键
		/// </summary>
		public Guid Id
		{
			set { _id = value; }
			get { return _id; }
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set { _columnid = value; }
			get { return _columnid; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set { _columnpath = value; }
			get { return _columnpath; }
		}
		/// <summary>
		/// 会议/事件名称
		/// </summary>
		public string Title
		{
			set { _title = value; }
			get { return _title; }
		}
		/// <summary>
		/// 会议开始时间
		/// </summary>
		public DateTime StartTime
		{
			set { _starttime = value; }
			get { return _starttime; }
		}
		/// <summary>
		/// 会议结束时间
		/// </summary>
		public DateTime OverTime
		{
			set { _overtime = value; }
			get { return _overtime; }
		}
		/// <summary>
		/// 考勤开始时间
		/// </summary>
		public DateTime BeginTime
		{
			set { _begintime = value; }
			get { return _begintime; }
		}
		/// <summary>
		/// 考勤结束时间
		/// </summary>
		public DateTime EndTime
		{
			set { _endtime = value; }
			get { return _endtime; }
		}
		/// <summary>
		/// 考勤设备编号
		/// </summary>
		public string DeviceNo
		{
			set { _deviceno = value; }
			get { return _deviceno; }
		}
		#endregion Model

	}
}