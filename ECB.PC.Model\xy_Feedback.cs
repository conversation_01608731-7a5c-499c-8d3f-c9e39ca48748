﻿using System;

namespace ECB.PC.Model
{
    /// <summary>
    /// xy_Feedback:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class xy_Feedback
    {
        public xy_Feedback()
        { }
        #region Model
        private Guid _id;
        private string _deviceinfo;
        private string _content;
        private string _contact;
        private DateTime? _creattime;
        private int _score;
        private Guid _creatuser;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string deviceInfo
        {
            set { _deviceinfo = value; }
            get { return _deviceinfo; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string content
        {
            set { _content = value; }
            get { return _content; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string contact
        {
            set { _contact = value; }
            get { return _contact; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? CreatTime
        {
            set { _creattime = value; }
            get { return _creattime; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int Score
        {
            set { _score = value; }
            get { return _score; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid CreatUser
        {
            set { _creatuser = value; }
            get { return _creatuser; }
        }
        #endregion Model

    }
}
