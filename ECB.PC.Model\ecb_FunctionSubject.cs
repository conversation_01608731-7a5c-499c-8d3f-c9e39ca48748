﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 功能室课程指定
    /// </summary>
    [Serializable]
    public partial class ecb_FunctionSubject
    {
        public ecb_FunctionSubject()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _roomid;
        private Guid _classid;
        private string _subjectcode;
        private DateTime _statime;
        private DateTime _endtime;
        private Guid _createuserid;
        private DateTime _createtime;
        private int _weekday;
        private int _number;
        /// <summary>
        /// 
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 功能室ID
        /// </summary>
        public Guid RoomId
        {
            set { _roomid = value; }
            get { return _roomid; }
        }
        /// <summary>
        /// 班级ID
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 科目Code
        /// </summary>
        public string SubjectCode
        {
            set { _subjectcode = value; }
            get { return _subjectcode; }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StaTime
        {
            set { _statime = value; }
            get { return _statime; }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime
        {
            set { _endtime = value; }
            get { return _endtime; }
        }
        /// <summary>
        /// 操作人
        /// </summary>
        public Guid CreateUserId
        {
            set { _createuserid = value; }
            get { return _createuserid; }
        }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime CreateTime
        {
            set { _createtime = value; }
            get { return _createtime; }
        }
        /// <summary>
        /// 周次
        /// </summary>
        public int WeekDay
        {
            set { _weekday = value; }
            get { return _weekday; }
        }
        /// <summary>
        /// 节次
        /// </summary>
        public int Number
        {
            set { _number = value; }
            get { return _weekday; }
        }
        #endregion Model

    }
}

