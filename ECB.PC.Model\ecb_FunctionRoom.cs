﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 功能室
    /// </summary>
    [Serializable]
    public partial class ecb_FunctionRoom
    {
        public ecb_FunctionRoom()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private string _roomname;
        private string _roomaddress;
        private string _imgurl;
        private Guid _placeid;
        private string _categorycode;
        private int _status;
        private DateTime? _closebegintime;
        private DateTime? _closeendtime;
        private int? _orderid;
        private int? _isreservation;
        /// <summary>
        /// 活动图片
        /// </summary>
        public string ActivityImgs { get; set; }
        /// <summary>
        /// 管理员
        /// </summary>
        public Guid ManageUserId { get; set; }
        /// <summary>
        /// 简介
        /// </summary>
        public string RoomDesc { get; set; }
        /// <summary>
        /// 操作视频
        /// </summary>
        public string OperateVideo { get; set; }
        /// <summary>
        /// 管理制度
        /// </summary>
        public string ManageRule { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 名称
        /// </summary>
        public string RoomName
        {
            set { _roomname = value; }
            get { return _roomname; }
        }
        /// <summary>
        /// 所在地址
        /// </summary>
        public string RoomAddress
        {
            set { _roomaddress = value; }
            get { return _roomaddress; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ImgUrl
        {
            set { _imgurl = value; }
            get { return _imgurl; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        /// <summary>
        /// 功能室分类
        /// </summary>
        public string CategoryCode
        {
            set { _categorycode = value; }
            get { return _categorycode; }
        }
        /// <summary>
        /// 功能室状态 1 正常使用 2暂停使用
        /// </summary>
        public int Status
        {
            set { _status = value; }
            get { return _status; }
        }
        /// <summary>
        /// 暂停使用开始时间
        /// </summary>
        public DateTime? CloseBeginTime
        {
            set { _closebegintime = value; }
            get { return _closebegintime; }
        }
        /// <summary>
        /// 暂停使用结束时间
        /// </summary>
        public DateTime? CloseEndTime
        {
            set { _closeendtime = value; }
            get { return _closeendtime; }
        }
        /// <summary>
        /// 排序
        /// </summary>
        public int? OrderId
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        /// <summary>
        /// 是否需要预约 0 不需要预约的 1需要预约的
        /// </summary>
        public int? IsReservation
        {
            set { _isreservation = value; }
            get { return _isreservation; }
        }
        #endregion Model

    }
}

