﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_kq_temp_record:临时考勤记录表
	/// </summary>
	[Serializable]
	public partial class ecb_kq_temp_record
	{
		public ecb_kq_temp_record()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _atteid;
		private Guid _userid;
		private DateTime? _signtime;
		private int _status;
		private int _brandstype;
		private Guid _placeid;
		private int _usertype;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 临时考勤id
		/// </summary>
		public Guid AtteId
		{
			set{ _atteid=value;}
			get{return _atteid;}
		}
		/// <summary>
		/// 考勤人员id
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 考勤时间
		/// </summary>
		public DateTime? SignTime
		{
			set{ _signtime=value;}
			get{return _signtime;}
		}
		/// <summary>
		/// 考勤状态(枚举:1正常 2迟到 3缺席)
		/// </summary>
		public int Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 设备类型
		/// </summary>
		public int BrandsType
		{
			set{ _brandstype=value;}
			get{return _brandstype;}
		}
		/// <summary>
		/// 考勤场地
		/// </summary>
		public Guid PlaceId
		{
			set{ _placeid=value;}
			get{return _placeid;}
		}
		/// <summary>
		/// 人员类型 1学生 2教师
		/// </summary>
		public int UserType
		{
			set { _usertype = value; }
			get { return _usertype; }
		}
		#endregion Model

	}
}

