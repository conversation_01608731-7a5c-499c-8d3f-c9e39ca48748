﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using YunEdu.Common;

namespace ECB.PC.BLL
{
    public class CommonEnum
    {
        /// <summary>
        /// 考勤状态
        /// </summary>
        public enum KQStatus
        {
            /// <summary>
            /// 正常
            /// </summary> 
            [EnumDescription("正常")]
            normal = 1,
            /// <summary>
            /// 迟到
            /// </summary> 
            [EnumDescription("迟到")]
            late = 2,
            /// <summary>
            /// 缺卡
            /// </summary> 
            [EnumDescription("缺卡")]
            absence = 3,
            /// <summary>
            /// 请假
            /// </summary> 
            [EnumDescription("请假")]
            leave = 4
        }
        public enum WeekDay
        {
            /// <summary>
            /// 星期一
            /// </summary>
            [EnumDescription("星期一")]
            Monday = 1,
            /// <summary>
            /// 星期二
            /// </summary>
            [EnumDescription("星期二")]
            Tuesday = 2,
            /// <summary>
            /// 星期三
            /// </summary>
            [EnumDescription("星期三")]
            Wednesday = 3,
            /// <summary>
            /// 星期四
            /// </summary>
            [EnumDescription("星期四")]
            Thursday = 4,
            /// <summary>
            /// 星期五
            /// </summary>
            [EnumDescription("星期五")]
            Friday = 5,
            /// <summary>
            /// 星期六
            /// </summary>
            [EnumDescription("星期六")]
            Saturday = 6,
            /// <summary>
            /// 星期天
            /// </summary>
            [EnumDescription("星期天")]
            Sunday = 7
        }
        /// <summary>
        /// 考勤类型
        /// </summary>
        public enum AttendanceType
        {
            /// <summary>
            /// 进校
            /// </summary>
            [EnumDescription("进校")]
            inschool = 1,
            /// <summary>
            /// 出校
            /// </summary>
            [EnumDescription("出校")]
            outschool = 2,
            /// <summary>
            /// 进班
            /// </summary>
            [EnumDescription("进班")]
            inclass = 3,
            /// <summary>
            /// 出班
            /// </summary>
            [EnumDescription("出班")]
            outclass = 4,
            /// <summary>
            /// 上车
            /// </summary>
            [EnumDescription("上车")]
            gotincar = 5,
            /// <summary>
            /// 下车
            /// </summary>
            [EnumDescription("下车")]
            gotoutcar = 6
        }
        #region 学校活跃指数得分类型
        public enum ecb_ScoreType
        {
            /// <summary>
            /// 风采
            /// </summary>
            [EnumDescription("风采")]
            Activity = 1,
            /// <summary>
            /// 荣誉
            /// </summary>
            [EnumDescription("荣誉")]
            Honors = 2,
            /// <summary>
            /// 作品
            /// </summary>
            [EnumDescription("作品")]
            Works = 3,
            /// <summary>
            /// 表扬
            /// </summary>
            [EnumDescription("表扬")]
            Praise = 4
        }
        #endregion
        /// <summary>
        /// 设备类型
        /// </summary>
        public enum DeviceType
        {
            /// <summary>
            /// 考勤机
            /// </summary> 
            [EnumDescription("考勤机")]
            attendance_machine = 1,
            /// <summary>
            /// 摄像头
            /// </summary> 
            [EnumDescription("摄像头")]
            camera = 2,
            /// <summary>
            /// 门禁
            /// </summary>
            [EnumDescription("门禁")]
            MenJin = 3,
            /// <summary>
            /// 海康摄像头
            /// </summary> 
            [EnumDescription("海康摄像头")]
            HKcamera = 4,
            /// <summary>
            /// 兑奖设备
            /// </summary> 
            [EnumDescription("兑奖设备")]
            RedemptionEquipment = 5,
            /// <summary>
            /// 其他
            /// </summary> 
            [EnumDescription("其他")]
            other = 99
        }
        /// <summary>
        /// 预约类型
        /// </summary>
        public enum ReservationType
        {
            /// <summary>
            /// 班级预约
            /// </summary> 
            [EnumDescription("班级预约")]
            class_reservation = 1,
            /// <summary>
            /// 个人预约
            /// </summary> 
            [EnumDescription("个人预约")]
            person_reservation = 2
        }
        /// <summary>
        /// 预约状态
        /// </summary>
        public enum ReservationStatus
        {
            /// <summary>
            /// 待审核
            /// </summary> 
            [EnumDescription("待审核")]
            wait = 0,
            /// <summary>
            /// 已通过
            /// </summary> 
            [EnumDescription("已通过")]
            pass = 1,
            /// <summary>
            /// 已驳回
            /// </summary> 
            [EnumDescription("已驳回")]
            react = 2,
            /// <summary>
            /// 已取消
            /// </summary> 
            [EnumDescription("已取消")]
            cancel = 9
        }
        /// <summary>
        /// 审批结果
        /// </summary>
        [EnumDescription("审批结果")]
        public enum CheckResult
        {
            /// <summary>
            /// 通过
            /// </summary>
            [EnumDescription("通过")]
            pass = 1,
            /// <summary>
            /// 驳回
            /// </summary>
            [EnumDescription("驳回")]
            reject = 2
        }
        #region 心里预约状态
        public enum XinLiReservationStatus
        {
            /// <summary>
            /// 审核中
            /// </summary>
            [EnumDescription("审核中")]
            WaitAudit = 1,
            /// <summary>
            /// 审核未通过
            /// </summary>
            [EnumDescription("审核未通过")]
            Back = 2,
            /// <summary>
            /// 审核超时
            /// </summary>
            [EnumDescription("审核超时")]
            Timeout = 3,
            /// <summary>
            /// 审核通过
            /// </summary>
            [EnumDescription("审核通过")]
            Pass = 4,
            /// <summary>
            /// 未签到
            /// </summary>
            [EnumDescription("未签到")]
            UnSignIn = 5,
            /// <summary>
            /// 签到成功
            /// </summary>
            [EnumDescription("签到成功")]
            SignIn = 6,
            /// <summary>
            /// 取消预约
            /// </summary>
            [EnumDescription("取消预约")]
            Cancel = 9
        }
        #endregion
        #region 教师办公室状态
        /// <summary>
        /// 教师办公室状态
        /// </summary>
        public enum OfficeStatus
        {
            /// <summary>
            /// 到校
            /// </summary>
            [EnumDescription("到校")]
            inschool = 1,
            /// <summary>
            /// 离校
            /// </summary>
            [EnumDescription("离校")]
            outschool = 2,
            /// <summary>
            /// 请假
            /// </summary>
            [EnumDescription("请假")]
            leave = 3,
            /// <summary>
            /// 外出
            /// </summary>
            [EnumDescription("外出")]
            go_out = 4,
            /// <summary>
            /// 上课
            /// </summary>
            [EnumDescription("上课")]
            attendclass = 5,
            /// <summary>
            /// 开会
            /// </summary>
            [EnumDescription("开会")]
            holdmeetin = 6,
            /// <summary>
            /// 休息
            /// </summary>
            [EnumDescription("休息")]
            rest = 7,
            /// <summary>
            /// 休假
            /// </summary>
            [EnumDescription("休假")]
            haveholiday = 8,
            /// <summary>
            /// 请勿打扰
            /// </summary>
            [EnumDescription("请勿打扰")]
            notdisturb = 9
        }
        #endregion
        #region 任务类型
        /// <summary>
        /// 任务类型
        /// </summary>
        [EnumDescription("任务类型")]
        public enum TaskType
        {
            /// <summary>
            /// 短信
            /// </summary>
            [EnumDescription("短信")]
            SMSPush = 1,
            /// <summary>
            /// 服务号推送
            /// </summary>
            [EnumDescription("服务号推送")]
            ServicePush = 2,
            /// <summary>
            /// 企业号推送
            /// </summary>
            [EnumDescription("企业号推送")]
            EnterprisePush = 3,
            /// <summary>
            /// 站内消息推送
            /// </summary>
            [EnumDescription("站内消息推送")]
            SitePush = 4,
            /// <summary>
            /// 脚本执行
            /// </summary>
            [EnumDescription("脚本执行")]
            ScriptExecution = 5,
            /// <summary>
            /// 设备请求指令,设备主动请求服务器获取指令
            /// </summary>
            [EnumDescription("设备请求指令")]
            DeviceRequestCmd = 6
        }
        #endregion
        #region 业务类型
        /// <summary>
        /// 任务类型
        /// </summary>
        [EnumDescription("业务类型")]
        public enum BusinessType
        {
            /// <summary>
            /// 请假推送
            /// </summary>
            [EnumDescription("请假推送")]
            LeavePush = 1,
            /// <summary>
            /// 请假修改办公状态
            /// </summary>
            [EnumDescription("请假修改办公状态")]
            LeaveToStatus = 2,
            /// <summary>
            /// 教师异常考勤推送
            /// </summary>
            [EnumDescription("教师异常考勤推送")]
            TeacherAttendancePush = 3,
            /// <summary>
            /// 学生异常考勤推送
            /// </summary>
            [EnumDescription("学生异常考勤推送")]
            StudentAttendancePush = 4,
            /// <summary>
            /// 中控公司门禁指令
            /// </summary>
            [EnumDescription("中控公司门禁指令")]
            ZhongKongMenJin = 5,
            /// <summary>
            /// 门禁未关门推送
            /// </summary>
            [EnumDescription("门禁未关门推送")]
            MenJinUnClosedPush = 6,
            /// <summary>
            /// 工资推送
            /// </summary>
            [EnumDescription("工资推送")]
            SalaryPush = 7,
            /// <summary>
            /// 成绩推送
            /// </summary>
            [EnumDescription("成绩推送")]
            ExamResultPush = 8,
            /// <summary>
            /// 作业推送
            /// </summary>
            [EnumDescription("作业推送")]
            HomeworkPush = 9,
            /// <summary>
            /// 心理档案系统预警推送
            /// </summary>
            [EnumDescription("心理档案系统预警推送")]
            PsychWarning = 10,
        }
        #endregion

        /// <summary>
        /// AI使用场景枚举
        /// </summary>
        public enum AiSceneType
        {
            /// <summary>
            /// 答疑解惑
            /// </summary>
            [EnumDescription("答疑")]
            DaYi = 1,
            /// <summary>
            /// 心理
            /// </summary>
            [EnumDescription("心理")]
            XinLi = 2
        }

        /// <summary>
        /// 下拉列表（绑定值为枚举值）
        /// </summary>
        /// <param name="DropDownList">单选按钮列表</param>
        /// <param name="obj">CodeTable</param>
        public static void DropDownList(DropDownList ddlListObject, Type obj)
        {
            ddlListObject.DataSource = EnumDescription.GetFieldTexts(obj);
            ddlListObject.DataValueField = "EnumValue";
            ddlListObject.DataTextField = "EnumDisplayText";
            ddlListObject.DataBind();
            ddlListObject.Items.Insert(0, new ListItem("==请选择==", ""));

        }
        /// <summary>
        /// 单选按钮（绑定值为枚举值）
        /// </summary>
        /// <param name="RadioButtonList">单选按钮列表</param>
        /// <param name="obj">CodeTable</param>
        public static void RadioButtonList(RadioButtonList RadioButtonListObject, Type obj)
        {
            RadioButtonListObject.DataSource = EnumDescription.GetFieldTexts(obj);
            RadioButtonListObject.DataValueField = "EnumValue";
            RadioButtonListObject.DataTextField = "EnumDisplayText";
            RadioButtonListObject.DataBind();


        }
        /// <summary>
        /// 获得考勤类型枚举
        /// </summary>
        /// <param name="Value">流转code</param>
        /// <returns></returns>
        public static string GetAttendanceTypeDescription(string Value)
        {
            AttendanceType _type = (AttendanceType)Enum.Parse(typeof(AttendanceType), Value);
            return YunEdu.Common.EnumDescription.GetDescription(_type);
        }
        #region 班牌类型
        public enum ecb_BrandType
        {
            /// <summary>
            /// 班牌
            /// </summary>
            [EnumDescription("班牌")]
            ClassBrand = 1,
            /// <summary>
            /// 功能室
            /// </summary>
            [EnumDescription("功能室")]
            Function = 2,
            /// <summary>
            /// 楼牌
            /// </summary>
            [EnumDescription("楼牌")]
            LouPai = 3,
            /// <summary>
            /// 看板
            /// </summary>
            [EnumDescription("看板")]
            KanBan = 4,
            /// <summary>
            /// 门卫牌
            /// </summary>
            [EnumDescription("门卫牌")]
            MenWei = 5,
            /// <summary>
            /// 校庆牌
            /// </summary>
            [EnumDescription("校庆牌")]
            XiaoQing = 6,
            /// <summary>
            /// 宿管牌
            /// </summary>
            [EnumDescription("宿管牌")]
            SH = 7,
            /// <summary>
            /// 宣传牌
            /// </summary>
            [EnumDescription("宣传牌")]
            XuanChuan = 8,
            /// <summary>
            /// 心理中心牌
            /// </summary>
            [EnumDescription("心理中心牌")]
            XinLiZhongXin = 9,
            /// <summary>
            /// 心理牌
            /// </summary>
            [EnumDescription("心理牌")]
            XinLi = 10,
            /// <summary>
            /// 心理管理看板
            /// </summary>
            [EnumDescription("心理管理看板")]
            XinLiKanBan = 11,
            /// <summary>
            /// 办公室牌
            /// </summary>
            [EnumDescription("办公室牌")]
            Office = 12
        }
        public static string Getecb_BrandType(string Value)
        {
            ecb_BrandType _status = (ecb_BrandType)Enum.Parse(typeof(ecb_BrandType), Value);
            return EnumDescription.GetDescription(_status);
        }
        #endregion
        /// <summary>
        /// 获得设备类型枚举
        /// </summary>
        /// <param name="Value">流转code</param>
        /// <returns></returns>
        public static string GetDeviceTypeDescription(string Value)
        {
            DeviceType _type = (DeviceType)Enum.Parse(typeof(DeviceType), Value);
            return YunEdu.Common.EnumDescription.GetDescription(_type);
        }
        /// <summary>
        /// 获得预约类型枚举描述
        /// </summary>
        /// <param name="Value">类型值</param>
        /// <returns></returns>
        public static string GetReservationTypeDescription(string Value)
        {
            ReservationType _type = (ReservationType)Enum.Parse(typeof(ReservationType), Value);
            return YunEdu.Common.EnumDescription.GetDescription(_type);
        }
        /// <summary>
        /// 获得预约状态枚举描述
        /// </summary>
        /// <param name="Value">状态值</param>
        /// <returns></returns>
        public static string GetReservationStatusDescription(string Value)
        {
            ReservationStatus _type = (ReservationStatus)Enum.Parse(typeof(ReservationStatus), Value);
            return YunEdu.Common.EnumDescription.GetDescription(_type);
        }
        /// <summary>
        /// 获得教师办公室状态枚举描述
        /// </summary>
        /// <param name="Value">类型值</param>
        /// <returns></returns>
        public static string GetOfficeStatusDescription(string Value)
        {
            OfficeStatus _type = (OfficeStatus)Enum.Parse(typeof(OfficeStatus), Value);
            return YunEdu.Common.EnumDescription.GetDescription(_type);
        }
        public enum WarningSourceType
        {
            /// <summary>
            /// 日常信息
            /// </summary>
            [EnumDescription("日常信息")]
            Info = 1,
            /// <summary>
            /// 心理测评
            /// </summary>
            [EnumDescription("心理测评")]
            Test = 2,
            /// <summary>
            /// 院校领导重点谈
            /// </summary>
            [EnumDescription("院校领导重点谈")]
            LeaderInterview = 31,
            /// <summary>
            /// 班主任/辅导员全面谈
            /// </summary>
            [EnumDescription("班主任/辅导员全面谈")]
            HeaderInterview = 32,
            /// <summary>
            /// 专任老师用心谈
            /// </summary>
            [EnumDescription("专任老师用心谈")]
            TeacherInterview = 33,
            /// <summary>
            /// 心理教师深入谈
            /// </summary>
            [EnumDescription("心理教师深入谈")]
            PsychInterview = 34,
            /// <summary>
            /// 家访
            /// </summary>
            [EnumDescription("家访")]
            FamilyInterview = 4,
            /// <summary>
            /// 周记异常反馈
            /// </summary>
            [EnumDescription("周记异常反馈")]
            Consult = 5
        }

        public enum activityType
        {
            /// <summary>
            /// 家长学校
            /// </summary>
            [EnumDescription("家长学校")]
            ParentSchool=1,
            /// <summary>
            /// 家长会
            /// </summary>
            [EnumDescription("家长会")]
            ParentMeeting=2,
            /// <summary>
            /// 万师润万心
            /// </summary>
            [EnumDescription("万师润万心")]
            WSRWX=3,
            /// <summary>
            /// 其它活动
            /// </summary>
            [EnumDescription("其它活动")]
            Other = 9
        }

        /// <summary>
        /// 获取枚举的描述
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="value">枚举的值</param>
        /// <returns>枚举值对应的描述</returns>
        /// <exception cref="ArgumentException"></exception>
        public static string GetEnumDescription<T>(string value)
        {
            T enumValue = (T)Enum.Parse(typeof(T), value);
            if (value == null)
            {
                throw new ArgumentException("value");
            }
            // 默认去枚举的字符串表达式
            string description = enumValue.ToString();
            // 获得字段信息
            FieldInfo field = enumValue.GetType().GetField(description);
            if (field == null)
            {
                return "";
            }
            // 取自定义特性的值
            EnumDescription[] array = (EnumDescription[])field.GetCustomAttributes(typeof(EnumDescription), inherit: false);
            if (array != null && array.Length != 0)
            {
                description = array[0].EnumDisplayText;
            }
            return description;
        }
    }
}
