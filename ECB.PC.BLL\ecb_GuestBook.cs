﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_GuestBook
    /// </summary>
    public partial class ecb_GuestBook
    {
        public ecb_GuestBook()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_GuestBook");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_GuestBook model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_GuestBook(");
            strSql.Append("ID,ColumnId,ColumnPath,SendUserId,ReceiveUserId,Content,SendTime,MsgType,TimeSpan)");
            strSql.Append(" values (");
            strSql.Append("@ID,@ColumnId,@ColumnPath,@SendUserId,@ReceiveUserId,@Content,@SendTime,@MsgType,@TimeSpan)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@SendUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ReceiveUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Content", SqlDbType.NVarChar,-1),
                    new SqlParameter("@SendTime", SqlDbType.DateTime),
                    new SqlParameter("@MsgType", SqlDbType.Int,4),
                    new SqlParameter("@TimeSpan", SqlDbType.Int,4)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.SendUserId;
            parameters[4].Value = model.ReceiveUserId;
            parameters[5].Value = model.Content;
            parameters[6].Value = model.SendTime;
            parameters[7].Value = model.MsgType;
            parameters[8].Value = model.TimeSpan;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_GuestBook model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_GuestBook set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("SendUserId=@SendUserId,");
            strSql.Append("ReceiveUserId=@ReceiveUserId,");
            strSql.Append("Content=@Content,");
            strSql.Append("SendTime=@SendTime,");
            strSql.Append("MsgType=@MsgType,");
            strSql.Append("TimeSpan=@TimeSpan");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@SendUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ReceiveUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Content", SqlDbType.NVarChar,-1),
                    new SqlParameter("@SendTime", SqlDbType.DateTime),
                    new SqlParameter("@MsgType", SqlDbType.Int,4),
                    new SqlParameter("@TimeSpan", SqlDbType.Int,4),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.SendUserId;
            parameters[3].Value = model.ReceiveUserId;
            parameters[4].Value = model.Content;
            parameters[5].Value = model.SendTime;
            parameters[6].Value = model.MsgType;
            parameters[7].Value = model.TimeSpan;
            parameters[8].Value = model.ID;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_GuestBook ");
            strSql.Append(" where ID=@ID ");
            //删除消息明细表
            strSql.Append(" delete from xy_app_msg_receive ");
            strSql.Append(" where msgId IN(select id from xy_app_msg where businessId=cast(@ID as nvarchar(100))) ");
            //删除消息表主表
            strSql.Append(" delete xy_app_msg  ");
            strSql.Append(" where businessId=cast(@ID as nvarchar(100)) ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_GuestBook ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_GuestBook GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,ColumnId,ColumnPath,SendUserId,ReceiveUserId,Content,SendTime,MsgType,TimeSpan from ecb_GuestBook ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.ecb_GuestBook model = new ECB.PC.Model.ecb_GuestBook();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_GuestBook DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_GuestBook model = new ECB.PC.Model.ecb_GuestBook();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["SendUserId"] != null && row["SendUserId"].ToString() != "")
                {
                    model.SendUserId = new Guid(row["SendUserId"].ToString());
                }
                if (row["ReceiveUserId"] != null && row["ReceiveUserId"].ToString() != "")
                {
                    model.ReceiveUserId = new Guid(row["ReceiveUserId"].ToString());
                }
                if (row["Content"] != null)
                {
                    model.Content = row["Content"].ToString();
                }
                if (row["SendTime"] != null && row["SendTime"].ToString() != "")
                {
                    model.SendTime = DateTime.Parse(row["SendTime"].ToString());
                }
                if (row["MsgType"] != null && row["MsgType"].ToString() != "")
                {
                    model.MsgType = int.Parse(row["MsgType"].ToString());
                }
                if (row["TimeSpan"] != null && row["TimeSpan"].ToString() != "")
                {
                    model.TimeSpan = int.Parse(row["TimeSpan"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,ColumnId,ColumnPath,SendUserId,ReceiveUserId,Content,SendTime,MsgType,TimeSpan ");
            strSql.Append(" FROM ecb_GuestBook ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,ColumnId,ColumnPath,SendUserId,ReceiveUserId,Content,SendTime,MsgType,TimeSpan ");
            strSql.Append(" FROM ecb_GuestBook ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_GuestBook ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_GuestBook T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_GuestBook";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 自定义 表格数据
        /// </summary>
        public DataSet GetList(string fileName, string tabName, string strWhere, string order)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select " + fileName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            if (order.Trim() != "")
            {
                strSql.Append(" ORDER BY  " + order);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

