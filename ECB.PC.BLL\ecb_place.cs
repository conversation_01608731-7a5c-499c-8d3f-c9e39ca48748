﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;
using System.Collections;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_place
    /// </summary>
    public partial class ecb_place
    {
        public ecb_place()
        { }
        #region  BasicMethod
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_place");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_place model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_place(");
            strSql.Append("Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@Code,@Name,@ShortName,@Location,@CategoryCode,@MaxNum)");
            strSql.Append(";select @@IDENTITY");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,200),
                    new SqlParameter("@Code", SqlDbType.NVarChar,20),
                    new SqlParameter("@Name", SqlDbType.NVarChar,50),
                    new SqlParameter("@ShortName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Location", SqlDbType.NVarChar,50),
                    new SqlParameter("@CategoryCode", SqlDbType.NVarChar,2),
                    new SqlParameter("@MaxNum", SqlDbType.Int,4)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.Code;
            parameters[4].Value = model.Name;
            parameters[5].Value = model.ShortName;
            parameters[6].Value = model.Location;
            parameters[7].Value = model.CategoryCode;
            parameters[8].Value = model.MaxNum;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_place model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_place set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Code=@Code,");
            strSql.Append("Name=@Name,");
            strSql.Append("ShortName=@ShortName,");
            strSql.Append("Location=@Location,");
            strSql.Append("CategoryCode=@CategoryCode,");
            strSql.Append("MaxNum=@MaxNum");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,200),
                    new SqlParameter("@Code", SqlDbType.NVarChar,20),
                    new SqlParameter("@Name", SqlDbType.NVarChar,50),
                    new SqlParameter("@ShortName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Location", SqlDbType.NVarChar,50),
                    new SqlParameter("@CategoryCode", SqlDbType.NVarChar,2),
                    new SqlParameter("@MaxNum", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.Code;
            parameters[3].Value = model.Name;
            parameters[4].Value = model.ShortName;
            parameters[5].Value = model.Location;
            parameters[6].Value = model.CategoryCode;
            parameters[7].Value = model.MaxNum;
            parameters[8].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_place ");
            strSql.Append(" where Code=@Code");
            SqlParameter[] parameters = {
                    new SqlParameter("@Code", SqlDbType.NVarChar,20)
            };
            parameters[0].Value = Code;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_place ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string SortIDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_place ");
            strSql.Append(" where SortID in (" + SortIDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_place GetModel(string Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,SortID from ecb_place ");
            strSql.Append(" where Code=@Code");
            SqlParameter[] parameters = {
                    new SqlParameter("@Code", SqlDbType.NVarChar,20)
            };
            parameters[0].Value = Code;

            Model.ecb_place model = new Model.ecb_place();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_place GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,SortID from ecb_place ");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = Id;

            Model.ecb_place model = new Model.ecb_place();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_place DataRowToModel(DataRow row)
        {
            Model.ecb_place model = new Model.ecb_place();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Code"] != null)
                {
                    model.Code = row["Code"].ToString();
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["ShortName"] != null)
                {
                    model.ShortName = row["ShortName"].ToString();
                }
                if (row["Location"] != null)
                {
                    model.Location = row["Location"].ToString();
                }
                if (row["CategoryCode"] != null)
                {
                    model.CategoryCode = row["CategoryCode"].ToString();
                }
                if (row["MaxNum"] != null && row["MaxNum"].ToString() != "")
                {
                    model.MaxNum = int.Parse(row["MaxNum"].ToString());
                }
                if (row["SortID"] != null && row["SortID"].ToString() != "")
                {
                    model.SortID = int.Parse(row["SortID"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,SortID ");
            strSql.Append(" FROM ecb_place ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,SortID ");
            strSql.Append(" FROM ecb_place ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere, string[] ConnStr=null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_place ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), ConnStr);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.SortID desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_place T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_place";
            parameters[1].Value = "SortID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        public DataSet GetAllList()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,SortID ");
            strSql.Append(" FROM ecb_place ");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 根据学校id获取班级的地点列表
        /// </summary>
        /// <param name="columnId"></param>
        /// <returns></returns>
        public DataSet GetClassPlaceList(string ColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,SortID ");
            strSql.Append(" FROM ecb_place a where a.ColumnPath+'|' like '" + ColumnPath + "|%' and a.CategoryCode='01' order by Name COLLATE Chinese_PRC_Stroke_CS_AS");
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 根据学校id获取地点列表
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <returns></returns>
        public DataSet GetPlaceList(int columnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,SortID ");
            strSql.Append(" FROM ecb_place a where a.ColumnId=@ColumnId  and Name <>'' order by a.Name");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int)
            };
            parameters[0].Value = columnId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        public int AddTranList(ArrayList list)
        {
            return DbHelperSQL.ExecuteSqlTran(list);
        }
        #endregion  ExtensionMethod
    }
}

