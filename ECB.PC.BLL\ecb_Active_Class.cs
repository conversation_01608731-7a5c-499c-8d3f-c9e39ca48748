﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_Active_Class
    /// </summary>
    public partial class ecb_Active_Class
    {
        public ecb_Active_Class()
        { }
        #region  BasicMethod



        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_Active_Class model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_Active_Class(");
            strSql.Append("Id,ClassId,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ClassId,@ColumnId,@ColumnPath,@ScoreType,@Score,@ContinueDays,@RecordDate)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ScoreType", SqlDbType.Int,4),
                    new SqlParameter("@Score", SqlDbType.Decimal,5),
                    new SqlParameter("@ContinueDays", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ClassId;
            parameters[2].Value = model.ColumnId;
            parameters[3].Value = model.ColumnPath;
            parameters[4].Value = model.ScoreType;
            parameters[5].Value = model.Score;
            parameters[6].Value = model.ContinueDays;
            parameters[7].Value = model.RecordDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_Active_Class model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Active_Class set ");
            strSql.Append("Id=@Id,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ScoreType=@ScoreType,");
            strSql.Append("Score=@Score,");
            strSql.Append("ContinueDays=@ContinueDays,");
            strSql.Append("RecordDate=@RecordDate");
            strSql.Append(" where ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ScoreType", SqlDbType.Int,4),
                    new SqlParameter("@Score", SqlDbType.Decimal,5),
                    new SqlParameter("@ContinueDays", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ClassId;
            parameters[2].Value = model.ColumnId;
            parameters[3].Value = model.ColumnPath;
            parameters[4].Value = model.ScoreType;
            parameters[5].Value = model.Score;
            parameters[6].Value = model.ContinueDays;
            parameters[7].Value = model.RecordDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete()
        {
            //该表无主键信息，请自定义主键/条件字段
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Active_Class ");
            strSql.Append(" where ");
            SqlParameter[] parameters = {
            };

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Active_Class GetModel()
        {
            //该表无主键信息，请自定义主键/条件字段
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ClassId,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate from ecb_Active_Class ");
            strSql.Append(" where ");
            SqlParameter[] parameters = {
            };

            ECB.PC.Model.ecb_Active_Class model = new ECB.PC.Model.ecb_Active_Class();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Active_Class DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_Active_Class model = new ECB.PC.Model.ecb_Active_Class();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ScoreType"] != null && row["ScoreType"].ToString() != "")
                {
                    model.ScoreType = int.Parse(row["ScoreType"].ToString());
                }
                if (row["Score"] != null && row["Score"].ToString() != "")
                {
                    model.Score = decimal.Parse(row["Score"].ToString());
                }
                if (row["ContinueDays"] != null && row["ContinueDays"].ToString() != "")
                {
                    model.ContinueDays = int.Parse(row["ContinueDays"].ToString());
                }
                if (row["RecordDate"] != null && row["RecordDate"].ToString() != "")
                {
                    model.RecordDate = DateTime.Parse(row["RecordDate"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ClassId,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate ");
            strSql.Append(" FROM ecb_Active_Class ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ClassId,ColumnId,ColumnPath,ScoreType,Score,ContinueDays,RecordDate ");
            strSql.Append(" FROM ecb_Active_Class ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Active_Class ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T. desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_Active_Class T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Active_Class";
			parameters[1].Value = "";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 班牌活跃指数
        /// </summary>
        /// <param name="model"></param>
        public void addActiveScore(Model.ecb_Active_Class model)
        {
            //获取今日活跃情况
            Model.ecb_Active_Class model_active_now = GetModelByType(model.ClassId, int.Parse(model.ScoreType.ToString()), Convert.ToDateTime(model.RecordDate));
            //获取昨天活跃情况
            Model.ecb_Active_Class model_active_pre = GetModelByType(model.ClassId, int.Parse(model.ScoreType.ToString()), Convert.ToDateTime(model.RecordDate).AddDays(-1));

            //判断该班级今天是否有得分--没有则添加
            if (model_active_now == null)
            {
                //判断昨天是否有得分(没有--直接加分)
                if (model_active_pre == null)
                {
                    Add(model);
                }
                //判断昨天是否有得分（有--加连续奖励分）
                else
                {
                    //连续天数大于7天则连续奖励分一直为7
                    if (model_active_pre.ContinueDays < 7)
                    {
                        model.ContinueDays = model_active_pre.ContinueDays + 1;
                    }
                    else
                    {
                        model.ContinueDays = 7;
                    }
                    model.Score = model.ContinueDays + 1;
                    Add(model);
                }
            }
        }
        /// <summary>
        /// 根据学校Id和类型编号得到一个对象实体
        /// </summary>
        public Model.ecb_Active_Class GetModelByType(Guid classId, int ScoreType, DateTime RecordDate)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 * from ecb_Active_Class ");
            strSql.Append(" where ClassId=@ClassId and ScoreType=@ScoreType and RecordDate=@RecordDate");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ScoreType", SqlDbType.Int,4),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = classId;
            parameters[1].Value = ScoreType;
            parameters[2].Value = RecordDate;

            Model.ecb_Active_School model = new Model.ecb_Active_School();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        #endregion  ExtensionMethod
    }
}

