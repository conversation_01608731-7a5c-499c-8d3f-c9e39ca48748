﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 重大生活事件
    /// </summary>
    [Serializable]
    public partial class psych_home_visit
    {
        public psych_home_visit()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private Guid _studentid;
        private string _sex;
        private int? _age;
        private string _contact;
        private string _classname;
        private string _homeaddress;
        private Guid _userid;
        private string _participants;
        private DateTime? _interviewtime;
        private string _homevisitmethod;
        private string _contents;
        private string _visitpic;
        private string _studentstatus;
        private string _purposeofvisit;
        private string _homevisitcontent;
        private string _liveandlearn;
        private string _jzyqhjy;
        private string _experience;
        private Guid _lasteditby;
        private DateTime? _lastedittime;
		/// <summary>
		/// 家访状态 0:取消 1:未家访 2:已家访
		/// </summary>
		public int Status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid StudentId
        {
            set { _studentid = value; }
            get { return _studentid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Sex
        {
            set { _sex = value; }
            get { return _sex; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int? Age
        {
            set { _age = value; }
            get { return _age; }
        }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string Contact
        {
            set { _contact = value; }
            get { return _contact; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ClassName
        {
            set { _classname = value; }
            get { return _classname; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string HomeAddress
        {
            set { _homeaddress = value; }
            get { return _homeaddress; }
        }
        /// <summary>
        /// 家访发起Id
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 参与人员
        /// </summary>
        public string Participants
        {
            set { _participants = value; }
            get { return _participants; }
        }
        /// <summary>
        /// 家访时间
        /// </summary>
        public DateTime? InterviewTime
        {
            set { _interviewtime = value; }
            get { return _interviewtime; }
        }
        /// <summary>
        /// 家访方式
        /// </summary>
        public string HomeVisitMethod
        {
            set { _homevisitmethod = value; }
            get { return _homevisitmethod; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string GJRecod
        {
            set { _contents = value; }
            get { return _contents; }
        }
        /// <summary>
        /// 访谈记录图片
        /// </summary>
        public string VisitPic
        {
            set { _visitpic = value; }
            get { return _visitpic; }
        }
        /// <summary>
        /// 学生情况
        /// </summary>
        public string StudentStatus
        {
            set { _studentstatus = value; }
            get { return _studentstatus; }
        }
        /// <summary>
        /// 家访目的
        /// </summary>
        public string PurposeOfVisit
        {
            set { _purposeofvisit = value; }
            get { return _purposeofvisit; }
        }
        /// <summary>
        /// 谈话内容
        /// </summary>
        public string HomeVisitContent
        {
            set { _homevisitcontent = value; }
            get { return _homevisitcontent; }
        }
        /// <summary>
        /// 学生在家学习和生活情况
        /// </summary>
        public string LiveAndLearn
        {
            set { _liveandlearn = value; }
            get { return _liveandlearn; }
        }
        /// <summary>
        /// 家长要求和建议
        /// </summary>
        public string JZYQHJY
        {
            set { _jzyqhjy = value; }
            get { return _jzyqhjy; }
        }
        /// <summary>
        /// 家访心得体会
        /// </summary>
        public string Experience
        {
            set { _experience = value; }
            get { return _experience; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid LastEditBy
        {
            set { _lasteditby = value; }
            get { return _lasteditby; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        /// <summary>
        /// 参与人员照片
        /// </summary>
        public string ParticipantsPic { get; set; }
        #endregion Model

    }
}