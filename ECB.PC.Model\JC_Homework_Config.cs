﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 作业收发指定人
	/// </summary>
	[Serializable]
	public partial class JC_Homework_Config
	{
		public JC_Homework_Config()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _gradeid;
		private Guid _classid;
		private Guid _userid;
		private string _subjectcode;
		private int _type;
		private Guid _lasteditor;
		private DateTime _lastedittime;
		/// <summary>
		/// 主键ID
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 年级ID
		/// </summary>
		public Guid GradeId
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 班级ID
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学生ID
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 科目Code
		/// </summary>
		public string SubjectCode
		{
			set{ _subjectcode=value;}
			get{return _subjectcode;}
		}
		/// <summary>
		/// 1收作业 2 发布作业
		/// </summary>
		public int Type
		{
			set{ _type=value;}
			get{return _type;}
		}
		/// <summary>
		/// 操作人
		/// </summary>
		public Guid LastEditor
		{
			set{ _lasteditor=value;}
			get{return _lasteditor;}
		}
		/// <summary>
		/// 操作时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

