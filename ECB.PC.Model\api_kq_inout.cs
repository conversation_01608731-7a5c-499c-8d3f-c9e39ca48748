﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// api_kq_inout:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class api_kq_inout
	{
		public api_kq_inout()
		{}
		#region Model
		private Guid _id;
		private string _cardno;
		private Guid _userid;
		private string _name;
		private string _idcardno;
		private string _attendancetype;
		private string _deviceno;
		private string _place;
		private int? _placetype;
		private DateTime? _readdatetime;
		private DateTime? _senddatetime;
		private int? _columnid;
		private int? _ispush;
		private string _company;
        public string ColumnPath;
        public Guid ClassId;
        public Guid GradeId;
        public string ClassName;
        public string SendResault;
        public string TeacherNo;
        /// <summary>
        /// 0:正常 1:异常
        /// </summary>
        public int AttendanceStatus{get;set;}
        /// <summary>
        /// 阶段1:上午 2:下午 3:晚上
        /// </summary>
        public int AttendStage { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 卡号
		/// </summary>
		public string CardNo
		{
			set{ _cardno=value;}
			get{return _cardno;}
		}
		/// <summary>
		/// 用户ID
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Name
		{
			set{ _name=value;}
			get{return _name;}
		}
		/// <summary>
		/// 身份证
		/// </summary>
		public string IdCardNo
		{
			set{ _idcardno=value;}
			get{return _idcardno;}
		}
		/// <summary>
		/// 进出状态(1:进 2:出)
		/// </summary>
		public string AttendanceType
		{
			set{ _attendancetype=value;}
			get{return _attendancetype;}
		}
		/// <summary>
		/// 设备编号
		/// </summary>
		public string DeviceNo
		{
			set{ _deviceno=value;}
			get{return _deviceno;}
		}
		/// <summary>
		/// 设备地点
		/// </summary>
		public string Place
		{
			set{ _place=value;}
			get{return _place;}
		}
		/// <summary>
		/// 地点类型(0 进出校，1宿舍)
		/// </summary>
		public int? PlaceType
		{
			set{ _placetype=value;}
			get{return _placetype;}
		}
		/// <summary>
		/// 监测时间
		/// </summary>
		public DateTime? ReadDateTime
		{
			set{ _readdatetime=value;}
			get{return _readdatetime;}
		}
		/// <summary>
		/// 推送时间
		/// </summary>
		public DateTime? SendDateTime
		{
			set{ _senddatetime=value;}
			get{return _senddatetime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 是否消息推送
		/// </summary>
		public int? IsPush
		{
			set{ _ispush=value;}
			get{return _ispush;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Company
		{
			set{ _company=value;}
			get{return _company;}
		}
		#endregion Model

	}
}

