﻿﻿
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
using System.Collections;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:custom_Working_record
	/// </summary>
	public partial class custom_Working_record
	{
		public custom_Working_record()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from custom_Working_record");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.custom_Working_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into custom_Working_record(");
			strSql.Append("ID,ColumnId,ColumnPath,UserId,WorkName,WorkContent,BeginDate,EndDate,Dept,DeptType,WeekNum,Mark,Createtime,Creator)");
			strSql.Append(" values (");
			strSql.Append("@ID,@ColumnId,@ColumnPath,@UserId,@WorkName,@WorkContent,@BeginDate,@EndDate,@Dept,@DeptType,@WeekNum,@Mark,@Createtime,@Creator)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@WorkName", SqlDbType.NVarChar,255),
					new SqlParameter("@WorkContent", SqlDbType.NVarChar,-1),
					new SqlParameter("@BeginDate", SqlDbType.DateTime),
					new SqlParameter("@EndDate", SqlDbType.DateTime),
					new SqlParameter("@Dept", SqlDbType.NVarChar,50),
					new SqlParameter("@DeptType", SqlDbType.NVarChar,50),
					new SqlParameter("@WeekNum", SqlDbType.Int,4),
					new SqlParameter("@Mark", SqlDbType.NVarChar,255),
					new SqlParameter("@Createtime", SqlDbType.DateTime),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ID;
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.UserId;
			parameters[4].Value = model.WorkName;
			parameters[5].Value = model.WorkContent;
			parameters[6].Value = model.BeginDate;
			parameters[7].Value = model.EndDate;
			parameters[8].Value = model.Dept;
			parameters[9].Value = model.DeptType;
			parameters[10].Value = model.WeekNum;
			parameters[11].Value = model.Mark;
			parameters[12].Value = model.Createtime;
			parameters[13].Value = model.Creator;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.custom_Working_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update custom_Working_record set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("UserId=@UserId,");
			strSql.Append("WorkName=@WorkName,");
			strSql.Append("WorkContent=@WorkContent,");
			strSql.Append("BeginDate=@BeginDate,");
			strSql.Append("EndDate=@EndDate,");
			strSql.Append("Dept=@Dept,");
			strSql.Append("DeptType=@DeptType,");
			strSql.Append("WeekNum=@WeekNum,");
			strSql.Append("Mark=@Mark,");
			strSql.Append("Createtime=@Createtime,");
			strSql.Append("Creator=@Creator");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@WorkName", SqlDbType.NVarChar,255),
					new SqlParameter("@WorkContent", SqlDbType.NVarChar,-1),
					new SqlParameter("@BeginDate", SqlDbType.DateTime),
					new SqlParameter("@EndDate", SqlDbType.DateTime),
					new SqlParameter("@Dept", SqlDbType.NVarChar,50),
					new SqlParameter("@DeptType", SqlDbType.NVarChar,50),
					new SqlParameter("@WeekNum", SqlDbType.Int,4),
					new SqlParameter("@Mark", SqlDbType.NVarChar,255),
					new SqlParameter("@Createtime", SqlDbType.DateTime),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.UserId;
			parameters[3].Value = model.WorkName;
			parameters[4].Value = model.WorkContent;
			parameters[5].Value = model.BeginDate;
			parameters[6].Value = model.EndDate;
			parameters[7].Value = model.Dept;
			parameters[8].Value = model.DeptType;
			parameters[9].Value = model.WeekNum;
			parameters[10].Value = model.Mark;
			parameters[11].Value = model.Createtime;
			parameters[12].Value = model.Creator;
			parameters[13].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Working_record ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Working_record ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Working_record GetModel(Guid ID)
		{

			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,ColumnId,ColumnPath,UserId,WorkName,WorkContent,BeginDate,EndDate,Dept,DeptType,WeekNum,Mark,Createtime,Creator from custom_Working_record ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.custom_Working_record model=new ECB.PC.Model.custom_Working_record();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Working_record DataRowToModel(DataRow row)
		{
			ECB.PC.Model.custom_Working_record model=new ECB.PC.Model.custom_Working_record();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["UserId"]!=null && row["UserId"].ToString()!="")
				{
					model.UserId= new Guid(row["UserId"].ToString());
				}
				if(row["WorkName"]!=null)
				{
					model.WorkName=row["WorkName"].ToString();
				}
				if(row["WorkContent"]!=null)
				{
					model.WorkContent=row["WorkContent"].ToString();
				}
				if(row["BeginDate"]!=null && row["BeginDate"].ToString()!="")
				{
					model.BeginDate=DateTime.Parse(row["BeginDate"].ToString());
				}
				if(row["EndDate"]!=null && row["EndDate"].ToString()!="")
				{
					model.EndDate=DateTime.Parse(row["EndDate"].ToString());
				}
				if(row["Dept"]!=null)
				{
					model.Dept=row["Dept"].ToString();
				}
				if(row["DeptType"]!=null)
				{
					model.DeptType=row["DeptType"].ToString();
				}
				if(row["WeekNum"]!=null && row["WeekNum"].ToString()!="")
				{
					model.WeekNum=int.Parse(row["WeekNum"].ToString());
				}
				if(row["Mark"]!=null)
				{
					model.Mark=row["Mark"].ToString();
				}
				if(row["Createtime"]!=null && row["Createtime"].ToString()!="")
				{
					model.Createtime=DateTime.Parse(row["Createtime"].ToString());
				}
				if(row["Creator"]!=null && row["Creator"].ToString()!="")
				{
					model.Creator= new Guid(row["Creator"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,ColumnId,ColumnPath,UserId,WorkName,WorkContent,BeginDate,EndDate,Dept,DeptType,WeekNum,Mark,Createtime,Creator ");
			strSql.Append(" FROM custom_Working_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,ColumnId,ColumnPath,UserId,WorkName,WorkContent,BeginDate,EndDate,Dept,DeptType,WeekNum,Mark,Createtime,Creator ");
			strSql.Append(" FROM custom_Working_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM custom_Working_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from custom_Working_record T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 添加工作记录到事务列表
		/// </summary>
		/// <param name="model">工作记录模型</param>
		/// <param name="sqlList">SQL事务列表</param>
		public void Add(ECB.PC.Model.custom_Working_record model, ArrayList sqlList)
		{
			string sql = $@"INSERT INTO custom_Working_record(ID,ColumnId,ColumnPath,UserId,WorkName,WorkContent,BeginDate,EndDate,Dept,DeptType,WeekNum,Mark,Createtime,Creator)
                           VALUES('{model.ID}',{model.ColumnId},'{model.ColumnPath}','{model.UserId}','{model.WorkName}','{model.WorkContent}','{model.BeginDate:yyyy-MM-dd HH:mm:ss}','{model.EndDate:yyyy-MM-dd HH:mm:ss}','{model.Dept}','{model.DeptType}',{model.WeekNum},'{model.Mark}','{model.Createtime:yyyy-MM-dd HH:mm:ss}','{model.Creator}')";
			sqlList.Add(sql);
		}

		/// <summary>
		/// 执行SQL事务
		/// </summary>
		/// <param name="sqlList">SQL语句列表</param>
		/// <returns>执行结果</returns>
		public bool ExecuteSqlTran(ArrayList sqlList)
		{
			return DbHelperSQL.ExecuteSqlTran(sqlList) > 0;
		}

		#endregion  ExtensionMethod
	}
}
