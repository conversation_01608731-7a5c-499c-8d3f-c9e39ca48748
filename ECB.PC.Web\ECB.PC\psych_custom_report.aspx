<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="psych_custom_report.aspx.cs" Inherits="ECB.PC.Web.ECB.PC.psych_custom_report" StylesheetTheme="Admin_Default" EnableEventValidation="false" %>

<%@ Register Assembly="XYArea" Namespace="XYArea.Class" TagPrefix="cc3" %>
<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>学生健康档案</title>
    <link href="../mp_images/font/iconfont.css" rel="stylesheet" />
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/layer/layer.js"></script>
    <style>
        .warning-tag { display: inline-block; padding: 2px 5px; color: white; border-radius: 3px; font-size: 12px; min-width: 48px; }
        .warning-level-3 { background-color: #ff0000; }
        .warning-level-2 { background-color: #ffa500; }
        .warning-level-1 { background-color: #005aff; }
        .warning-level-null { background-color: #979797; }

        .multiple-select { display: inline-block; position: relative; vertical-align: middle; margin-bottom: 3px; width: 240px; }
        .multiple-select__wrapper { box-sizing: border-box; display: flex; gap: 6px; padding: 4px 12px; min-height: 29px; line-height: 19px; align-items: center; border-radius: 4px; box-shadow: 0 0 0 1px #dcdfe6; }
        .multiple-select__selection { flex: 1; display: flex; gap: 6px; align-items: center; flex-wrap: wrap; }
        .multiple-select__stuffix .iconfont { color: #a8abb2; font-size: 12px; transform: rotate(90deg); transition: transform .3s; }
        .multiple-select__selected-item { user-select: none; }
        .multiple-tag { display: inline-flex; align-items: center; justify-content: center; white-space: nowrap; max-width: 150px; height: 21px; padding: 0 9px; background: #f4f4f5; color: #909399; font-size: 12px; }
        .multiple-tag .tag-content { min-width: 0; }
        .multiple-tag .tag-content span { display: block; overflow: hidden; text-overflow: ellipsis; }
        .multiple-select__dropdown { position: absolute; top: calc(100% + 10px); left: 50%; transform-origin: top; transform: translateX(-50%) scaleY(0); border: 1px solid #e4e7ed; z-index: 999; background: #FFF; transition: transform .3s; box-shadow: 0px 0px 12px rgba(0, 0, 0, .12); }
        .multiple-select__dropdown.open { transform: translateX(-50%) scaleY(1) }
        .multiple-select__dropdown-menu { min-width: 240px; }
        .multiple-select__dropdown-list { list-style: none; padding: 6px 0; margin: 0; box-sizing: border-box; }
        .multiple-select__dropdown-item { position: relative; font-size: 14px; padding: 0 32px 0 20px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; cursor: pointer; height: 30px; line-height: 30px; }
        .multiple-select__dropdown-item:hover { background: #f5f7fa; }
        .multiple-select__dropdown-item.selected { color: #409eff; }
        .multiple-select__dropdown-item .iconfont { position: absolute; opacity: 0; top: 50%; right: 20px; transform: translateY(-50%); }
        .multiple-select__dropdown-item.selected .iconfont { opacity: 1; }
        .arrow { position: absolute; top: -5px; left: 114px; }
        .arrow::after { position: absolute; width: 10px; height: 10px; content: ""; z-index: 1; transform: rotate(45deg); box-sizing: border-box; background: #fff; border: 1px solid #e4e7ed; border-bottom-color: transparent; border-right-color: transparent; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <div class="search-bar">
                <cc3:Area_Class ID="Area_Class" runat="server" Style="display: inline-block; vertical-align: middle;" IsTermEnable="false" />
                <span>姓名：</span>
                <asp:TextBox ID="txtKeyword" runat="server" placeholder="学生姓名" Width="150px"></asp:TextBox>
                <span>状态：</span>
                <asp:DropDownList ID="ddlStatus" runat="server">
                    <asp:ListItem Value="">==全部==</asp:ListItem>
                    <asp:ListItem Value="-1">未建档</asp:ListItem>
                    <asp:ListItem Value="0">所有关注</asp:ListItem>
                    <asp:ListItem Value="1">常规关注</asp:ListItem>
                    <asp:ListItem Value="2">重点关注</asp:ListItem>
                    <asp:ListItem Value="3">高关怀</asp:ListItem>
                </asp:DropDownList>
                <div id="colums" class="multiple-select">
                    <div class="multiple-select__wrapper">
                        <div class="multiple-select__selection">
                        </div>
                        <div class="multiple-select__stuffix">
                            <div class="iconfont icon-jinru"></div>
                        </div>
                    </div>
                    <div class="multiple-select__dropdown">
                        <div class="multiple-select__dropdown-menu">
                            <ul class="multiple-select__dropdown-list"></ul>
                        </div>
                        <span class="arrow"></span>
                    </div>
                </div>
                <asp:Button ID="btnSearch" runat="server" Text="查 询" ValidationGroup="1" CssClass="btn" OnClick="btnSearch_Click" />
            </div>
            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                <ContentTemplate>
                    <asp:GridView ID="gvInfo" runat="server" DataKeyNames="ID" AutoGenerateColumns="False" EmptyDataText="暂无数据!" RowStyle-HorizontalAlign="Center" OnRowDataBound="gvInfo_RowDataBound">
                        <Columns>
                            <asp:BoundField HeaderText="姓名" DataField="StudentName" ItemStyle-HorizontalAlign="left" />
                            <asp:BoundField HeaderText="学校" DataField="AreaName" ItemStyle-HorizontalAlign="center" />
                            <asp:BoundField HeaderText="班级" DataField="ClassName" ItemStyle-HorizontalAlign="center" />
                            <asp:TemplateField HeaderText="性别">
                                <ItemTemplate>
                                    <asp:Label ID="lblSex" runat="server" Text='<%# Eval("Sex") %>'></asp:Label>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="状态">
                                <ItemTemplate>
                                    <asp:Label ID="lblStatus" runat="server"></asp:Label>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center" />
                            </asp:TemplateField>
                        </Columns>
                        <RowStyle HorizontalAlign="Center"></RowStyle>
                    </asp:GridView>
                    <div class="paging-bar">
                        <div class="l-btns">
                            <span>显示</span><asp:TextBox ID="txtPageNum" runat="server"
                                AutoPostBack="True"
                                CssClass="pagenum" onkeydown="return checkNumber(event);"
                                OnTextChanged="txtPageNum_TextChanged"></asp:TextBox><span>条/页</span>
                        </div>
                        <webdiyer:AspNetPager ID="AspNetPager1" runat="server" AlwaysShow="True" AlwaysShowFirstLastPageNumber="True" CssClass="paging-default" CurrentPageButtonClass="current" CurrentPageButtonPosition="Center" CustomInfoHTML="&lt;span&gt;共%RecordCount%条记录&lt;/span&gt;" CustomInfoSectionWidth="" CustomInfoStyle="" CustomInfoTextAlign="NotSet" FirstPageText="首页" LastPageText="尾页" NavigationToolTipTextFormatString="{0}" NextPageText="下一页" NumericButtonCount="7" OnPageChanged="AspNetPager1_PageChanged" PageIndexBoxType="TextBox" PageSize="10" PagingButtonSpacing="0px" PrevPageText="上一页" ShowCustomInfoSection="Left" ShowFirstLast="False" ShowNavigationToolTip="True" ShowPageIndexBox="Never" UrlPaging="false">
                        </webdiyer:AspNetPager>
                    </div>
                </ContentTemplate>
                <Triggers>
                    <asp:AsyncPostBackTrigger ControlID="btnSearch" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="txtPageNum" EventName="TextChanged" />
                    <asp:AsyncPostBackTrigger ControlID="AspNetPager1" EventName="PageChanged" />
                </Triggers>
            </asp:UpdatePanel>
            <div class="footer-bar">
                <asp:HiddenField ID="hidColumns" runat="server" />
            </div>
        </div>
    </form>
    <script>
        function View(id) {
            var index = layer.open({
                type: 2,
                title: '学生健康成长档案',
                content: "/ECB.PC/psych_student_archive_edit.aspx?id=" + id,
                area: ['900px', '95%'],
            });
        }
        var multipleSelectData = [
            { label: '政治面貌', value: '1' },
            { label: '出生年月', value: '2' },
            { label: '民族', value: '3' },
            { label: '籍贯', value: '4' },
            { label: '性别', value: '5' },
            { label: '特长', value: '6' },
            { label: '家庭住址', value: '7' },
            { label: '学习情况', value: '8' },
            { label: '重大或慢性疾病', value: '9' },
            { label: '长期用药情况', value: '10' },
            { label: '体能限制', value: '11' },
            { label: '既往心理诊断', value: '12' },
            { label: '父母关系', value: '13' },
            { label: '家庭氛围', value: '14' }
        ];
        var selectedColumns = new Set();
        var isSelectShow = false;
        function initMultipleSelect() {
            if (!multipleSelectData || !multipleSelectData.length) {
                return;
            }
            multipleSelectData.forEach(item => {
                var itemDom = $('<li class="multiple-select__dropdown-item"><span>' + item.label + '</span><span class="iconfont icon-gou"></span></li>');
                itemDom.click(function () {
                    if (this.classList.contains('selected')) {
                        this.classList.remove('selected');
                        selectedColumns.delete(item.value);
                    }
                    else {
                        this.classList.add('selected');
                        selectedColumns.add(item.value);
                    }
                    if (selectedColumns.size) {
                        if (selectedColumns.size === 1) {
                            if (!$('.multiple-select__selected-item').length) {
                                $('<div class="multiple-select__selected-item"><span class="multiple-tag"><span class="tag-content"><span>' + item.label + '</span></span></span></div>').appendTo('.multiple-select__selection');
                            }
                        }
                        if (selectedColumns.size > 1) {
                            if ($('.multiple-select__tooltip').length) {
                                $('.multiple-select__tooltip .tag-content span').text('+ ' + selectedColumns.size);
                            }
                            else {
                                $('<div class="multiple-select__selected-item multiple-select__tooltip"><span class="multiple-tag"><span class="tag-content"><span>+ ' + selectedColumns.size + '</span></span></span></div>').appendTo('.multiple-select__selection');
                            }
                        }
                        else {
                            $('.multiple-select__tooltip').remove();
                        }
                    }
                    else {
                        $('.multiple-select__selection').empty();
                    }
                });
                itemDom.appendTo('#colums .multiple-select__dropdown-list');
            });
        }
        initMultipleSelect();
        $(function () {
            $('#colums .multiple-select__wrapper').click(function () {
                if (isSelectShow) {
                    $('.multiple-select__dropdown').removeClass('open');
                }
                else {
                    $('.multiple-select__dropdown').addClass('open');
                }
                isSelectShow = !isSelectShow;
            });
            $(document).click(function (e) {
                if (!$(e.target).closest('#colums').length) {
                    $('.multiple-select__dropdown').removeClass('open');
                    isSelectShow = false;
                }
                $('#btnSearch').click(function () {
                    $('#hidColumns').val(Array.from(selectedColumns).join(','));
                });
            });
        });
    </script>
</body>
</html>

