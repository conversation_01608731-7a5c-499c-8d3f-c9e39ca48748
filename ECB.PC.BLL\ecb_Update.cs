﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_Update
    /// </summary>
    public partial class ecb_Update
    {
        public ecb_Update()
        { }
        #region  BasicMethod



        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_Update model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_Update(");
            strSql.Append("VersionCode,UpdateContent,DownLoadUrl)");
            strSql.Append(" values (");
            strSql.Append("@VersionCode,@UpdateContent,@DownLoadUrl)");
            SqlParameter[] parameters = {
                    new SqlParameter("@VersionCode", SqlDbType.Int,4),
                    new SqlParameter("@UpdateContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@DownLoadUrl", SqlDbType.NVarChar,255)};
            parameters[0].Value = model.VersionCode;
            parameters[1].Value = model.UpdateContent;
            parameters[2].Value = model.DownLoadUrl;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_Update model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Update set ");
            strSql.Append("UpdateContent=@UpdateContent,");
            strSql.Append("DownLoadUrl=@DownLoadUrl");
            strSql.Append(" where VersionCode=@VersionCode");
            SqlParameter[] parameters = {
                    new SqlParameter("@VersionCode", SqlDbType.Int,4),
                    new SqlParameter("@UpdateContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@DownLoadUrl", SqlDbType.NVarChar,255)};
            parameters[0].Value = model.VersionCode;
            parameters[1].Value = model.UpdateContent;
            parameters[2].Value = model.DownLoadUrl;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int VersionCode)
        {
            //该表无主键信息，请自定义主键/条件字段
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Update ");
            strSql.Append(" where VersionCode=@VersionCode ");
            SqlParameter[] parameters = {
                new SqlParameter("@VersionCode", SqlDbType.Int,4)
            };
            parameters[0].Value = VersionCode;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Update GetModel(int VersionCode)
        {
            //该表无主键信息，请自定义主键/条件字段
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 VersionCode,UpdateContent,DownLoadUrl from ecb_Update ");
            strSql.Append(" where VersionCode=@VersionCode ");
            SqlParameter[] parameters = {
                new SqlParameter("@VersionCode", SqlDbType.Int,4)
            };
            parameters[0].Value = VersionCode;

            ECB.PC.Model.ecb_Update model = new ECB.PC.Model.ecb_Update();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Update DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_Update model = new ECB.PC.Model.ecb_Update();
            if (row != null)
            {
                if (row["VersionCode"] != null && row["VersionCode"].ToString() != "")
                {
                    model.VersionCode = int.Parse(row["VersionCode"].ToString());
                }
                if (row["UpdateContent"] != null)
                {
                    model.UpdateContent = row["UpdateContent"].ToString();
                }
                if (row["DownLoadUrl"] != null)
                {
                    model.DownLoadUrl = row["DownLoadUrl"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select VersionCode,UpdateContent,DownLoadUrl ");
            strSql.Append(" FROM ecb_Update ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" VersionCode,UpdateContent,DownLoadUrl ");
            strSql.Append(" FROM ecb_Update ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Update ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T. desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_Update T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Update";
			parameters[1].Value = "";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

