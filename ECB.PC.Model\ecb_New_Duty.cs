﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 值日表
	/// </summary>
	[Serializable]
	public partial class ecb_New_Duty
	{
		public ecb_New_Duty()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _gradeid;
		private Guid _classid;
		private Guid _studentid;
		private int _studentstatus;
		private string _studentremarks;
		private string _dutydate;
		private Guid _arrange;
		private DateTime _arrangetime;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 区域id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 区域路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 值日项目
		/// </summary>
		public Guid GradeId
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 学生状态 1.不能安排 2.已经安排
		/// </summary>
		public int StudentStatus
		{
			set{ _studentstatus=value;}
			get{return _studentstatus;}
		}
		/// <summary>
		/// 学生备注
		/// </summary>
		public string StudentRemarks
		{
			set{ _studentremarks=value;}
			get{return _studentremarks;}
		}
		/// <summary>
		/// 值日时间
		/// </summary>
		public string DutyDate
		{
			set{ _dutydate=value;}
			get{return _dutydate;}
		}
		/// <summary>
		/// 安排人
		/// </summary>
		public Guid Arrange
		{
			set{ _arrange=value;}
			get{return _arrange;}
		}
		/// <summary>
		/// 安排时间
		/// </summary>
		public DateTime ArrangeTime
		{
			set{ _arrangetime=value;}
			get{return _arrangetime;}
		}
		#endregion Model

	}
}

