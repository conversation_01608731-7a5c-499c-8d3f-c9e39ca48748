﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_Duty
    /// </summary>
    public partial class ecb_Duty
    {
        public ecb_Duty()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_Duty");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_Duty model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_Duty(");
            strSql.Append("Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,DutyDate,ItemId,Mark,Status,TermId,LastEditor,LastEditTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@GradeId,@ClassId,@StudentId,@DutyDate,@ItemId,@Mark,@Status,@TermId,@LastEditor,@LastEditTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DutyDate", SqlDbType.Date,3),
                    new SqlParameter("@ItemId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Mark", SqlDbType.NVarChar,255),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.GradeId;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.StudentId;
            parameters[6].Value = model.DutyDate;
            parameters[7].Value = model.ItemId;
            parameters[8].Value = model.Mark;
            parameters[9].Value = model.Status;
            parameters[10].Value = model.TermId;
            parameters[11].Value = model.LastEditor;
            parameters[12].Value = model.LastEditTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_Duty model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Duty set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("DutyDate=@DutyDate,");
            strSql.Append("ItemId=@ItemId,");
            strSql.Append("Mark=@Mark,");
            strSql.Append("Status=@Status,");
            strSql.Append("TermId=@TermId,");
            strSql.Append("LastEditor=@LastEditor,");
            strSql.Append("LastEditTime=@LastEditTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DutyDate", SqlDbType.Date,3),
                    new SqlParameter("@ItemId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Mark", SqlDbType.NVarChar,255),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.GradeId;
            parameters[3].Value = model.ClassId;
            parameters[4].Value = model.StudentId;
            parameters[5].Value = model.DutyDate;
            parameters[6].Value = model.ItemId;
            parameters[7].Value = model.Mark;
            parameters[8].Value = model.Status;
            parameters[9].Value = model.TermId;
            parameters[10].Value = model.LastEditor;
            parameters[11].Value = model.LastEditTime;
            parameters[12].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Duty ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Duty ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Duty GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,DutyDate,ItemId,Mark,Status,TermId,LastEditor,LastEditTime from ecb_Duty ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_Duty model = new ECB.PC.Model.ecb_Duty();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Duty DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_Duty model = new ECB.PC.Model.ecb_Duty();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["DutyDate"] != null && row["DutyDate"].ToString() != "")
                {
                    model.DutyDate = DateTime.Parse(row["DutyDate"].ToString());
                }
                if (row["ItemId"] != null && row["ItemId"].ToString() != "")
                {
                    model.ItemId = new Guid(row["ItemId"].ToString());
                }
                if (row["Mark"] != null)
                {
                    model.Mark = row["Mark"].ToString();
                }
                if (row["Status"] != null && row["Status"].ToString() != "")
                {
                    model.Status = int.Parse(row["Status"].ToString());
                }
                if (row["TermId"] != null && row["TermId"].ToString() != "")
                {
                    model.TermId = new Guid(row["TermId"].ToString());
                }
                if (row["LastEditor"] != null && row["LastEditor"].ToString() != "")
                {
                    model.LastEditor = new Guid(row["LastEditor"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,DutyDate,ItemId,Mark,Status,TermId,LastEditor,LastEditTime ");
            strSql.Append(" FROM ecb_Duty ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,DutyDate,ItemId,Mark,Status,TermId,LastEditor,LastEditTime ");
            strSql.Append(" FROM ecb_Duty ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Duty ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_Duty T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Duty";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获取会议人员
        /// </summary>
        /// <param name="MeetingInfoId">会议id</param>
        /// <param name="_judge">判断是主持人还是参会人员</param>
        /// <returns></returns>
        public string[] GetselUser(DateTime Time, Guid ClassId)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(" SELECT b.StudentName,b.ID,b.UserName FROM dbo.ecb_Duty a LEFT JOIN dbo.JC_StudentInfos b ON a.StudentId=b.ID  ");
            strSql.Append("  where DutyDate=@DutyDate and a.ClassId=@ClassId  ");


            SqlParameter[] parameters = {
                    new SqlParameter("@DutyDate", SqlDbType.DateTime),new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Time;
            parameters[1].Value = ClassId;
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            string[] _result = new string[] { "", "" };
            List<selUser> _idsStr = new List<selUser>();
            List<string> _namesStr = new List<string>();
            if (ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow item in ds.Tables[0].Rows)
                {
                    if (item["StudentName"] != null && !string.IsNullOrEmpty(item["StudentName"].ToString()))
                    {
                        _namesStr.Add(item["StudentName"].ToString());
                        _idsStr.Add(new selUser()
                        {
                            Name = item["StudentName"].ToString(),
                            UserName = item["UserName"].ToString(),
                            UserId = item["ID"].ToString()
                        });
                    }
                }
                _result[0] = string.Join(",", _namesStr);
                Newtonsoft.Json.JsonSerializerSettings _setting = new Newtonsoft.Json.JsonSerializerSettings();
                _setting.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
                _result[1] = Newtonsoft.Json.JsonConvert.SerializeObject(_idsStr, _setting).TrimStart('[').TrimEnd(']') + ",";
            }
            return _result;
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Duty GetModel(DateTime Time, Guid ClassId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 * from ecb_Duty ");
            strSql.Append(" where DutyDate=@DutyDate and  ClassId=@ClassId");
            SqlParameter[] parameters = {
                    new SqlParameter("@DutyDate", SqlDbType.DateTime),new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)               };
            parameters[0].Value = Time;
            parameters[1].Value = ClassId;

            ECB.PC.Model.ecb_Duty model = new ECB.PC.Model.ecb_Duty();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        /// <param name="tblName">表名</param> 
        /// <param name="PageSize">每页的大小</param>
        /// <param name="PageIndex">当前要显示的页码</param>
        /// <param name="FieldShow">以逗号分隔的要显示的字段列表,如果为空,则显示所有字段</param>
        /// <param name="strWhere">查询条件</param>
        /// <param name="orderConditon">以逗号分隔的排序字段列表</param>
        /// <returns>DataSet</returns>
        /// <summary> 
        /// 根据RowID分页
        /// </summary>
        public DataSet GetList(string tblName, int PageSize, int PageIndex, string ShowField, string strWhere, string OrderField, string[] ConnStr = null)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.NVarChar, -1),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@ShowField", SqlDbType.NVarChar,1000),
                    new SqlParameter("@OrderField", SqlDbType.NVarChar,1000),
                    new SqlParameter("@strWhere", SqlDbType.NVarChar,-1)
                    };
            parameters[0].Value = tblName;
            parameters[1].Value = PageSize;
            parameters[2].Value = PageIndex;
            parameters[3].Value = ShowField;
            parameters[4].Value = OrderField;
            parameters[5].Value = strWhere;
            return DbHelperSQL.RunProcedure("UP_GetListByPage_Distinct", parameters, tblName, 3600, ConnStr);
        }

        public DataSet GetList(DateTime Time, Guid ClassId)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(" SELECT b.StudentName,b.ID,b.UserName FROM dbo.ecb_Duty a LEFT JOIN dbo.JC_StudentInfos b ON a.StudentId=b.ID  ");
            strSql.Append("  where DutyDate=@DutyDate and a.ClassId=@ClassId AND Status='1'  Order BY StudentName");
            SqlParameter[] parameters = {
                    new SqlParameter("@DutyDate", SqlDbType.DateTime),new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Time;
            parameters[1].Value = ClassId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string bieName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Duty " + bieName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            DataSet obj = DbHelperSQL.Query(strSql.ToString());

            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj.Tables[0].Rows.Count);
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid TermId, Guid ClassId, int Status, string where)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Duty ");
            strSql.Append(" where TermId=@TermId and ClassId=@ClassId AND Status=@Status AND " + where);
            SqlParameter[] parameters = {
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),new SqlParameter("@Status", SqlDbType.Int,4)};
            parameters[0].Value = TermId;
            parameters[1].Value = ClassId;
            parameters[2].Value = Status;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(DateTime DutyDate, Guid ClassId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Duty ");
            strSql.Append(" where DutyDate=@DutyDate and ClassId=@ClassId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@DutyDate", SqlDbType.Date,3),new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = DutyDate;
            parameters[1].Value = ClassId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(string fieName, string tabName, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");

            strSql.Append(" " + fieName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Update(DateTime DutyDate, Guid ClassId,Guid studentId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Duty set StudentId=@studentId ");
            strSql.Append(" where DutyDate=@DutyDate and ClassId=@ClassId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@DutyDate", SqlDbType.Date,3),new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),new SqlParameter("@studentId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = DutyDate;
            parameters[1].Value = ClassId;
            parameters[2].Value = studentId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCountByTab(string tabName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            DataSet obj = DbHelperSQL.Query(strSql.ToString());

            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj.Tables[0].Rows.Count);
            }
        }
        #endregion  ExtensionMethod
    }
    public partial class selUser
    {
        public string Name { get; set; }
        public string UserName { get; set; }
        public string UserId { get; set; }
    }

}

