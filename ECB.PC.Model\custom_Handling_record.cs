﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// custom_Handling_record:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class custom_Handling_record
	{
		public custom_Handling_record()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _userid;
		private DateTime? _handtime;
		private string _reason;
		private string _handdept;
		private string _handtype;
		private DateTime? _createtime;
		private Guid _creator;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? HandTime
		{
			set{ _handtime=value;}
			get{return _handtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Reason
		{
			set{ _reason=value;}
			get{return _reason;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string HandDept
		{
			set{ _handdept=value;}
			get{return _handdept;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string HandType
		{
			set{ _handtype=value;}
			get{return _handtype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Createtime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		#endregion Model

	}
}

