﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// AI角色卡
	/// </summary>
	[Serializable]
	public partial class ai_prompts
	{
		public ai_prompts()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private string _model;
		private string _promptname;
		private string _promptvalue;
		private int _scenetype;
		private int _isspeech=0;
		private int? _brandtype;
		private int? _orderid;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 模型 唯一
		/// </summary>
		public string Model
		{
			set{ _model=value;}
			get{return _model;}
		}
		/// <summary>
		/// 角色卡名称
		/// </summary>
		public string PromptName
		{
			set{ _promptname=value;}
			get{return _promptname;}
		}
		/// <summary>
		/// 角色卡配置
		/// </summary>
		public string PromptValue
		{
			set{ _promptvalue=value;}
			get{return _promptvalue;}
		}
		/// <summary>
		/// 场景类型 枚举
		/// </summary>
		public int SceneType
		{
			set{ _scenetype=value;}
			get{return _scenetype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int IsSpeech
		{
			set{ _isspeech=value;}
			get{return _isspeech;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? BrandType
		{
			set{ _brandtype=value;}
			get{return _brandtype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? OrderId
		{
			set{ _orderid=value;}
			get{return _orderid;}
		}
		#endregion Model

	}
}

