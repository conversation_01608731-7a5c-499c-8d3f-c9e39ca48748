﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:DA_XS_GrowAttachment
    /// </summary>
    public partial class DA_XS_GrowAttachment
    {
        public DA_XS_GrowAttachment()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from DA_XS_GrowAttachment");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.DA_XS_GrowAttachment model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into DA_XS_GrowAttachment(");
            strSql.Append("ID,RelationId,SchoolColumnId,SchoolColumnPath,FilePath,FileName,UploadTime,FileType,SchoolYear,CreatorUserId,CreatorName)");
            strSql.Append(" values (");
            strSql.Append("@ID,@RelationId,@SchoolColumnId,@SchoolColumnPath,@FilePath,@FileName,@UploadTime,@FileType,@SchoolYear,@CreatorUserId,@CreatorName)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RelationId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@FilePath", SqlDbType.NVarChar,255),
                    new SqlParameter("@FileName", SqlDbType.NVarChar,50),
                    new SqlParameter("@UploadTime", SqlDbType.DateTime),
                    new SqlParameter("@FileType", SqlDbType.Int,4),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
                    new SqlParameter("@CreatorUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreatorName", SqlDbType.NVarChar,50)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.RelationId;
            parameters[2].Value = model.SchoolColumnId;
            parameters[3].Value = model.SchoolColumnPath;
            parameters[4].Value = model.FilePath;
            parameters[5].Value = model.FileName;
            parameters[6].Value = model.UploadTime;
            parameters[7].Value = model.FileType;
            parameters[8].Value = model.SchoolYear;
            parameters[9].Value = model.CreatorUserId;
            parameters[10].Value = model.CreatorName;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.DA_XS_GrowAttachment model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update DA_XS_GrowAttachment set ");
            strSql.Append("RelationId=@RelationId,");
            strSql.Append("SchoolColumnId=@SchoolColumnId,");
            strSql.Append("SchoolColumnPath=@SchoolColumnPath,");
            strSql.Append("FilePath=@FilePath,");
            strSql.Append("FileName=@FileName,");
            strSql.Append("UploadTime=@UploadTime,");
            strSql.Append("FileType=@FileType,");
            strSql.Append("SchoolYear=@SchoolYear,");
            strSql.Append("CreatorUserId=@CreatorUserId,");
            strSql.Append("CreatorName=@CreatorName");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@RelationId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@FilePath", SqlDbType.NVarChar,255),
                    new SqlParameter("@FileName", SqlDbType.NVarChar,50),
                    new SqlParameter("@UploadTime", SqlDbType.DateTime),
                    new SqlParameter("@FileType", SqlDbType.Int,4),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
                    new SqlParameter("@CreatorUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreatorName", SqlDbType.NVarChar,50),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.RelationId;
            parameters[1].Value = model.SchoolColumnId;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.FilePath;
            parameters[4].Value = model.FileName;
            parameters[5].Value = model.UploadTime;
            parameters[6].Value = model.FileType;
            parameters[7].Value = model.SchoolYear;
            parameters[8].Value = model.CreatorUserId;
            parameters[9].Value = model.CreatorName;
            parameters[10].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from DA_XS_GrowAttachment ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from DA_XS_GrowAttachment ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_XS_GrowAttachment GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,RelationId,SchoolColumnId,SchoolColumnPath,FilePath,FileName,UploadTime,FileType,SchoolYear,CreatorUserId,CreatorName from DA_XS_GrowAttachment ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.DA_XS_GrowAttachment model = new ECB.PC.Model.DA_XS_GrowAttachment();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_XS_GrowAttachment DataRowToModel(DataRow row)
        {
            ECB.PC.Model.DA_XS_GrowAttachment model = new ECB.PC.Model.DA_XS_GrowAttachment();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["RelationId"] != null && row["RelationId"].ToString() != "")
                {
                    model.RelationId = new Guid(row["RelationId"].ToString());
                }
                if (row["SchoolColumnId"] != null && row["SchoolColumnId"].ToString() != "")
                {
                    model.SchoolColumnId = int.Parse(row["SchoolColumnId"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["FilePath"] != null)
                {
                    model.FilePath = row["FilePath"].ToString();
                }
                if (row["FileName"] != null)
                {
                    model.FileName = row["FileName"].ToString();
                }
                if (row["UploadTime"] != null && row["UploadTime"].ToString() != "")
                {
                    model.UploadTime = DateTime.Parse(row["UploadTime"].ToString());
                }
                if (row["FileType"] != null && row["FileType"].ToString() != "")
                {
                    model.FileType = int.Parse(row["FileType"].ToString());
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
                if (row["CreatorUserId"] != null && row["CreatorUserId"].ToString() != "")
                {
                    model.CreatorUserId = new Guid(row["CreatorUserId"].ToString());
                }
                if (row["CreatorName"] != null)
                {
                    model.CreatorName = row["CreatorName"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,RelationId,SchoolColumnId,SchoolColumnPath,FilePath,FileName,UploadTime,FileType,SchoolYear,CreatorUserId,CreatorName ");
            strSql.Append(" FROM DA_XS_GrowAttachment ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,RelationId,SchoolColumnId,SchoolColumnPath,FilePath,FileName,UploadTime,FileType,SchoolYear,CreatorUserId,CreatorName ");
            strSql.Append(" FROM DA_XS_GrowAttachment ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM DA_XS_GrowAttachment ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from DA_XS_GrowAttachment T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DA_XS_GrowAttachment";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  MethodEx
        /// <summary>
        /// 自定义字段
        /// </summary>
        public DataSet GetList(string strWhere, string field)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (!string.IsNullOrEmpty(field))
            {
                strSql.Append(field);
            }
            strSql.Append(" FROM DA_XS_GrowAttachment ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 自定义字段
        /// </summary>
        public DataSet GetList(string strWhere, string field, string tabName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (!string.IsNullOrEmpty(field))
            {
                strSql.Append(field);
            }
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 根据关联ID删除数据
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        public bool DeleteByRelationId(Guid RelationId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from DA_XS_GrowAttachment ");
            strSql.Append(" where RelationId=@RelationId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@RelationId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = RelationId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  MethodEx
    }
}

