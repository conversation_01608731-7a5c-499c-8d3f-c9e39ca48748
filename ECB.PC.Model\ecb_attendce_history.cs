﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_attendce_ history:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_attendce_history
    {
        public ecb_attendce_history()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private Guid _gradeid;
        private Guid _classid;
        private int? _normal;
        private int? _lack;
        private int? _leave;
        private int? _late;
        private int? _all;
        private DateTime? _recorddate;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 区域ID
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 区域Path
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 年级
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 班级
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 正常人数
        /// </summary>
        public int? Normal
        {
            set { _normal = value; }
            get { return _normal; }
        }
        /// <summary>
        /// 缺卡人数
        /// </summary>
        public int? Lack
        {
            set { _lack = value; }
            get { return _lack; }
        }
        /// <summary>
        /// 请假人数
        /// </summary>
        public int? Leave
        {
            set { _leave = value; }
            get { return _leave; }
        }
        /// <summary>
        /// 迟到人数
        /// </summary>
        public int? Late
        {
            set { _late = value; }
            get { return _late; }
        }
        /// <summary>
        /// 总人数
        /// </summary>
        public int? ALL
        {
            set { _all = value; }
            get { return _all; }
        }
        /// <summary>
        /// 记录日期
        /// </summary>
        public DateTime? RecordDate
        {
            set { _recorddate = value; }
            get { return _recorddate; }
        }
        #endregion Model

    }
}

