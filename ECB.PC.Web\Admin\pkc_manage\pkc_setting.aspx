﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="pkc_setting.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_setting" StylesheetTheme="Admin_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>排考场规则设置</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script>
        function CloseBox(type) {
			parent.__doPostBack('btnSearch', '1'); 
            parent.layer.close(parent.layer.getFrameIndex(window.name));
        }
	</script>
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <div id="sidebar-tab">
                <div id="tab-title">
                </div>
                <div id="tab-content">
                    <div id="list">
                        <div style="display: block" id="div1">
                            <table id="infoTable" cellspacing="1" cellpadding="3" border="0" align="center" style="width: 99%;" class="margin-bottom-bar">
                                <tbody>
                                    <tr>
                                        <td height="25" width="20%" align="right" class="left_title_1">
                                            <input type="radio" name="options" id="option1" autocomplete="off" runat="server" checked>
                                            <span class="red">*</span>方案一：
                                        </td>
                                        <td>按考试成绩编排，且前后左右考生不同班             
                                        </td>
                                    </tr>
                                    <%--  <tr>
                                    <td height="25" width="30%" align="right" class="left_title_1">
                                    </td>
                                    <td>
                                        选择某考试成绩
                                    </td>
                                </tr>--%>
                                    <tr>
                                        <td height="25" width="20%" align="right" class="left_title_2">
                                            <input type="radio" name="options" id="option2" autocomplete="off" runat="server">
                                            <span class="red">*</span>方案二：
                                        </td>
                                        <td>按随机规则编排考场，且前后左右考生不同班
                                        </td>
                                    </tr>
                                     <tr>
                                        <td height="25" width="20%" align="right" class="left_title_1">
                                            <input type="radio" name="options" id="option3" autocomplete="off" runat="server">
                                            <span class="red">*</span>方案三：
                                        </td>
                                        <td>按本班学生编排考场,在本班考试
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="bottom-bar">
                                 <asp:Button ID="btnInput" runat="server" Text="提交" CssClass="btnGreen" OnClick="btnInput_Click" OnClientClick="HidePanel()" />
                                 <input id="btnClose" type="button" value="关闭" class="btnGray" onclick="CloseBox()"  />          
                            </div>
                        </div>
                        <div style="display: none;" id="loading">
                            处理中……
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <script type="text/javascript">
        function HidePanel() {
            $("#div1").hide().addClass();
            $("#loading").show().addClass();
        }
    </script>
</body>
</html>
