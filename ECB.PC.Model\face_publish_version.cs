﻿using System;

namespace ECB.PC.Model
{
    /// <summary>
	/// 人脸发布更新版本库
	/// </summary>
	[Serializable]
    public partial class face_publish_version
    {
        public face_publish_version()
        { }

        #region Model

        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private DateTime _createtime;
        private Guid _creator;
        private long? _timestamp;

        /// <summary>
        /// 主键ID
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }

        /// <summary>
        /// 地区Id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }

        /// <summary>
        /// 地区path
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime
        {
            set { _createtime = value; }
            get { return _createtime; }
        }

        /// <summary>
        /// 创建人
        /// </summary>
        public Guid Creator
        {
            set { _creator = value; }
            get { return _creator; }
        }

        /// <summary>
        /// 时间戳
        /// </summary>
        public long? Timestamp
        {
            set { _timestamp = value; }
            get { return _timestamp; }
        }

        #endregion Model

    }
}
