﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// Ecb_Xinli_StuCaseDetail:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class Ecb_Xinli_StuCaseDetail
	{
		public Ecb_Xinli_StuCaseDetail()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _userid;
		private DateTime? _recorddate;
		private string _caseinfo;
		private Guid _lastedituserid;
		private DateTime? _lastedittime;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? RecordDate
		{
			set{ _recorddate=value;}
			get{return _recorddate;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CaseInfo
		{
			set{ _caseinfo=value;}
			get{return _caseinfo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid LastEditUserId
		{
			set{ _lastedituserid=value;}
			get{return _lastedituserid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

