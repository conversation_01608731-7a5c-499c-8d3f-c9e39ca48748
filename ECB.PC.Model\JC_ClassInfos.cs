﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 班级信息表
    /// </summary>
    [Serializable]
    public partial class JC_ClassInfos
    {
        public JC_ClassInfos()
        { }
        #region Model
        private Guid _id;
        private string _classname;
        private string _aliasname;
        private string _classtypecode;
        private string _classpropertycode;
        private string _schoolid;
        private Guid _gradeid;
        private string _headteachername;
        private string _classdesc;
        private string _classmotto;
        private string _classroomaddress;
        private int? _orderid;
        private string _teacherno;
        private string _schoolcolumnpath;
        private string _namerule;
        private string _schoolyear;
        private byte[] _newclassid;
        private Guid _placeid;
        private string _isstatistisc;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 班级名称
        /// </summary>
        public string ClassName
        {
            set { _classname = value; }
            get { return _classname; }
        }
        /// <summary>
        /// 别名
        /// </summary>
        public string AliasName
        {
            set { _aliasname = value; }
            get { return _aliasname; }
        }
        /// <summary>
        /// 班级类型
        /// </summary>
        public string ClassTypeCode
        {
            set { _classtypecode = value; }
            get { return _classtypecode; }
        }
        /// <summary>
        /// 班级性质
        /// </summary>
        public string ClassPropertyCode
        {
            set { _classpropertycode = value; }
            get { return _classpropertycode; }
        }
        /// <summary>
        /// 所属学校
        /// </summary>
        public string SchoolId
        {
            set { _schoolid = value; }
            get { return _schoolid; }
        }
        /// <summary>
        /// 所属年级
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 班主任姓名 
        /// </summary>
        public string HeadTeacherName
        {
            set { _headteachername = value; }
            get { return _headteachername; }
        }
        /// <summary>
        /// 班级简介
        /// </summary>
        public string ClassDesc
        {
            set { _classdesc = value; }
            get { return _classdesc; }
        }
        /// <summary>
        /// 班训
        /// </summary>
        public string ClassMotto
        {
            set { _classmotto = value; }
            get { return _classmotto; }
        }
        /// <summary>
        /// 教室地点
        /// </summary>
        public string ClassroomAddress
        {
            set { _classroomaddress = value; }
            get { return _classroomaddress; }
        }
        /// <summary>
        /// 排序
        /// </summary>
        public int? OrderId
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string TeacherNo
        {
            set { _teacherno = value; }
            get { return _teacherno; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string SchoolColumnPath
        {
            set { _schoolcolumnpath = value; }
            get { return _schoolcolumnpath; }
        }
        /// <summary>
        /// 命名规则
        /// </summary>
        public string Namerule
        {
            set { _namerule = value; }
            get { return _namerule; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string SchoolYear
        {
            set { _schoolyear = value; }
            get { return _schoolyear; }
        }
        /// <summary>
        /// 
        /// </summary>
        public byte[] NewClassId
        {
            set { _newclassid = value; }
            get { return _newclassid; }
        }
        /// <summary>
        /// 场地ID
        /// </summary>
        public Guid PlaceId
        {
            get { return _placeid; }

            set { _placeid = value; }
        }
        /// <summary>
        /// 是否到校统计
        /// </summary>
        public string IsStatistics
        {
            get { return _isstatistisc; }
            set { _isstatistisc = value; }
        }
        #endregion Model

    }
}

