﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:psych_activity
	/// </summary>
	public partial class psych_activity
	{
		public psych_activity()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from psych_activity");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.psych_activity model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into psych_activity(");
			strSql.Append("Id,ColumnId,ColumnPath,Title,Type,TeacherNum,StudentNum,Summary,AttachmentPath,ActivityDate,CreateTime,Creator,LastEditBy,LastEditTime)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@Title,@Type,@TeacherNum,@StudentNum,@Summary,@AttachmentPath,@ActivityDate,@CreateTime,@Creator,@LastEditBy,@LastEditTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@Title", SqlDbType.NVarChar,200),
					new SqlParameter("@Type", SqlDbType.Int,4),
					new SqlParameter("@TeacherNum", SqlDbType.Int,4),
					new SqlParameter("@StudentNum", SqlDbType.Int,4),
					new SqlParameter("@Summary", SqlDbType.NVarChar,-1),
					new SqlParameter("@AttachmentPath", SqlDbType.NVarChar,-1),
					new SqlParameter("@ActivityDate", SqlDbType.DateTime),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.Title;
			parameters[4].Value = model.Type;
			parameters[5].Value = model.TeacherNum;
			parameters[6].Value = model.StudentNum;
			parameters[7].Value = model.Summary;
			parameters[8].Value = model.AttachmentPath;
			parameters[9].Value = model.ActivityDate;
			parameters[10].Value = model.CreateTime;
			parameters[11].Value = model.Creator;
			parameters[12].Value = model.LastEditBy;
			parameters[13].Value = model.LastEditTime;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.psych_activity model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update psych_activity set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("Title=@Title,");
			strSql.Append("Type=@Type,");
			strSql.Append("TeacherNum=@TeacherNum,");
			strSql.Append("StudentNum=@StudentNum,");
			strSql.Append("Summary=@Summary,");
			strSql.Append("AttachmentPath=@AttachmentPath,");
			strSql.Append("ActivityDate=@ActivityDate,");
			strSql.Append("CreateTime=@CreateTime,");
			strSql.Append("Creator=@Creator,");
			strSql.Append("LastEditBy=@LastEditBy,");
			strSql.Append("LastEditTime=@LastEditTime");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@Title", SqlDbType.NVarChar,200),
					new SqlParameter("@Type", SqlDbType.Int,4),
					new SqlParameter("@TeacherNum", SqlDbType.Int,4),
					new SqlParameter("@StudentNum", SqlDbType.Int,4),
					new SqlParameter("@Summary", SqlDbType.NVarChar,-1),
					new SqlParameter("@AttachmentPath", SqlDbType.NVarChar,-1),
					new SqlParameter("@ActivityDate", SqlDbType.DateTime),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.Title;
			parameters[3].Value = model.Type;
			parameters[4].Value = model.TeacherNum;
			parameters[5].Value = model.StudentNum;
			parameters[6].Value = model.Summary;
			parameters[7].Value = model.AttachmentPath;
			parameters[8].Value = model.ActivityDate;
			parameters[9].Value = model.CreateTime;
			parameters[10].Value = model.Creator;
			parameters[11].Value = model.LastEditBy;
			parameters[12].Value = model.LastEditTime;
			parameters[13].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from psych_activity ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from psych_activity ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.psych_activity GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Title,Type,TeacherNum,StudentNum,Summary,AttachmentPath,ActivityDate,CreateTime,Creator,LastEditBy,LastEditTime from psych_activity ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			Model.psych_activity model=new Model.psych_activity();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.psych_activity DataRowToModel(DataRow row)
		{
			Model.psych_activity model=new Model.psych_activity();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["Title"]!=null)
				{
					model.Title=row["Title"].ToString();
				}
				if(row["Type"]!=null && row["Type"].ToString()!="")
				{
					model.Type=int.Parse(row["Type"].ToString());
				}
				if(row["TeacherNum"]!=null && row["TeacherNum"].ToString()!="")
				{
					model.TeacherNum=int.Parse(row["TeacherNum"].ToString());
				}
				if(row["StudentNum"]!=null && row["StudentNum"].ToString()!="")
				{
					model.StudentNum=int.Parse(row["StudentNum"].ToString());
				}
				if(row["Summary"]!=null)
				{
					model.Summary=row["Summary"].ToString();
				}
				if(row["AttachmentPath"]!=null)
				{
					model.AttachmentPath=row["AttachmentPath"].ToString();
				}
				if(row["ActivityDate"]!=null && row["ActivityDate"].ToString()!="")
				{
					model.ActivityDate=DateTime.Parse(row["ActivityDate"].ToString());
				}
				if(row["CreateTime"]!=null && row["CreateTime"].ToString()!="")
				{
					model.CreateTime=DateTime.Parse(row["CreateTime"].ToString());
				}
				if(row["Creator"]!=null && row["Creator"].ToString()!="")
				{
					model.Creator= new Guid(row["Creator"].ToString());
				}
				if(row["LastEditBy"]!=null && row["LastEditBy"].ToString()!="")
				{
					model.LastEditBy= new Guid(row["LastEditBy"].ToString());
				}
				if(row["LastEditTime"]!=null && row["LastEditTime"].ToString()!="")
				{
					model.LastEditTime=DateTime.Parse(row["LastEditTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,Title,Type,TeacherNum,StudentNum,Summary,AttachmentPath,ActivityDate,CreateTime,Creator,LastEditBy,LastEditTime ");
			strSql.Append(" FROM psych_activity ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,Title,Type,TeacherNum,StudentNum,Summary,AttachmentPath,ActivityDate,CreateTime,Creator,LastEditBy,LastEditTime ");
			strSql.Append(" FROM psych_activity ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM psych_activity ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from psych_activity T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_activity";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        public int GetCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_activity t ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        #endregion  ExtensionMethod
    }
}

