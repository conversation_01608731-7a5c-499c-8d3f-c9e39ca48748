﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_New_Duty
	/// </summary>
	public partial class ecb_New_Duty
	{
		public ecb_New_Duty()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_New_Duty");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_New_Duty model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_New_Duty(");
			strSql.Append("Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,StudentStatus,StudentRemarks,DutyDate,Arrange,ArrangeTime)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@GradeId,@ClassId,@StudentId,@StudentStatus,@StudentRemarks,@DutyDate,@Arrange,@ArrangeTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentStatus", SqlDbType.Int,4),
					new SqlParameter("@StudentRemarks", SqlDbType.NVarChar,255),
					new SqlParameter("@DutyDate", SqlDbType.NVarChar,255),
					new SqlParameter("@Arrange", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ArrangeTime", SqlDbType.DateTime)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = Guid.NewGuid();
			parameters[4].Value = Guid.NewGuid();
			parameters[5].Value = Guid.NewGuid();
			parameters[6].Value = model.StudentStatus;
			parameters[7].Value = model.StudentRemarks;
			parameters[8].Value = model.DutyDate;
			parameters[9].Value = Guid.NewGuid();
			parameters[10].Value = model.ArrangeTime;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_New_Duty model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_New_Duty set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("GradeId=@GradeId,");
			strSql.Append("ClassId=@ClassId,");
			strSql.Append("StudentId=@StudentId,");
			strSql.Append("StudentStatus=@StudentStatus,");
			strSql.Append("StudentRemarks=@StudentRemarks,");
			strSql.Append("DutyDate=@DutyDate,");
			strSql.Append("Arrange=@Arrange,");
			strSql.Append("ArrangeTime=@ArrangeTime");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentStatus", SqlDbType.Int,4),
					new SqlParameter("@StudentRemarks", SqlDbType.NVarChar,255),
					new SqlParameter("@DutyDate", SqlDbType.NVarChar,255),
					new SqlParameter("@Arrange", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ArrangeTime", SqlDbType.DateTime),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.GradeId;
			parameters[3].Value = model.ClassId;
			parameters[4].Value = model.StudentId;
			parameters[5].Value = model.StudentStatus;
			parameters[6].Value = model.StudentRemarks;
			parameters[7].Value = model.DutyDate;
			parameters[8].Value = model.Arrange;
			parameters[9].Value = model.ArrangeTime;
			parameters[10].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_New_Duty ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_New_Duty ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_New_Duty GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,StudentStatus,StudentRemarks,DutyDate,Arrange,ArrangeTime from ecb_New_Duty ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_New_Duty model=new ECB.PC.Model.ecb_New_Duty();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_New_Duty DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_New_Duty model=new ECB.PC.Model.ecb_New_Duty();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["GradeId"]!=null && row["GradeId"].ToString()!="")
				{
					model.GradeId= new Guid(row["GradeId"].ToString());
				}
				if(row["ClassId"]!=null && row["ClassId"].ToString()!="")
				{
					model.ClassId= new Guid(row["ClassId"].ToString());
				}
				if(row["StudentId"]!=null && row["StudentId"].ToString()!="")
				{
					model.StudentId= new Guid(row["StudentId"].ToString());
				}
				if(row["StudentStatus"]!=null && row["StudentStatus"].ToString()!="")
				{
					model.StudentStatus=int.Parse(row["StudentStatus"].ToString());
				}
				if(row["StudentRemarks"]!=null)
				{
					model.StudentRemarks=row["StudentRemarks"].ToString();
				}
				if(row["DutyDate"]!=null)
				{
					model.DutyDate=row["DutyDate"].ToString();
				}
				if(row["Arrange"]!=null && row["Arrange"].ToString()!="")
				{
					model.Arrange= new Guid(row["Arrange"].ToString());
				}
				if(row["ArrangeTime"]!=null && row["ArrangeTime"].ToString()!="")
				{
					model.ArrangeTime=DateTime.Parse(row["ArrangeTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,StudentStatus,StudentRemarks,DutyDate,Arrange,ArrangeTime ");
			strSql.Append(" FROM ecb_New_Duty ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,GradeId,ClassId,StudentId,StudentStatus,StudentRemarks,DutyDate,Arrange,ArrangeTime ");
			strSql.Append(" FROM ecb_New_Duty ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_New_Duty ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_New_Duty T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_New_Duty";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

