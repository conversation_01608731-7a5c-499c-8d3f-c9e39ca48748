﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_kq_meeting_record:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_kq_meeting_record
	{
		public ecb_kq_meeting_record()
		{ }
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _meetingid;
		private Guid _userid;
		private DateTime? _signtime;
		private int _status;
		private string _deviceno;
		private int _usertype;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set { _id = value; }
			get { return _id; }
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set { _columnid = value; }
			get { return _columnid; }
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set { _columnpath = value; }
			get { return _columnpath; }
		}
		/// <summary>
		/// 会议考勤id
		/// </summary>
		public Guid MeetingId
		{
			set { _meetingid = value; }
			get { return _meetingid; }
		}
		/// <summary>
		/// 考勤人员id
		/// </summary>
		public Guid UserId
		{
			set { _userid = value; }
			get { return _userid; }
		}
		/// <summary>
		/// 考勤时间
		/// </summary>
		public DateTime? SignTime
		{
			set { _signtime = value; }
			get { return _signtime; }
		}
		/// <summary>
		/// 考勤状态(枚举:1正常 2迟到 3缺席)
		/// </summary>
		public int Status
		{
			set { _status = value; }
			get { return _status; }
		}
		/// <summary>
		/// 考勤设备编号
		/// </summary>
		public string DeviceNo
		{
			set { _deviceno = value; }
			get { return _deviceno; }
		}
		/// <summary>
		/// 人员类型 1学生 2教师
		/// </summary>
		public int UserType
		{
			set { _usertype = value; }
			get { return _usertype; }
		}
		#endregion Model

	}
}