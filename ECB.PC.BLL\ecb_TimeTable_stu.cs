﻿
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
using System.Collections.Generic;
using System.Collections;
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_TimeTable_stu
    /// </summary>
    public partial class ecb_TimeTable_stu
    {
        public ecb_TimeTable_stu()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_TimeTable_stu");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_TimeTable_stu model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_TimeTable_stu(");
            strSql.Append("Id,ColumnId,ColumnPath,TermID,StudentId,ClassId,ClassTypeCode,SubjectNo,SubjectType,TeacherId,Weekday,Number,LastEditTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@TermID,@StudentId,@ClassId,@ClassTypeCode,@SubjectNo,@SubjectType,@TeacherId,@Weekday,@Number,@LastEditTime)");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassTypeCode", SqlDbType.NVarChar,50),
					new SqlParameter("@SubjectNo", SqlDbType.NVarChar,50),
					new SqlParameter("@SubjectType", SqlDbType.Int,4),
					new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Weekday", SqlDbType.Int,4),
					new SqlParameter("@Number", SqlDbType.Int,4),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.TermID;
            parameters[4].Value = model.StudentId;
            parameters[5].Value = model.ClassId;
            parameters[6].Value = model.ClassTypeCode;
            parameters[7].Value = model.SubjectNo;
            parameters[8].Value = model.SubjectType;
            parameters[9].Value = model.TeacherId;
            parameters[10].Value = model.Weekday;
            parameters[11].Value = model.Number;
            parameters[12].Value = model.LastEditTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_TimeTable_stu model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_TimeTable_stu set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("TermID=@TermID,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("ClassTypeCode=@ClassTypeCode,");
            strSql.Append("SubjectNo=@SubjectNo,");
            strSql.Append("SubjectType=@SubjectType,");
            strSql.Append("TeacherId=@TeacherId,");
            strSql.Append("Weekday=@Weekday,");
            strSql.Append("Number=@Number,");
            strSql.Append("LastEditTime=@LastEditTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassTypeCode", SqlDbType.NVarChar,50),
					new SqlParameter("@SubjectNo", SqlDbType.NVarChar,50),
					new SqlParameter("@SubjectType", SqlDbType.Int,4),
					new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Weekday", SqlDbType.Int,4),
					new SqlParameter("@Number", SqlDbType.Int,4),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.TermID;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.ClassTypeCode;
            parameters[6].Value = model.SubjectNo;
            parameters[7].Value = model.SubjectType;
            parameters[8].Value = model.TeacherId;
            parameters[9].Value = model.Weekday;
            parameters[10].Value = model.Number;
            parameters[11].Value = model.LastEditTime;
            parameters[12].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id, Guid TermID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable_stu ");
            strSql.Append(" where StudentId=@StudentId and TermID=@TermID ");
            SqlParameter[] parameters = {
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Id;
            parameters[1].Value = TermID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable_stu ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_TimeTable_stu GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,TermID,StudentId,ClassId,ClassTypeCode,SubjectNo,SubjectType,TeacherId,Weekday,Number,LastEditTime from ecb_TimeTable_stu ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_TimeTable_stu model = new ECB.PC.Model.ecb_TimeTable_stu();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_TimeTable_stu DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_TimeTable_stu model = new ECB.PC.Model.ecb_TimeTable_stu();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["TermID"] != null && row["TermID"].ToString() != "")
                {
                    model.TermID = new Guid(row["TermID"].ToString());
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["ClassTypeCode"] != null)
                {
                    model.ClassTypeCode = row["ClassTypeCode"].ToString();
                }
                if (row["SubjectNo"] != null)
                {
                    model.SubjectNo = row["SubjectNo"].ToString();
                }
                if (row["SubjectType"] != null && row["SubjectType"].ToString() != "")
                {
                    model.SubjectType = int.Parse(row["SubjectType"].ToString());
                }
                if (row["TeacherId"] != null && row["TeacherId"].ToString() != "")
                {
                    model.TeacherId = new Guid(row["TeacherId"].ToString());
                }
                if (row["Weekday"] != null && row["Weekday"].ToString() != "")
                {
                    model.Weekday = int.Parse(row["Weekday"].ToString());
                }
                if (row["Number"] != null && row["Number"].ToString() != "")
                {
                    model.Number = int.Parse(row["Number"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,TermID,StudentId,ClassId,ClassTypeCode,SubjectNo,SubjectType,TeacherId,Weekday,Number,LastEditTime ");
            strSql.Append(" FROM ecb_TimeTable_stu ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,TermID,StudentId,ClassId,ClassTypeCode,SubjectNo,SubjectType,TeacherId,Weekday,Number,LastEditTime ");
            strSql.Append(" FROM ecb_TimeTable_stu ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_TimeTable_stu ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_TimeTable_stu T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_TimeTable_stu";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable_stu ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public int AddTranList(ArrayList list)
        {
          return  DbHelperSQL.ExecuteSqlTran(list);
        }
        #endregion  ExtensionMethod
    }
}

