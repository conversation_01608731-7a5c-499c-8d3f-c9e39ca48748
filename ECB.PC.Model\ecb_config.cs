﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_config:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_config
    {
        public ecb_config()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private string _attendancemode;
        private int? _startpoint;
        private int? _iscontinuoussignonce;
        private string _inclasstitle;
        private string _testtitle;
        private string _afterschoolbanner;
        private string _schoolmotto;
        private string _schooldesc;
        private int? _redflagcount;
        private string _qiniuak;
        private string _qiniusk;
        private string _qiniubucket;
        private string _qiniudomain;
        private int _videotype;
        public string TeaWorkTime { get; set; }
        public int TimeSpan { get; set; }
        public string EnableAttedanceMode { get; set; }//启用考勤模式
        public int IsArriveSchoolAttendance { get; set; }//是否到校考勤教师
        public int IsArriveSchoolAttendance_stu { get; set; }//是否到校考勤 学生
        public string StuWorkTime { get; set; }
        public int InClassAttendType { get; set; }//上课展示考勤数据类型
        public int MaxStuCount { get; set; }//班额限定
        public int IsContentNeedCheck { get; set; } //是否启用内容审核
        public int XinLiReservationLimitNum { get; set; }//心理预约最大人数
        public string SchoolBadge { get; set; }//校徽
        public string XinliBiaoYu { get; set; }//心理标语
        public int? IsXinLiReservation { get; set; }//心理预约最大人数
        public int? IsFaceToAvatar { get; set; } //是否用人脸代替头像
        private string _stumenweimenjintime;
        private string _teamenweimenjintime;
        public int StuDelayTime { get; set; }//学生考勤延迟推送时长 分钟
        public int IsNeedOpenDoor { get; set; }//是否启用闸机
        /// <summary>
        /// 
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区Id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 考勤模式
        /// </summary>
        public string AttendanceMode
        {
            set { _attendancemode = value; }
            get { return _attendancemode; }
        }
        /// <summary>
        /// 考勤提前时间
        /// </summary>
        public int? StartPoint
        {
            set { _startpoint = value; }
            get { return _startpoint; }
        }
        /// <summary>
        /// 是否连堂打卡
        /// </summary>
        public int? IsContinuousSignOnce
        {
            set { _iscontinuoussignonce = value; }
            get { return _iscontinuoussignonce; }
        }
        /// <summary>
        /// 上课模式标语
        /// </summary>
        public string InClassTitle
        {
            set { _inclasstitle = value; }
            get { return _inclasstitle; }
        }
        /// <summary>
        /// 考试模式标语
        /// </summary>
        public string TestTitle
        {
            set { _testtitle = value; }
            get { return _testtitle; }
        }
        /// <summary>
        /// 放学模式标语
        /// </summary>
        public string AfterSchoolBanner
        {
            set { _afterschoolbanner = value; }
            get { return _afterschoolbanner; }
        }
        /// <summary>
        /// 校训
        /// </summary>
        public string SchoolMotto
        {
            set { _schoolmotto = value; }
            get { return _schoolmotto; }
        }
        /// <summary>
        /// 学校简介
        /// </summary>
        public string SchoolDesc
        {
            set { _schooldesc = value; }
            get { return _schooldesc; }
        }
        /// <summary>
        /// 得流动红旗的班级个数
        /// </summary>
        public int? RedFlagCount
        {
            set { _redflagcount = value; }
            get { return _redflagcount; }
        }
        /// <summary>
		/// 
		/// </summary>
		public string QiniuAK
        {
            set { _qiniuak = value; }
            get { return _qiniuak; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string QiniuSK
        {
            set { _qiniusk = value; }
            get { return _qiniusk; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string QiniuBucket
        {
            set { _qiniubucket = value; }
            get { return _qiniubucket; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string QiniuDomain
        {
            set { _qiniudomain = value; }
            get { return _qiniudomain; }
        }
        /// <summary>
        /// 视频地址配置 0本地 1七牛云
        /// </summary>
        public int VideoType
        {
            set { _videotype = value; }
            get { return _videotype; }
        }
        /// <summary>
        /// 学生门卫门禁开关时间
        /// </summary>
        public string StuMenWeiMenJinTime
        {
            set { _stumenweimenjintime = value; }
            get { return _stumenweimenjintime; }
        }
        /// <summary>
        /// 教师门卫门禁开关时间
        /// </summary>
        public string TeaMenWeiMenJinTime
        {
            set { _teamenweimenjintime = value; }
            get { return _teamenweimenjintime; }
        }
        /// <summary>
        /// 门禁未关推送时间，单位分钟
        /// </summary>
        public int? MenJinUnClosedPushTime { get; set; }

        /// <summary>
        /// 是否人脸开门禁
        /// </summary>
        public int IsFaceOpenDoor { get; set; }
        #endregion Model

    }
}

