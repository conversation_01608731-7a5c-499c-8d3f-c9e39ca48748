﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 审批流程明细
    /// </summary>
    [Serializable]
    public partial class wpsl_flow_detail
    {
        public wpsl_flow_detail()
        { }
        #region Model
        private Guid _id;
        private int _columnId;
        private string _columnpath;
        private Guid _flowid;
        private string _flowname;
        private string _flowcode;
        private decimal? _priceline;
        private Guid _lasteditor;
        private DateTime? _lastedittime;
        /// <summary>
        /// Int
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 所属地区
        /// </summary>
        public int ColumnId
        {
            set { _columnId = value; }
            get { return _columnId; }
        }
        /// <summary>
        /// 所属地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 流程组Id
        /// </summary>
        public Guid FlowId
        {
            set { _flowid = value; }
            get { return _flowid; }
        }
        /// <summary>
        /// 流程名称
        /// </summary>
        public string FlowName
        {
            set { _flowname = value; }
            get { return _flowname; }
        }
        /// <summary>
        /// 流程编号
        /// </summary>
        public string FlowCode
        {
            set { _flowcode = value; }
            get { return _flowcode; }
        }
        /// <summary>
        /// 审批额度
        /// </summary>
        public decimal? PriceLine
        {
            set { _priceline = value; }
            get { return _priceline; }
        }
        /// <summary>
        /// 修改人
        /// </summary>
        public Guid LastEditor
        {
            set { _lasteditor = value; }
            get { return _lasteditor; }
        }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        #endregion Model

    }
}

