﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:zhsz_prize
	/// </summary>
	public partial class zhsz_prize
	{
		public zhsz_prize()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from zhsz_prize");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.zhsz_prize model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into zhsz_prize(");
			strSql.Append("Id,ColumnId,ColumnPath,PrizeCode,PrizeName,RedeemPoint,Imgs,IsShelves,StockNum,LastEditBy,LastEditTime)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@PrizeCode,@PrizeName,@RedeemPoint,@Imgs,@IsShelves,@StockNum,@LastEditBy,@LastEditTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@PrizeCode", SqlDbType.NVarChar,20),
					new SqlParameter("@PrizeName", SqlDbType.NVarChar,50),
					new SqlParameter("@RedeemPoint", SqlDbType.Decimal,5),
					new SqlParameter("@Imgs", SqlDbType.NVarChar,-1),
					new SqlParameter("@IsShelves", SqlDbType.Int,4),
					new SqlParameter("@StockNum", SqlDbType.Int,4),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
			parameters[0].Value = model.Id;
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.PrizeCode;
			parameters[4].Value = model.PrizeName;
			parameters[5].Value = model.RedeemPoint;
			parameters[6].Value = model.Imgs;
			parameters[7].Value = model.IsShelves;
			parameters[8].Value = model.StockNum;
			parameters[9].Value = model.LastEditBy;
			parameters[10].Value = model.LastEditTime;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.zhsz_prize model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update zhsz_prize set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("PrizeCode=@PrizeCode,");
			strSql.Append("PrizeName=@PrizeName,");
			strSql.Append("RedeemPoint=@RedeemPoint,");
			strSql.Append("Imgs=@Imgs,");
			strSql.Append("IsShelves=@IsShelves,");
			strSql.Append("StockNum=@StockNum,");
			strSql.Append("LastEditBy=@LastEditBy,");
			strSql.Append("LastEditTime=@LastEditTime");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@PrizeCode", SqlDbType.NVarChar,20),
					new SqlParameter("@PrizeName", SqlDbType.NVarChar,50),
					new SqlParameter("@RedeemPoint", SqlDbType.Decimal,5),
					new SqlParameter("@Imgs", SqlDbType.NVarChar,-1),
					new SqlParameter("@IsShelves", SqlDbType.Int,4),
					new SqlParameter("@StockNum", SqlDbType.Int,4),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.PrizeCode;
			parameters[3].Value = model.PrizeName;
			parameters[4].Value = model.RedeemPoint;
			parameters[5].Value = model.Imgs;
			parameters[6].Value = model.IsShelves;
			parameters[7].Value = model.StockNum;
			parameters[8].Value = model.LastEditBy;
			parameters[9].Value = model.LastEditTime;
			parameters[10].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from zhsz_prize ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from zhsz_prize ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.zhsz_prize GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,PrizeCode,PrizeName,RedeemPoint,Imgs,IsShelves,StockNum,LastEditBy,LastEditTime from zhsz_prize ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			Model.zhsz_prize model=new Model.zhsz_prize();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.zhsz_prize DataRowToModel(DataRow row)
		{
			Model.zhsz_prize model=new Model.zhsz_prize();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["PrizeCode"]!=null)
				{
					model.PrizeCode=row["PrizeCode"].ToString();
				}
				if(row["PrizeName"]!=null)
				{
					model.PrizeName=row["PrizeName"].ToString();
				}
				if(row["RedeemPoint"]!=null && row["RedeemPoint"].ToString()!="")
				{
					model.RedeemPoint=decimal.Parse(row["RedeemPoint"].ToString());
				}
				if(row["Imgs"]!=null)
				{
					model.Imgs=row["Imgs"].ToString();
				}
				if(row["IsShelves"]!=null && row["IsShelves"].ToString()!="")
				{
					model.IsShelves=int.Parse(row["IsShelves"].ToString());
				}
				if(row["StockNum"]!=null && row["StockNum"].ToString()!="")
				{
					model.StockNum=int.Parse(row["StockNum"].ToString());
				}
				if(row["LastEditBy"]!=null && row["LastEditBy"].ToString()!="")
				{
					model.LastEditBy= new Guid(row["LastEditBy"].ToString());
				}
				if(row["LastEditTime"]!=null && row["LastEditTime"].ToString()!="")
				{
					model.LastEditTime=DateTime.Parse(row["LastEditTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,PrizeCode,PrizeName,RedeemPoint,Imgs,IsShelves,StockNum,LastEditBy,LastEditTime ");
			strSql.Append(" FROM zhsz_prize ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,PrizeCode,PrizeName,RedeemPoint,Imgs,IsShelves,StockNum,LastEditBy,LastEditTime ");
			strSql.Append(" FROM zhsz_prize ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM zhsz_prize ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from zhsz_prize T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "zhsz_prize";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool ExistsByPrizeName(Guid Id, string PrizeName, int ColumnId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from zhsz_prize");
			strSql.Append(" where Id !=@Id and PrizeName =@PrizeName and ColumnId=@ColumnId ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
			new SqlParameter("@PrizeName",SqlDbType.NVarChar,50),
			new SqlParameter("@ColumnId",SqlDbType.Int,4)};
			parameters[0].Value = Id;
			parameters[1].Value = PrizeName;
			parameters[2].Value = ColumnId;

			return DbHelperSQL.Exists(strSql.ToString(), parameters);
		}
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool ExistsByPrizeCode(Guid Id, string PrizeCode, int ColumnId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from zhsz_prize");
			strSql.Append(" where Id !=@Id and PrizeCode =@PrizeCode and ColumnId=@ColumnId ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
			new SqlParameter("@PrizeCode",SqlDbType.NVarChar,50),
			new SqlParameter("@ColumnId",SqlDbType.Int,4)};
			parameters[0].Value = Id;
			parameters[1].Value = PrizeCode;
			parameters[2].Value = ColumnId;

			return DbHelperSQL.Exists(strSql.ToString(), parameters);
		}
		#endregion  ExtensionMethod
	}
}

