﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 历史上的今天
	/// </summary>
	[Serializable]
	public partial class ecb_ToDayInHistory
	{
		public ecb_ToDayInHistory()
		{}
		#region Model
		private Guid _id;
		private string _title;
		private string _content;
		private string _imgurl;
		private string _zhaiyao;
		private DateTime _today;
		private DateTime _recorddate;
		private Guid _recorduserid;
		private int _columnid;
		private string _columnpath;
        private int? _ispass;
        private Guid _ispassby;
        private DateTime? _ispassdate;
        /// <summary>
        /// 
        /// </summary>
        public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Title
		{
			set{ _title=value;}
			get{return _title;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Content
		{
			set{ _content=value;}
			get{return _content;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ImgUrl
		{
			set{ _imgurl=value;}
			get{return _imgurl;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZhaiYao
		{
			set{ _zhaiyao=value;}
			get{return _zhaiyao;}
		}
		/// <summary>
		/// "今天"日期
		/// </summary>
		public DateTime ToDay
		{
			set{ _today=value;}
			get{return _today;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime RecordDate
		{
			set{ _recorddate=value;}
			get{return _recorddate;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid RecordUserId
		{
			set{ _recorduserid=value;}
			get{return _recorduserid;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
        /// <summary>
		/// 是否通过审核
		/// </summary>
		public int? IsPass
        {
            set { _ispass = value; }
            get { return _ispass; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid IsPassBy
        {
            set { _ispassby = value; }
            get { return _ispassby; }
        }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? IsPassDate
        {
            set { _ispassdate = value; }
            get { return _ispassdate; }
        }
        #endregion Model

    }
}

