﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    [Serializable]
    public partial class ecb_welcome_message
    {
        public ecb_welcome_message()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private string _messagecontent;
        private int? _isenable;
        private DateTime? _startime;
        private DateTime? _endtime;
        private int? _type;
        private int? _sendtype;
        private string _Ids;
        public string Weeks;
        public int? IsIndex { get; set; }
        /// <summary>
        /// 主键ID
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区Path
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 欢迎语
        /// </summary>
        public string MessageContent
        {
            set { _messagecontent = value; }
            get { return _messagecontent; }
        }
        /// <summary>
        /// 是否启用
        /// </summary>
        public int? IsEnable
        {
            set { _isenable = value; }
            get { return _isenable; }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StarTime
        {
            set { _startime = value; }
            get { return _startime; }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime
        {
            set { _endtime = value; }
            get { return _endtime; }
        }
        /// <summary>
		/// 模式  0 欢迎模式  1 紧急通知模式 2 校宣模式 3自定义模式
		/// </summary>
		public int? Type
        {
            set { _type = value; }
            get { return _type; }
        }
        /// <summary>
        /// 指定类型  0全校  1年级 2班级 3班牌编号
        /// </summary>
        public int? SendType
		{
			set{ _sendtype=value;}
			get{return _sendtype;}
		}
        /// <summary>
        /// 指定类型的id  0全校  1年级 2班级 3班牌编号 |分割
        /// </summary>
        public string Ids
        {
            set { _Ids = value; }
            get { return _Ids; }
        }
        #endregion Model
    }
}
