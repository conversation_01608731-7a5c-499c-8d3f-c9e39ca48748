﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
	/// <summary>
	/// 校宣视频音量配置表
	/// </summary>
	[Serializable]
	public partial class ecb_AudioVolume
	{
		public ecb_AudioVolume()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private int _configtype;
		private DateTime? _boottime;
		private DateTime? _offtime;
		private int _volume;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 配置类型 1.课间 2.课余 3.自定义
		/// </summary>
		public int ConfigType
		{
			set{ _configtype=value;}
			get{return _configtype;}
		}
		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime? BootTime
		{
			set{ _boottime=value;}
			get{return _boottime;}
		}
		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime? OffTime
		{
			set{ _offtime=value;}
			get{return _offtime;}
		}
		/// <summary>
		/// 音量大小
		/// </summary>
		public int Volume
		{
			set{ _volume=value;}
			get{return _volume;}
		}
		#endregion Model

	}
}

