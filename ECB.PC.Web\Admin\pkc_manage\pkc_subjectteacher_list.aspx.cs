﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_subjectteacher_list : YunEdu.Authority.AdminCommonJC
    {
        string id;
        string strWhere;
        YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();

        YunEdu.BLL.PKC_Rooms bllrooms = new YunEdu.BLL.PKC_Rooms();
        YunEdu.Model.PKC_Rooms modelrooms = new YunEdu.Model.PKC_Rooms();

        YunEdu.BLL.PKC_TestTeachers blltestteachers = new YunEdu.BLL.PKC_TestTeachers();
        YunEdu.Model.PKC_TestTeachers modeltestteachers = new YunEdu.Model.PKC_TestTeachers();

        YunEdu.BLL.UserInfos blluserinfos = new YunEdu.BLL.UserInfos();
        YunEdu.Model.UserInfos modeluserinfos = new YunEdu.Model.UserInfos();

        YunEdu.BLL.PKC_TeacherSubjects bllteachersubjects = new YunEdu.BLL.PKC_TeacherSubjects();
        YunEdu.Model.PKC_TeacherSubjects modelteachersubjects = new YunEdu.Model.PKC_TeacherSubjects();

        YunEdu.BLL.PKC_Subjects bllsubjects = new YunEdu.BLL.PKC_Subjects();
        YunEdu.Model.PKC_Subjects modelsubjects = new YunEdu.Model.PKC_Subjects();

        YunEdu.BLL.aspnet_Users bllusers = new YunEdu.BLL.aspnet_Users();
        YunEdu.Model.aspnet_Users modelusers = new YunEdu.Model.aspnet_Users();

        YunEdu.BLL.JC_Message bllmessage = new YunEdu.BLL.JC_Message();
        YunEdu.BLL.JC_MessageRead bllmessageread = new YunEdu.BLL.JC_MessageRead();

        YunEdu.BLL.JC_StudentInfos bllstudentinfo = new YunEdu.BLL.JC_StudentInfos();
        YunEdu.Model.JC_StudentInfos modelstudentinfo = new YunEdu.Model.JC_StudentInfos();

        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();

        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();


        protected void Page_Load(object sender, EventArgs e)
        {
            //GetArea();
            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
            }
            if (!IsPostBack)
            {
                InitControls();
                BindData();
            }
        }

        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }

        /// <summary>
        /// 获取查询条件
        /// </summary>
        /// <returns></returns>
        private string GetWhere()
        {
            StringBuilder sbWhere = new StringBuilder();

            string strColumnId = string.Empty;
            string strColumnPath = string.Empty;
            //属于本次考试
            if (Request.QueryString["id"] != null)
            {
                sbWhere.Append("a.TestId='" + Request.QueryString["id"].ToString() + "'");

                if (!string.IsNullOrEmpty(this.dorpShowSize.SelectedValue) && !this.dorpShowSize.SelectedValue.ToString().Equals("0"))
                {
                    AspNetPager1.PageSize = int.Parse(this.dorpShowSize.SelectedValue);
                }
                if (!string.IsNullOrEmpty(this.ddlsubject.SelectedValue) && !this.ddlsubject.SelectedValue.ToString().Equals("0"))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and SubjectId='" + ddlsubject.SelectedValue + "'");
                    }
                }
                if (!string.IsNullOrEmpty(this.dropType.SelectedValue) && !this.dropType.SelectedValue.ToString().Equals("0"))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    string _text = YunEdu.Common.StringPlus.Filter(this.inputName.Text, "html");
                    switch (this.dropType.SelectedValue)
                    {
                        case "1":
                            sbWhere.Append(" b.TName like '%" + this.inputName.Text + "%'");
                            break;
                        default:
                            break;
                    }
                }


            }
            else
            {
                sbWhere.Append("1<>1");
            }
            return sbWhere.ToString();
        }
        /// <summary>
        /// 获取数据并绑定
        /// </summary>
        private void BindData()
        {
            this.txtPageNum.Text = this.AspNetPager1.PageSize.ToString();
            strWhere = GetWhere();

            //modelsubjects = bllsubjects.GetModel(new Guid(ddlsubject.SelectedValue));
            //lblShowTime.Text = "考试时间：" + modelsubjects.StartTime + "--" + modelsubjects.EndTime;

            DataTable dt = new DataTable();
            AspNetPager1.RecordCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects a left join UserInfos b on a.TeacherId=b.UserID", strWhere);

            modeltests = blltests.GetModel(new Guid(id));
            dt.Columns.Add("ID");
            dt.Columns.Add("InvigilatorNum");
            dt.Columns.Add("InvigilatorTeacher");
            dt.Columns.Add("SubjectName");
            dt.Columns.Add("StartTime");
            dt.Columns.Add("EndTime");
            dt.Columns.Add("RoomNum");
            dt.Columns.Add("RoomLocation");
            //DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetList("PKC_TeacherSubjects a left join PKC_Rooms b on a.RoomId=b.RoomId", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "a.*,b.RoomTitle", strWhere, " RoomNum asc ");
            foreach (DataRow itemTestInvigilator in YunEdu.Common.GetRecordByPageOrder.GetList("PKC_TeacherSubjects a left join UserInfos b on a.TeacherId=b.UserID", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "a.*,b.TName", strWhere, " a.CreateTime asc ").Tables[0].Rows)
            {
                DataRow dr = dt.NewRow();
                dr["ID"] = itemTestInvigilator["ID"];
                modelrooms = bllrooms.GetModel(new Guid(itemTestInvigilator["RoomId"].ToString()));
                if (modelrooms != null)
                {
                    dr["RoomNum"] = modelrooms.RoomNum;
                    dr["RoomLocation"] = modelrooms.RoomLocation;
                }
                modeltests = blltests.GetModel(new Guid(id));
                if (modeltests != null)
                {
                    dr["InvigilatorNum"] = modeltests.InvigilatorNum;
                }
                dr["InvigilatorTeacher"] = itemTestInvigilator["TName"].ToString();
                //if (itemTestInvigilator["TeacherId"].ToString().Equals(""+Guid.Empty+"")) dr["InvigilatorTeacher"] = null;
                //else dr["InvigilatorTeacher"] = YunEdu.Common.GetRecordByPageOrder.GetModel("UserInfos", "TName", "UserID='" + itemTestInvigilator["TeacherId"] + "'").Tables[0].Rows[0]["TName"];
                modelsubjects = bllsubjects.GetModel(new Guid(itemTestInvigilator["SubjectId"].ToString()));
                if (modelsubjects != null)
                {
                    dr["SubjectName"] = modelsubjects.SubjectName;
                    dr["StartTime"] = modelsubjects.StartTime;
                    dr["EndTime"] = modelsubjects.EndTime;
                }
                dt.Rows.Add(dr);
            }
            gvTestTeachersList.DataSource = dt;
            gvTestTeachersList.DataBind();
        }

        protected void btnDelAll_Click(object sender, EventArgs e)
        {

            if (bllteachersubjects.DeleteAll(new Guid(id)))
            {
                MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('删除失败!');");
            }
            BindData();
        }

        private void InitControls()
        {
            //    int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 1);
            //    int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 2);
            //    if (Art != 0 && Math != 0)
            //    {
            //        ddlartormath.Visible = true;
            //        YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Subjects", "SubjectName,ID", "SubjectName", "ID", "TestId='" + id + "' and PropertyCode=3 or PropertyCode=" + ddlartormath.SelectedValue, "SubjectCode asc", ddlsubject, "0", false, "请选择");
            //    }
            //    else
            //    {
            //        ddlartormath.Visible = false;
            YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Subjects", "SubjectName,ID", "SubjectName", "ID", "TestId='" + id + "'", "SubjectCode asc", ddlsubject, "0", true, "请选择");
            //    }
        }
        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            BindData();
        }
        //设置分页显示数量
        protected void txtPageNum_TextChanged(object sender, EventArgs e)
        {
            int _pagesize;
            if (int.TryParse(txtPageNum.Text.Trim(), out _pagesize))
            {
                this.AspNetPager1.PageSize = _pagesize;
                this.AspNetPager1.CurrentPageIndex = 1;
                BindData();
            }
        }
        protected void btnSearch_Click(object sender, EventArgs e)
        {

            BindData();
        }
        protected void btnBack_Click(object sender, EventArgs e)
        {
            Response.Redirect("pkc_tests_list.aspx?MenuId=" + Request.QueryString["MenuId"]);
        }

        protected void btnPTeacher_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId='" + Guid.Empty + "'") == 0 && YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "'") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('已排完!');");
                return;
            }
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and OrderNum is not null and OrderNum<>0") < YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "'"))
            {
                MessageBox.ResponseScript(this, "layer.msg('请到科目管理为科目加上场次序号才可排监考!');");
                return;
            }
            //int teacherSum = 0;
            //foreach (DataRow teacher in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "*", "TestId='" + id + "' and Position=1", "").Tables[0].Rows)//读取每个老师
            //{
            //    teacherSum += (int)teacher["JianKaoCount"];
            //}
            //if (teacherSum < YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "'"))
            //{
            //    int k = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "") - teacherSum;
            //    MessageBox.Show(this, "安排的监考场次还少了" + k + "，请平均增加老师的监考场次");
            //}
            //else
            //{
            //    int k = teacherSum - YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "");
            //}
            //
            bool IsOver = false;
            foreach (DataRow teacher in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "*", "TestId='" + id + "' and Position=1 and JianKaoCount<>0", "newid()").Tables[0].Rows)//读取每个老师
            {
                if (teacher["JianKaoCount"] != null)//没有设置老师监考场次就退出
                {
                    bool flag = true;
                    string existsOrder = "100";
                    int haveSubject = 0;
                    int count = 0;
                    for (int i = 0; i < 2 && flag; i++)//没排完就一直排
                    {

                        if (i == 0 && !IsOver)//第一遍
                        {
                            foreach (DataRow subject in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "ID,OrderNum", "TestId='" + id + "'", "OrderNum").Tables[0].Rows)
                            {
                                haveSubject = 0;
                                for (int k = 0; k < existsOrder.Split(',').Length; k++)
                                {
                                    if (existsOrder.Split(',')[k].Equals(subject["OrderNum"].ToString()))
                                    {
                                        haveSubject = 1;
                                        break;
                                    }
                                }
                                if (haveSubject != 1)//可以安排老师
                                {
                                    DataSet dsTS = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 0, "ID", "TestId='" + id + "' and TeacherId='" + Guid.Empty + "' and SubjectId='" + subject["ID"] + "'", "");
                                    if (dsTS != null && dsTS.Tables.Count != 0 && dsTS.Tables[0].Rows.Count != 0)
                                    {
                                        modelteachersubjects = bllteachersubjects.GetModel(new Guid(dsTS.Tables[0].Rows[0]["ID"].ToString()));
                                        modelteachersubjects.TeacherId = (Guid)teacher["UserId"];
                                        bllteachersubjects.Update(modelteachersubjects);

                                        existsOrder += "," + subject["OrderNum"];
                                        count++;
                                        if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId='" + teacher["UserId"] + "'") == (int)teacher["JianKaoCount"])
                                        {//排完
                                            flag = false;
                                            break;
                                        }
                                    }

                                }
                                if (!flag)
                                {
                                    break;
                                }
                            }
                            if (count < (int)teacher["JianKaoCount"])//表示这个老师的监考还没安排好
                            {
                                IsOver = true;
                            }
                        }
                        else//第二遍
                        {
                            //找出没安排老师的考试
                            foreach (DataRow subject in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects left join PKC_Subjects on SubjectId=PKC_Subjects.ID", 0, "PKC_TeacherSubjects.ID,SubjectId,OrderNum", "PKC_TeacherSubjects.TestId='" + id + "' and  TeacherId='" + Guid.Empty + "'", "newid()").Tables[0].Rows)
                            {
                                haveSubject = 0;
                                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId='" + teacher["UserId"] + "'") == (int)teacher["JianKaoCount"])
                                {//排完
                                    break;
                                }
                                //找出这门课的序号，是否已有
                                for (int k = 0; k < existsOrder.Split(',').Length; k++)
                                {
                                    if (existsOrder.Split(',')[k].Equals(subject["OrderNum"].ToString()))
                                    {
                                        haveSubject = 2;//表示这门科目已有
                                        break;
                                    }
                                }
                                if (haveSubject == 2)
                                {
                                    //找出没有这门科目并且有其他不同科目的老师，与其调换
                                    DataSet dst = YunEdu.Common.GetRecordByPageOrder.GetListByVW(@"PKC_TestTeachers", 0, "UserId", "TestId='" + id + "' and UserId not in('" + Guid.Empty + "','" + teacher["UserId"] + "') and Position=1 and JianKaoCount<>0", "newid()");
                                    foreach (DataRow teac in dst.Tables[0].Rows)
                                    {
                                        haveSubject = 0;
                                        //找出这个老师的所有科目有没有同到
                                        foreach (DataRow dwsubject in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects left join PKC_Subjects on SubjectId=PKC_Subjects.ID", 0, "SubjectId,OrderNum", "PKC_TeacherSubjects.TestId='" + id + "' and  TeacherId='" + teac["UserId"] + "'", "").Tables[0].Rows)
                                        {
                                            if (subject["OrderNum"].ToString().Equals(dwsubject["OrderNum"].ToString()))
                                            {
                                                haveSubject = 3;
                                                break;
                                            }
                                        }
                                        //如果可以就交换
                                        if (haveSubject != 3)//表示这名老师没有这科目
                                        {
                                            //找出这个老师不同于strexists的科目
                                            DataSet dsd = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects left join PKC_Subjects on SubjectId=PKC_Subjects.ID", 0, "PKC_TeacherSubjects.ID,SubjectId,OrderNum", "PKC_TeacherSubjects.TestId='" + id + "' and  TeacherId='" + teac["UserId"] + "' and OrderNum not in(" + existsOrder + ")", "");

                                            if (dsd != null && dsd.Tables.Count != 0 && dsd.Tables[0].Rows.Count != 0)
                                            {
                                                string exchange = teac["UserId"].ToString();
                                                modelteachersubjects = bllteachersubjects.GetModel(new Guid(dsd.Tables[0].Rows[0]["ID"].ToString()));
                                                modelteachersubjects.TeacherId = (Guid)teacher["UserId"];
                                                bllteachersubjects.Update(modelteachersubjects);

                                                modelteachersubjects = bllteachersubjects.GetModel(new Guid(subject["ID"].ToString()));
                                                modelteachersubjects.TeacherId = new Guid(exchange);
                                                bllteachersubjects.Update(modelteachersubjects);

                                                existsOrder += "," + dsd.Tables[0].Rows[0]["OrderNum"];
                                                break;
                                            }
                                        }
                                    }
                                }
                                else//可以直接插入老师id
                                {
                                    modelteachersubjects = bllteachersubjects.GetModel(new Guid(subject["ID"].ToString()));
                                    modelteachersubjects.TeacherId = (Guid)teacher["UserId"];
                                    bllteachersubjects.Update(modelteachersubjects);

                                    existsOrder += "," + subject["OrderNum"];
                                }

                            }

                        }
                    }
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先设置每个老师的监考场次!');");
                }
            }
            //不固定考场监考
            //每个老师随机选考试科目进行排监考 

            //foreach (DataRow teacher in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "*", "TestId='" + id + "'", "newid()").Tables[0].Rows)//读取每个老师
            //{
            //    if (teacher["JianKaoCount"] != null)//没有设置老师监考场次就退出
            //    {
            //        //随机找出给老师安排的几场考试
            //        int count = 0;
            //        foreach (DataRow subject in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "ID", "TestId='" + id + "'", "newid()").Tables[0].Rows)
            //        {   
            //            if (count < (int)teacher["JianKaoCount"])
            //            {
            //                DataSet dsTS = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 1, "ID", "TestId='"+id+"' and TeacherId='"+Guid.Empty+"' and SubjectId='" + subject["ID"] + "'", "newid()");
            //                if (dsTS != null && dsTS.Tables.Count != 0 && dsTS.Tables[0].Rows.Count != 0)
            //                {
            //                    count++;
            //                    modelteachersubjects = bllteachersubjects.GetModel(new Guid(dsTS.Tables[0].Rows[0]["ID"].ToString()));
            //                    modelteachersubjects.TeacherId = (Guid)teacher["UserId"];
            //                    bllteachersubjects.Update(modelteachersubjects);
            //                }
            //            }
            //            else break;//这个老师已经排完了
            //        }
            //    }
            //    else
            //    {
            //        YunEdu.Common.MessageBox.Show(this, "请先确定每个老师监考场次!");
            //        return;
            //    }

            //}

            BindData();
            MessageBox.ResponseScript(this, "layer.msg('监考编排完成!');");
        }

        protected void btnPTeacher2_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId='" + Guid.Empty + "'") == 0 && YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "'") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('已排完!');");
                return;
            }
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and OrderNum is not null and OrderNum<>0") < YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "'"))
            {
                MessageBox.ResponseScript(this, "layer.msg('监考编排完成!');");
                return;
            }
            int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 1);
            int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 2);

            //固定考场监考
            modeltests = blltests.GetModel(new Guid(id));
            DataSet dsTeacher = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "UserId,JianKaoCount", "TestId='" + id + "' and Position=1", "JianKaoCount");
            if (dsTeacher==null|| dsTeacher.Tables[0].Rows.Count==0)
            {
                MessageBox.ResponseScript(this, "layer.msg('未添加监考老师!');");
                return;
            }
            if (dsTeacher.Tables[0].Rows[0]["JianKaoCount"] == null)//没有设置老师监考场次就退出
            {
                MessageBox.ResponseScript(this, "layer.msg('请先确定每个老师监考场次!');");
                return;
            }
            bool IsOver = false;//是否固定的已经全部排完
            DataTable dtTestTeacher = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "UserId,JianKaoCount", "TestId='" + id + "' and Position=1 and JianKaoCount<>0", "JianKaoCount").Tables[0];
            if (dtTestTeacher.Rows.Count<=0)
            {
                MessageBox.ResponseScript(this, "layer.msg('不存在监考老师!');");
                return;
            }
            foreach (DataRow teacher in dtTestTeacher.Rows)//读取每个老师
            {
                bool flag = true;
                string existRoomId = string.Empty;
                for (int i = 0; i < 2 && flag; i++)//没排完就一直排
                {

                    if (i == 0 && !IsOver)//第一遍
                    {
                        DataTable dtRoom = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "RoomId", "TestId='" + id + "' and IsUsing=1", "newid()").Tables[0];
                        if (dtRoom.Rows.Count<=0)
                        {
                            MessageBox.ResponseScript(this, "layer.msg('不存在考场信息!');");
                            return;
                        }
                        foreach (DataRow room in dtRoom.Rows)
                        {
                            for (int j = 1; j <= modeltests.InvigilatorNum && flag; j++)
                            {
                                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId='" + Guid.Empty + "' and RoomId='" + room["RoomId"] + "' and SameNum=" + j) >= (int)teacher["JianKaoCount"])//可以安排老师
                                {
                                    foreach (DataRow dwTs in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 0, "distinct SubjectId", "TestId='" + id + "' and TeacherId='" + Guid.Empty + "' and RoomId='" + room["RoomId"] + "'", "").Tables[0].Rows)
                                    {
                                        DataSet dsTS = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 0, "ID", "TestId='" + id + "' and TeacherId='" + Guid.Empty + "' and RoomId='" + room["RoomId"] + "' and SubjectId='" + dwTs["SubjectId"] + "' and SameNum=" + j, "");
                                        modelteachersubjects = bllteachersubjects.GetModel(new Guid(dsTS.Tables[0].Rows[0]["ID"].ToString()));
                                        modelteachersubjects.TeacherId = (Guid)teacher["UserId"];
                                        bllteachersubjects.Update(modelteachersubjects);

                                        if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId='" + teacher["UserId"] + "'") == (int)teacher["JianKaoCount"])
                                        {//排完
                                            flag = false;
                                            break;
                                        }
                                    }
                                }
                                if (!flag)
                                {
                                    break;
                                }

                            }
                            if (!flag)
                            {
                                break;
                            }
                        }
                        if (flag) IsOver = true;
                    }
                    else//第二遍,可能留下的都是同一门课，所以会有调换
                    {
                        string strexists = "100";
                        DataTable dtRoom = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "RoomId", "TestId='" + id + "' and IsUsing=1", "newid()").Tables[0];
                        if (dtRoom.Rows.Count <= 0)
                        {
                            MessageBox.ResponseScript(this, "layer.msg('不存在考场信息!');");
                            return;
                        }
                        foreach (DataRow room in dtRoom.Rows)
                        {
                            DataTable dtTeacherSubjects = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 0, "distinct SubjectId", "TestId='" + id + "' and TeacherId='" + Guid.Empty + "' and RoomId='" + room["RoomId"] + "'", "").Tables[0];
                            foreach (DataRow dwTs in dtTeacherSubjects.Rows)
                            {
                                bool haveSubject = false;
                                //找出这门课的序号
                                string strorder = bllsubjects.GetList("TestId='" + id + "' and ID='" + dwTs["SubjectId"] + "'").Tables[0].Rows[0]["Ordernum"].ToString();
                                for (int k = 0; k < strexists.Split(',').Length; k++)
                                {
                                    if (strexists.Split(',')[k].Equals(strorder))
                                    {
                                        haveSubject = true;
                                        break;
                                    }
                                }
                                if (haveSubject)//如果科目已经有过一次了
                                {
                                    //找出序号
                                    //找出不同序号的科目  dwTs["SubjectId"]
                                    //找出这个科目，这个考场的一个老师
                                    //找出这个考场已经固定安排的老师，随机与其调换一场科目

                                    bool haveSame = false;
                                    DataSet dsteacher = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 0, "distinct TeacherId", "TestId='" + id + "' and RoomId='" + room["RoomId"] + "' and TeacherId not in('" + Guid.Empty + "','" + teacher["UserId"] + "')", "");
                                    foreach (DataRow dwc in dsteacher.Tables[0].Rows)//找出这个考场的老师
                                    {
                                        //找出这个老师的所有科目
                                        DataSet dsh = bllteachersubjects.GetList("TestId='" + id + "' and TeacherId='" + dwc["TeacherId"] + "' and RoomId='" + room["RoomId"] + "'");
                                        foreach (DataRow dwh in dsh.Tables[0].Rows)
                                        {
                                            //找出这科目的序号
                                            string strhave = bllsubjects.GetList("TestId='" + id + "' and ID='" + dwh["SubjectId"] + "'").Tables[0].Rows[0]["OrderNum"].ToString();
                                            if (strhave.Equals(strorder))//如果和空缺的一门序号一样就说明这老师已经有了这么科目
                                            {
                                                haveSame = true;
                                            }
                                        }
                                        if (!haveSame)//要替换的科目这老师没有,就可以替换
                                        {
                                            //找出不在已有序号的所有科目
                                            DataSet dsc = bllsubjects.GetList("TestId='" + id + "' and OrderNum not in(" + strexists + ")");
                                            foreach (DataRow dwSu in dsc.Tables[0].Rows)
                                            {
                                                //找这次考试，这个考场，这个老师，这个科目的场次 
                                                DataSet dsd = bllteachersubjects.GetList("TestId='" + id + "' and RoomId='" + room["RoomId"] + "' and SubjectId='" + dwSu["ID"] + "' and TeacherId='" + dwc["TeacherId"] + "'");
                                                if (dsd != null && dsd.Tables.Count != 0 && dsd.Tables[0].Rows.Count != 0)
                                                {
                                                    string exchange = dwc["TeacherId"].ToString();
                                                    modelteachersubjects = bllteachersubjects.GetModel(new Guid(dsd.Tables[0].Rows[0]["ID"].ToString()));
                                                    modelteachersubjects.TeacherId = (Guid)teacher["UserId"];
                                                    bllteachersubjects.Update(modelteachersubjects);

                                                    DataSet dsb = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 0, "ID", "TestId='" + id + "' and RoomId='" + room["RoomId"] + "' and SubjectId='" + dwTs["SubjectId"] + "' and TeacherId='" + Guid.Empty + "'", "");
                                                    modelteachersubjects = bllteachersubjects.GetModel(new Guid(dsb.Tables[0].Rows[0]["ID"].ToString()));
                                                    modelteachersubjects.TeacherId = new Guid(exchange);
                                                    bllteachersubjects.Update(modelteachersubjects);

                                                    strexists += "," + bllsubjects.GetList("TestId='" + id + "' and ID='" + dsd.Tables[0].Rows[0]["SubjectId"] + "'").Tables[0].Rows[0]["OrderNum"];
                                                    break;
                                                }
                                            }
                                            break;
                                        }
                                    }
                                }
                                else
                                {
                                    DataSet dsb = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TeacherSubjects", 0, "ID,SubjectId", "TestId='" + id + "' and RoomId='" + room["RoomId"] + "' and SubjectId='" + dwTs["SubjectId"] + "' and TeacherId='" + Guid.Empty + "'", "");
                                    modelteachersubjects = bllteachersubjects.GetModel(new Guid(dsb.Tables[0].Rows[0]["ID"].ToString()));
                                    modelteachersubjects.TeacherId = (Guid)teacher["UserId"];
                                    bllteachersubjects.Update(modelteachersubjects);
                                    strexists += "," + bllsubjects.GetList("TestId='" + id + "' and ID='" + dsb.Tables[0].Rows[0]["SubjectId"] + "'").Tables[0].Rows[0]["OrderNum"];
                                }
                                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId='" + teacher["UserId"] + "'") == (int)teacher["JianKaoCount"])
                                {//排完
                                    flag = false;
                                    break;
                                }
                            }
                            if (!flag) break;
                        }
                    }
                }

            }
            BindData();
            MessageBox.ResponseScript(this, "layer.msg('监考编排完成!');");

        }
        private void ExportTeacherInfoToWordByXmls(string docPath)
        {
            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/teacherinfo.xml");

            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            rootWord.CreateXmlDom(xmlPath);//加载xml

            ArrayList sectList = new ArrayList();

            #region 动态生成word文档

            Dictionary<string, string> wordTexts = new Dictionary<string, string>();
            Dictionary<string, YunEdu.Common.WordMLHelper.WordTable> wordTable = new Dictionary<string, YunEdu.Common.WordMLHelper.WordTable>();

            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            myWordHelper.CreateXmlDom(xmlPath);

            modeltests = blltests.GetModel(new Guid(id));
            //已使用的考场
            DataSet allTeacher = YunEdu.Common.GetRecordByPageOrder.GetListByVW(@"PKC_TeacherSubjects left join PKC_Subjects on PKC_TeacherSubjects.SubjectId=PKC_Subjects.ID", 0, "TeacherId,SubjectId,RoomId", "PKC_TeacherSubjects.TestId='" + id + "'", "TeacherId,OrderNum");

            int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "'");
            //替换标签
            wordTexts.Clear();
            wordTexts.Add("TestTitle", modeltests.TestTitle);
            wordTexts.Add("TeacherCount", count.ToString());
            wordTexts.Add("PerCount", modeltests.InvigilatorNum.ToString());
            wordTexts.Add("StartDate", modeltests.TestStartDate.ToString());
            wordTexts.Add("EndDate", modeltests.TestEndDate.ToString());

            myWordHelper.ReplaceNodeText(wordTexts);
            //加入表格
            wordTable.Clear();
            //获取数据源
            System.Data.DataTable tbl = new System.Data.DataTable("tbTeachers");

            DataColumn idColumn = new DataColumn();
            idColumn.DataType = System.Type.GetType("System.String");
            idColumn.ColumnName = "教师";
            tbl.Columns.Add(idColumn);

            DataColumn rColumn = new DataColumn();
            rColumn.DataType = System.Type.GetType("System.String");
            rColumn.ColumnName = "教师编号";
            tbl.Columns.Add(rColumn);

            DataColumn sColumn = new DataColumn();
            sColumn.DataType = System.Type.GetType("System.String");
            sColumn.ColumnName = "职位";
            tbl.Columns.Add(sColumn);

            DataColumn cnColumn = new DataColumn();
            cnColumn.DataType = System.Type.GetType("System.String");
            cnColumn.ColumnName = "科目";
            tbl.Columns.Add(cnColumn);

            DataColumn BeiZhuColumn = new DataColumn();
            BeiZhuColumn.DataType = System.Type.GetType("System.String");
            BeiZhuColumn.ColumnName = "开始时间";
            tbl.Columns.Add(BeiZhuColumn);

            DataColumn Starttime = new DataColumn();
            Starttime.DataType = System.Type.GetType("System.String");
            Starttime.ColumnName = "结束时间";
            tbl.Columns.Add(Starttime);

            DataColumn Teacher = new DataColumn();
            Teacher.DataType = System.Type.GetType("System.String");
            Teacher.ColumnName = "考场号";
            tbl.Columns.Add(Teacher);

            DataColumn pwdColumn = new DataColumn();
            pwdColumn.DataType = System.Type.GetType("System.String");
            pwdColumn.ColumnName = "考场所在位置";
            tbl.Columns.Add(pwdColumn);

            //DataColumn[] keys = new DataColumn[1];
            //keys[0] = entranceIdColumn;
            //tbl.PrimaryKey = keys;
            foreach (DataRow teacherXunKao in YunEdu.Common.GetRecordByPageOrder.GetListByVW(@"PKC_TestTeachers", 0, "UserId", "TestId='" + id + "' and Position=2", "").Tables[0].Rows)
            {
                DataRow newRow = tbl.NewRow();

                YunEdu.BLL.aspnet_Users bllUser = new YunEdu.BLL.aspnet_Users();
                newRow[0] = bllUser.GetCName(new Guid(teacherXunKao["UserId"].ToString()));
                newRow[1] = YunEdu.Common.GetRecordByPageOrder.GetListByVW("UserInfos", 0, "UserNo", "UserID='" + teacherXunKao["UserId"] + "'", "").Tables[0].Rows[0]["UserNo"];
                newRow[2] = "巡考员";
                newRow[3] = "";
                newRow[4] = "";
                newRow[5] = "";
                newRow[6] = "";
                newRow[7] = "";

                tbl.Rows.Add(newRow);
            }
            foreach (DataRow teacher in allTeacher.Tables[0].Rows)
            {
                DataRow newRow = tbl.NewRow();

                YunEdu.BLL.aspnet_Users bllUser = new YunEdu.BLL.aspnet_Users();
                newRow[0] = bllUser.GetCName(new Guid(teacher["TeacherId"].ToString()));
                newRow[1] = YunEdu.Common.GetRecordByPageOrder.GetListByVW("UserInfos", 0, "UserNo", "UserID='" + teacher["TeacherId"] + "'", "").Tables[0].Rows[0]["UserNo"];
                newRow[2] = "监考员";
                modelsubjects = bllsubjects.GetModel(new Guid(teacher["SubjectId"].ToString()));
                newRow[3] = modelsubjects.SubjectName;
                //newRow[3] = modelsubjects.StartTime;
                DateTime dt;
                if (modelsubjects.StartTime != null)
                {
                    dt = (DateTime)modelsubjects.StartTime;
                    newRow[4] = dt.ToString("MM/dd hh:mm");
                }
                else
                {
                    newRow[4] = "";
                }
                if (modelsubjects.StartTime != null)
                {
                    dt = (DateTime)modelsubjects.EndTime;
                    newRow[5] = dt.ToString("MM/dd hh:mm");
                }
                else
                {
                    newRow[5] = "";
                }

                modelrooms = bllrooms.GetModel((Guid)teacher["RoomId"]);
                newRow[6] = modelrooms.RoomNum;
                newRow[7] = modelrooms.RoomLocation;

                tbl.Rows.Add(newRow);
            }

            Dictionary<string, string> bookmarks = new Dictionary<string, string>();
            foreach (DataColumn column in tbl.Columns)
            {
                bookmarks.Add(column.ColumnName, column.ColumnName);
            }
            myWordHelper.ReplaceNodeTable(tbl, bookmarks);

            sectList.Add(myWordHelper.GetDocSection());
            //}
            #endregion

            rootWord.MerginDoc(sectList);//合并word文档内容
            rootWord.Save(docPath);//保存文档
        }
        protected void btnExport_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "' and TeacherId<>'" + Guid.Empty + "'") <= 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请先添加老师，设置每个考场的监考人数，排完监考后才可导出word文档!');");

                return;
            }
            modeltests = blltests.GetModel(new Guid(id));
            DataSet dsa = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "UserId", "TestId='" + modeltests.TestId + "'");
            modelstudentinfo = bllstudentinfo.GetModel(new Guid(dsa.Tables[0].Rows[0]["UserId"].ToString()));
            string foldName = Server.MapPath(CodeTable.GetDirectoryTemp(modelstudentinfo.ColumnPath, UserName, CodeTable.FileType.files));
            string tmpDesDir = foldName + modeltests.TestTitle;//Server.MapPath("/") 
            if (!Directory.Exists(tmpDesDir))
            {
                Directory.CreateDirectory(tmpDesDir);
            }
            //文件保存位置
            string docPath;
            docPath = tmpDesDir + "\\" + modeltests.TestTitle + "监考安排表.doc";
            ExportTeacherInfoToWordByXmls(docPath);
            // 下载文件
            DownLoadFile("监考安排表.doc", docPath, "application/octet-stream");
            //Response.Clear();
            //Response.ContentType = "application/ms-word";
            //Response.AddHeader("Content-Disposition", "attachment;filename=" + Server.UrlEncode("监考安排.doc"));
            //Response.TransmitFile(docPath);
        }

        private void DownLoadFile(string customerFileName, string filePath, string fileType)
        {
            // 下载文件
            FileInfo fileInfo = new FileInfo(filePath);
            Response.Clear();
            Response.ClearContent();
            Response.ClearHeaders();
            Response.AddHeader("Content-Disposition", "attachment;filename=" + customerFileName);
            Response.AddHeader("Content-Length", fileInfo.Length.ToString());
            Response.AddHeader("Content-Transfer-Encoding", "binary");
            Response.ContentType = "application/octet-stream";
            Response.ContentEncoding = System.Text.Encoding.GetEncoding("gb2312");
            Response.WriteFile(fileInfo.FullName);
            Response.Flush();
            Response.End();
        }

        //protected void ddlartormath_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Subjects", "SubjectName,ID", "SubjectName", "ID", "TestId='" + id + "' and PropertyCode=3 or PropertyCode=" + ddlartormath.SelectedValue, "SubjectCode asc", ddlsubject, "0", false, "请选择");
        //}
    }
}