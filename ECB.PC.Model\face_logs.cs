﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// face_logs:实体类
	/// </summary>
	[Serializable]
	public partial class face_logs
	{
		public face_logs()
		{}

		#region Model

		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private DateTime? _createdate;		 
		private string _comment;

		/// <summary>
		/// 主键
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}

        /// <summary>
        /// 地区id
        /// </summary>
        public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}

        /// <summary>
        /// 地区path
        /// </summary>
        public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}

        /// <summary>
        /// 人脸版本id
        /// </summary>
        public Guid? VersionId { get; set; }

		/// <summary>
		/// 操作类型
		/// 1更新 2删除
        /// </summary>
        public int? OperationType { get; set; }

        /// <summary>
        /// 班牌编号
        /// </summary>
        public string SeriaNo { get; set; }

        /// <summary>
        /// 人脸用户id
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 执行操作结果
		/// 1成功 0失败
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 下发时间
        /// </summary>
        public DateTime? CreateDate
		{
			set{ _createdate=value;}
			get{return _createdate;}
		}

        /// <summary>
        /// 操作人
        /// </summary>
        public Guid? Creator { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        public string Comment
		{
			set{ _comment=value;}
			get{return _comment;}
		}

		#endregion Model
	}
}