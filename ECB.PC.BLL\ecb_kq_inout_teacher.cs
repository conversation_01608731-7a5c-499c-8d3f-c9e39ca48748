﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_kq_inout_teacher
    /// </summary>
    public partial class ecb_kq_inout_teacher
    {
        public ecb_kq_inout_teacher()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_kq_inout_teacher");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_kq_inout_teacher model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_kq_inout_teacher(");
            strSql.Append("Id,ColumnId,ColumnPath,UserId,KQStage,KQType,KQTime,SignTime,KQStatus)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@UserId,@KQStage,@KQType,@KQTime,@SignTime,@KQStatus)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@KQStage", SqlDbType.Int,4),
                    new SqlParameter("@KQType", SqlDbType.Int,4),
                    new SqlParameter("@KQTime", SqlDbType.DateTime),
                    new SqlParameter("@SignTime", SqlDbType.DateTime),
                    new SqlParameter("@KQStatus", SqlDbType.Int,4)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = Guid.NewGuid();
            parameters[4].Value = model.KQStage;
            parameters[5].Value = model.KQType;
            parameters[6].Value = model.KQTime;
            parameters[7].Value = model.SignTime;
            parameters[8].Value = model.KQStatus;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_kq_inout_teacher model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_kq_inout_teacher set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("KQStage=@KQStage,");
            strSql.Append("KQType=@KQType,");
            strSql.Append("KQTime=@KQTime,");
            strSql.Append("SignTime=@SignTime,");
            strSql.Append("KQStatus=@KQStatus");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@KQStage", SqlDbType.Int,4),
                    new SqlParameter("@KQType", SqlDbType.Int,4),
                    new SqlParameter("@KQTime", SqlDbType.DateTime),
                    new SqlParameter("@SignTime", SqlDbType.DateTime),
                    new SqlParameter("@KQStatus", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.UserId;
            parameters[3].Value = model.KQStage;
            parameters[4].Value = model.KQType;
            parameters[5].Value = model.KQTime;
            parameters[6].Value = model.SignTime;
            parameters[7].Value = model.KQStatus;
            parameters[8].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_teacher ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_teacher ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_inout_teacher GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,UserId,KQStage,KQType,KQTime,SignTime,KQStatus from ecb_kq_inout_teacher ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_kq_inout_teacher model = new ECB.PC.Model.ecb_kq_inout_teacher();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_inout_teacher DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_kq_inout_teacher model = new ECB.PC.Model.ecb_kq_inout_teacher();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["KQStage"] != null && row["KQStage"].ToString() != "")
                {
                    model.KQStage = int.Parse(row["KQStage"].ToString());
                }
                if (row["KQType"] != null && row["KQType"].ToString() != "")
                {
                    model.KQType = int.Parse(row["KQType"].ToString());
                }
                if (row["KQTime"] != null && row["KQTime"].ToString() != "")
                {
                    model.KQTime = DateTime.Parse(row["KQTime"].ToString());
                }
                if (row["SignTime"] != null && row["SignTime"].ToString() != "")
                {
                    model.SignTime = DateTime.Parse(row["SignTime"].ToString());
                }
                if (row["KQStatus"] != null && row["KQStatus"].ToString() != "")
                {
                    model.KQStatus = int.Parse(row["KQStatus"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,UserId,KQStage,KQType,KQTime,SignTime,KQStatus ");
            strSql.Append(" FROM ecb_kq_inout_teacher ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,UserId,KQStage,KQType,KQTime,SignTime,KQStatus ");
            strSql.Append(" FROM ecb_kq_inout_teacher ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_kq_inout_teacher ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_kq_inout_teacher T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_inout_teacher";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获取教师到离校管理列表
        /// </summary>
        /// <param name="strWhere">条件</param>
        /// <param name="type"></param>
        /// <returns></returns>
        public DataSet GetStaList(string strWhere, string type = "")
        {
            StringBuilder strSql = new StringBuilder();
            //获取详情
            if (!string.IsNullOrEmpty(type) && type == "detail")
            {
                strSql.Append("select b.UserID,b.TName 'UserName',COUNT(1) total");
            }
            else
            {
                strSql.Append("select KQStage,KQType,ISNULL(SUM(CASE KQStatus WHEN 1 THEN 1 ELSE 0 END), 0) '正常',ISNULL(SUM(CASE KQStatus WHEN 2 THEN 1 ELSE 0 END), 0) '迟到',ISNULL(SUM(CASE KQStatus WHEN 3 THEN 1 ELSE 0 END),0) '缺卡',CAST(Round(convert(float,SUM(case when KQStatus=3 then 1 else 0 end))/convert(float,COUNT(KQStatus))*100,2) as nvarchar)+'%' as '缺卡率'");
            }
            strSql.Append(" FROM ecb_kq_inout_teacher a left join UserInfos b on a.UserId=b.UserID where KQTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            if (!string.IsNullOrEmpty(type) && type == "detail")
            {
                strSql.Append(" group by b.UserID,b.TName order by total desc");
            }
            else
            {
                strSql.Append(" group by KQStage,KQType order by KQStage,KQType");
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select KQStage,KQType,SUM(CASE WHEN b.[Status] = 1 THEN 1 ELSE 0 END) '正常',SUM(CASE WHEN b.[Status] = 2 THEN 1 ELSE 0 END) '迟到',SUM(CASE WHEN b.[Status] = 3 THEN 1 ELSE 0 END) '缺卡',SUM(CASE WHEN b.[Status] = 4 THEN 1 ELSE 0 END) '请假',CAST(Round(convert(float,SUM(case when  b.[Status]=3 then 1 else 0 end))/convert(float,COUNT( b.[Status]))*100,2) as nvarchar)+'%' as '缺卡率'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher_history ) a cross apply openjson(a.KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME) as b where KQTime<=GETDATE() ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }

            strSql.Append(" group by KQStage,KQType order by KQStage,KQType");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select c.UserId as ID,CName 'UserName',COUNT(1) total");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher_history ) a cross apply openjson(a.KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME) as b left join aspnet_Membership c on a.UserId=c.UserId   where KQTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" group by c.UserId,CName order by total desc");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendListInfo(int ColumnId, DateTime stadate, DateTime enddate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select CName '姓名',CONVERT(varchar(100),SignTime, 23) as '日期',case when KQStage=1 then '上午' when KQStage=2 then '下午' else '晚上' end '考勤时段',case when KQType=1 then '到校' else '离校' end '考勤类型',case when Status = 1 then '正常' when (Status = 2 and KQType = 1)  then '迟到' when (Status = 2 and KQType = 2) then '早退' when Status = 3 then '缺卡' else '' end '考勤状态',CASE WHEN Status=3 THEN '' ELSE CONVERT(varchar(100),SignTime, 120) END  AS '考勤时间'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher_history ) a cross apply openjson(KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME) as b LEFT JOIN aspnet_Membership C ON a.UserId=C.UserId ");
            strSql.Append(" WHERE a.ColumnId='" + ColumnId + "' and RecordDate>='" + stadate.ToString("yyyy-MM-dd") + "' and RecordDate<='" + enddate.ToString("yyyy-MM-dd") + "' AND Status<>1 and KQTime<=GETDATE()");
            strSql.Append(" ORDER BY RecordDate ,KQStage ,KQType ,SignTime ,CName");
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得统计列表（湾里一中用）
        /// </summary>
        public DataSet GetWStaAttendListInfo(int ColumnId, DateTime stadate, DateTime enddate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT a.UserId, CName,CONVERT(varchar(100),a.RecordDate, 23) as RecordDate ,case when KQStage=1 then '上午' when KQStage=2 then '下午' else '晚上' end KQStage ,case when KQType=1 then '上班' else '下班' end KQType  ,case when Status = 1 then '正常' when (Status = 2 and KQType = 1)  then '迟到' when (Status = 2 and KQType = 2) then '早退' when Status = 3 then '缺卡' else '' end KQStatus ,CASE WHEN Status=3 THEN '' ELSE CONVERT(varchar(100),SignTime, 108) END  AS KQTime  ,LateTime");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher_history ) a cross apply openjson(KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME,LateTime INT) as b LEFT JOIN aspnet_Membership C ON a.UserId=C.UserId  INNER JOIN" +
                " (SELECT  DISTINCT UserId, RecordDate FROM(select ColumnId, KQRecords, RecordDate, UserId from ecb_kq_inout_teacher union all select ColumnId, KQRecords, RecordDate, UserId from ecb_kq_inout_teacher_history ) a cross apply openjson(KQRecords) with([Status] INT, KQStage INT, KQType INT, SignTime DATETIME, KQTime DATETIME, LateTime INT) as b " +
                " WHERE STATUS <> 1 AND ColumnId ='" + ColumnId + "' and RecordDate>='" + stadate.ToString("yyyy-MM-dd") + "' and RecordDate<='" + enddate.ToString("yyyy-MM-dd") + "'  and KQTime<=GETDATE()) bb ON a.UserId = bb.UserId AND a.RecordDate = bb.RecordDate  ");
            strSql.Append(" WHERE CName IS NOT NULL");
            //strSql.Append(" WHERE a.ColumnId='" + ColumnId + "' and RecordDate>='" + stadate.ToString("yyyy-MM-dd") + "' and RecordDate<='" + enddate.ToString("yyyy-MM-dd") + "'  and KQTime<=GETDATE()");
            strSql.Append(" ORDER BY  CName, RecordDate ,KQStage ,KQType ,SignTime");
            return DbHelperSQL.Query(strSql.ToString());
        }
        public DataSet GetWStaAttendAllListInfo(int ColumnId, DateTime stadate, DateTime enddate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT a.UserId, CName,CONVERT(varchar(100),a.RecordDate, 23) as RecordDate ,case when KQStage=1 then '上午' when KQStage=2 then '下午' else '晚上' end KQStage ,case when KQType=1 then '上班' else '下班' end KQType  ,case when Status = 1 then '正常' when (Status = 2 and KQType = 1)  then '迟到' when (Status = 2 and KQType = 2) then '早退' when Status = 3 then '缺卡' else '' end KQStatus ,CASE WHEN Status=3 THEN '' ELSE CONVERT(varchar(100),SignTime, 108) END  AS KQTime  ,LateTime");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_teacher_history ) a cross apply openjson(KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME,LateTime INT) as b LEFT JOIN aspnet_Membership C ON a.UserId=C.UserId  INNER JOIN" +
                " (SELECT  DISTINCT UserId, RecordDate FROM(select ColumnId, KQRecords, RecordDate, UserId from ecb_kq_inout_teacher union all select ColumnId, KQRecords, RecordDate, UserId from ecb_kq_inout_teacher_history ) a cross apply openjson(KQRecords) with([Status] INT, KQStage INT, KQType INT, SignTime DATETIME, KQTime DATETIME, LateTime INT) as b " +
                " WHERE ColumnId ='" + ColumnId + "' and RecordDate>='" + stadate.ToString("yyyy-MM-dd") + "' and RecordDate<='" + enddate.ToString("yyyy-MM-dd") + "'  and KQTime<=GETDATE()) bb ON a.UserId = bb.UserId AND a.RecordDate = bb.RecordDate  ");
            strSql.Append(" WHERE CName IS NOT NULL");
            //strSql.Append(" WHERE a.ColumnId='" + ColumnId + "' and RecordDate>='" + stadate.ToString("yyyy-MM-dd") + "' and RecordDate<='" + enddate.ToString("yyyy-MM-dd") + "'  and KQTime<=GETDATE()");
            strSql.Append(" ORDER BY  CName, RecordDate ,KQStage ,KQType ,SignTime");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 修改教师到离校考勤状态
        /// </summary>
        /// <param name="columnId">地区</param>
        /// <param name="beginDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="kqStage">要修改的类型 1上午 2下午</param>
        /// <param name="kqType">要修改的类型 1到校 2离校</param>
        /// <param name="preStatus">改之前的状态</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <param name="mark">备注</param>
        /// <param name="userids">用户id集合</param>
        /// <returns></returns>
        public bool ModifyStatus(int columnId, DateTime beginDate, DateTime endDate, int kqStage, int kqType, int preStatus, int toStatus, string mark, string userids = "")
        {
            StringBuilder strSql = new StringBuilder();
            //修改时间
            if (endDate.Date == DateTime.Now.Date)
            {
                strSql.AppendLine("UPDATE ecb_kq_inout_teacher SET KQRecords = (");
                strSql.AppendLine("SELECT b.PlaceId,b.Photo,b.KQStage,b.KQType,b.KQTime,");
                strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN @ToStatus ELSE b.[Status] END [Status],");
                strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN @Mark ELSE b.Mark END Mark,");
                if (toStatus == 1)
                {
                    // 改为正常
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN DATEADD(ss," + (kqType == 1 ? "-cast(rand(checksum(a.Id))*599+1 as int)" : "cast(rand(checksum(a.Id))*599+1 as int)") + ",b.KQTime) ELSE b.SignTime END SignTime,");
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
                }
                else if (toStatus == 2)
                {
                    // 改为迟到
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN DATEADD(ss,cast(rand(checksum(a.Id))*599+1 as int),b.KQTime) ELSE b.SignTime END SignTime,");
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN cast(rand(checksum(a.Id))*599+1 as int) ELSE b.LateTime END LateTime");
                }
                else if (toStatus == 3)
                {
                    // 改为缺卡
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN '" + endDate.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
                }
                strSql.AppendLine("FROM ecb_kq_inout_teacher a CROSS APPLY openjson(a.KQRecords)");
                strSql.AppendLine("WITH (PlaceId UNIQUEIDENTIFIER,[Status] INT,SignTime DATETIME,LateTime INT,Photo NVARCHAR(250),KQStage INT,KQType INT,KQTime DATETIME,Mark nvarchar(40)) b");
                strSql.AppendLine("WHERE a.ColumnId=@ColumnId and a.RecordDate=@endDate AND a.UserId=ecb_kq_inout_teacher.UserId FOR json PATH");
                strSql.AppendLine("),");
                if (toStatus == 1 || toStatus == 2)
                {
                    // 如果是改为正常或迟到，则为不缺卡
                    strSql.AppendLine("IsAbsent=0");
                }
                else
                {
                    int otherStage = 0, otherType = 0;
                    // 如果是上午
                    if (kqStage == 1)
                    {
                        // 判断下午
                        otherStage = 2;
                        otherType = 2;
                    }
                    else
                    {
                        // 判断上午
                        otherStage = 1;
                        otherType = 1;
                    }
                    // 改为缺卡，则需要判断另外一半是否有打卡
                    strSql.AppendFormat("IsAbsent=(case when exists(select 1 from ecb_kq_inout_teacher a CROSS APPLY openjson(a.KQRecords) WITH ([Status] INT,KQStage INT,KQType INT) b where a.RecordDate=@endDate and a.UserId=ecb_kq_inout_teacher.UserId and b.KQStage={0} and b.KQType={1} and b.[Status]<>3) then 0 else 1 end)", otherStage, otherType);
                    strSql.AppendLine();
                }
                strSql.AppendLine("WHERE ColumnId = @ColumnId and RecordDate = @endDate");

                if (!string.IsNullOrEmpty(userids))
                {
                    strSql.AppendLine(" AND UserId in ('" + userids + "')");
                }
            }
            if (beginDate.Date != DateTime.Now.Date)
            {
                strSql.AppendLine(" UPDATE ecb_kq_inout_teacher_history SET KQRecords = (");
                strSql.AppendLine("SELECT max(b.PlaceId) as PlaceId,b.Photo,b.KQStage,b.KQType,CONVERT(nvarchar(100),(CONVERT(nvarchar(100),CONVERT(datetime,a.RecordDate),23)+' '+CONVERT(nvarchar(100),min(b.KQTime),24)),120) as KQTime,");
                strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN @ToStatus ELSE b.[Status] END [Status],");
                strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN @Mark ELSE b.Mark END Mark,");
                if (toStatus == 1)
                {
                    // 改为正常
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN DATEADD(ss," + (kqType == 1 ? "-cast(rand(checksum(a.Id))*599+1 as int)" : "cast(rand(checksum(a.Id))*599+1 as int)") + ",CONVERT(nvarchar(100),(CONVERT(nvarchar(100),CONVERT(datetime,a.RecordDate),23)+' '+CONVERT(nvarchar(100),min(b.KQTime),24)),120)) ELSE CONVERT(nvarchar(100),(CONVERT(nvarchar(100),CONVERT(datetime,a.RecordDate),23)+' '+CONVERT(nvarchar(100),MIN(b.SignTime),24)),120) END SignTime,");
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
                }
                else if (toStatus == 2)
                {
                    // 改为迟到
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN DATEADD(ss,cast(rand(checksum(a.Id))*599+1 as int),CONVERT(nvarchar(100),(CONVERT(nvarchar(100),CONVERT(datetime,a.RecordDate),23)+' '+CONVERT(nvarchar(100),min(b.KQTime),24)),120)) ELSE CONVERT(nvarchar(100),(CONVERT(nvarchar(100),CONVERT(datetime,a.RecordDate),23)+' '+CONVERT(nvarchar(100),MIN(b.SignTime),24)),120) END SignTime,");
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN cast(rand(checksum(a.Id))*599+1 as int) ELSE b.LateTime END LateTime");
                }
                else if (toStatus == 3)
                {
                    // 改为缺卡
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN dateadd(day,0,CONVERT(varchar(100), RecordDate)) ELSE MIN( b.SignTime) END SignTime,");
                    strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
                }
                strSql.AppendLine("FROM ecb_kq_inout_teacher_history a CROSS APPLY openjson(a.KQRecords)");
                strSql.AppendLine("WITH (PlaceId UNIQUEIDENTIFIER,[Status] INT,SignTime DATETIME,LateTime INT,Photo NVARCHAR(250),KQStage INT,KQType INT,KQTime DATETIME,Mark nvarchar(40)) b");
                strSql.AppendLine("WHERE a.ColumnId=@ColumnId and a.RecordDate=ecb_kq_inout_teacher_history.RecordDate AND a.UserId=ecb_kq_inout_teacher_history.UserId GROUP BY Photo,KQStage,KQType,UserId,Status,LateTime,a.Id,RecordDate,b.Mark FOR json PATH");
                strSql.AppendLine("),");
                if (toStatus == 1 || toStatus == 2)
                {
                    // 如果是改为正常或迟到，则为不缺卡
                    strSql.AppendLine("IsAbsent=0");
                }
                else
                {
                    int otherStage = 0, otherType = 0;
                    // 如果是上午
                    if (kqStage == 1)
                    {
                        // 判断下午
                        otherStage = 2;
                        otherType = 2;
                    }
                    else
                    {
                        // 判断上午
                        otherStage = 1;
                        otherType = 1;
                    }
                    // 改为缺卡，则需要判断另外一半是否有打卡
                    strSql.AppendFormat("IsAbsent=(case when exists(select 1 from ecb_kq_inout_teacher_history a CROSS APPLY openjson(a.KQRecords) WITH ([Status] INT,KQStage INT,KQType INT) b where a.RecordDate=ecb_kq_inout_teacher_history.RecordDate and a.UserId=ecb_kq_inout_teacher_history.UserId and b.KQStage={0} and b.KQType={1} and b.[Status]<>3) then 0 else 1 end)", otherStage, otherType);
                    strSql.AppendLine();
                }
                strSql.AppendLine("WHERE ColumnId = @ColumnId and @beginDate<= RecordDate and RecordDate<=@endDate");
                if (!string.IsNullOrEmpty(userids))
                {
                    strSql.AppendLine(" AND UserId in ('" + userids + "')");
                }
            }

            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@beginDate",SqlDbType.Date),
                new SqlParameter("@endDate",SqlDbType.Date),
                new SqlParameter("@KQStage",SqlDbType.Int,4),
                new SqlParameter("@KQType",SqlDbType.Int,4),
                new SqlParameter("@PreStatus",SqlDbType.Int,4),
                new SqlParameter("@ToStatus",SqlDbType.Int,4),
                new SqlParameter("@Mark",SqlDbType.NVarChar,40)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = beginDate;
            parameters[2].Value = endDate;
            parameters[3].Value = kqStage;
            parameters[4].Value = kqType;
            parameters[5].Value = preStatus;
            parameters[6].Value = toStatus;
            parameters[7].Value = mark;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }
        /// <summary>
        /// 修改教师个人到离校考勤状态
        /// </summary>
        /// <param name="UserId">用户ID</param>
        /// <param name="day">日期</param>
        /// <param name="kqStage">要修改的类型 1上午 2下午</param>
        /// <param name="kqType">要修改的类型 1到校 2离校</param>
        /// <param name="preStatus">改之前的状态</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <returns></returns>
        public bool ModifyInoutStatus(Guid UserId, DateTime day, int kqType, int kqStage, int toStatus, string mark)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //修改时间
            if (day.Date == DateTime.Now.Date)
            {
                tabName = "ecb_kq_inout_teacher";
            }
            else
            {
                tabName = "ecb_kq_inout_teacher_history";
            }
            strSql.AppendLine("UPDATE " + tabName + " SET KQRecords = (");
            strSql.AppendLine("SELECT b.PlaceId,b.Photo,b.KQStage,b.KQType,b.KQTime,");
            strSql.AppendLine("CASE WHEN b.KQStage=@KQStage AND b.KQType=@KQType THEN @ToStatus ELSE b.[Status] END [Status],");
            strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.KQStage=@KQStage THEN @Mark ELSE b.Mark END Mark,");
            if (toStatus == 1)
            {
                // 改为正常
                strSql.AppendLine("CASE WHEN b.KQStage=@KQStage AND b.KQType=@KQType THEN DATEADD(mi," + (kqType == 1 ? -1 : 1) + ",b.KQTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.KQStage=@KQStage AND b.KQType=@KQType THEN 0 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 2)
            {
                // 改为迟到
                strSql.AppendLine("CASE WHEN b.KQStage=@KQStage AND b.KQType=@KQType THEN DATEADD(mi,2,b.KQTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.KQStage=@KQStage AND b.KQType=@KQType THEN 120 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 3)
            {
                // 改为缺卡
                strSql.AppendLine("CASE WHEN b.KQStage=@KQStage AND b.KQType=@KQType THEN '" + day.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.KQStage=@KQStage AND b.KQType=@KQType THEN 0 ELSE b.LateTime END LateTime");
            }
            strSql.AppendLine("FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords)");
            strSql.AppendLine("WITH (PlaceId UNIQUEIDENTIFIER,[Status] INT,SignTime DATETIME,LateTime INT,Photo NVARCHAR(250),KQStage INT,KQType INT,KQTime DATETIME,Mark nvarchar(40)) b");
            strSql.AppendLine("WHERE a.UserId=@UserId and a.RecordDate=@Date FOR json PATH");
            strSql.AppendLine("),");
            if (toStatus == 1 || toStatus == 2)
            {
                // 如果是改为正常或迟到，则为不缺卡
                strSql.AppendLine("IsAbsent=0");
            }
            else
            {
                int otherStage = 0, otherType = 0;
                // 如果是上午
                if (kqStage == 1)
                {
                    // 判断下午
                    otherStage = 2;
                    otherType = 2;
                }
                else
                {
                    // 判断上午
                    otherStage = 1;
                    otherType = 1;
                }
                // 改为缺卡，则需要判断另外一半是否有打卡
                strSql.AppendFormat("IsAbsent=(case when exists(select 1 from " + tabName + " a CROSS APPLY openjson(a.KQRecords) WITH ([Status] INT,KQStage INT,KQType INT) b where a.RecordDate=@Date and a.UserId=@UserId and b.KQStage={0} and b.KQType={1} and b.[Status]<>3) then 0 else 1 end)", otherStage, otherType);
                strSql.AppendLine();
            }
            strSql.AppendLine("WHERE UserId = @UserId and RecordDate = @Date");
            SqlParameter[] parameters = {
                new SqlParameter("@UserId",SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@KQStage",SqlDbType.Int,4),
                new SqlParameter("@KQType",SqlDbType.Int,4),
                new SqlParameter("@ToStatus",SqlDbType.Int,4),
                new SqlParameter("@Mark",SqlDbType.NVarChar,40)
            };
            parameters[0].Value = UserId;
            parameters[1].Value = day;
            parameters[2].Value = kqStage;
            parameters[3].Value = kqType;
            parameters[4].Value = toStatus;
            parameters[5].Value = mark;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }
        /// <summary>
        /// 删除指定日期之后的课堂考勤数据,不包括历史数据
        /// </summary>
        /// <param name="UserId">学生id</param>
        /// <param name="RecordDate">日期</param>
        /// <returns></returns>
        public bool Delete(Guid UserId, DateTime RecordDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_teacher");
            strSql.Append(" where UserId=@UserId AND RecordDate>@RecordDate");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16) ,
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = UserId;
            parameters[1].Value = RecordDate;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 获取考勤列表，每行数据为单个用户的所有日期的考勤
        /// 包含单个用户在所有日期内的状态统计
        /// </summary>
        /// <param name="dteBegin">开始时间</param>
        /// <param name="dteEnd">结束时间</param>
        /// <param name="kqStages">考勤阶段列表</param>
        /// <param name="kqTypes">考勤类型列表</param>
        /// <returns>考勤数据集</returns>
        public DataSet GetList(int columnId, DateTime dteBegin, DateTime dteEnd, List<string> kqStages, List<string> kqTypes)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("select (select top 1 n.DeptName from JC_TeacherDept m left join JC_Department n on n.ColumnId=m.SchoolColumnID and n.ID=m.DeptId where m.TeacherNo=b.UserName) '部门',");
            strSql.AppendLine("b.CName '姓名',tmp.* from (");
            strSql.AppendLine("select t.UserId,");
            DateTime dteTemp = dteBegin;
            while (dteTemp <= dteEnd)
            {
                foreach (string kqStage in kqStages)
                {
                    string kqStageName = kqStage == "1" ? "上午" : kqStage == "2" ? "下午" : "晚上";
                    foreach (string kqType in kqTypes)
                    {
                        string kqTypeName = kqType == "1" ? "到校" : "离校";
                        strSql.AppendLine($"max(case when t.RecordDate='{dteTemp.Date:yyyy-MM-dd}' and t.KQStage={kqStage} and t.KQType={kqType} then (case when t.Status=1 then '正常'when t.Status=2 and t.KQType=1 then '迟到' when t.Status=2 and t.KQType=2 then '早退' when t.Status=3 then '缺卡' else '' end +'|'+t.Mark) else '' end) '{dteTemp.Date:yyyy-MM-dd}|{kqStageName}{kqTypeName}',");
                    }
                }
                dteTemp = dteTemp.AddDays(1);
            }
            strSql.AppendLine("sum(case when t.Status=1 then 1 else 0 end) '正常',");
            strSql.AppendLine("sum(case when t.Status=2 then 1 else 0 end) '迟到/早退',");
            strSql.AppendLine("sum(case when t.Status=3 then 1 else 0 end) '缺卡'");
            strSql.AppendLine("from (");
            strSql.AppendLine("select a.UserId,a.RecordDate,b.KQStage,b.KQType,b.Status,isnull(b.Mark,'') Mark");
            strSql.AppendLine("from ecb_kq_inout_teacher_history a");
            strSql.AppendLine("cross apply openjson(KQRecords)with([Status] INT, KQStage INT, KQType INT, SignTime DATETIME, KQTime DATETIME,PlaceId UniqueIdentifier,Mark nvarchar(50)) b");
            strSql.AppendLine($"where a.ColumnId={columnId} and a.RecordDate>='{dteBegin:yyyy-MM-dd}' and a.RecordDate<='{dteEnd:yyyy-MM-dd}'");
            strSql.AppendLine("union");
            strSql.AppendLine("select a.UserId,a.RecordDate,b.KQStage,b.KQType,b.Status,isnull(b.Mark,'') Mark");
            strSql.AppendLine("from ecb_kq_inout_teacher a");
            strSql.AppendLine("cross apply openjson(KQRecords)with([Status] INT, KQStage INT, KQType INT, SignTime DATETIME, KQTime DATETIME,PlaceId UniqueIdentifier,Mark nvarchar(50)) b");
            strSql.AppendLine($"where a.ColumnId= {columnId} and a.RecordDate>='{dteBegin:yyyy-MM-dd}' and a.RecordDate<='{dteEnd:yyyy-MM-dd}'");
            strSql.AppendLine(") t group by t.UserId");
            strSql.AppendLine(") tmp inner join aspnet_Membership b on b.UserId=tmp.UserId");
            strSql.AppendLine("order by b.CName");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

