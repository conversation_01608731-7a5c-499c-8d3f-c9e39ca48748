﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_reboot_once:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_reboot_once
	{
		public ecb_reboot_once()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private string _rulename;
		private DateTime? _begindate;
		private DateTime? _enddate;
		private string _ids;
		private DateTime? _lastedittime;
		private Guid _lasteditor;
		private int? _oncetype;
		/// <summary>
		/// 
		/// </summary>
		public Guid id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnID
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RuleName
		{
			set{ _rulename=value;}
			get{return _rulename;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? BeginDate
		{
			set{ _begindate=value;}
			get{return _begindate;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? EndDate
		{
			set{ _enddate=value;}
			get{return _enddate;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ids
		{
			set{ _ids=value;}
			get{return _ids;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid LastEditor
		{
			set{ _lasteditor=value;}
			get{return _lasteditor;}
		}
		/// <summary>
		/// 临时开关机类型（1开机 2关机）
		/// </summary>
		public int? OnceType
		{
			set { _oncetype = value; }
			get { return _oncetype; }
		}
		#endregion Model

	}
}

