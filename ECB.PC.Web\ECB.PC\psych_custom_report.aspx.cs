using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.DBUtility;

namespace ECB.PC.Web.ECB.PC
{
    public partial class psych_custom_report : YunEdu.Authority.AdminCommonJC
    {
        List<QueryField> queryFields = new List<QueryField>();

        protected void Page_Load(object sender, EventArgs e)
        {
            InitQueryFields();
            if (!IsPostBack)
            {
                Area_Class.DefaultColumnPath = modelAreaUser.ColumnPath;
                Area_Class.UserId = this.UserId;
                Area_Class.RoleLevel = RoleLevel;
                Area_Class.UserName = UserName;
                Area_Class.SysMode = XYArea.SysMode.Psych;
                BindData();
            }
        }

        /// <summary>
        /// 初始化查询字段集合
        /// </summary>
        private void InitQueryFields()
        {
            queryFields.Add(new QueryField
            {
                Key = "1",
                Name = "政治面貌",
                Value = "Politics",
                Expression = "c1.DictText Politics"
            });
            queryFields.Add(new QueryField
            {
                Key = "2",
                Name = "出生年月",
                Value = "Birthday",
                Expression = "a.Birthday"
            });
            queryFields.Add(new QueryField
            {
                Key = "3",
                Name = "民族",
                Value = "Nation",
                Expression = "c3.DictText Nation"
            });
            queryFields.Add(new QueryField
            {
                Key = "4",
                Name = "籍贯",
                Value = "Native",
                Expression = "a.Native"
            });
            queryFields.Add(new QueryField
            {
                Key = "5",
                Name = "性别",
                Value = "Sex",
                Expression = "c5.DictText Sex"
            });
            queryFields.Add(new QueryField
            {
                Key = "6",
                Name = "特长",
                Value = "Specialty",
                Expression = "a.Specialty"
            });
            queryFields.Add(new QueryField
            {
                Key = "7",
                Name = "家庭住址",
                Value = "HomeAddress",
                Expression = "a.HomeAddress"
            });
            queryFields.Add(new QueryField
            {
                Key = "8",
                Name = "学习情况",
                Value = "StudyStatus",
                Expression = "a.StudyStatus"
            });
            queryFields.Add(new QueryField
            {
                Key = "9",
                Name = "重大或慢性疾病",
                Value = "ZDJB",
                Expression = "c9.DictText ZDJB"
            });
            queryFields.Add(new QueryField
            {
                Key = "10",
                Name = "长期用药情况",
                Value = "LongMedicationUse",
                Expression = "c10.DictText LongMedicationUse"
            });
            queryFields.Add(new QueryField
            {
                Key = "11",
                Name = "体能限制",
                Value = "PhysicalLimitations",
                Expression = "c11.DictText PhysicalLimitations"
            });
            queryFields.Add(new QueryField
            {
                Key = "12",
                Name = "既往心理诊断",
                Value = "JWXLZD",
                Expression = "c12.DictText JWXLZD"
            });
            queryFields.Add(new QueryField
            {
                Key = "13",
                Name = "父母关系",
                Value = "ParentRelation",
                Expression = "c13.DictText ParentRelation"
            });
            queryFields.Add(new QueryField
            {
                Key = "14",
                Name = "家庭氛围",
                Value = "FamilyAtmosphere",
                Expression = "c14.DictText FamilyAtmosphere"
            });
        }

        /// <summary>
        /// 获取查询条件
        /// </summary>
        /// <returns></returns>
        private string GetWhere()
        {
            StringBuilder strWhere = new StringBuilder();
            strWhere.Append($"a.ColumnPath+'|' like '{modelAreaUser.ColumnPath}|%'");
            strWhere.Append($" and a.ColumnID={Area_Class.CurrentColumnId}");

            // 拿到最大角色级别
            var maxRoleLevel = RoleLevel.Select(t => int.Parse(t)).Min();
            // 如果是管理员级别以下的级别
            if (maxRoleLevel > (int)CodeTable.AdminRoleLevel.SchoolAdmin)
            {
                bool hasCondition1 = false;
                BLL.JC_GradeLeader bllGradeLeader = new BLL.JC_GradeLeader();
                bool isGradeLeader = bllGradeLeader.GetRecordCount("GradeLeaderId='" + UserId + "' AND DutyTypeCode=4") > 0;
                bool isPsychTeacher = new BLL.JC_GradeLeader().GetRecordCount("GradeLeaderId='" + UserId + "' AND DutyTypeCode=5") > 0;
                bool isDSTeacher = new BLL.psych_basicInfo().GetRecordCount($"DSTeacherId='{UserId}'") > 0;
                bool isBGTeacher = new BLL.psych_basicInfo().GetRecordCount($"BGTeacherId='{UserId}'") > 0;
                bool isParent = RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.Parents).ToString());
                bool isHeadTeacher = new BLL.JC_ClassInfos().GetRecordCount($"TeacherNo='{UserName}'") > 0;
                strWhere.AppendLine(" and (");
                // 年级组长/心理老师
                if (isGradeLeader || isPsychTeacher)
                {
                    hasCondition1 = true;
                    strWhere.AppendLine($"a.ClassID in (SELECT JC_GradeLeader.ClassId FROM JC_GradeLeader WHERE GradeLeaderId='{UserId}' AND DutyTypeCode in (4,5)) ");
                }
                // 导师
                if (isDSTeacher)
                {
                    if (hasCondition1) strWhere.Append("or ");
                    hasCondition1 = true;
                    strWhere.AppendLine($"a.ID in (SELECT b.StudentId FROM psych_basicInfo b where DSTeacherId='{UserId}')");
                }
                // 行政包干
                if (isDSTeacher)
                {
                    if (hasCondition1) strWhere.Append("or ");
                    hasCondition1 = true;
                    strWhere.AppendLine($"a.ID in (SELECT b.StudentId FROM psych_basicInfo b where BGTeacherId='{UserId}')");
                }
                // 班主任
                if (isHeadTeacher)
                {
                    if (hasCondition1) strWhere.Append("or ");
                    hasCondition1 = true;
                    strWhere.AppendLine($"b.TeacherNo='{UserName}'");
                }
                // 家长
                if (isParent)
                {
                    if (hasCondition1) strWhere.Append("or ");
                    hasCondition1 = true;
                    strWhere.AppendLine($"a.ID in (select a.StudentId from JC_StudentFamily a left join JC_FamilyInfos c on a.FamilyId=c.ID where c.UserId='{UserId}')");
                }
                // 如果没有任何权限，则不查找任何数据
                if (!hasCondition1)
                {
                    strWhere.AppendLine("1=2");
                }
                strWhere.AppendLine(")");
            }

            // 年级筛选
            if (!string.IsNullOrEmpty(Area_Class.GradeId))
            {
                strWhere.Append($" and a.GradeID='{Area_Class.GradeId}'");
            }
            // 班级筛选
            if (!string.IsNullOrEmpty(Area_Class.ClassId))
            {
                strWhere.Append($" and a.ClassID='{Area_Class.ClassId}'");
            }
            // 状态筛选
            if (!string.IsNullOrEmpty(ddlStatus.SelectedValue))
            {
                if (ddlStatus.SelectedValue == "0")
                {
                    strWhere.Append(" AND (isnull(c.AbnormalStatus,'')<>'')");
                }
                else if (ddlStatus.SelectedValue == "-1")
                {
                    strWhere.Append(" AND c.id is null");
                }
                else
                {
                    strWhere.Append($" AND (c.AbnormalStatus='{ddlStatus.SelectedValue}')");
                }
            }
            // 关键字筛选条件
            if (!string.IsNullOrEmpty(txtKeyword.Text.Trim()))
            {
                strWhere.Append($" AND (a.StudentName LIKE '%{DataSecurity.FilteSQLStr(txtKeyword.Text.Trim())}%')");
            }
            return strWhere.ToString();
        }

        private List<QueryField> GetSelectedColumns()
        {
            List<QueryField> columns = new List<QueryField>();
            // 没有选择额外的字段就返回空列表
            if (string.IsNullOrEmpty(hidColumns.Value))
            {
                return columns;
            }
            string[] selectedColumns = hidColumns.Value.Split(new char[','], StringSplitOptions.RemoveEmptyEntries);
            if (!selectedColumns.Any())
            {
                return columns;
            }
            return queryFields.Where(x => selectedColumns.Contains(x.Key)).ToList();
        }

        /// <summary>
        /// 获取查询字段
        /// </summary>
        /// <returns></returns>
        private string GetFields(List<QueryField> selectedFields)
        {
            StringBuilder strFields = new StringBuilder();
            strFields.Append("a.ID,a.StudentName,e.AreaName,isnull(b.ClassName,'') ClassName,a.TelPhone,m.Photo,a.Sex,");

            // 安全地删除动态添加的列（只删除标记为动态的列）
            RemoveDynamicColumns();

            // 确保固定列完整性
            EnsureFixedColumns();

            // 拿到前台传递的字段
            if (selectedFields.Any())
            {
                foreach (var item in selectedFields)
                {
                    // 给GrideView添加列，并标记为动态列
                    var column = new BoundField();
                    column.HeaderText = item.Name;
                    column.DataField = item.Value;
                    column.HeaderStyle.CssClass = "dynamic-column"; // 标记为动态列
                    gvInfo.Columns.Add(column);
                    strFields.Append(item.Expression + ",");
                }
            }
            strFields.Append("c.AbnormalStatus,case when c.Id is null then '未建档' else isnull(d.DictText,'正常') end AbnormalStatusName");
            return strFields.ToString();
        }

        /// <summary>
        /// 安全地删除动态添加的列
        /// </summary>
        private void RemoveDynamicColumns()
        {
            // 从后往前删除标记为动态的列
            for (int i = gvInfo.Columns.Count - 1; i >= 0; i--)
            {
                var column = gvInfo.Columns[i];
                // 只删除标记为动态的列
                if (column.HeaderStyle.CssClass != null && column.HeaderStyle.CssClass.Contains("dynamic-column"))
                {
                    gvInfo.Columns.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 确保固定列完整性
        /// </summary>
        private void EnsureFixedColumns()
        {
            // 检查是否有5个固定列，如果不足则重新构建
            if (gvInfo.Columns.Count < 5)
            {
                gvInfo.Columns.Clear();

                // 重新添加固定列
                gvInfo.Columns.Add(new BoundField { HeaderText = "姓名", DataField = "StudentName", ItemStyle = { HorizontalAlign = HorizontalAlign.Left } });
                gvInfo.Columns.Add(new BoundField { HeaderText = "学校", DataField = "AreaName", ItemStyle = { HorizontalAlign = HorizontalAlign.Center } });
                gvInfo.Columns.Add(new BoundField { HeaderText = "班级", DataField = "ClassName", ItemStyle = { HorizontalAlign = HorizontalAlign.Center } });

                // 性别列（TemplateField）
                var sexColumn = new TemplateField { HeaderText = "性别" };
                sexColumn.ItemTemplate = new CustomTemplate("lblSex", "Sex");
                sexColumn.ItemStyle.HorizontalAlign = HorizontalAlign.Center;
                gvInfo.Columns.Add(sexColumn);

                // 状态列（TemplateField）
                var statusColumn = new TemplateField { HeaderText = "状态" };
                statusColumn.ItemTemplate = new CustomTemplate("lblStatus", "");
                statusColumn.ItemStyle.HorizontalAlign = HorizontalAlign.Center;
                gvInfo.Columns.Add(statusColumn);
            }
        }

        /// <summary>
        /// 获取查询条件
        /// </summary>
        /// <returns></returns>
        private string GetTables()
        {
            StringBuilder strTables = new StringBuilder();
            strTables.Append("JC_StudentInfos a left join aspnet_Membership m on a.ID=m.UserId ");
            strTables.Append("LEFT JOIN dbo.JC_ClassInfos b ON a.ClassID=b.ID ");
            strTables.Append("left join psych_basicInfo c on a.ID=c.StudentId ");
            strTables.Append("left join psych_dict d on d.DictTypeId=29 and d.DictValue=c.AbnormalStatus ");
            strTables.Append("left join BM_Areas e on a.ColumnID=e.ColumnId ");
            strTables.Append("left join Site_Dictionary c1 on c1.DictTypeId=33 and c1.DictValue=a.PoliticsCode ");
            strTables.Append("left join Site_Dictionary c3 on c1.DictTypeId=29 and c1.DictValue=a.NationCode ");
            strTables.Append("left join Site_Dictionary c5 on c1.DictTypeId=27 and c1.DictValue=a.Sex ");
            strTables.Append("left join psych_dict c9 on c9.DictTypeId=10 and c9.DictValue=c.ZDJB ");
            strTables.Append("left join psych_dict c10 on c10.DictTypeId=11 and c10.DictValue=c.LongMedicationUse ");
            strTables.Append("left join psych_dict c11 on c11.DictTypeId=11 and c11.DictValue=c.PhysicalLimitations ");
            strTables.Append("left join psych_dict c12 on c12.DictTypeId=13 and c12.DictValue=c.JWXLZD ");
            strTables.Append("left join psych_dict c13 on c13.DictTypeId=8 and c13.DictValue=c.ParentRelation ");
            strTables.Append("left join psych_dict c14 on c14.DictTypeId=9 and c14.DictValue=c.FamilyAtmosphere ");
            return strTables.ToString();
        }

        /// <summary>
        /// 绑定页面数据
        /// </summary>
        /// <param name="isMain"></param>
        private void BindData(bool isMain = false)
        {
            List<QueryField> selectedFields = GetSelectedColumns();
            this.txtPageNum.Text = this.AspNetPager1.PageSize.ToString();
            string where = GetWhere();
            string tables = GetTables();
            string fields = GetFields(selectedFields);
            string orders = "a.GradeID,a.ClassID,a.StudentName";
            AspNetPager1.RecordCount = GetRecordByPageOrder.GetCount(tables, where, isMain ? DbHelperSQL.ConnMain : null);
            DataTable dt = GetRecordByPageOrder.GetList(tables, AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, fields, where, orders, isMain ? DbHelperSQL.ConnMain : null).Tables[0];
            gvInfo.DataSource = dt;
            gvInfo.DataBind();
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            BindData(!string.IsNullOrEmpty(Request["__EVENTARGUMENT"]));
        }

        /// <summary>
        /// 绑定行数据
        /// </summary>
        protected void gvInfo_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                string Id = DataBinder.Eval(e.Row.DataItem, "ID").ToString();
                Label lblSex = (Label)e.Row.FindControl("lblSex");
                DataRowView drv = e.Row.DataItem as DataRowView;
                //值为1为男生，值为2为女生,值为3为其他	
                string sex = drv["Sex"].ToString();
                lblSex.Text = sex == "1" ? "男" :
                              sex == "2" ? "女" :
                              "未知";
                // 设置预警状态
                Label lblStatus = (Label)e.Row.FindControl("lblStatus");
                string AbnormalStatus = drv["AbnormalStatus"].ToString();
                string AbnormalStatusName = drv["AbnormalStatusName"].ToString();
                lblStatus.Text = AbnormalStatusName;
                if (AbnormalStatusName.Equals("未建档"))
                {
                    lblStatus.CssClass = "warning-tag warning-level-null";
                }
                else if (!AbnormalStatusName.Equals("正常"))
                {
                    lblStatus.CssClass = $"warning-tag warning-level-{AbnormalStatus}";
                }
            }
        }

        protected void txtPageNum_TextChanged(object sender, EventArgs e)
        {
            if (txtPageNum.Text.Trim() != "")
            {
                int PageNum = 0;
                if (int.TryParse(txtPageNum.Text.Trim(), out PageNum) && PageNum > 0)
                {
                    AspNetPager1.PageSize = PageNum;
                }
            }
            BindData();
        }

        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            BindData();
        }

        class QueryField
        {
            /// <summary>
            /// 主键
            /// </summary>
            public string Key { get; set; }
            /// <summary>
            /// 字段文本描述
            /// </summary>
            public string Name { get; set; }
            /// <summary>
            /// 字段查询表达式
            /// </summary>
            public string Expression { get; set; }
            /// <summary>
            /// 字段名
            /// </summary>
            public string Value { get; set; }
        }

        /// <summary>
        /// 自定义模板类，用于动态创建TemplateField
        /// </summary>
        public class CustomTemplate : ITemplate
        {
            private string _controlId;
            private string _dataField;

            public CustomTemplate(string controlId, string dataField)
            {
                _controlId = controlId;
                _dataField = dataField;
            }

            public void InstantiateIn(Control container)
            {
                Label label = new Label();
                label.ID = _controlId;
                if (!string.IsNullOrEmpty(_dataField))
                {
                    label.DataBinding += (sender, e) =>
                    {
                        Label lbl = (Label)sender;
                        GridViewRow row = (GridViewRow)lbl.NamingContainer;
                        lbl.Text = DataBinder.Eval(row.DataItem, _dataField).ToString();
                    };
                }
                container.Controls.Add(label);
            }
        }
    }
}