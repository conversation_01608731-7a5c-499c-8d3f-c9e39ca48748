﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_home_visit
    /// </summary>
    public partial class psych_home_visit
    {
        public psych_home_visit()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_home_visit");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.psych_home_visit model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_home_visit(");
            strSql.Append("Id,ColumnId,ColumnPath,StudentId,Sex,Age,Contact,ClassName,HomeAddress,UserId,Participants,InterviewTime,HomeVisitMethod,GJRecod,VisitPic,StudentStatus,PurposeOfVisit,HomeVisitContent,LiveAndLearn,JZYQHJY,Experience,LastEditBy,LastEditTime,ParticipantsPic)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@Sex,@Age,@Contact,@ClassName,@HomeAddress,@UserId,@Participants,@InterviewTime,@HomeVisitMethod,@GJRecod,@VisitPic,@StudentStatus,@PurposeOfVisit,@HomeVisitContent,@LiveAndLearn,@JZYQHJY,@Experience,@LastEditBy,@LastEditTime,@ParticipantsPic)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Sex", SqlDbType.NVarChar,2),
                    new SqlParameter("@Age", SqlDbType.Int,4),
                    new SqlParameter("@Contact", SqlDbType.NVarChar,20),
                    new SqlParameter("@ClassName", SqlDbType.NVarChar,50),
                    new SqlParameter("@HomeAddress", SqlDbType.NVarChar,200),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Participants", SqlDbType.NVarChar,200),
                    new SqlParameter("@InterviewTime", SqlDbType.DateTime),
                    new SqlParameter("@HomeVisitMethod", SqlDbType.NVarChar,4),
                    new SqlParameter("@GJRecod", SqlDbType.NVarChar,500),
                    new SqlParameter("@VisitPic", SqlDbType.NVarChar,-1),
                    new SqlParameter("@StudentStatus", SqlDbType.NVarChar,500),
                    new SqlParameter("@PurposeOfVisit", SqlDbType.NVarChar,500),
                    new SqlParameter("@HomeVisitContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LiveAndLearn", SqlDbType.NVarChar,500),
                    new SqlParameter("@JZYQHJY", SqlDbType.NVarChar,500),
                    new SqlParameter("@Experience", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@ParticipantsPic",SqlDbType.NVarChar,-1)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.Sex;
            parameters[5].Value = model.Age;
            parameters[6].Value = model.Contact;
            parameters[7].Value = model.ClassName;
            parameters[8].Value = model.HomeAddress;
            parameters[9].Value = model.UserId;
            parameters[10].Value = model.Participants;
            parameters[11].Value = model.InterviewTime;
            parameters[12].Value = model.HomeVisitMethod;
            parameters[13].Value = model.GJRecod;
            parameters[14].Value = model.VisitPic;
            parameters[15].Value = model.StudentStatus;
            parameters[16].Value = model.PurposeOfVisit;
            parameters[17].Value = model.HomeVisitContent;
            parameters[18].Value = model.LiveAndLearn;
            parameters[19].Value = model.JZYQHJY;
            parameters[20].Value = model.Experience;
            parameters[21].Value = model.LastEditBy;
            parameters[22].Value = model.LastEditTime;
            parameters[23].Value = model.ParticipantsPic;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.psych_home_visit model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_home_visit set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("Sex=@Sex,");
            strSql.Append("Age=@Age,");
            strSql.Append("Contact=@Contact,");
            strSql.Append("ClassName=@ClassName,");
            strSql.Append("HomeAddress=@HomeAddress,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("Participants=@Participants,");
            strSql.Append("InterviewTime=@InterviewTime,");
            strSql.Append("HomeVisitMethod=@HomeVisitMethod,");
            strSql.Append("GJRecod=@GJRecod,");
            strSql.Append("VisitPic=@VisitPic,");
            strSql.Append("StudentStatus=@StudentStatus,");
            strSql.Append("PurposeOfVisit=@PurposeOfVisit,");
            strSql.Append("HomeVisitContent=@HomeVisitContent,");
            strSql.Append("LiveAndLearn=@LiveAndLearn,");
            strSql.Append("JZYQHJY=@JZYQHJY,");
            strSql.Append("Experience=@Experience,");
            strSql.Append("LastEditBy=@LastEditBy,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("ParticipantsPic=@ParticipantsPic");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Sex", SqlDbType.NVarChar,2),
                    new SqlParameter("@Age", SqlDbType.Int,4),
                    new SqlParameter("@Contact", SqlDbType.NVarChar,20),
                    new SqlParameter("@ClassName", SqlDbType.NVarChar,50),
                    new SqlParameter("@HomeAddress", SqlDbType.NVarChar,200),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Participants", SqlDbType.NVarChar,200),
                    new SqlParameter("@InterviewTime", SqlDbType.DateTime),
                    new SqlParameter("@HomeVisitMethod", SqlDbType.NVarChar,4),
                    new SqlParameter("@GJRecod", SqlDbType.NVarChar,500),
                    new SqlParameter("@VisitPic", SqlDbType.NVarChar,-1),
                    new SqlParameter("@StudentStatus", SqlDbType.NVarChar,500),
                    new SqlParameter("@PurposeOfVisit", SqlDbType.NVarChar,500),
                    new SqlParameter("@HomeVisitContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LiveAndLearn", SqlDbType.NVarChar,500),
                    new SqlParameter("@JZYQHJY", SqlDbType.NVarChar,500),
                    new SqlParameter("@Experience", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@ParticipantsPic",SqlDbType.NVarChar,-1),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.StudentId;
            parameters[3].Value = model.Sex;
            parameters[4].Value = model.Age;
            parameters[5].Value = model.Contact;
            parameters[6].Value = model.ClassName;
            parameters[7].Value = model.HomeAddress;
            parameters[8].Value = model.UserId;
            parameters[9].Value = model.Participants;
            parameters[10].Value = model.InterviewTime;
            parameters[11].Value = model.HomeVisitMethod;
            parameters[12].Value = model.GJRecod;
            parameters[13].Value = model.VisitPic;
            parameters[14].Value = model.StudentStatus;
            parameters[15].Value = model.PurposeOfVisit;
            parameters[16].Value = model.HomeVisitContent;
            parameters[17].Value = model.LiveAndLearn;
            parameters[18].Value = model.JZYQHJY;
            parameters[19].Value = model.Experience;
            parameters[20].Value = model.LastEditBy;
            parameters[21].Value = model.LastEditTime;
            parameters[22].Value = model.ParticipantsPic;
            parameters[23].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_home_visit ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_home_visit ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }   

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_home_visit GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,Sex,Age,Contact,ClassName,HomeAddress,UserId,Participants,InterviewTime,HomeVisitMethod,GJRecod,VisitPic,StudentStatus,PurposeOfVisit,HomeVisitContent,LiveAndLearn,JZYQHJY,Experience,LastEditBy,LastEditTime,ParticipantsPic from psych_home_visit ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.psych_home_visit model = new ECB.PC.Model.psych_home_visit();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_home_visit DataRowToModel(DataRow row)
        {
            ECB.PC.Model.psych_home_visit model = new ECB.PC.Model.psych_home_visit();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["Sex"] != null)
                {
                    model.Sex = row["Sex"].ToString();
                }
                if (row["Age"] != null && row["Age"].ToString() != "")
                {
                    model.Age = int.Parse(row["Age"].ToString());
                }
                if (row["Contact"] != null)
                {
                    model.Contact = row["Contact"].ToString();
                }
                if (row["ClassName"] != null)
                {
                    model.ClassName = row["ClassName"].ToString();
                }
                if (row["HomeAddress"] != null)
                {
                    model.HomeAddress = row["HomeAddress"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["Participants"] != null)
                {
                    model.Participants = row["Participants"].ToString();
                }
                if (row["ParticipantsPic"] != null)
                {
                    model.ParticipantsPic = row["ParticipantsPic"].ToString();
                }
                if (row["InterviewTime"] != null && row["InterviewTime"].ToString() != "")
                {
                    model.InterviewTime = DateTime.Parse(row["InterviewTime"].ToString());
                }
                if (row["HomeVisitMethod"] != null)
                {
                    model.HomeVisitMethod = row["HomeVisitMethod"].ToString();
                }
                if (row["GJRecod"] != null)
                {
                    model.GJRecod = row["GJRecod"].ToString();
                }
                if (row["VisitPic"] != null)
                {
                    model.VisitPic = row["VisitPic"].ToString();
                }
                if (row["StudentStatus"] != null)
                {
                    model.StudentStatus = row["StudentStatus"].ToString();
                }
                if (row["PurposeOfVisit"] != null)
                {
                    model.PurposeOfVisit = row["PurposeOfVisit"].ToString();
                }
                if (row["HomeVisitContent"] != null)
                {
                    model.HomeVisitContent = row["HomeVisitContent"].ToString();
                }
                if (row["LiveAndLearn"] != null)
                {
                    model.LiveAndLearn = row["LiveAndLearn"].ToString();
                }
                if (row["JZYQHJY"] != null)
                {
                    model.JZYQHJY = row["JZYQHJY"].ToString();
                }
                if (row["Experience"] != null)
                {
                    model.Experience = row["Experience"].ToString();
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,StudentId,Sex,Age,Contact,ClassName,HomeAddress,UserId,Participants,InterviewTime,HomeVisitMethod,GJRecod,VisitPic,StudentStatus,PurposeOfVisit,HomeVisitContent,LiveAndLearn,JZYQHJY,Experience,LastEditBy,LastEditTime,ParticipantsPic ");
            strSql.Append(" FROM psych_home_visit ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,StudentId,Sex,Age,Contact,ClassName,HomeAddress,UserId,Participants,InterviewTime,HomeVisitMethod,GJRecod,VisitPic,StudentStatus,PurposeOfVisit,HomeVisitContent,LiveAndLearn,JZYQHJY,Experience,LastEditBy,LastEditTime,ParticipantsPic ");
            strSql.Append(" FROM psych_home_visit ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_home_visit ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_home_visit T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_home_visit";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 更新家访状态
        /// </summary>
        /// <param name="id">家访记录ID</param>
        /// <param name="status">状态：0取消 1未家访 2已家访</param>
        /// <param name="userId">操作用户ID</param>
        /// <returns></returns>
        public bool UpdateStatus(Guid id, int status, Guid userId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("UPDATE psych_home_visit SET Status=@Status, LastEditBy=@UserId, LastEditTime=GETDATE() WHERE Id=@Id");

            SqlParameter[] parameters = {
                new SqlParameter("@Status", SqlDbType.Int),
                new SqlParameter("@UserId", SqlDbType.UniqueIdentifier),
                new SqlParameter("@Id", SqlDbType.UniqueIdentifier)
            };
            parameters[0].Value = status;
            parameters[1].Value = userId;
            parameters[2].Value = id;

            return DbHelperSQL.ExecuteSql(strSql.ToString(), parameters) > 0;
        }
        // 获取跟进记录，合并周记异常和心理访谈数据
        public DataTable GetFollowupRecords(Guid studentId, int columnid)
        {
            StringBuilder strSql = new StringBuilder();

            // 构建周记异常记录查询
            strSql.Append("SELECT ");
            strSql.Append("  A.Id, ");
            strSql.Append("  '周记异常' AS TypeText, ");
            strSql.Append("  A.CreateTime, ");
            strSql.Append("  A.ReviewerId Creator, ");
            strSql.Append("  A.GJRecod ");
            strSql.Append("FROM psych_week_diary_abnormal A ");
            strSql.Append($"WHERE A.StudentId='{studentId}' ");

            // UNION 连接心理访谈记录
            strSql.Append("UNION ");

            strSql.Append("SELECT ");
            strSql.Append("  B.Id, ");
            strSql.Append("  B.InterviewType AS TypeText, ");
            strSql.Append("  B.InterviewTime CreateTime, ");
            strSql.Append("  B.UserId Creator, ");
            strSql.Append("  B.GJRecod ");
            strSql.Append("FROM psych_interview B ");
            strSql.Append($"WHERE B.StudentId='{studentId}' ");

            // 按创建时间排序
            strSql.Append("ORDER BY CreateTime ");

            return DbHelperSQL.Query(strSql.ToString()).Tables[0];
        }


        /// <summary>
        /// 获取家访率
        /// </summary>
        /// <param name="columnId"></param>
        /// <param name="gradeId"></param>
        /// <param name="classId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public DataSet GetHomeVisitRate(int? columnId = null, Guid? gradeId = null, Guid? classId = null, DateTime? startTime = null, DateTime? endTime = null)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"
        SELECT 
            COUNT(DISTINCT CASE WHEN phv.HomeVisitContent IS NOT NULL THEN phv.StudentId END) AS CompletedCount,
            COUNT(DISTINCT pb.StudentId) AS TotalStudentCount
        FROM psych_basicInfo pb
        LEFT JOIN psych_home_visit phv ON pb.StudentId = phv.StudentId   LEFT JOIN JC_StudentInfos stu
ON stu.ID = pb.StudentId ");

            List<SqlParameter> parameters = new List<SqlParameter>();

            if (startTime.HasValue)
            {
                sql.AppendLine(" AND phv.InterviewTime >= @StartTime");
                parameters.Add(new SqlParameter("@StartTime", SqlDbType.DateTime) { Value = startTime.Value });
            }

            if (endTime.HasValue)
            {
                sql.AppendLine(" AND phv.InterviewTime <= @EndTime");
                parameters.Add(new SqlParameter("@EndTime", SqlDbType.DateTime) { Value = endTime.Value });
            }

            sql.AppendLine(" WHERE 1=1");

            if (columnId.HasValue)
            {
                sql.AppendLine(" AND stu.ColumnId = @ColumnId");
                parameters.Add(new SqlParameter("@ColumnId", SqlDbType.Int) { Value = columnId.Value });
            }

            if (gradeId.HasValue)
            {
                sql.AppendLine(" AND stu.GradeID = @GradeId");
                parameters.Add(new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier) { Value = gradeId.Value });
            }

            if (classId.HasValue)
            {
                sql.AppendLine(" AND stu.ClassID = @ClassId");
                parameters.Add(new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier) { Value = classId.Value });
            }

            DataSet result = DbHelperSQL.Query(sql.ToString(), parameters.ToArray());
            return result;
        }


        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_home_visit t");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 大数据家访统计
        /// </summary>
        /// <param name="strWhereClause"></param>
        /// <returns></returns>
        public DataTable GetHomeVisitStat(string columnId,string columnPath,string isSchool,Guid termId)
        {
            string strSql = @"
        SELECT 
            COUNT(DISTINCT CASE WHEN t.HomeVisitContent IS NOT NULL THEN t.StudentId END) AS CompletedCount,
            COUNT(DISTINCT pb.StudentId) AS TotalStudentCount
        FROM psych_basicInfo pb
        INNER JOIN JC_StudentInfos stu ON stu.ID = pb.StudentId 
        LEFT JOIN psych_home_visit t ON pb.StudentId = t.StudentId   ";
            if (termId != Guid.Empty)
            {
                YunEdu.Model.JC_TermInfos model_term = new YunEdu.BLL.JC_TermInfos().GetModel(termId);
                if (model_term != null)
                {
                    strSql += $" and t.InterviewTime>='{model_term.BeginDate:yyyy-MM-dd HH:mm}'";
                    strSql += $" and t.InterviewTime<'{model_term.EndDate.AddDays(1):yyyy-MM-dd}'";
                }
            }
            strSql += " where 1=1  ";
            if (columnPath != "")
            {
                strSql += $"  and stu.ColumnPath+'|' like '{columnPath}|%' ";
            }
            if (columnId != "" && isSchool == "1")
            {
                strSql += $" and stu.ColumnId = '{columnId}'";
            }
            if (termId != Guid.Empty)
            {
                strSql += $" and stu.SchoolYear IN (SELECT JC_TermInfos.SchoolYear FROM JC_TermInfos WHERE ID='{termId}')";
            }
            
            DataSet ds = DbHelperSQL.Query(strSql);
            return ds != null && ds.Tables.Count > 0 ? ds.Tables[0] : new DataTable();
        }

        #endregion  ExtensionMethod
    }
}