﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    [Serializable]
    public partial class ecb_place_category
    {
        public ecb_place_category()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private string _categorycode;
        private string _categoryname;
        private int? _sortid;
        /// <summary>
        /// 主键ID
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区Path
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 类型编码
        /// </summary>
        public string CategoryCode
        {
            set { _categorycode = value; }
            get { return _categorycode; }
        }
        /// <summary>
        /// 类型名称
        /// </summary>
        public string CategoryName
        {
            set { _categoryname = value; }
            get { return _categoryname; }
        }
        /// <summary>
        /// 排序
        /// </summary>
        public int? SortID
        {
            set { _sortid = value; }
            get { return _sortid; }
        }
        #endregion Model
    }
}
