﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 重大生活事件
	/// </summary>
	[Serializable]
	public partial class psych_interfere
	{
		public psych_interfere()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _studentid;
		private string _interferelevel;
		private DateTime? _lastinterviewtime;
		private string _lastinterviewmethod;
		private string _lastinterviewtheme;
		private string _referralsituation;
		private string _hospitaldiagnosis;
		private string _hospitaldiagnosispic;
		private string _memo;
		private Guid _creatorid;
		private DateTime _createtime;
		/// <summary>
		/// 来源类型
		/// </summary>
		public int SourceType { get; set; }
		/// <summary>
		/// 来源数据ID
		/// </summary>
		public Guid SourceId { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 干预等级 字典
		/// </summary>
		public string InterfereLevel
		{
			set{ _interferelevel=value;}
			get{return _interferelevel;}
		}
		/// <summary>
		/// 最近会谈日期
		/// </summary>
		public DateTime? LastInterviewTime
		{
			set{ _lastinterviewtime=value;}
			get{return _lastinterviewtime;}
		}
		/// <summary>
		/// 最近会谈方式
		/// </summary>
		public string LastInterviewMethod
		{
			set{ _lastinterviewmethod=value;}
			get{return _lastinterviewmethod;}
		}
		/// <summary>
		/// 最近会谈议题
		/// </summary>
		public string LastInterviewTheme
		{
			set{ _lastinterviewtheme=value;}
			get{return _lastinterviewtheme;}
		}
		/// <summary>
		/// 转介情况
		/// </summary>
		public string ReferralSituation
		{
			set{ _referralsituation=value;}
			get{return _referralsituation;}
		}
		/// <summary>
		/// 医院诊断
		/// </summary>
		public string HospitalDiagnosis
		{
			set{ _hospitaldiagnosis=value;}
			get{return _hospitaldiagnosis;}
		}
		/// <summary>
		/// 医院诊断图片
		/// </summary>
		public string HospitalDiagnosisPic
		{
			set{ _hospitaldiagnosispic=value;}
			get{return _hospitaldiagnosispic;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid CreatorId
		{
			set{ _creatorid=value;}
			get{return _creatorid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}