﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_Mode_Config
	/// </summary>
	public partial class ecb_Mode_Config
	{
		public ecb_Mode_Config()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_Mode_Config");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_Mode_Config model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_Mode_Config(");
			strSql.Append("ID,ColumnId,ColumnPath,ModeId,ShowType,WeekDays,StaTime,EndTime,ClassIds)");
			strSql.Append(" values (");
			strSql.Append("@ID,@ColumnId,@ColumnPath,@ModeId,@ShowType,@WeekDays,@StaTime,@EndTime,@ClassIds)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@ModeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ShowType", SqlDbType.Int,4),
					new SqlParameter("@WeekDays", SqlDbType.NVarChar,50),
					new SqlParameter("@StaTime", SqlDbType.DateTime),
					new SqlParameter("@EndTime", SqlDbType.DateTime),
					new SqlParameter("@ClassIds", SqlDbType.NVarChar,-1)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ModeId;
			parameters[4].Value = model.ShowType;
			parameters[5].Value = model.WeekDays;
			parameters[6].Value = model.StaTime;
			parameters[7].Value = model.EndTime;
			parameters[8].Value = model.ClassIds;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_Mode_Config model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_Mode_Config set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("ModeId=@ModeId,");
			strSql.Append("ShowType=@ShowType,");
			strSql.Append("WeekDays=@WeekDays,");
			strSql.Append("StaTime=@StaTime,");
			strSql.Append("EndTime=@EndTime,");
			strSql.Append("ClassIds=@ClassIds");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@ModeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ShowType", SqlDbType.Int,4),
					new SqlParameter("@WeekDays", SqlDbType.NVarChar,50),
					new SqlParameter("@StaTime", SqlDbType.DateTime),
					new SqlParameter("@EndTime", SqlDbType.DateTime),
					new SqlParameter("@ClassIds", SqlDbType.NVarChar,-1),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.ModeId;
			parameters[3].Value = model.ShowType;
			parameters[4].Value = model.WeekDays;
			parameters[5].Value = model.StaTime;
			parameters[6].Value = model.EndTime;
			parameters[7].Value = model.ClassIds;
			parameters[8].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_Mode_Config ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_Mode_Config ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_Mode_Config GetModel(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,ColumnId,ColumnPath,ModeId,ShowType,WeekDays,StaTime,EndTime,ClassIds from ecb_Mode_Config ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.ecb_Mode_Config model=new ECB.PC.Model.ecb_Mode_Config();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_Mode_Config DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_Mode_Config model=new ECB.PC.Model.ecb_Mode_Config();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["ModeId"]!=null && row["ModeId"].ToString()!="")
				{
					model.ModeId= new Guid(row["ModeId"].ToString());
				}
				if(row["ShowType"]!=null && row["ShowType"].ToString()!="")
				{
					model.ShowType=int.Parse(row["ShowType"].ToString());
				}
				if(row["WeekDays"]!=null)
				{
					model.WeekDays=row["WeekDays"].ToString();
				}
				if(row["StaTime"]!=null && row["StaTime"].ToString()!="")
				{
					model.StaTime=DateTime.Parse(row["StaTime"].ToString());
				}
				if(row["EndTime"]!=null && row["EndTime"].ToString()!="")
				{
					model.EndTime=DateTime.Parse(row["EndTime"].ToString());
				}
				if(row["ClassIds"]!=null)
				{
					model.ClassIds=row["ClassIds"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,ColumnId,ColumnPath,ModeId,ShowType,WeekDays,StaTime,EndTime,ClassIds ");
			strSql.Append(" FROM ecb_Mode_Config ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,ColumnId,ColumnPath,ModeId,ShowType,WeekDays,StaTime,EndTime,ClassIds ");
			strSql.Append(" FROM ecb_Mode_Config ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_Mode_Config ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_Mode_Config T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Mode_Config";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

