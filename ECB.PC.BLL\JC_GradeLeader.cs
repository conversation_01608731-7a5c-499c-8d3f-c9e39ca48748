﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using YunEdu.DBUtility;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:JC_GradeLeader
    /// </summary>
    public partial class JC_GradeLeader
    {
        public JC_GradeLeader()
        { }
        #region  Method

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int result = DbHelperSQL.RunProcedure("UP_JC_GradeLeader_Exists", parameters, out rowsAffected);
            if (result == 1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  增加一条数据
        /// </summary>
        public bool Add(Model.JC_GradeLeader model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("INSERT INTO [dbo].[JC_GradeLeader]");
            strSql.AppendLine("([ID],[GradeID],[GradeLeaderId],[DutyTypeCode],[StatusCode],[SchoolColumnID],[SchoolColumnPath],[SchoolYear])");
            strSql.AppendLine("VALUES(NEWID(),'" + model.GradeID + "','" + model.GradeLeaderId + "','" + model.DutyTypeCode + "','" + model.StatusCode + "'," + model.SchoolColumnID + ",'" + model.SchoolColumnPath + "','" + model.SchoolYear + "')");
            int rowsAffected = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  更新一条数据
        /// </summary>
        public bool Update(Model.JC_GradeLeader model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("UPDATE [dbo].[JC_GradeLeader]");
            strSql.AppendLine("SET [GradeID] = '" + model.GradeID + "',[GradeLeaderId] = '" + model.GradeLeaderId + "',[DutyTypeCode] = '" + model.DutyTypeCode + "',[StatusCode] = '" + model.StatusCode + "',[SchoolColumnID] = " + model.SchoolColumnID + ",[SchoolColumnPath] = '" + model.SchoolColumnPath + "',[SchoolYear] = " + model.SchoolYear + "'");
            strSql.AppendLine("WHERE ID='" + model.ID + "'");
            int rowsAffected = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            DbHelperSQL.RunProcedure("UP_JC_GradeLeader_Delete", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_GradeLeader ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.JC_GradeLeader GetModel(Guid ID)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            Model.JC_GradeLeader model = new Model.JC_GradeLeader();
            DataSet ds = DbHelperSQL.RunProcedure("UP_JC_GradeLeader_GetModel", parameters, "ds");
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.JC_GradeLeader DataRowToModel(DataRow row)
        {
            Model.JC_GradeLeader model = new Model.JC_GradeLeader();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["GradeID"] != null && row["GradeID"].ToString() != "")
                {
                    model.GradeID = new Guid(row["GradeID"].ToString());
                }
                if (row["GradeLeaderId"] != null && row["GradeLeaderId"].ToString() != "")
                {
                    model.GradeLeaderId = new Guid(row["GradeLeaderId"].ToString());
                }
                if (row["DutyTypeCode"] != null)
                {
                    model.DutyTypeCode = row["DutyTypeCode"].ToString();
                }
                if (row["StatusCode"] != null)
                {
                    model.StatusCode = row["StatusCode"].ToString();
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM JC_GradeLeader ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" * ");
            strSql.Append(" FROM JC_GradeLeader ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_GradeLeader ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_GradeLeader T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "JC_GradeLeader";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  Method
        #region  MethodEx

        /// <summary>
        /// 判断是否是年级组长
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public bool IsGradeLeader(int columnId, Guid userId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_GradeLeader where SchoolColumnID=@SchoolColumnID and DutyTypeCode=4 and GradeLeaderId=@GradeLeaderId ");
            strSql.Append("and SchoolYear=(select SchoolYear from JC_TermInfos where SchoolColumnId=@SchoolColumnID and IsCurrentTerm=1)");
            SqlParameter[] parameters = {
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@GradeLeaderId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = userId;
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return false;
            }
            else
            {
                return Convert.ToInt32(obj) > 0;
            }
        }

        /// <summary>
        /// 判断是否是年级组长
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetGrades(int columnId, Guid userId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select b.ID,b.GradeName from JC_GradeLeader a left join JC_GradeInfos b on a.GradeID=b.ID where a.SchoolColumnID=@SchoolColumnID and DutyTypeCode=4 ");
            strSql.Append("and a.GradeLeaderId=@GradeLeaderId and a.SchoolYear=(select SchoolYear from JC_TermInfos where SchoolColumnId=@SchoolColumnID and IsCurrentTerm=1)");
            SqlParameter[] parameters = {
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@GradeLeaderId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = userId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        #endregion  MethodEx
    }
}
