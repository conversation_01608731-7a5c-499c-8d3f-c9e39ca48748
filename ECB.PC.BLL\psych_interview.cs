﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_interview
    /// </summary>
    public partial class psych_interview
    {
        public psych_interview()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_interview");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.psych_interview model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_interview(");
            strSql.Append("Id,ColumnId,ColumnPath,InterviewType,StudentId,UserId,InterviewTime,DietaryStatus,DietaryStatusExt,SleepStatus,SleepStatusExt,EmotionalCharact,EmotionalCharactExt,SocialSupport,SocialSupportExt,IsRiskOfSelfHarm,ZWSHXF_Exists,ZWSHXF_EarliestTime,ZWSHXF_Frequency,ZWSHXF_LastTime,ZWSHXF_Detail,ZWSHXW_Exists,ZWSHXW_EarliestTime,ZWSHXW_Frequency,ZWSHXW_LastTime,ZWSHXW_Detail,ZSXF_Exists,ZSXF_EarliestTime,ZSXF_Frequency,ZSXF_LastTime,ZSXF_Plan,ZSXF_PlanType,ZSXW_Exists,ZSXW_EarliestTime,ZSXW_Frequency,ZSXW_LastTime,ZSXW_Detail,GJRecod,LastEditBy,LastEditTime,InterViewPic)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@InterviewType,@StudentId,@UserId,@InterviewTime,@DietaryStatus,@DietaryStatusExt,@SleepStatus,@SleepStatusExt,@EmotionalCharact,@EmotionalCharactExt,@SocialSupport,@SocialSupportExt,@IsRiskOfSelfHarm,@ZWSHXF_Exists,@ZWSHXF_EarliestTime,@ZWSHXF_Frequency,@ZWSHXF_LastTime,@ZWSHXF_Detail,@ZWSHXW_Exists,@ZWSHXW_EarliestTime,@ZWSHXW_Frequency,@ZWSHXW_LastTime,@ZWSHXW_Detail,@ZSXF_Exists,@ZSXF_EarliestTime,@ZSXF_Frequency,@ZSXF_LastTime,@ZSXF_Plan,@ZSXF_PlanType,@ZSXW_Exists,@ZSXW_EarliestTime,@ZSXW_Frequency,@ZSXW_LastTime,@ZSXW_Detail,@GJRecod,@LastEditBy,@LastEditTime,@InterViewPic)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@InterviewType", SqlDbType.NVarChar,4),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@InterviewTime", SqlDbType.DateTime),
                    new SqlParameter("@DietaryStatus", SqlDbType.NVarChar,4),
                    new SqlParameter("@DietaryStatusExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@SleepStatus", SqlDbType.NVarChar,4),
                    new SqlParameter("@SleepStatusExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@EmotionalCharact", SqlDbType.NVarChar,4),
                    new SqlParameter("@EmotionalCharactExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@SocialSupport", SqlDbType.NVarChar,4),
                    new SqlParameter("@SocialSupportExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@IsRiskOfSelfHarm", SqlDbType.Int,4),
                    new SqlParameter("@ZWSHXF_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZWSHXF_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXF_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZWSHXF_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXF_Detail", SqlDbType.NVarChar,500),
                    new SqlParameter("@ZWSHXW_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZWSHXW_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXW_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZWSHXW_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXW_Detail", SqlDbType.NVarChar,500),
                    new SqlParameter("@ZSXF_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZSXF_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXF_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZSXF_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXF_Plan", SqlDbType.NVarChar,50),
                    new SqlParameter("@ZSXF_PlanType", SqlDbType.NVarChar,500),
                    new SqlParameter("@ZSXW_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZSXW_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXW_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZSXW_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXW_Detail", SqlDbType.NVarChar,-1),
                    new SqlParameter("@GJRecod", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@InterViewPic",SqlDbType.NVarChar,-1)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.InterviewType;
            parameters[4].Value = model.StudentId;
            parameters[5].Value = model.UserId;
            parameters[6].Value = model.InterviewTime;
            parameters[7].Value = model.DietaryStatus;
            parameters[8].Value = model.DietaryStatusExt;
            parameters[9].Value = model.SleepStatus;
            parameters[10].Value = model.SleepStatusExt;
            parameters[11].Value = model.EmotionalCharact;
            parameters[12].Value = model.EmotionalCharactExt;
            parameters[13].Value = model.SocialSupport;
            parameters[14].Value = model.SocialSupportExt;
            parameters[15].Value = model.IsRiskOfSelfHarm;
            parameters[16].Value = model.ZWSHXF_Exists;
            parameters[17].Value = model.ZWSHXF_EarliestTime;
            parameters[18].Value = model.ZWSHXF_Frequency;
            parameters[19].Value = model.ZWSHXF_LastTime;
            parameters[20].Value = model.ZWSHXF_Detail;
            parameters[21].Value = model.ZWSHXW_Exists;
            parameters[22].Value = model.ZWSHXW_EarliestTime;
            parameters[23].Value = model.ZWSHXW_Frequency;
            parameters[24].Value = model.ZWSHXW_LastTime;
            parameters[25].Value = model.ZWSHXW_Detail;
            parameters[26].Value = model.ZSXF_Exists;
            parameters[27].Value = model.ZSXF_EarliestTime;
            parameters[28].Value = model.ZSXF_Frequency;
            parameters[29].Value = model.ZSXF_LastTime;
            parameters[30].Value = model.ZSXF_Plan;
            parameters[31].Value = model.ZSXF_PlanType;
            parameters[32].Value = model.ZSXW_Exists;
            parameters[33].Value = model.ZSXW_EarliestTime;
            parameters[34].Value = model.ZSXW_Frequency;
            parameters[35].Value = model.ZSXW_LastTime;
            parameters[36].Value = model.ZSXW_Detail;
            parameters[37].Value = model.GJRecod;
            parameters[38].Value = model.LastEditBy;
            parameters[39].Value = model.LastEditTime;
            parameters[40].Value = model.InterViewPic;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.psych_interview model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_interview set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("InterviewType=@InterviewType,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("InterviewTime=@InterviewTime,");
            strSql.Append("DietaryStatus=@DietaryStatus,");
            strSql.Append("DietaryStatusExt=@DietaryStatusExt,");
            strSql.Append("SleepStatus=@SleepStatus,");
            strSql.Append("SleepStatusExt=@SleepStatusExt,");
            strSql.Append("EmotionalCharact=@EmotionalCharact,");
            strSql.Append("EmotionalCharactExt=@EmotionalCharactExt,");
            strSql.Append("SocialSupport=@SocialSupport,");
            strSql.Append("SocialSupportExt=@SocialSupportExt,");
            strSql.Append("IsRiskOfSelfHarm=@IsRiskOfSelfHarm,");
            strSql.Append("ZWSHXF_Exists=@ZWSHXF_Exists,");
            strSql.Append("ZWSHXF_EarliestTime=@ZWSHXF_EarliestTime,");
            strSql.Append("ZWSHXF_Frequency=@ZWSHXF_Frequency,");
            strSql.Append("ZWSHXF_LastTime=@ZWSHXF_LastTime,");
            strSql.Append("ZWSHXF_Detail=@ZWSHXF_Detail,");
            strSql.Append("ZWSHXW_Exists=@ZWSHXW_Exists,");
            strSql.Append("ZWSHXW_EarliestTime=@ZWSHXW_EarliestTime,");
            strSql.Append("ZWSHXW_Frequency=@ZWSHXW_Frequency,");
            strSql.Append("ZWSHXW_LastTime=@ZWSHXW_LastTime,");
            strSql.Append("ZWSHXW_Detail=@ZWSHXW_Detail,");
            strSql.Append("ZSXF_Exists=@ZSXF_Exists,");
            strSql.Append("ZSXF_EarliestTime=@ZSXF_EarliestTime,");
            strSql.Append("ZSXF_Frequency=@ZSXF_Frequency,");
            strSql.Append("ZSXF_LastTime=@ZSXF_LastTime,");
            strSql.Append("ZSXF_Plan=@ZSXF_Plan,");
            strSql.Append("ZSXF_PlanType=@ZSXF_PlanType,");
            strSql.Append("ZSXW_Exists=@ZSXW_Exists,");
            strSql.Append("ZSXW_EarliestTime=@ZSXW_EarliestTime,");
            strSql.Append("ZSXW_Frequency=@ZSXW_Frequency,");
            strSql.Append("ZSXW_LastTime=@ZSXW_LastTime,");
            strSql.Append("ZSXW_Detail=@ZSXW_Detail,");
            strSql.Append("GJRecod=@GJRecod,");
            strSql.Append("LastEditBy=@LastEditBy,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("InterViewPic=@InterViewPic ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@InterviewType", SqlDbType.NVarChar,4),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@InterviewTime", SqlDbType.DateTime),
                    new SqlParameter("@DietaryStatus", SqlDbType.NVarChar,4),
                    new SqlParameter("@DietaryStatusExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@SleepStatus", SqlDbType.NVarChar,4),
                    new SqlParameter("@SleepStatusExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@EmotionalCharact", SqlDbType.NVarChar,4),
                    new SqlParameter("@EmotionalCharactExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@SocialSupport", SqlDbType.NVarChar,4),
                    new SqlParameter("@SocialSupportExt", SqlDbType.NVarChar,200),
                    new SqlParameter("@IsRiskOfSelfHarm", SqlDbType.Int,4),
                    new SqlParameter("@ZWSHXF_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZWSHXF_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXF_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZWSHXF_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXF_Detail", SqlDbType.NVarChar,500),
                    new SqlParameter("@ZWSHXW_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZWSHXW_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXW_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZWSHXW_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZWSHXW_Detail", SqlDbType.NVarChar,500),
                    new SqlParameter("@ZSXF_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZSXF_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXF_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZSXF_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXF_Plan", SqlDbType.NVarChar,50),
                    new SqlParameter("@ZSXF_PlanType", SqlDbType.NVarChar,500),
                    new SqlParameter("@ZSXW_Exists", SqlDbType.Int,4),
                    new SqlParameter("@ZSXW_EarliestTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXW_Frequency", SqlDbType.NVarChar,100),
                    new SqlParameter("@ZSXW_LastTime", SqlDbType.DateTime),
                    new SqlParameter("@ZSXW_Detail", SqlDbType.NVarChar,-1),
                    new SqlParameter("@GJRecod", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@InterViewPic", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.InterviewType;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.UserId;
            parameters[5].Value = model.InterviewTime;
            parameters[6].Value = model.DietaryStatus;
            parameters[7].Value = model.DietaryStatusExt;
            parameters[8].Value = model.SleepStatus;
            parameters[9].Value = model.SleepStatusExt;
            parameters[10].Value = model.EmotionalCharact;
            parameters[11].Value = model.EmotionalCharactExt;
            parameters[12].Value = model.SocialSupport;
            parameters[13].Value = model.SocialSupportExt;
            parameters[14].Value = model.IsRiskOfSelfHarm;
            parameters[15].Value = model.ZWSHXF_Exists;
            parameters[16].Value = model.ZWSHXF_EarliestTime;
            parameters[17].Value = model.ZWSHXF_Frequency;
            parameters[18].Value = model.ZWSHXF_LastTime;
            parameters[19].Value = model.ZWSHXF_Detail;
            parameters[20].Value = model.ZWSHXW_Exists;
            parameters[21].Value = model.ZWSHXW_EarliestTime;
            parameters[22].Value = model.ZWSHXW_Frequency;
            parameters[23].Value = model.ZWSHXW_LastTime;
            parameters[24].Value = model.ZWSHXW_Detail;
            parameters[25].Value = model.ZSXF_Exists;
            parameters[26].Value = model.ZSXF_EarliestTime;
            parameters[27].Value = model.ZSXF_Frequency;
            parameters[28].Value = model.ZSXF_LastTime;
            parameters[29].Value = model.ZSXF_Plan;
            parameters[30].Value = model.ZSXF_PlanType;
            parameters[31].Value = model.ZSXW_Exists;
            parameters[32].Value = model.ZSXW_EarliestTime;
            parameters[33].Value = model.ZSXW_Frequency;
            parameters[34].Value = model.ZSXW_LastTime;
            parameters[35].Value = model.ZSXW_Detail;
            parameters[36].Value = model.GJRecod;
            parameters[37].Value = model.LastEditBy;
            parameters[38].Value = model.LastEditTime;
            parameters[39].Value = model.InterViewPic;
            parameters[40].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_interview ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_interview ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_interview GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,InterviewType,StudentId,UserId,InterviewTime,DietaryStatus,DietaryStatusExt,SleepStatus,SleepStatusExt,EmotionalCharact,EmotionalCharactExt,SocialSupport,SocialSupportExt,IsRiskOfSelfHarm,ZWSHXF_Exists,ZWSHXF_EarliestTime,ZWSHXF_Frequency,ZWSHXF_LastTime,ZWSHXF_Detail,ZWSHXW_Exists,ZWSHXW_EarliestTime,ZWSHXW_Frequency,ZWSHXW_LastTime,ZWSHXW_Detail,ZSXF_Exists,ZSXF_EarliestTime,ZSXF_Frequency,ZSXF_LastTime,ZSXF_Plan,ZSXF_PlanType,ZSXW_Exists,ZSXW_EarliestTime,ZSXW_Frequency,ZSXW_LastTime,ZSXW_Detail,GJRecod,LastEditBy,LastEditTime,InterViewPic from psych_interview ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.psych_interview model = new ECB.PC.Model.psych_interview();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_interview DataRowToModel(DataRow row)
        {
            ECB.PC.Model.psych_interview model = new ECB.PC.Model.psych_interview();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["InterviewType"] != null)
                {
                    model.InterviewType = row["InterviewType"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["InterviewTime"] != null && row["InterviewTime"].ToString() != "")
                {
                    model.InterviewTime = DateTime.Parse(row["InterviewTime"].ToString());
                }
                if (row["DietaryStatus"] != null)
                {
                    model.DietaryStatus = row["DietaryStatus"].ToString();
                }
                if (row["DietaryStatusExt"] != null)
                {
                    model.DietaryStatusExt = row["DietaryStatusExt"].ToString();
                }
                if (row["SleepStatus"] != null)
                {
                    model.SleepStatus = row["SleepStatus"].ToString();
                }
                if (row["SleepStatusExt"] != null)
                {
                    model.SleepStatusExt = row["SleepStatusExt"].ToString();
                }
                if (row["EmotionalCharact"] != null)
                {
                    model.EmotionalCharact = row["EmotionalCharact"].ToString();
                }
                if (row["EmotionalCharactExt"] != null)
                {
                    model.EmotionalCharactExt = row["EmotionalCharactExt"].ToString();
                }
                if (row["SocialSupport"] != null)
                {
                    model.SocialSupport = row["SocialSupport"].ToString();
                }
                if (row["SocialSupportExt"] != null)
                {
                    model.SocialSupportExt = row["SocialSupportExt"].ToString();
                }
                if (row["IsRiskOfSelfHarm"] != null && row["IsRiskOfSelfHarm"].ToString() != "")
                {
                    model.IsRiskOfSelfHarm = int.Parse(row["IsRiskOfSelfHarm"].ToString());
                }
                if (row["ZWSHXF_Exists"] != null && row["ZWSHXF_Exists"].ToString() != "")
                {
                    model.ZWSHXF_Exists = int.Parse(row["ZWSHXF_Exists"].ToString());
                }
                if (row["ZWSHXF_EarliestTime"] != null && row["ZWSHXF_EarliestTime"].ToString() != "")
                {
                    model.ZWSHXF_EarliestTime = DateTime.Parse(row["ZWSHXF_EarliestTime"].ToString());
                }
                if (row["ZWSHXF_Frequency"] != null)
                {
                    model.ZWSHXF_Frequency = row["ZWSHXF_Frequency"].ToString();
                }
                if (row["ZWSHXF_LastTime"] != null && row["ZWSHXF_LastTime"].ToString() != "")
                {
                    model.ZWSHXF_LastTime = DateTime.Parse(row["ZWSHXF_LastTime"].ToString());
                }
                if (row["ZWSHXF_Detail"] != null)
                {
                    model.ZWSHXF_Detail = row["ZWSHXF_Detail"].ToString();
                }
                if (row["ZWSHXW_Exists"] != null && row["ZWSHXW_Exists"].ToString() != "")
                {
                    model.ZWSHXW_Exists = int.Parse(row["ZWSHXW_Exists"].ToString());
                }
                if (row["ZWSHXW_EarliestTime"] != null && row["ZWSHXW_EarliestTime"].ToString() != "")
                {
                    model.ZWSHXW_EarliestTime = DateTime.Parse(row["ZWSHXW_EarliestTime"].ToString());
                }
                if (row["ZWSHXW_Frequency"] != null)
                {
                    model.ZWSHXW_Frequency = row["ZWSHXW_Frequency"].ToString();
                }
                if (row["ZWSHXW_LastTime"] != null && row["ZWSHXW_LastTime"].ToString() != "")
                {
                    model.ZWSHXW_LastTime = DateTime.Parse(row["ZWSHXW_LastTime"].ToString());
                }
                if (row["ZWSHXW_Detail"] != null)
                {
                    model.ZWSHXW_Detail = row["ZWSHXW_Detail"].ToString();
                }
                if (row["ZSXF_Exists"] != null && row["ZSXF_Exists"].ToString() != "")
                {
                    model.ZSXF_Exists = int.Parse(row["ZSXF_Exists"].ToString());
                }
                if (row["ZSXF_EarliestTime"] != null && row["ZSXF_EarliestTime"].ToString() != "")
                {
                    model.ZSXF_EarliestTime = DateTime.Parse(row["ZSXF_EarliestTime"].ToString());
                }
                if (row["ZSXF_Frequency"] != null)
                {
                    model.ZSXF_Frequency = row["ZSXF_Frequency"].ToString();
                }
                if (row["ZSXF_LastTime"] != null && row["ZSXF_LastTime"].ToString() != "")
                {
                    model.ZSXF_LastTime = DateTime.Parse(row["ZSXF_LastTime"].ToString());
                }
                if (row["ZSXF_Plan"] != null)
                {
                    model.ZSXF_Plan = row["ZSXF_Plan"].ToString();
                }
                if (row["ZSXF_PlanType"] != null)
                {
                    model.ZSXF_PlanType = row["ZSXF_PlanType"].ToString();
                }
                if (row["ZSXW_Exists"] != null && row["ZSXW_Exists"].ToString() != "")
                {
                    model.ZSXW_Exists = int.Parse(row["ZSXW_Exists"].ToString());
                }
                if (row["ZSXW_EarliestTime"] != null && row["ZSXW_EarliestTime"].ToString() != "")
                {
                    model.ZSXW_EarliestTime = DateTime.Parse(row["ZSXW_EarliestTime"].ToString());
                }
                if (row["ZSXW_Frequency"] != null)
                {
                    model.ZSXW_Frequency = row["ZSXW_Frequency"].ToString();
                }
                if (row["ZSXW_LastTime"] != null && row["ZSXW_LastTime"].ToString() != "")
                {
                    model.ZSXW_LastTime = DateTime.Parse(row["ZSXW_LastTime"].ToString());
                }
                if (row["ZSXW_Detail"] != null)
                {
                    model.ZSXW_Detail = row["ZSXW_Detail"].ToString();
                }
                if (row["GJRecod"] != null)
                {
                    model.GJRecod = row["GJRecod"].ToString();
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
                if (row["InterViewPic"] != null)
                {
                    model.InterViewPic = row["InterViewPic"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,InterviewType,StudentId,UserId,InterviewTime,DietaryStatus,DietaryStatusExt,SleepStatus,SleepStatusExt,EmotionalCharact,EmotionalCharactExt,SocialSupport,SocialSupportExt,IsRiskOfSelfHarm,ZWSHXF_Exists,ZWSHXF_EarliestTime,ZWSHXF_Frequency,ZWSHXF_LastTime,ZWSHXF_Detail,ZWSHXW_Exists,ZWSHXW_EarliestTime,ZWSHXW_Frequency,ZWSHXW_LastTime,ZWSHXW_Detail,ZSXF_Exists,ZSXF_EarliestTime,ZSXF_Frequency,ZSXF_LastTime,ZSXF_Plan,ZSXF_PlanType,ZSXW_Exists,ZSXW_EarliestTime,ZSXW_Frequency,ZSXW_LastTime,ZSXW_Detail,GJRecod,LastEditBy,LastEditTime,InterViewPic ");
            strSql.Append(" FROM psych_interview ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,InterviewType,StudentId,UserId,InterviewTime,DietaryStatus,DietaryStatusExt,SleepStatus,SleepStatusExt,EmotionalCharact,EmotionalCharactExt,SocialSupport,SocialSupportExt,IsRiskOfSelfHarm,ZWSHXF_Exists,ZWSHXF_EarliestTime,ZWSHXF_Frequency,ZWSHXF_LastTime,ZWSHXF_Detail,ZWSHXW_Exists,ZWSHXW_EarliestTime,ZWSHXW_Frequency,ZWSHXW_LastTime,ZWSHXW_Detail,ZSXF_Exists,ZSXF_EarliestTime,ZSXF_Frequency,ZSXF_LastTime,ZSXF_Plan,ZSXF_PlanType,ZSXW_Exists,ZSXW_EarliestTime,ZSXW_Frequency,ZSXW_LastTime,ZSXW_Detail,GJRecod,LastEditBy,LastEditTime,InterViewPic ");
            strSql.Append(" FROM psych_interview ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_interview ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_interview T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_interview";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 获取访谈统计数据
        /// 功能：按访谈类型分组统计参与人数、谈话次数、谈心学生数量
        /// 关键逻辑：使用大字符串SQL查询，按访谈类型分组统计，关联学生信息表
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <returns>统计数据表</returns>
        public DataTable GetInterviewStatistics(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"
                SELECT
                    i.InterviewType,
                    COUNT(DISTINCT i.UserId) as ParticipantCount,
                    COUNT(i.Id) as InterviewCount,
                    COUNT(DISTINCT i.StudentId) as StudentCount
                FROM psych_interview i
                INNER JOIN JC_StudentInfos s ON i.StudentId = s.ID
                WHERE {0}
                GROUP BY i.InterviewType
                ORDER BY i.InterviewType
            ");

            string finalSql = string.Format(strSql.ToString(), strWhere);
            return DbHelperSQL.Query(finalSql).Tables[0];
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_interview t ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetStudentCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count( DISTINCT StudentId) FROM psych_interview t ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 大数据获取访谈统计数据
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataTable GetInterviewStat(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"
                SELECT
                    t.InterviewType,
                    COUNT(DISTINCT t.StudentId) as StudentCount,
                    COUNT(DISTINCT t.UserId) as ParticipantCount,
                    COUNT(t.Id) as InterviewCount
                FROM psych_interview t
                WHERE {0}
                GROUP BY t.InterviewType
                ORDER BY t.InterviewType
            ");

            string finalSql = string.Format(strSql.ToString(), strWhere);
            return DbHelperSQL.Query(finalSql).Tables[0];
        }
        #endregion  ExtensionMethod
    }   
}