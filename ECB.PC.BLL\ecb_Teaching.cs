﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_Teaching
    /// </summary>
    public partial class ecb_Teaching
    {
        public ecb_Teaching()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_Teaching");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_Teaching model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_Teaching(");
            strSql.Append("Id,ColumnId,ColumnPath,SubjectCode,TeacherId,PlaceId,WeekClass,WeekEvenClassNum,AlwayEvenClassNum,WXWeekClass,Message,GradeId,ClassId,WeekType)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@SubjectCode,@TeacherId,@PlaceId,@WeekClass,@WeekEvenClassNum,@AlwayEvenClassNum,@WXWeekClass,@Message,@GradeId,@ClassId,@WeekType)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.VarChar,500),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,2),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@WeekClass", SqlDbType.Int,4),
                    new SqlParameter("@WeekEvenClassNum", SqlDbType.Int,4),
                    new SqlParameter("@AlwayEvenClassNum", SqlDbType.Int,4),
                    new SqlParameter("@WXWeekClass", SqlDbType.Int,4),
                    new SqlParameter("@Message", SqlDbType.NVarChar,-1),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@WeekType", SqlDbType.NVarChar,50)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.SubjectCode;
            parameters[4].Value = model.TeacherId;
            parameters[5].Value = model.PlaceId;
            parameters[6].Value = model.WeekClass;
            parameters[7].Value = model.WeekEvenClassNum;
            parameters[8].Value = model.AlwayEvenClassNum;
            parameters[9].Value = model.WXWeekClass;
            parameters[10].Value = model.Message;
            parameters[11].Value = model.GradeId;
            parameters[12].Value = model.ClassId;
            parameters[13].Value = model.WeekType;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_Teaching model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Teaching set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("SubjectCode=@SubjectCode,");
            strSql.Append("TeacherId=@TeacherId,");
            strSql.Append("PlaceId=@PlaceId,");
            strSql.Append("WeekClass=@WeekClass,");
            strSql.Append("WeekEvenClassNum=@WeekEvenClassNum,");
            strSql.Append("AlwayEvenClassNum=@AlwayEvenClassNum,");
            strSql.Append("WXWeekClass=@WXWeekClass,");
          
            strSql.Append("Message=@Message,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("WeekType=@WeekType");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.VarChar,500),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,2),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@WeekClass", SqlDbType.Int,4),
                    new SqlParameter("@WeekEvenClassNum", SqlDbType.Int,4),
                    new SqlParameter("@AlwayEvenClassNum", SqlDbType.Int,4),
                    new SqlParameter("@WXWeekClass", SqlDbType.Int,4),
                    new SqlParameter("@Message", SqlDbType.NVarChar,-1),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@WeekType", SqlDbType.NVarChar,50),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.SubjectCode;
            parameters[3].Value = model.TeacherId;
            parameters[4].Value = model.PlaceId;
            parameters[5].Value = model.WeekClass;
            parameters[6].Value = model.WeekEvenClassNum;
            parameters[7].Value = model.AlwayEvenClassNum;
            parameters[8].Value = model.WXWeekClass;
            parameters[9].Value = model.Message;
            parameters[10].Value = model.GradeId;
            parameters[11].Value = model.ClassId;
            parameters[12].Value = model.WeekType;
            parameters[13].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Teaching ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Teaching ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Teaching GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,SubjectCode,TeacherId,PlaceId,WeekClass,WeekEvenClassNum,AlwayEvenClassNum,WXWeekClass,Message,GradeId,ClassId,WeekType from ecb_Teaching ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_Teaching model = new ECB.PC.Model.ecb_Teaching();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Teaching DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_Teaching model = new ECB.PC.Model.ecb_Teaching();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["SubjectCode"] != null)
                {
                    model.SubjectCode = row["SubjectCode"].ToString();
                }
                if (row["TeacherId"] != null && row["TeacherId"].ToString() != "")
                {
                    model.TeacherId = new Guid(row["TeacherId"].ToString());
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
                if (row["WeekClass"] != null && row["WeekClass"].ToString() != "")
                {
                    model.WeekClass = int.Parse(row["WeekClass"].ToString());
                }
                if (row["WeekEvenClassNum"] != null && row["WeekEvenClassNum"].ToString() != "")
                {
                    model.WeekEvenClassNum = int.Parse(row["WeekEvenClassNum"].ToString());
                }
                if (row["AlwayEvenClassNum"] != null && row["AlwayEvenClassNum"].ToString() != "")
                {
                    model.AlwayEvenClassNum = int.Parse(row["AlwayEvenClassNum"].ToString());
                }
                if (row["WXWeekClass"] != null && row["WXWeekClass"].ToString() != "")
                {
                    model.WXWeekClass = int.Parse(row["WXWeekClass"].ToString());
                }
                if (row["Message"] != null)
                {
                    model.Message = row["Message"].ToString();
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["WeekType"] != null)
                {
                    model.WeekType = row["WeekType"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,SubjectCode,TeacherId,PlaceId,WeekClass,WeekEvenClassNum,AlwayEvenClassNum,WXWeekClass,Message,GradeId,ClassId,WeekType ");
            strSql.Append(" FROM ecb_Teaching ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,SubjectCode,TeacherId,PlaceId,WeekClass,WeekEvenClassNum,AlwayEvenClassNum,WXWeekClass,Message,GradeId,ClassId,WeekType ");
            strSql.Append(" FROM ecb_Teaching ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Teaching ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_Teaching T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Teaching";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得数据列表 职教计划
        /// </summary>
        public DataSet GetListByColumnPath(string ColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT b.GradeName,c.ClassName,e.CName,e.UserName TeacherNo,f.DictText,d.Name as RoomName,WeekClass,WeekEvenClassNum,AlwayEvenClassNum,WXWeekClass,Message,a.ColumnPath,a.WeekType  ");
            strSql.Append(" FROM ecb_Teaching a   ");
            strSql.Append(" LEFT JOIN dbo.JC_GradeInfos b ON a.GradeId=b.ID ");
            strSql.Append(" LEFT JOIN dbo.JC_ClassInfos c ON a.ClassId=c.ID");
            strSql.Append(" LEFT JOIN dbo.ecb_place d ON a.PlaceId=d.ID");
            strSql.Append(" LEFT JOIN dbo.aspnet_Membership e ON a.TeacherId=e.UserId");
            strSql.Append(" LEFT JOIN dbo.Site_Dictionary f ON a.SubjectCode=f.DictValue AND f.DictTypeId=28 and f.ColumnId in (0,a.ColumnId)");
            strSql.Append(" where a.ColumnPath+'|' LIKE '" + ColumnPath + "|%'");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

