﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_config
    /// </summary>
    public partial class ecb_config
    {
        public ecb_config()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_config");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_config(");
            strSql.Append("ID,ColumnId,ColumnPath,AttendanceMode,StartPoint,IsContinuousSignOnce,InClassTitle,TestTitle,AfterSchoolBanner,SchoolMotto,SchoolDesc,RedFlagCount,VideoType,TeaWorkTime,TimeSpan,EnableAttedanceMode,IsArriveSchoolAttendance,IsArriveSchoolAttendance_stu,StuWorkTime,InClassAttendType,MaxStuCount,IsContentNeedCheck,XinLiReservationLimitNum,SchoolBadge,XinliBiaoYu,IsXinLiReservation,IsFaceToAvatar,StuMenWeiMenJinTime,TeaMenWeiMenJinTime,MenJinUnClosedPushTime,StuDelayTime,IsNeedOpenDoor,IsFaceOpenDoor)");//,QiniuAK,QiniuSK,QiniuBucket,QiniuDomain
            strSql.Append(" values (");
            strSql.Append("@ID,@ColumnId,@ColumnPath,@AttendanceMode,@StartPoint,@IsContinuousSignOnce,@InClassTitle,@TestTitle,@AfterSchoolBanner,@SchoolMotto,@SchoolDesc,@RedFlagCount,@VideoType,@TeaWorkTime,@TimeSpan,@EnableAttedanceMode,@IsArriveSchoolAttendance,@IsArriveSchoolAttendance_stu,@StuWorkTime,@InClassAttendType,@MaxStuCount,@IsContentNeedCheck,@XinLiReservationLimitNum,@SchoolBadge,@XinliBiaoYu,@IsXinLiReservation,@IsFaceToAvatar,@StuMenWeiMenJinTime,@TeaMenWeiMenJinTime,@MenJinUnClosedPushTime,@StuDelayTime,@IsNeedOpenDoor,@IsFaceOpenDoor)"); //,@QiniuAK,@QiniuSK,@QiniuBucket,@QiniuDomain
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@AttendanceMode", SqlDbType.NVarChar,2),
                    new SqlParameter("@StartPoint", SqlDbType.Int,4),
                    new SqlParameter("@IsContinuousSignOnce", SqlDbType.Int,4),
                    new SqlParameter("@InClassTitle", SqlDbType.NVarChar,50),
                    new SqlParameter("@TestTitle", SqlDbType.NVarChar,50),
                    new SqlParameter("@AfterSchoolBanner", SqlDbType.NVarChar,50),
                    new SqlParameter("@SchoolMotto", SqlDbType.NVarChar,100),
                    new SqlParameter("@SchoolDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@RedFlagCount", SqlDbType.Int,4),
                    new SqlParameter("@VideoType", SqlDbType.Int,4) ,
                    new SqlParameter("@TeaWorkTime", SqlDbType.NVarChar,100),
                    new SqlParameter("@TimeSpan", SqlDbType.Int,4),
                    //new SqlParameter("@QiniuAK", SqlDbType.NVarChar,255),
                    //new SqlParameter("@QiniuSK", SqlDbType.NVarChar,255),
                    //new SqlParameter("@QiniuBucket", SqlDbType.NVarChar,255),
                    //new SqlParameter("@QiniuDomain", SqlDbType.NVarChar,255)
                    new SqlParameter("@EnableAttedanceMode", SqlDbType.NVarChar,50),
                    new SqlParameter("@IsArriveSchoolAttendance", SqlDbType.Int,4),
                    new SqlParameter("@IsArriveSchoolAttendance_stu", SqlDbType.Int,4),
                    new SqlParameter("@StuWorkTime", SqlDbType.NVarChar,100),
                    new SqlParameter("@InClassAttendType", SqlDbType.Int,4),
                    new SqlParameter("@MaxStuCount", SqlDbType.Int,4),
                    new SqlParameter("@IsContentNeedCheck",SqlDbType.Int,4),
                    new SqlParameter("@XinLiReservationLimitNum",SqlDbType.Int,4),
                    new SqlParameter("@SchoolBadge",SqlDbType.NVarChar,150),
                    new SqlParameter("@XinliBiaoYu",SqlDbType.NVarChar,250),
                    new SqlParameter("@IsXinLiReservation",SqlDbType.Int,4),
                    new SqlParameter("@IsFaceToAvatar", SqlDbType.Int,4),
                    new SqlParameter("@StuMenWeiMenJinTime", SqlDbType.NVarChar,50),
                    new SqlParameter("@TeaMenWeiMenJinTime", SqlDbType.NVarChar,50),
                    new SqlParameter("@MenJinUnClosedPushTime",SqlDbType.Int,4),
                    new SqlParameter("@StuDelayTime",SqlDbType.Int,4),
                    new SqlParameter("@IsNeedOpenDoor", SqlDbType.Int,4),
                    new SqlParameter("@IsFaceOpenDoor",SqlDbType.Int,4)
            };
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.AttendanceMode;
            parameters[4].Value = model.StartPoint;
            parameters[5].Value = model.IsContinuousSignOnce;
            parameters[6].Value = model.InClassTitle;
            parameters[7].Value = model.TestTitle;
            parameters[8].Value = model.AfterSchoolBanner;
            parameters[9].Value = model.SchoolMotto;
            parameters[10].Value = model.SchoolDesc;
            parameters[11].Value = model.RedFlagCount;
            parameters[12].Value = model.VideoType;
            parameters[13].Value = model.TeaWorkTime;
            parameters[14].Value = model.TimeSpan;
            //parameters[12].Value = model.QiniuAK;
            //parameters[13].Value = model.QiniuSK;
            //parameters[14].Value = model.QiniuBucket;
            //parameters[15].Value = model.QiniuDomain;
            parameters[15].Value = model.EnableAttedanceMode;
            parameters[16].Value = model.IsArriveSchoolAttendance;
            parameters[17].Value = model.IsArriveSchoolAttendance_stu;
            parameters[18].Value = model.StuWorkTime;
            parameters[19].Value = model.InClassAttendType;
            parameters[20].Value = model.MaxStuCount;
            parameters[21].Value = model.IsContentNeedCheck;
            parameters[22].Value = model.XinLiReservationLimitNum;
            parameters[23].Value = model.SchoolBadge;
            parameters[24].Value = model.XinliBiaoYu;
            parameters[25].Value = model.IsXinLiReservation;
            parameters[26].Value = model.IsFaceToAvatar;
            parameters[27].Value = model.StuMenWeiMenJinTime;
            parameters[28].Value = model.TeaMenWeiMenJinTime;
            parameters[29].Value = model.MenJinUnClosedPushTime;
            parameters[30].Value = model.StuDelayTime;
            parameters[31].Value = model.IsNeedOpenDoor;
            parameters[32].Value = model.IsFaceOpenDoor;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_config set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("AttendanceMode=@AttendanceMode,");
            strSql.Append("StartPoint=@StartPoint,");
            strSql.Append("IsContinuousSignOnce=@IsContinuousSignOnce,");
            strSql.Append("InClassTitle=@InClassTitle,");
            strSql.Append("TestTitle=@TestTitle,");
            strSql.Append("AfterSchoolBanner=@AfterSchoolBanner,");
            strSql.Append("SchoolMotto=@SchoolMotto,");
            strSql.Append("SchoolDesc=@SchoolDesc,");
            strSql.Append("RedFlagCount=@RedFlagCount,");
            strSql.Append("QiniuAK=@QiniuAK,");
            strSql.Append("QiniuSK=@QiniuSK,");
            strSql.Append("QiniuBucket=@QiniuBucket,");
            strSql.Append("QiniuDomain=@QiniuDomain,");
            strSql.Append("VideoType=@VideoType,");
            strSql.Append("TeaWorkTime=@TeaWorkTime,");
            strSql.Append("TimeSpan=@TimeSpan,");
            strSql.Append("EnableAttedanceMode=@EnableAttedanceMode,");
            strSql.Append("IsArriveSchoolAttendance=@IsArriveSchoolAttendance,");
            strSql.Append("IsArriveSchoolAttendance_stu=@IsArriveSchoolAttendance_stu,");
            strSql.Append("StuWorkTime=@StuWorkTime,");
            strSql.Append("InClassAttendType=@InClassAttendType,");
            strSql.Append("MaxStuCount=@MaxStuCount,");
            strSql.Append("XinLiReservationLimitNum=@XinLiReservationLimitNum,");
            strSql.Append("SchoolBadge=@SchoolBadge,");
            strSql.Append("XinliBiaoYu=@XinliBiaoYu,");
            strSql.Append("IsContentNeedCheck=@IsContentNeedCheck,");
            strSql.Append("IsXinLiReservation=@IsXinLiReservation,");
            strSql.Append("IsFaceToAvatar=@IsFaceToAvatar,");
            strSql.Append("StuMenWeiMenJinTime=@StuMenWeiMenJinTime,");
            strSql.Append("TeaMenWeiMenJinTime=@TeaMenWeiMenJinTime,");
            strSql.Append("MenJinUnClosedPushTime=@MenJinUnClosedPushTime,");
            strSql.Append("StuDelayTime=@StuDelayTime,");
            strSql.Append("IsNeedOpenDoor=@IsNeedOpenDoor,");
            strSql.Append("IsFaceOpenDoor=@IsFaceOpenDoor");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@AttendanceMode", SqlDbType.NVarChar,2),
                    new SqlParameter("@StartPoint", SqlDbType.Int,4),
                    new SqlParameter("@IsContinuousSignOnce", SqlDbType.Int,4),
                    new SqlParameter("@InClassTitle", SqlDbType.NVarChar,50),
                    new SqlParameter("@TestTitle", SqlDbType.NVarChar,50),
                    new SqlParameter("@AfterSchoolBanner", SqlDbType.NVarChar,50),
                    new SqlParameter("@SchoolMotto", SqlDbType.NVarChar,100),
                    new SqlParameter("@SchoolDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@RedFlagCount", SqlDbType.Int,4),
                    new SqlParameter("@QiniuAK", SqlDbType.NVarChar,255),
                    new SqlParameter("@QiniuSK", SqlDbType.NVarChar,255),
                    new SqlParameter("@QiniuBucket", SqlDbType.NVarChar,255),
                    new SqlParameter("@QiniuDomain", SqlDbType.NVarChar,255),
                    new SqlParameter("@VideoType", SqlDbType.Int,4),
                    new SqlParameter("@TeaWorkTime", SqlDbType.NVarChar,100),
                    new SqlParameter("@TimeSpan", SqlDbType.Int,4),
                    new SqlParameter("@EnableAttedanceMode", SqlDbType.NVarChar,50),
                    new SqlParameter("@IsArriveSchoolAttendance", SqlDbType.Int,4),
                    new SqlParameter("@IsArriveSchoolAttendance_stu", SqlDbType.Int,4),
                    new SqlParameter("@StuWorkTime", SqlDbType.NVarChar,100),
                    new SqlParameter("@InClassAttendType", SqlDbType.Int,4),
                    new SqlParameter("@MaxStuCount", SqlDbType.Int,4),
                    new SqlParameter("@IsContentNeedCheck",SqlDbType.Int,4),
                    new SqlParameter("@XinLiReservationLimitNum", SqlDbType.Int,4),
                    new SqlParameter("@SchoolBadge",SqlDbType.NVarChar,150),
                    new SqlParameter("@XinliBiaoYu",SqlDbType.NVarChar,250),
                    new SqlParameter("@IsXinLiReservation",SqlDbType.Int,4),
                    new SqlParameter("@IsFaceToAvatar", SqlDbType.Int,4),
                    new SqlParameter("@StuMenWeiMenJinTime", SqlDbType.NVarChar,50),
                    new SqlParameter("@TeaMenWeiMenJinTime", SqlDbType.NVarChar,50),
                    new SqlParameter("@MenJinUnClosedPushTime",SqlDbType.Int,4),
                    new SqlParameter("@StuDelayTime",SqlDbType.Int,4),
                    new SqlParameter("@IsNeedOpenDoor",SqlDbType.Int,4),
                    new SqlParameter("@IsFaceOpenDoor",SqlDbType.Int,4),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.AttendanceMode;
            parameters[3].Value = model.StartPoint;
            parameters[4].Value = model.IsContinuousSignOnce;
            parameters[5].Value = model.InClassTitle;
            parameters[6].Value = model.TestTitle;
            parameters[7].Value = model.AfterSchoolBanner;
            parameters[8].Value = model.SchoolMotto;
            parameters[9].Value = model.SchoolDesc;
            parameters[10].Value = model.RedFlagCount;
            parameters[11].Value = model.QiniuAK;
            parameters[12].Value = model.QiniuSK;
            parameters[13].Value = model.QiniuBucket;
            parameters[14].Value = model.QiniuDomain;
            parameters[15].Value = model.VideoType;
            parameters[16].Value = model.TeaWorkTime;
            parameters[17].Value = model.TimeSpan;
            parameters[18].Value = model.EnableAttedanceMode;
            parameters[19].Value = model.IsArriveSchoolAttendance;
            parameters[20].Value = model.IsArriveSchoolAttendance_stu;
            parameters[21].Value = model.StuWorkTime;
            parameters[22].Value = model.InClassAttendType;
            parameters[23].Value = model.MaxStuCount;
            parameters[24].Value = model.IsContentNeedCheck;
            parameters[25].Value = model.XinLiReservationLimitNum;
            parameters[26].Value = model.SchoolBadge;
            parameters[27].Value = model.XinliBiaoYu;
            parameters[28].Value = model.IsXinLiReservation;
            parameters[29].Value = model.IsFaceToAvatar;
            parameters[30].Value = model.StuMenWeiMenJinTime;
            parameters[31].Value = model.TeaMenWeiMenJinTime;
            parameters[32].Value = model.MenJinUnClosedPushTime;
            parameters[33].Value = model.StuDelayTime;
            parameters[34].Value = model.IsNeedOpenDoor;
            parameters[35].Value = model.IsFaceOpenDoor;
            parameters[36].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_config ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_config ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_config GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 * from ecb_config ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.ecb_config model = new ECB.PC.Model.ecb_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_config DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_config model = new ECB.PC.Model.ecb_config();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["AttendanceMode"] != null)
                {
                    model.AttendanceMode = row["AttendanceMode"].ToString();
                }
                if (row["StartPoint"] != null && row["StartPoint"].ToString() != "")
                {
                    model.StartPoint = int.Parse(row["StartPoint"].ToString());
                }
                if (row["IsContinuousSignOnce"] != null && row["IsContinuousSignOnce"].ToString() != "")
                {
                    model.IsContinuousSignOnce = int.Parse(row["IsContinuousSignOnce"].ToString());
                }
                if (row["InClassTitle"] != null)
                {
                    model.InClassTitle = row["InClassTitle"].ToString();
                }
                if (row["TestTitle"] != null)
                {
                    model.TestTitle = row["TestTitle"].ToString();
                }
                if (row["AfterSchoolBanner"] != null)
                {
                    model.AfterSchoolBanner = row["AfterSchoolBanner"].ToString();
                }
                if (row["SchoolMotto"] != null)
                {
                    model.SchoolMotto = row["SchoolMotto"].ToString();
                }
                if (row["SchoolDesc"] != null)
                {
                    model.SchoolDesc = row["SchoolDesc"].ToString();
                }
                if (row["RedFlagCount"] != null && row["RedFlagCount"].ToString() != "")
                {
                    model.RedFlagCount = int.Parse(row["RedFlagCount"].ToString());
                }
                if (row["QiniuAK"] != null)
                {
                    model.QiniuAK = row["QiniuAK"].ToString();
                }
                if (row["QiniuSK"] != null)
                {
                    model.QiniuSK = row["QiniuSK"].ToString();
                }
                if (row["QiniuBucket"] != null)
                {
                    model.QiniuBucket = row["QiniuBucket"].ToString();
                }
                if (row["QiniuDomain"] != null)
                {
                    model.QiniuDomain = row["QiniuDomain"].ToString();
                }
                if (row["VideoType"] != null && row["VideoType"].ToString() != "")
                {
                    model.VideoType = int.Parse(row["VideoType"].ToString());
                }
                if (row["TeaWorkTime"] != null)
                {
                    model.TeaWorkTime = row["TeaWorkTime"].ToString();
                }
                if (row["TimeSpan"] != null && row["TimeSpan"].ToString() != "")
                {
                    model.TimeSpan = int.Parse(row["TimeSpan"].ToString());
                }
                if (row["EnableAttedanceMode"] != null)
                {
                    model.EnableAttedanceMode = row["EnableAttedanceMode"].ToString();
                }
                if (row["IsArriveSchoolAttendance"] != null && row["IsArriveSchoolAttendance"].ToString() != "")
                {
                    model.IsArriveSchoolAttendance = int.Parse(row["IsArriveSchoolAttendance"].ToString());
                }
                if (row["IsArriveSchoolAttendance_stu"] != null && row["IsArriveSchoolAttendance_stu"].ToString() != "")
                {
                    model.IsArriveSchoolAttendance_stu = int.Parse(row["IsArriveSchoolAttendance_stu"].ToString());
                }
                if (row["StuWorkTime"] != null)
                {
                    model.StuWorkTime = row["StuWorkTime"].ToString();
                }
                if (row["InClassAttendType"] != null && row["InClassAttendType"].ToString() != "")
                {
                    model.InClassAttendType = int.Parse(row["InClassAttendType"].ToString());
                }
                if (row["MaxStuCount"] != null && row["MaxStuCount"].ToString() != "")
                {
                    model.MaxStuCount = int.Parse(row["MaxStuCount"].ToString());
                }
                if (row["IsContentNeedCheck"] != null && row["IsContentNeedCheck"].ToString() != "")
                {
                    model.IsContentNeedCheck = int.Parse(row["IsContentNeedCheck"].ToString());
                }
                if (row["XinLiReservationLimitNum"] != null && row["XinLiReservationLimitNum"].ToString() != "")
                {
                    model.XinLiReservationLimitNum = int.Parse(row["XinLiReservationLimitNum"].ToString());
                }
                if (row["SchoolBadge"] != null)
                {
                    model.SchoolBadge = row["SchoolBadge"].ToString();
                }
                if (row["XinliBiaoYu"] != null)
                {
                    model.XinliBiaoYu = row["XinliBiaoYu"].ToString();
                }
                if (row["IsXinLiReservation"] != null && row["IsXinLiReservation"].ToString() != "")
                {
                    model.IsXinLiReservation = int.Parse(row["IsXinLiReservation"].ToString());
                }
                if (row["IsFaceToAvatar"] != null && row["IsFaceToAvatar"].ToString() != "")
                {
                    model.IsFaceToAvatar = int.Parse(row["IsFaceToAvatar"].ToString());
                }
                if (row["StuMenWeiMenJinTime"] != null)
                {
                    model.StuMenWeiMenJinTime = row["StuMenWeiMenJinTime"].ToString();
                }
                if (row["TeaMenWeiMenJinTime"] != null)
                {
                    model.TeaMenWeiMenJinTime = row["TeaMenWeiMenJinTime"].ToString();
                }
                if (row["MenJinUnClosedPushTime"] != null && row["MenJinUnClosedPushTime"].ToString() != "")
                {
                    model.MenJinUnClosedPushTime = int.Parse(row["MenJinUnClosedPushTime"].ToString());
                }
                if (row["StuDelayTime"] != null && row["StuDelayTime"].ToString() != "")
                {
                    model.StuDelayTime = int.Parse(row["StuDelayTime"].ToString());
                }
                if (row["IsNeedOpenDoor"] != null && row["IsNeedOpenDoor"].ToString() != "")
                {
                    model.IsNeedOpenDoor = int.Parse(row["IsNeedOpenDoor"].ToString());
                }
                if (row["IsFaceOpenDoor"] != null && row["IsFaceOpenDoor"].ToString()!="")
                {
                    model.IsFaceOpenDoor = int.Parse(row["IsFaceOpenDoor"].ToString());
                }                
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM ecb_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" * ");
            strSql.Append(" FROM ecb_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_config T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_config";
            parameters[1].Value = "ID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_config GetModel(string ColumnPath)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 * from ecb_config ");
            strSql.Append(" where ColumnPath=@ColumnPath ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnPath", SqlDbType.NChar,255)            };
            parameters[0].Value = ColumnPath;

            DataSet ds = DbHelperSQL.Query(strSql.ToString(),parameters,DbHelperSQL.ConnMain);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string fileName, string tabName, string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by " + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, " + fileName + "  from " + tabName);
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            if (endIndex >= 0)
            {
                strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        public int GetRecordCount(string tabName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        //public DataSet GetJC_ConfigList(string strWhere)
        //{
        //    StringBuilder strSql = new StringBuilder();
        //    strSql.Append("select * ");
        //    strSql.Append(" FROM JC_Config ");
        //    if (strWhere.Trim() != "")
        //    {
        //        strSql.Append(" where " + strWhere);
        //    }
        //    return DbHelperSQL.Query(strSql.ToString());
        //}
    }
}

