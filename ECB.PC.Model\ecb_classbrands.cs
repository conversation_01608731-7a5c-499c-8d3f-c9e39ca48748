﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_ClassBrands:班牌管理
    /// </summary>
    [Serializable]
    public partial class ecb_ClassBrands
    {
        public ecb_ClassBrands()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private string _seriano;
        private Guid _classid;
        private string _name;
        private DateTime? _lastedittime;
        private string _version;
        private int? _brandstype;
        private DateTime? _lastopentime;
        private int? _isonline;
        private decimal? _onlinetime;
        private int? _istest;
        private Guid _placeId;
        private string _facemodelversion;
        private string _indexmodel;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区Id
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 班牌序列号
        /// </summary>
        public string SeriaNo
        {
            set { _seriano = value; }
            get { return _seriano; }
        }
        /// <summary>
        /// 班级ID
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 班牌所在地名称
        /// </summary>
        public string Name
        {
            set { _name = value; }
            get { return _name; }
        }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        /// <summary>
        /// 版本
        /// </summary>
        public string Version
        {
            set { _version = value; }
            get { return _version; }
        }
        /// <summary>
        /// 机器设备类型 1 班牌 2功能室3楼牌
        /// </summary>
        public int? BrandsType
        {
            set { _brandstype = value; }
            get { return _brandstype; }
        }
        /// <summary>
        /// 最近一次开机时间/最近一次连接时间
        /// </summary>
        public DateTime? LastOpenTime
        {
            set { _lastopentime = value; }
            get { return _lastopentime; }
        }
        /// <summary>
        /// 是否在线
        /// </summary>
        public int? IsOnline
        {
            set { _isonline = value; }
            get { return _isonline; }
        }
        /// <summary>
        /// 在线总时长
        /// </summary>
        public decimal? OnlineTime
        {
            set { _onlinetime = value; }
            get { return _onlinetime; }
        }
        /// <summary>
        /// 是否测试机
        /// </summary>
        public int? IsTest
        {
            set { _istest = value; }
            get { return _istest; }
        }
        /// <summary>
        /// 地点id
        /// </summary>
        public Guid PlaceId
        {
            set { _placeId = value; }
            get { return _placeId; }
        }
        /// <summary>
		/// 
		/// </summary>
		public string FaceModelVersion
        {
            set { _facemodelversion = value; }
            get { return _facemodelversion; }
        }
        /// <summary>
        /// 首页样式
        /// </summary>
        public string IndexModel
        {
            set { _indexmodel = value; }
            get { return _indexmodel; }
        }
        #endregion Model

    }
}