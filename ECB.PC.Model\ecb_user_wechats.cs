﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
	/// 班牌用户微信账号
	/// </summary>
	[Serializable]
    public partial class ecb_user_wechats
    {
        public ecb_user_wechats()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _userid;
        private string _wechatappid;
        private string _openid;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区Id路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 用户id
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 微信公众号/小程序的id
        /// </summary>
        public string WeChatAppId
        {
            set { _wechatappid = value; }
            get { return _wechatappid; }
        }
        /// <summary>
        /// 微信用户的openid
        /// </summary>
        public string OpenId
        {
            set { _openid = value; }
            get { return _openid; }
        }
        /// <summary>
        /// 微信昵称
        /// </summary>
        public string NickName { get; set; }
        /// <summary>
        /// 微信头像
        /// </summary>
        public string HeadImgUrl { get; set; }
        /// <summary>
        /// 创建时间/绑定时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
        #endregion Model

    }
}
