﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_kq_temp:临时考勤表
	/// </summary>
	[Serializable]
	public partial class ecb_kq_temp
	{
        public ecb_kq_temp()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private string _eventname;
        private int _brandstype;
        private Guid _placeid;
        private DateTime? _stadate;
        private DateTime? _enddate;
        private DateTime? _statime;
        private DateTime? _endtime;
        private int? _temptype;
        private string _weekdays;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 事件名称
        /// </summary>
        public string EventName
        {
            set { _eventname = value; }
            get { return _eventname; }
        }
        /// <summary>
        /// 设备类型
        /// </summary>
        public int BrandsType
        {
            set { _brandstype = value; }
            get { return _brandstype; }
        }
        /// <summary>
        /// 考勤场地
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StaDate
        {
            set { _stadate = value; }
            get { return _stadate; }
        }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate
        {
            set { _enddate = value; }
            get { return _enddate; }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StaTime
        {
            set { _statime = value; }
            get { return _statime; }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime
        {
            set { _endtime = value; }
            get { return _endtime; }
        }
        /// <summary>
        /// 临时考勤类型 1一次 2每日一次 3法定工作日 4 法定节假日 5 周一至周五 6自定义
        /// </summary>
        public int? TempType
        {
            set { _temptype = value; }
            get { return _temptype; }
        }
        /// <summary>
        /// 考勤周次 逗号分割
        /// </summary>
        public string WeekDays
        {
            set { _weekdays = value; }
            get { return _weekdays; }
        }
        #endregion Model

    }
}

