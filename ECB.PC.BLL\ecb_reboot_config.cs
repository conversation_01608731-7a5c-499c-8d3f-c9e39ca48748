﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_reboot_config
    /// </summary>
    public partial class ecb_reboot_config
    {
        public ecb_reboot_config()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_reboot_config");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_reboot_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_reboot_config(");
            strSql.Append("Id,ColumnId,ColumnPath,GradeId,ClassId,BootTime,OffTime,WeekId,IsEnable,SeriaNo)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@GradeId,@ClassId,@BootTime,@OffTime,@WeekId,@IsEnable,@SeriaNo)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@BootTime", SqlDbType.DateTime),
                    new SqlParameter("@OffTime", SqlDbType.DateTime),
                    new SqlParameter("@WeekId", SqlDbType.Int,4),
                    new SqlParameter("@IsEnable", SqlDbType.Int,4),
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,50)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.GradeId;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.BootTime;
            parameters[6].Value = model.OffTime;
            parameters[7].Value = model.WeekId;
            parameters[8].Value = model.IsEnable;
            parameters[9].Value = model.SeriaNo;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_reboot_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_reboot_config set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("BootTime=@BootTime,");
            strSql.Append("OffTime=@OffTime,");
            strSql.Append("WeekId=@WeekId,");
            strSql.Append("IsEnable=@IsEnable,");
            strSql.Append("SeriaNo=@SeriaNo");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@BootTime", SqlDbType.DateTime),
                    new SqlParameter("@OffTime", SqlDbType.DateTime),
                    new SqlParameter("@WeekId", SqlDbType.Int,4),
                    new SqlParameter("@IsEnable", SqlDbType.Int,4),
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,50),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.GradeId;
            parameters[3].Value = model.ClassId;
            parameters[4].Value = model.BootTime;
            parameters[5].Value = model.OffTime;
            parameters[6].Value = model.WeekId;
            parameters[7].Value = model.IsEnable;
            parameters[8].Value = model.SeriaNo;
            parameters[9].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_config ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_reboot_config GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,GradeId,ClassId,BootTime,OffTime,WeekId,IsEnable,SeriaNo from ecb_reboot_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.ecb_reboot_config model = new Model.ecb_reboot_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_reboot_config DataRowToModel(DataRow row)
        {
            Model.ecb_reboot_config model = new Model.ecb_reboot_config();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["BootTime"] != null && row["BootTime"].ToString() != "")
                {
                    model.BootTime = DateTime.Parse(row["BootTime"].ToString());
                }
                if (row["OffTime"] != null && row["OffTime"].ToString() != "")
                {
                    model.OffTime = DateTime.Parse(row["OffTime"].ToString());
                }
                if (row["WeekId"] != null && row["WeekId"].ToString() != "")
                {
                    model.WeekId = int.Parse(row["WeekId"].ToString());
                }
                if (row["IsEnable"] != null && row["IsEnable"].ToString() != "")
                {
                    model.IsEnable = int.Parse(row["IsEnable"].ToString());
                }
                if (row["SeriaNo"] != null)
                {
                    model.SeriaNo = row["SeriaNo"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,GradeId,ClassId,BootTime,OffTime,WeekId,IsEnable,SeriaNo ");
            strSql.Append(" FROM ecb_reboot_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,GradeId,ClassId,BootTime,OffTime,WeekId,IsEnable,SeriaNo ");
            strSql.Append(" FROM ecb_reboot_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_reboot_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_reboot_config T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_reboot_config";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int ColumnId, int WeekId, Guid GradeId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_reboot_config");
            strSql.Append(" where ColumnId=@ColumnId and WeekId=@WeekId and GradeId=@GradeId");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int),
                                        new SqlParameter("@WeekId", SqlDbType.Int),
                                        new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = ColumnId;
            parameters[1].Value = WeekId;
            parameters[2].Value = GradeId;
            return DbHelperSQL.Exists(strSql.ToString(), parameters,DbHelperSQL.ConnMain);
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_reboot_config GetModel(string SeriaNo, int weekId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,GradeId,ClassId,BootTime,OffTime,WeekId,IsEnable,SeriaNo from ecb_reboot_config ");
            strSql.Append(" where SeriaNo=@SeriaNo AND  WeekId=@WeekId");
            SqlParameter[] parameters = {
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,50)  , new SqlParameter("@WeekId", SqlDbType.Int,4)         };
            parameters[0].Value = SeriaNo;
            parameters[1].Value = weekId;

            Model.ecb_reboot_config model = new Model.ecb_reboot_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string SeriaNo)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_config ");
            strSql.Append(" where SeriaNo=@SeriaNo ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,50)          };
            parameters[0].Value = SeriaNo;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据,根据班级删除
        /// </summary>
        public bool DeleteClass(Guid ClassId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_config ");
            strSql.Append(" where ClassId=@ClassId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ClassId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(string fileName, string tabName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  " + fileName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}