﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// JC_SchoolNotes:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class JC_SchoolNotes
	{
		public JC_SchoolNotes()
		{}
		#region Model
		private Guid _id;
		private int? _schoolcolumnid;
		private string _schoolcolumnpath;
		private string _notetitle;
		private string _notecontent;
		private DateTime? _notedate;
		private Guid _userid;
		private int? _notetype;
		private bool _isremind;
		private bool _isenabled;
		private int? _sendtype;
		private string _schoolyear;
		private Guid _termid;
		private string _attachment;
		private string _sendarea;
		private string _creator;
		private string _ids;
        private int? _ispass;
        private Guid _ispassby;
        private DateTime? _ispassdate;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校ID
		/// </summary>
		public int? SchoolColumnID
		{
			set{ _schoolcolumnid=value;}
			get{return _schoolcolumnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string SchoolColumnPath
		{
			set{ _schoolcolumnpath=value;}
			get{return _schoolcolumnpath;}
		}
		/// <summary>
		/// 标题
		/// </summary>
		public string NoteTitle
		{
			set{ _notetitle=value;}
			get{return _notetitle;}
		}
		/// <summary>
		/// 内容
		/// </summary>
		public string NoteContent
		{
			set{ _notecontent=value;}
			get{return _notecontent;}
		}
		/// <summary>
		/// 发布时间
		/// </summary>
		public DateTime? NoteDate
		{
			set{ _notedate=value;}
			get{return _notedate;}
		}
		/// <summary>
		/// 发布人
		/// </summary>
		public Guid UserID
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 消息类型 
		/// </summary>
		public int? NoteType
		{
			set{ _notetype=value;}
			get{return _notetype;}
		}
		/// <summary>
		/// 是否提醒
		/// </summary>
		public bool IsRemind
		{
			set{ _isremind=value;}
			get{return _isremind;}
		}
		/// <summary>
		/// 是否生效
		/// </summary>
		public bool IsEnabled
		{
			set{ _isenabled=value;}
			get{return _isenabled;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? SendType
		{
			set{ _sendtype=value;}
			get{return _sendtype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SchoolYear
		{
			set{ _schoolyear=value;}
			get{return _schoolyear;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid TermID
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Attachment
		{
			set{ _attachment=value;}
			get{return _attachment;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SendArea
		{
			set{ _sendarea=value;}
			get{return _sendarea;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ids
		{
			set{ _ids=value;}
			get{return _ids;}
		}
        /// <summary>
		/// 是否通过审核
		/// </summary>
		public int? IsPass
        {
            set { _ispass = value; }
            get { return _ispass; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid IsPassBy
        {
            set { _ispassby = value; }
            get { return _ispassby; }
        }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? IsPassDate
        {
            set { _ispassdate = value; }
            get { return _ispassdate; }
        }
        #endregion Model

    }
}

