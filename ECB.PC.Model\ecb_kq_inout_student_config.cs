﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_kq_inout_student_config:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_kq_inout_student_config
	{
		public ecb_kq_inout_student_config()
		{}
		#region Model
		private Guid _id;
		private Guid _classid;
		private int _startweek;
		private int _endweek;
		private int _kqStage;
		private DateTime? _begintime;
		private DateTime? _endtime;
		private int _columnid;
		private string _columnpath;
        public int KQType { get; set; }

        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 班级id
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 周一
		/// </summary>
		public int StartWeek
		{
			set{ _startweek=value;}
			get{return _startweek;}
		}
		/// <summary>
		/// 周日
		/// </summary>
		public int EndWeek
		{
			set{ _endweek=value;}
			get{return _endweek;}
		}
        /// <summary>
        ///1:上午,2：下午，3：晚上
        /// </summary>
        public int KQStage
		{
			set{ _kqStage = value;}
			get{return _kqStage; }
		}
        /// <summary>
        /// 打卡开始时间
        /// </summary>
        public DateTime? BeginTime
		{
			set{ _begintime=value;}
			get{return _begintime;}
		}
        /// <summary>
        /// 打卡结束时间
        /// </summary>
        public DateTime? EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
        /// <summary>
        /// 
        /// </summary>
        public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		#endregion Model

	}
}

