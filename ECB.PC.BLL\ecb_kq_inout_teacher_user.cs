﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_kq_inout_teacher_user
	/// </summary>
	public partial class ecb_kq_inout_teacher_user
	{
		public ecb_kq_inout_teacher_user()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_kq_inout_teacher_user");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_kq_inout_teacher_user model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_kq_inout_teacher_user(");
			strSql.Append("Id,KQTeachersId,ColumnId,ColumnPath,DeptId)");
			strSql.Append(" values (");
			strSql.Append("@Id,@KQTeachersId,@ColumnId,@ColumnPath,@DeptId)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@KQTeachersId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
            new SqlParameter("@DeptId",SqlDbType.Int,4)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = Guid.NewGuid();
			parameters[2].Value = model.ColumnId;
			parameters[3].Value = model.ColumnPath;
            parameters[4].Value = model.DeptId;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_kq_inout_teacher_user model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_kq_inout_teacher_user set ");
			strSql.Append("KQTeachersId=@KQTeachersId,");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("DeptId=@DeptId");
			strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@KQTeachersId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@DeptId",SqlDbType.Int,4),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.KQTeachersId;
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.DeptId;
			parameters[4].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_kq_inout_teacher_user ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_kq_inout_teacher_user ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_kq_inout_teacher_user GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,KQTeachersId,ColumnId,ColumnPath,DeptId from ecb_kq_inout_teacher_user ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_kq_inout_teacher_user model=new ECB.PC.Model.ecb_kq_inout_teacher_user();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_kq_inout_teacher_user DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_kq_inout_teacher_user model=new ECB.PC.Model.ecb_kq_inout_teacher_user();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["KQTeachersId"]!=null && row["KQTeachersId"].ToString()!="")
				{
					model.KQTeachersId= new Guid(row["KQTeachersId"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
                if (row["DeptId"] != null && row["DeptId"].ToString() != "")
                {
                    model.DeptId = int.Parse(row["DeptId"].ToString());
                }
            }
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,KQTeachersId,ColumnId,ColumnPath,DeptId ");
			strSql.Append(" FROM ecb_kq_inout_teacher_user ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,KQTeachersId,ColumnId,ColumnPath ");
			strSql.Append(" FROM ecb_kq_inout_teacher_user ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_kq_inout_teacher_user ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_kq_inout_teacher_user T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_inout_teacher_user";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetKqUserList(int ColumnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.KQTeachersId as UserId,b.UserName,b.CName as Name,b.MobilePhone as Phone,d.DeptName,a.ColumnId ");
            strSql.Append(" from ecb_kq_inout_teacher_user a left join aspnet_Membership b on a.KQTeachersId = b.UserId left join JC_TeacherDept c on c.TeacherNo = b.TeacherNo and c.DeptId=a.DeptId left join JC_Department d on d.ID = a.DeptId ");
            strSql.Append(" where a.ColumnId =@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = ColumnId;
            return DbHelperSQL.Query(strSql.ToString(),parameters);
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool DeletebySchool(int ColumnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_teacher_user ");
            strSql.Append(" where ColumnId =@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = ColumnId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public DataSet GetUserDept(string username,string deptname)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from JC_TeacherDept ");
            strSql.Append(" where TeacherNo=@username and DeptId in (select ID from JC_Department where DeptName =@deptname) ");
            SqlParameter[] parameters = {
                    new SqlParameter("@username", SqlDbType.NVarChar,500),
                    new SqlParameter("@deptname", SqlDbType.NVarChar,100)    };
            parameters[0].Value = username;
            parameters[1].Value = deptname;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        #endregion  ExtensionMethod
    }
}

