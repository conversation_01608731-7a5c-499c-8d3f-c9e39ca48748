﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_DutyItems
    /// </summary>
    public partial class ecb_DutyItems
    {
        public ecb_DutyItems()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_DutyItems");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_DutyItems model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_DutyItems(");
            strSql.Append("id,ColumnID,ColumnPath,ClassId,ItemName,Num,OrderId)");
            strSql.Append(" values (");
            strSql.Append("@id,@ColumnID,@ColumnPath,@ClassId,@ItemName,@Num,@OrderId)");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ItemName", SqlDbType.NVarChar,255),
                    new SqlParameter("@Num", SqlDbType.Int,4),
                    new SqlParameter("@OrderId", SqlDbType.Int,4)};
            parameters[0].Value = model.id;
            parameters[1].Value = model.ColumnID;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ClassId;
            parameters[4].Value = model.ItemName;
            parameters[5].Value = model.Num;
            parameters[6].Value = model.OrderId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_DutyItems model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_DutyItems set ");
            strSql.Append("ColumnID=@ColumnID,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("ItemName=@ItemName,");
            strSql.Append("Num=@Num,");
            strSql.Append("OrderId=@OrderId");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ItemName", SqlDbType.NVarChar,255),
                    new SqlParameter("@Num", SqlDbType.Int,4),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnID;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.ClassId;
            parameters[3].Value = model.ItemName;
            parameters[4].Value = model.Num;
            parameters[5].Value = model.OrderId;
            parameters[6].Value = model.id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_DutyItems ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_DutyItems ");
            strSql.Append(" where id in (" + idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_DutyItems GetModel(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 id,ColumnID,ColumnPath,ClassId,ItemName,Num,OrderId from ecb_DutyItems ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            ECB.PC.Model.ecb_DutyItems model = new ECB.PC.Model.ecb_DutyItems();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_DutyItems DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_DutyItems model = new ECB.PC.Model.ecb_DutyItems();
            if (row != null)
            {
                if (row["id"] != null && row["id"].ToString() != "")
                {
                    model.id = new Guid(row["id"].ToString());
                }
                if (row["ColumnID"] != null && row["ColumnID"].ToString() != "")
                {
                    model.ColumnID = int.Parse(row["ColumnID"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["ItemName"] != null)
                {
                    model.ItemName = row["ItemName"].ToString();
                }
                if (row["Num"] != null && row["Num"].ToString() != "")
                {
                    model.Num = int.Parse(row["Num"].ToString());
                }
                if (row["OrderId"] != null && row["OrderId"].ToString() != "")
                {
                    model.OrderId = int.Parse(row["OrderId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select id,ColumnID,ColumnPath,ClassId,ItemName,Num,OrderId ");
            strSql.Append(" FROM ecb_DutyItems ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" id,ColumnID,ColumnPath,ClassId,ItemName,Num,OrderId ");
            strSql.Append(" FROM ecb_DutyItems ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_DutyItems ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_DutyItems T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_DutyItems";
			parameters[1].Value = "id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool DeleteByClassId(Guid ClassId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_DutyItems ");
            strSql.Append(" where ClassId=@ClassId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ClassId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

