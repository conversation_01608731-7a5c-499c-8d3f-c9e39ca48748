﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理系统字典表
	/// </summary>
	[Serializable]
	public partial class psych_dict
	{
		public psych_dict()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private int _dicttypeid;
		private string _dicttext;
		private string _dictvalue;
		private string _memo;
		private int _iswarning;
		private string _warninglevel;
		private int _isneedinput;
		private int _sortid;
		private Guid _lasteditby;
		private DateTime _lastedittime;
		/// <summary>
		/// 主键
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 字典类型
		/// </summary>
		public int DictTypeId
		{
			set{ _dicttypeid=value;}
			get{return _dicttypeid;}
		}
		/// <summary>
		/// 字典文本
		/// </summary>
		public string DictText
		{
			set{ _dicttext=value;}
			get{return _dicttext;}
		}
		/// <summary>
		/// 字典值
		/// </summary>
		public string DictValue
		{
			set{ _dictvalue=value;}
			get{return _dictvalue;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		/// <summary>
		/// 是否预警
		/// </summary>
		public int IsWarning
		{
			set{ _iswarning=value;}
			get{return _iswarning;}
		}
		/// <summary>
		/// 预警级别
		/// </summary>
		public string WarningLevel
		{
			set{ _warninglevel=value;}
			get{return _warninglevel;}
		}
		/// <summary>
		/// 是否需要手填
		/// </summary>
		public int IsNeedInput
		{
			set{ _isneedinput=value;}
			get{return _isneedinput;}
		}
		/// <summary>
		/// 排序
		/// </summary>
		public int SortId
		{
			set{ _sortid=value;}
			get{return _sortid;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}