﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 年级红旗数量配置
	/// </summary>
	[Serializable]
	public partial class ecb_GradeRedFlagConfig
	{
		public ecb_GradeRedFlagConfig()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnptah;
		private Guid _gradeid;
		private int _flagcount;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPtah
		{
			set{ _columnptah=value;}
			get{return _columnptah;}
		}
		/// <summary>
		/// 年级ID
		/// </summary>
		public Guid GradeId
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 红旗数量
		/// </summary>
		public int FlagCount
		{
			set{ _flagcount=value;}
			get{return _flagcount;}
		}
		#endregion Model

	}
}

