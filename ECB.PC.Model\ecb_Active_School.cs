﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_Active_School:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_Active_School
    {
        public ecb_Active_School()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private int? _scoretype;
        private decimal? _score;
        private int? _continuedays;
        private DateTime? _recorddate;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 区域ID
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 区域Path
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 得分类型
        /// </summary>
        public int? ScoreType
        {
            set { _scoretype = value; }
            get { return _scoretype; }
        }
        /// <summary>
        /// 得分
        /// </summary>
        public decimal? Score
        {
            set { _score = value; }
            get { return _score; }
        }
        /// <summary>
        /// 连续天数 0无连续
        /// </summary>
        public int? ContinueDays
        {
            set { _continuedays = value; }
            get { return _continuedays; }
        }
        /// <summary>
        /// 记录日期
        /// </summary>
        public DateTime? RecordDate
        {
            set { _recorddate = value; }
            get { return _recorddate; }
        }
        #endregion Model

    }
}

