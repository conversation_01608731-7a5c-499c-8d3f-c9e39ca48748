﻿using System;

namespace ECB.PC.Model
{
    public class ecb_MenJ<PERSON>_Pusher
    {
        public ecb_MenJin_Pusher()
        { }

        #region Model

        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _doorid;
        private Guid _userid;

        /// <summary>
        /// 主键
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区id路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid DoorId
        {
            set { _doorid = value; }
            get { return _doorid; }
        }
        /// <summary>
        /// 推送人Id
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }

        #endregion Model
    }
}
