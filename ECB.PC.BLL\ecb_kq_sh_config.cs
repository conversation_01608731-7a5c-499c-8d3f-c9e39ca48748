﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_kq_sh_config
    /// </summary>
    public partial class ecb_kq_sh_config
    {
        public ecb_kq_sh_config()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_kq_sh_config");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_kq_sh_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_kq_sh_config(");
            strSql.Append("Id,ColumnId,ColumnPath,Week,BeginKQTime,NormalKQTime,EndKQTime,LastEditUserId,LastEditTime,KQType)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@Week,@BeginKQTime,@NormalKQTime,@EndKQTime,@LastEditUserId,@LastEditTime,@KQType)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
                    new SqlParameter("@Week", SqlDbType.Int,4),
                    new SqlParameter("@BeginKQTime", SqlDbType.Time,5),
                    new SqlParameter("@NormalKQTime", SqlDbType.Time,5),
                    new SqlParameter("@EndKQTime", SqlDbType.Time,5),
                    new SqlParameter("@LastEditUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@KQType", SqlDbType.Int,4)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.Week;
            parameters[4].Value = model.BeginKQTime;
            parameters[5].Value = model.NormalKQTime;
            parameters[6].Value = model.EndKQTime;
            parameters[7].Value = model.LastEditUserId;
            parameters[8].Value = model.LastEditTime;
            parameters[9].Value = model.KQType;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_kq_sh_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_kq_sh_config set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Week=@Week,");
            strSql.Append("BeginKQTime=@BeginKQTime,");
            strSql.Append("NormalKQTime=@NormalKQTime,");
            strSql.Append("EndKQTime=@EndKQTime,");
            strSql.Append("LastEditUserId=@LastEditUserId,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("KQType=@KQType");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
                    new SqlParameter("@Week", SqlDbType.Int,4),
                    new SqlParameter("@BeginKQTime", SqlDbType.Time,5),
                    new SqlParameter("@NormalKQTime", SqlDbType.Time,5),
                    new SqlParameter("@EndKQTime", SqlDbType.Time,5),
                    new SqlParameter("@LastEditUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@KQType", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.Week;
            parameters[3].Value = model.BeginKQTime;
            parameters[4].Value = model.NormalKQTime;
            parameters[5].Value = model.EndKQTime;
            parameters[6].Value = model.LastEditUserId;
            parameters[7].Value = model.LastEditTime;
            parameters[8].Value = model.KQType;
            parameters[9].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_sh_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_sh_config ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_sh_config GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Week,BeginKQTime,NormalKQTime,EndKQTime,LastEditUserId,LastEditTime,KQType from ecb_kq_sh_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_kq_sh_config model = new ECB.PC.Model.ecb_kq_sh_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_sh_config DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_kq_sh_config model = new ECB.PC.Model.ecb_kq_sh_config();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Week"] != null && row["Week"].ToString() != "")
                {
                    model.Week = int.Parse(row["Week"].ToString());
                }
                if (row["BeginKQTime"] != null && row["BeginKQTime"].ToString() != "")
                {
                    model.BeginKQTime = row["BeginKQTime"].ToString();
                }
                if (row["NormalKQTime"] != null && row["NormalKQTime"].ToString() != "")
                {
                    model.NormalKQTime = row["NormalKQTime"].ToString();
                }
                if (row["EndKQTime"] != null && row["EndKQTime"].ToString() != "")
                {
                    model.EndKQTime = row["EndKQTime"].ToString();
                }
                if (row["LastEditUserId"] != null && row["LastEditUserId"].ToString() != "")
                {
                    model.LastEditUserId = new Guid(row["LastEditUserId"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
                if (row["KQType"] != null && row["KQType"].ToString() != "")
                {
                    model.KQType = int.Parse(row["KQType"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Week,BeginKQTime,NormalKQTime,EndKQTime,LastEditUserId,LastEditTime,KQType ");
            strSql.Append(" FROM ecb_kq_sh_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,Week,BeginKQTime,NormalKQTime,EndKQTime,LastEditUserId,LastEditTime,KQType ");
            strSql.Append(" FROM ecb_kq_sh_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_kq_sh_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_kq_sh_config T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_sh_config";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获取楼栋
        /// </summary>
        /// <param name="columnId"></param>
        /// <returns></returns>
        public DataSet GetBuildings(int columnId)
        {
            string strSql = "select * from sh_building where ColumnId=@ColumnId order by OrderId";
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int)
            };
            parameters[0].Value = columnId;
            return DbHelperSQL.Query(strSql, parameters);
        }

        /// <summary>
        /// 获取楼层
        /// </summary>
        /// <param name="columnId"></param>
        /// <returns></returns>
        public DataSet GetFloors(Guid buildingId)
        {
            string strSql = " select * from sh_floor where BuildingId=@BuildingId order by OrderId";
            SqlParameter[] parameters = {
                    new SqlParameter("@BuildingId", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = buildingId;
            return DbHelperSQL.Query(strSql, parameters);
        }
        /// <summary>
        ///  修改全部考勤状态
        /// </summary>
        /// <param name="columnId">地区</param>
        /// <param name="day">日期</param>        
        /// <param name="kqType">要更改的考勤类型 1 出寝 2 归寝</param>
        /// <param name="preStatus">改之前的状态</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <param name="buildingId">筛选的楼栋id</param>
        /// <param name="floorId">筛选的楼层id</param>
        /// <returns></returns>
        public bool ModifyStatus(int columnId, DateTime day, int kqType, int preStatus, int toStatus, Guid buildingId, Guid floorId)
        {
            // 判断是否是修改当天的数据还是历史数据
            string tableName = "ecb_kq_sh";
            if (day.Date < DateTime.Now.Date)
            {
                tableName += "_history";
            }
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("UPDATE a SET ");
            if (toStatus == 1)
            {
                // 改为正常
                strSql.AppendLine("a.Status=@ToStatus,a.SignTime=DATEADD(mi,-1,a.KQTime),a.LateTime=0");
            }
            else if (toStatus == 2)
            {
                // 改为迟到
                strSql.AppendLine("a.Status=@ToStatus,a.SignTime=DATEADD(mi,2,a.KQTime),a.LateTime=120");
            }
            else if (toStatus == 3)
            {
                // 改为缺勤
                strSql.AppendLine("a.Status=@ToStatus,a.SignTime='" + day.ToString("yyyy-MM-dd 00:00:00") + "',a.LateTime=0");
            }
            strSql.AppendLine(" from " + tableName + " a,sh_room b where a.RoomId=b.Id");
            strSql.AppendLine(" and a.ColumnId=@ColumnId and a.RecordDate=@Date and a.KQType=@KQType and a.Status=@PreStatus");
            if (floorId != Guid.Empty)
            {
                strSql.AppendLine(" and b.FloorId='" + floorId + "'");
            }
            else if (buildingId != Guid.Empty)
            {
                strSql.AppendLine(" and b.BuildingId='" + buildingId + "'");
            }

            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@KQType",SqlDbType.Int,4),
                new SqlParameter("@PreStatus",SqlDbType.Int,4),
                new SqlParameter("@ToStatus",SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = day;
            parameters[2].Value = kqType;
            parameters[3].Value = preStatus;
            parameters[4].Value = toStatus;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }

        /// <summary>
        /// 修改学生个人宿舍考勤
        /// </summary>
        /// <param name="studentId">学生id</param>
        /// <param name="day">日期</param>        
        /// <param name="kqType">类型</param>
        /// <param name="toStatus">更改后的状态</param>
        /// <returns></returns>
        public bool ModifyStudentPersonalStatus(Guid studentId, DateTime day, int kqType, int toStatus)
        {
            // 判断是否是修改当天的数据还是历史数据
            string tableName = "ecb_kq_sh";
            if (day.Date < DateTime.Now.Date)
            {
                tableName += "_history";
            }
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("UPDATE a SET ");
            if (toStatus == 1)
            {
                // 改为正常
                strSql.AppendLine("a.Status=@Status,a.SignTime=DATEADD(mi,-1,a.KQTime),a.LateTime=0");
            }
            else if (toStatus == 2)
            {
                // 改为迟到
                strSql.AppendLine("a.Status=@Status,a.SignTime=DATEADD(mi,2,a.KQTime),a.LateTime=120");
            }
            else if (toStatus == 3)
            {
                // 改为缺勤
                strSql.AppendLine("a.Status=@Status,a.SignTime='" + day.ToString("yyyy-MM-dd 00:00:00") + "',a.LateTime=0");
            }
            strSql.AppendLine(" from " + tableName + " a");
            strSql.AppendLine(" where a.UserId=@UserId and a.RecordDate=@Date and a.KQType=@KQType");

            SqlParameter[] parameters = {
                new SqlParameter("@UserId",SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@KQType",SqlDbType.Int),
                new SqlParameter("@Status",SqlDbType.Int),
            };
            parameters[0].Value = studentId;
            parameters[1].Value = day;
            parameters[2].Value = kqType;
            parameters[3].Value = toStatus;
            int roweffect = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return roweffect > 0;
        }
        /// <summary>
        /// 获取宿舍考勤统计
        /// </summary>
        /// <param name="columnId">学校id</param>
        /// <param name="buildingId">楼栋id</param>
        /// <param name="floorId">楼层id</param>
        /// <returns></returns>
        public DataSet GetMonthStat(int columnId, DateTime beginDate, DateTime endDate, Guid buildingId, Guid floorId)
        {
            StringBuilder sql = new StringBuilder();
            string tableName = "(select * from ecb_kq_sh union all select * from ecb_kq_sh_history)";
            // 拼接sql
            sql.Append("select d.Name as buildingName,c.Name as floorName,b.FloorId,COUNT(1) total,");
            sql.Append("SUM(CASE WHEN [Status] = 1 THEN 1 ELSE 0 END) normal,");
            sql.Append("SUM(CASE WHEN [Status] = 4 THEN 1 ELSE 0 END) leave,");
            sql.Append("SUM(CASE WHEN a.Status = 2 and a.KQType=1 THEN 1 ELSE 0 END) wanchu,");
            sql.Append("SUM(CASE WHEN a.Status = 2 and a.KQType=2 THEN 1 ELSE 0 END) wangui,");
            sql.Append("SUM(CASE WHEN a.Status = 3 and a.KQType=1 THEN 1 ELSE 0 END) weichu,");
            sql.Append("SUM(CASE WHEN a.Status = 3 and a.KQType=2 THEN 1 ELSE 0 END) weigui");
            sql.AppendFormat(" from {0} a left join sh_room b on a.RoomId=b.Id left join sh_floor c on b.FloorId=c.Id  left join sh_building d on b.BuildingId=d.Id where RecordDate>=@BeginDate and RecordDate<=@EndDate and a.ColumnId=@ColumnId and [Status]>0 and KQType>0", tableName);
            // 判断是否有楼层筛选项
            if (floorId != Guid.Empty)
            {
                sql.AppendFormat(" and b.FloorId='{0}'", floorId);
            }
            else if (buildingId != Guid.Empty)
            {
                sql.AppendFormat(" and b.BuildingId='{0}'", buildingId);
            }
            sql.Append(" group by d.name,b.BuildingId,d.OrderId,b.FloorId,c.Name,c.OrderId order by d.OrderId,c.OrderId");
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@BeginDate",SqlDbType.Date),
                new SqlParameter("@EndDate",SqlDbType.Date)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = beginDate;
            parameters[2].Value = endDate;
            return DbHelperSQL.Query(sql.ToString(), parameters);
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaShAttendInfo(string status, DateTime beginDate, DateTime endDate, Guid floorId,string kqtype)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select StudentName,COUNT(1) total");
            strSql.Append(" FROM (select * from ecb_kq_sh union all select * from ecb_kq_sh_history) a left join sh_room b on a.RoomId=b.Id left join JC_StudentInfos c on a.UserId= c.ID  where RecordDate>=@BeginDate and RecordDate<=@EndDate  and [Status]=@status ");
            if (kqtype!="")
            {
                strSql.AppendFormat(" and KQType='{0}' ",kqtype);
            }
            else
            {
                     strSql.Append(" and KQType> 0 ");
            }
            // 判断是否有楼层筛选项
            if (floorId != Guid.Empty)
            {
                strSql.AppendFormat(" and b.FloorId='{0}'", floorId);
            }

            strSql.Append(" group by StudentName,a.UserId order by total desc,StudentName");
            SqlParameter[] parameters = {
                new SqlParameter("@status",SqlDbType.NVarChar,4),
                new SqlParameter("@BeginDate",SqlDbType.Date),
                new SqlParameter("@EndDate",SqlDbType.Date)
            };
            parameters[0].Value = status;
            parameters[1].Value = beginDate;
            parameters[2].Value = endDate;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取宿舍考勤统计
        /// </summary>
        /// <param name="columnId">学校id</param>
        /// <param name="buildingId">楼栋id</param>
        /// <param name="floorId">楼层id</param>
        /// <returns></returns>
        public DataSet ExportShAttend(int columnId, DateTime beginDate, DateTime endDate, Guid buildingId, Guid floorId)
        {
            StringBuilder sql = new StringBuilder();
            string tableName = "(select * from ecb_kq_sh union all select * from ecb_kq_sh_history)";
            // 拼接sql
            sql.Append("select b.BuildingId,d.Name as '楼栋',c.Name as '楼层',COUNT(1) '总人数',");
            sql.Append("SUM(CASE WHEN [Status] = 1 THEN 1 ELSE 0 END) '正常',");
            sql.Append("SUM(CASE WHEN [Status] = 4 THEN 1 ELSE 0 END) '请假',");
            sql.Append("SUM(CASE WHEN a.Status = 2 and a.KQType=1 THEN 1 ELSE 0 END) '晚出',");
            sql.Append("SUM(CASE WHEN a.Status = 2 and a.KQType=2 THEN 1 ELSE 0 END) '晚归',");
            sql.Append("SUM(CASE WHEN a.Status = 3 and a.KQType=1 THEN 1 ELSE 0 END) '未出',");
            sql.Append("SUM(CASE WHEN a.Status = 3 and a.KQType=2 THEN 1 ELSE 0 END) '未归' ");
            sql.AppendFormat(" from {0} a left join sh_room b on a.RoomId=b.Id left join sh_floor c on b.FloorId=c.Id  left join sh_building d on b.BuildingId=d.Id where RecordDate>=@BeginDate and RecordDate<=@EndDate and a.ColumnId=@ColumnId and [Status]>0 and KQType>0", tableName);
            // 判断是否有楼层筛选项
            if (floorId != Guid.Empty)
            {
                sql.AppendFormat(" and b.FloorId='{0}'", floorId);
            }
            else if (buildingId != Guid.Empty)
            {
                sql.AppendFormat(" and b.BuildingId='{0}'", buildingId);
            }
            sql.Append(" group by d.name,b.BuildingId,d.OrderId,b.FloorId,c.Name,c.OrderId order by d.OrderId,c.OrderId");
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@BeginDate",SqlDbType.Date),
                new SqlParameter("@EndDate",SqlDbType.Date)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = beginDate;
            parameters[2].Value = endDate;
            return DbHelperSQL.Query(sql.ToString(), parameters);
        }
        /// <summary>
        /// 获取宿舍考勤异常名单
        /// </summary>
        /// <param name="columnId">学校id</param>
        /// <param name="buildingId">楼栋id</param>
        /// <returns></returns>
        public DataSet ExecelDetailInfo(int columnId, DateTime beginDate, DateTime endDate, Guid buildingId)
        {
            StringBuilder sql = new StringBuilder();
            string tableName = "(select * from ecb_kq_sh union all select * from ecb_kq_sh_history)";
            // 拼接sql
            sql.Append("select StudentName '姓名',CONVERT(varchar(100),RecordDate,23) '日期',c.Name '楼层',b.Name '寝室号',case when a.KQType=1 then '出寝' when a.KQType=2 then '归寝' end '考勤类型',case when Status = 1 then '正常' when Status = 2   then '迟到'  when Status = 3 then '缺卡' when Status = 4 then '请假' else '' end '考勤状态'");
          
            sql.AppendFormat(" from {0} a left join sh_room b on a.RoomId=b.Id left join sh_floor c on b.FloorId=c.Id left join JC_StudentInfos d on a.UserId= d.ID  where RecordDate>=@BeginDate and RecordDate<=@EndDate and a.ColumnId=@ColumnId and KQType>0 and Status<>1", tableName);
            // 判断是否有楼层筛选项
            if (buildingId != Guid.Empty)
            {
                sql.AppendFormat(" and b.BuildingId='{0}'", buildingId);
            }
            sql.Append(" order by RecordDate,KQType,c.OrderId,b.OrderId, StudentName");
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@BeginDate",SqlDbType.Date),
                new SqlParameter("@EndDate",SqlDbType.Date)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = beginDate;
            parameters[2].Value = endDate;
            return DbHelperSQL.Query(sql.ToString(), parameters);
        }
        #endregion  ExtensionMethod
    }
}

