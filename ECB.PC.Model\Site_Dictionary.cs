﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 字典表
	/// </summary>
	[Serializable]
	public partial class Site_Dictionary
	{
		public Site_Dictionary()
		{}
		#region Model
		private int _dictionaryid;
		private int _dicttypeid;
		private string _dicttext;
		private string _dictvalue;
		private int? _dicttextorder;
		private int? _dictparentvalue;
		private int? _dictdepth;
		private int? _columnid;
		private string _columnpath;
		/// <summary>
		/// 
		/// </summary>
		public int DictionaryId
		{
			set{ _dictionaryid=value;}
			get{return _dictionaryid;}
		}
		/// <summary>
		/// 字典类型编号
		/// </summary>
		public int DictTypeId
		{
			set{ _dicttypeid=value;}
			get{return _dicttypeid;}
		}
		/// <summary>
		/// 显示文本
		/// </summary>
		public string DictText
		{
			set{ _dicttext=value;}
			get{return _dicttext;}
		}
		/// <summary>
		/// 所选值
		/// </summary>
		public string DictValue
		{
			set{ _dictvalue=value;}
			get{return _dictvalue;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? DictTextOrder
		{
			set{ _dicttextorder=value;}
			get{return _dicttextorder;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? DictParentValue
		{
			set{ _dictparentvalue=value;}
			get{return _dictparentvalue;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? DictDepth
		{
			set{ _dictdepth=value;}
			get{return _dictdepth;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		#endregion Model

	}
}

