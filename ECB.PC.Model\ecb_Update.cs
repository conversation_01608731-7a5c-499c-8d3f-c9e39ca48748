﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_Update:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_Update
    {
        public ecb_Update()
        { }
        #region Model
        private int _versioncode;
        private string _updatecontent;
        private string _downloadurl;
        /// <summary>
        /// 
        /// </summary>
        public int VersionCode
        {
            set { _versioncode = value; }
            get { return _versioncode; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string UpdateContent
        {
            set { _updatecontent = value; }
            get { return _updatecontent; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string DownLoadUrl
        {
            set { _downloadurl = value; }
            get { return _downloadurl; }
        }
        #endregion Model

    }
}

