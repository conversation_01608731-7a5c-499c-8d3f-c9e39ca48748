﻿
using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 职能部门表
    /// </summary>
    [Serializable]
    public partial class JC_Department
    {
        public JC_Department()
        { }
        #region Model
        private int _id;
        private string _deptname;
        private int? _parentid;
        private int? _columnid;
        private string _columnpath;
        private string _deptphone;
        private string _deptdesc;
        private int? _childsnum;
        private int? _orderid;
        private int? _deptdepth;
        private string _deptpath;
        private Guid _placeid;

        /// <summary>
        /// 部门路径
        /// </summary>
        public string DeptPath
        {
            get { return _deptpath; }
            set { _deptpath = value; }
        }
        /// <summary>
        /// 编号
        /// </summary>
        public int ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DeptName
        {
            set { _deptname = value; }
            get { return _deptname; }
        }
        /// <summary>
        /// 上级部门
        /// </summary>
        public int? ParentId
        {
            set { _parentid = value; }
            get { return _parentid; }
        }
        /// <summary>
        /// 学校ID
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 学校路径
        /// </summary>
        public string Columnpath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 部门电话
        /// </summary>
        public string DeptPhone
        {
            set { _deptphone = value; }
            get { return _deptphone; }
        }
        /// <summary>
        /// 部门职能
        /// </summary>
        public string DeptDesc
        {
            set { _deptdesc = value; }
            get { return _deptdesc; }
        }
        /// <summary>
        /// 子部门数量
        /// </summary>
        public int? ChildsNum
        {
            set { _childsnum = value; }
            get { return _childsnum; }
        }
        /// <summary>
        /// 排序
        /// </summary>
        public int? OrderId
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        /// <summary>
        /// 深度
        /// </summary>
        public int? DeptDepth
        {
            set { _deptdepth = value; }
            get { return _deptdepth; }
        }
        /// <summary>
        /// 地点Id
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        #endregion Model

    }
}

