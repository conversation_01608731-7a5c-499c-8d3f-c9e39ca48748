﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_classSubjectRepresent:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_classSubjectRepresent
	{
		public ecb_classSubjectRepresent()
		{}
		#region Model
		private Guid _id;
		private string _subjectcode;
		private Guid _classid;
		private Guid _studentid;
		private DateTime? _creatdate;
		private Guid _creatuserid;
		private int? _columnid;
		private string _columnpath;
		/// <summary>
		/// Id
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 科目编号
		/// </summary>
		public string SubjectCode
		{
			set{ _subjectcode=value;}
			get{return _subjectcode;}
		}
		/// <summary>
		/// 班级Id
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学生id
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 创建日期
		/// </summary>
		public DateTime? CreatDate
		{
			set{ _creatdate=value;}
			get{return _creatdate;}
		}
		/// <summary>
		/// 创建者
		/// </summary>
		public Guid CreatUserId
		{
			set{ _creatuserid=value;}
			get{return _creatuserid;}
		}
		/// <summary>
		/// 区域id
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 区域路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		#endregion Model

	}
}

