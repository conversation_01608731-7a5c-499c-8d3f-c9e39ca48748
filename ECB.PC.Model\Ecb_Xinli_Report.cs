﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// Ecb_Xinli_Report:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class Ecb_Xinli_Report
	{
		public Ecb_Xinli_Report()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _userid;
		private Guid _classid;
		private DateTime? _reporttime;
		private int? _reporttype;
		private Guid _reportuserid;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? ReportTime
		{
			set{ _reporttime=value;}
			get{return _reporttime;}
		}
		/// <summary>
		/// 0:全部 1:县级 2:家长 3:班主任
		/// </summary>
		public int? ReportType
		{
			set{ _reporttype=value;}
			get{return _reporttype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid ReportUserId
		{
			set{ _reportuserid=value;}
			get{return _reportuserid;}
		}
		#endregion Model

	}
}

