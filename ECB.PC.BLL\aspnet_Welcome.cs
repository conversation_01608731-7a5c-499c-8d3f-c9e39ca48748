﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:aspnet_Welcome
    /// </summary>
    public partial class aspnet_Welcome
    {
        public aspnet_Welcome()
        { }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return DbHelperSQL.GetMaxID("ModuleType", "aspnet_Welcome");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string ModuleId, int ModuleType, int ColumnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from aspnet_Welcome");
            strSql.Append(" where ModuleId=@ModuleId and ModuleType=@ModuleType and ColumnId=@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleType", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = ModuleId;
            parameters[1].Value = ModuleType;
            parameters[2].Value = ColumnId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }




        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string ModuleId, int ModuleType, int ColumnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from aspnet_Welcome ");
            strSql.Append(" where ModuleId=@ModuleId and ModuleType=@ModuleType and ColumnId=@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleType", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = ModuleId;
            parameters[1].Value = ModuleType;
            parameters[2].Value = ColumnId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.aspnet_Welcome GetModel(string ModuleId, int ModuleType, int ColumnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 AppId,ModuleId,ModuleName,ModuleUrl,ParentModuleId,ModulePath,ModuleDepth,IsButton,ButtonID,SortId,ModuleImg,LastEditDate,LastEditBy,ModuleType,ModuleColor,ModuleDescripion,IsShow,HelpUrl,HelpImg,HelpContent,ColumnId,ColumnPath,IsMessage,MessageType,oldModuleId from aspnet_Welcome ");
            strSql.Append(" where ModuleId=@ModuleId and ModuleType=@ModuleType and ColumnId=@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleType", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = ModuleId;
            parameters[1].Value = ModuleType;
            parameters[2].Value = ColumnId;

            ECB.PC.Model.aspnet_Welcome model = new ECB.PC.Model.aspnet_Welcome();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.aspnet_Welcome DataRowToModel(DataRow row)
        {
            ECB.PC.Model.aspnet_Welcome model = new ECB.PC.Model.aspnet_Welcome();
            if (row != null)
            {
                if (row["AppId"] != null && row["AppId"].ToString() != "")
                {
                    model.AppId = new Guid(row["AppId"].ToString());
                }
                if (row["ModuleId"] != null)
                {
                    model.ModuleId = row["ModuleId"].ToString();
                }
                if (row["ModuleName"] != null)
                {
                    model.ModuleName = row["ModuleName"].ToString();
                }
                if (row["ModuleUrl"] != null)
                {
                    model.ModuleUrl = row["ModuleUrl"].ToString();
                }
                if (row["ParentModuleId"] != null)
                {
                    model.ParentModuleId = row["ParentModuleId"].ToString();
                }
                if (row["ModulePath"] != null)
                {
                    model.ModulePath = row["ModulePath"].ToString();
                }
                if (row["ModuleDepth"] != null && row["ModuleDepth"].ToString() != "")
                {
                    model.ModuleDepth = int.Parse(row["ModuleDepth"].ToString());
                }
                if (row["IsButton"] != null && row["IsButton"].ToString() != "")
                {
                    if ((row["IsButton"].ToString() == "1") || (row["IsButton"].ToString().ToLower() == "true"))
                    {
                        model.IsButton = true;
                    }
                    else
                    {
                        model.IsButton = false;
                    }
                }
                if (row["ButtonID"] != null)
                {
                    model.ButtonID = row["ButtonID"].ToString();
                }
                if (row["SortId"] != null && row["SortId"].ToString() != "")
                {
                    model.SortId = int.Parse(row["SortId"].ToString());
                }
                if (row["ModuleImg"] != null)
                {
                    model.ModuleImg = row["ModuleImg"].ToString();
                }
                if (row["LastEditDate"] != null && row["LastEditDate"].ToString() != "")
                {
                    model.LastEditDate = DateTime.Parse(row["LastEditDate"].ToString());
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["ModuleType"] != null && row["ModuleType"].ToString() != "")
                {
                    model.ModuleType = int.Parse(row["ModuleType"].ToString());
                }
                if (row["ModuleColor"] != null)
                {
                    model.ModuleColor = row["ModuleColor"].ToString();
                }
                if (row["ModuleDescripion"] != null)
                {
                    model.ModuleDescripion = row["ModuleDescripion"].ToString();
                }
                if (row["IsShow"] != null && row["IsShow"].ToString() != "")
                {
                    if ((row["IsShow"].ToString() == "1") || (row["IsShow"].ToString().ToLower() == "true"))
                    {
                        model.IsShow = true;
                    }
                    else
                    {
                        model.IsShow = false;
                    }
                }
                if (row["HelpUrl"] != null)
                {
                    model.HelpUrl = row["HelpUrl"].ToString();
                }
                if (row["HelpImg"] != null)
                {
                    model.HelpImg = row["HelpImg"].ToString();
                }
                if (row["HelpContent"] != null)
                {
                    model.HelpContent = row["HelpContent"].ToString();
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["IsMessage"] != null && row["IsMessage"].ToString() != "")
                {
                    if ((row["IsMessage"].ToString() == "1") || (row["IsMessage"].ToString().ToLower() == "true"))
                    {
                        model.IsMessage = true;
                    }
                    else
                    {
                        model.IsMessage = false;
                    }
                }
                if (row["MessageType"] != null)
                {
                    model.MessageType = row["MessageType"].ToString();
                }
                if (row["oldModuleId"] != null)
                {
                    model.oldModuleId = row["oldModuleId"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select AppId,ModuleId,ModuleName,ModuleUrl,ParentModuleId,ModulePath,ModuleDepth,IsButton,ButtonID,SortId,ModuleImg,LastEditDate,LastEditBy,ModuleType,ModuleColor,ModuleDescripion,IsShow,HelpUrl,HelpImg,HelpContent,ColumnId,ColumnPath,IsMessage,MessageType,oldModuleId ");
            strSql.Append(" FROM aspnet_Welcome ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" AppId,ModuleId,ModuleName,ModuleUrl,ParentModuleId,ModulePath,ModuleDepth,IsButton,ButtonID,SortId,ModuleImg,LastEditDate,LastEditBy,ModuleType,ModuleColor,ModuleDescripion,IsShow,HelpUrl,HelpImg,HelpContent,ColumnId,ColumnPath,IsMessage,MessageType,oldModuleId ");
            strSql.Append(" FROM aspnet_Welcome ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM aspnet_Welcome ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ColumnId desc");
            }
            strSql.Append(")AS Row, T.*  from aspnet_Welcome T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "aspnet_Welcome";
			parameters[1].Value = "ColumnId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        //上移
        public object MoveUp(string ModuleId, int ColumnId, int ModuleType)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@ModuleId", SqlDbType.VarChar, 50),
                    new SqlParameter("@ColumnId", SqlDbType.Int, 4),
                    new SqlParameter("@ModuleType", SqlDbType.Int, 4)
                    };
            return DbHelperSQL.RunProcedure("UP_aspnet_Welcome_moveup", parameters);
        }
        //下移
        public object MoveDown(string ModuleId, int ColumnId, int ModuleType)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@ModuleId", SqlDbType.VarChar, 50),
                    new SqlParameter("@ColumnId", SqlDbType.Int, 4),
                    new SqlParameter("@ModuleType", SqlDbType.Int, 4)
                    };
            return DbHelperSQL.RunProcedure("UP_aspnet_Welcome_movedown", parameters);
        }
        //删除
        public object Del(string ModuleId, int ColumnId, int ModuleType)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@ModuleId", SqlDbType.VarChar, 50),
                    new SqlParameter("@ColumnId", SqlDbType.Int, 4),
                    new SqlParameter("@ModuleType", SqlDbType.Int, 4)
                    };
            return DbHelperSQL.RunProcedure("UP_aspnet_Welcome_Delete", parameters);
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.aspnet_Welcome model)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@AppId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleName", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleUrl", SqlDbType.NVarChar,255),
                    new SqlParameter("@ParentModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModulePath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ModuleDepth", SqlDbType.Int,4),
                    new SqlParameter("@IsButton", SqlDbType.Bit,1),
                    new SqlParameter("@ButtonID", SqlDbType.NVarChar,50),
                    new SqlParameter("@SortId", SqlDbType.Int,4),
                    new SqlParameter("@ModuleImg", SqlDbType.NVarChar,100),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ModuleType", SqlDbType.Int,4),
                    new SqlParameter("@ModuleColor", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleDescripion", SqlDbType.NVarChar,255),
                    new SqlParameter("@IsShow", SqlDbType.Bit,1),
                    new SqlParameter("@HelpUrl", SqlDbType.NVarChar,500),
                    new SqlParameter("@HelpImg", SqlDbType.NVarChar,100),
                    new SqlParameter("@HelpContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@IsMessage", SqlDbType.Bit,1),
                    new SqlParameter("@MessageType", SqlDbType.NVarChar,50),
                    new SqlParameter("@oldModuleId", SqlDbType.NVarChar,50)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ModuleId;
            parameters[2].Value = model.ModuleName;
            parameters[3].Value = model.ModuleUrl;
            parameters[4].Value = model.ParentModuleId;
            parameters[5].Value = model.ModulePath;
            parameters[6].Value = model.ModuleDepth;
            parameters[7].Value = model.IsButton;
            parameters[8].Value = model.ButtonID;
            parameters[9].Value = model.SortId;
            parameters[10].Value = model.ModuleImg;
            parameters[11].Value = model.LastEditDate;
            parameters[12].Value = model.LastEditBy;
            parameters[13].Value = model.ModuleType;
            parameters[14].Value = model.ModuleColor;
            parameters[15].Value = model.ModuleDescripion;
            parameters[16].Value = model.IsShow;
            parameters[17].Value = model.HelpUrl;
            parameters[18].Value = model.HelpImg;
            parameters[19].Value = model.HelpContent;
            parameters[20].Value = model.ColumnId;
            parameters[21].Value = model.ColumnPath;
            parameters[22].Value = model.IsMessage;
            parameters[23].Value = model.MessageType;
            parameters[24].Value = model.oldModuleId;

            int rows = DbHelperSQL.ExecuteSql("UP_aspnet_Welcome_ADD", parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.aspnet_Welcome model)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@AppId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ModuleName", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleUrl", SqlDbType.NVarChar,255),
                    new SqlParameter("@ParentModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModulePath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ModuleDepth", SqlDbType.Int,4),
                    new SqlParameter("@IsButton", SqlDbType.Bit,1),
                    new SqlParameter("@ButtonID", SqlDbType.NVarChar,50),
                    new SqlParameter("@SortId", SqlDbType.Int,4),
                    new SqlParameter("@ModuleImg", SqlDbType.NVarChar,100),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ModuleColor", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleDescripion", SqlDbType.NVarChar,255),
                    new SqlParameter("@IsShow", SqlDbType.Bit,1),
                    new SqlParameter("@HelpUrl", SqlDbType.NVarChar,500),
                    new SqlParameter("@HelpImg", SqlDbType.NVarChar,100),
                    new SqlParameter("@HelpContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@IsMessage", SqlDbType.Bit,1),
                    new SqlParameter("@MessageType", SqlDbType.NVarChar,50),
                    new SqlParameter("@oldModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleId", SqlDbType.NVarChar,50),
                    new SqlParameter("@ModuleType", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)};
            parameters[0].Value = model.AppId;
            parameters[1].Value = model.ModuleName;
            parameters[2].Value = model.ModuleUrl;
            parameters[3].Value = model.ParentModuleId;
            parameters[4].Value = model.ModulePath;
            parameters[5].Value = model.ModuleDepth;
            parameters[6].Value = model.IsButton;
            parameters[7].Value = model.ButtonID;
            parameters[8].Value = model.SortId;
            parameters[9].Value = model.ModuleImg;
            parameters[10].Value = model.LastEditDate;
            parameters[11].Value = model.LastEditBy;
            parameters[12].Value = model.ModuleColor;
            parameters[13].Value = model.ModuleDescripion;
            parameters[14].Value = model.IsShow;
            parameters[15].Value = model.HelpUrl;
            parameters[16].Value = model.HelpImg;
            parameters[17].Value = model.HelpContent;
            parameters[18].Value = model.ColumnPath;
            parameters[19].Value = model.IsMessage;
            parameters[20].Value = model.MessageType;
            parameters[21].Value = model.oldModuleId;
            parameters[22].Value = model.ModuleId;
            parameters[23].Value = model.ModuleType;
            parameters[24].Value = model.ColumnId;

            int rows = DbHelperSQL.ExecuteSql("UP_aspnet_Welcome_Update", parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

