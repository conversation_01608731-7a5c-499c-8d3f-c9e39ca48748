﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_Device
    /// </summary>
    public partial class ecb_Device
    {
        public ecb_Device()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_Device");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_Device model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_Device(");
            strSql.Append("ID,ColumnId,ColumnPath,DeviceNo,DeviceName,Place,CreateDate,CreateUserId,DeviceUrl,DeviceType,PlaceId,DeviceModel,SeriaNo)");
            strSql.Append(" values (");
            strSql.Append("@ID,@ColumnId,@ColumnPath,@DeviceNo,@DeviceName,@Place,@CreateDate,@CreateUserId,@DeviceUrl,@DeviceType,@PlaceId,@DeviceModel,@SeriaNo)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@DeviceNo", SqlDbType.NVarChar,50),
                    new SqlParameter("@DeviceName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Place", SqlDbType.NVarChar,255),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DeviceUrl", SqlDbType.NVarChar,256),
                    new SqlParameter("@DeviceType", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DeviceModel", SqlDbType.NVarChar,50),
                    new SqlParameter("@SeriaNo",SqlDbType.NVarChar,50)
            };
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.DeviceNo;
            parameters[4].Value = model.DeviceName;
            parameters[5].Value = model.Place;
            parameters[6].Value = model.CreateDate;
            parameters[7].Value = model.CreateUserId;
            parameters[8].Value = model.DeviceUrl;
            parameters[9].Value = model.DeviceType;
            parameters[10].Value = model.PlaceId;
            parameters[11].Value = model.DeviceModel;
            parameters[12].Value = model.SeriaNo;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_Device model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Device set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("DeviceNo=@DeviceNo,");
            strSql.Append("DeviceName=@DeviceName,");
            strSql.Append("Place=@Place,");
            strSql.Append("CreateDate=@CreateDate,");
            strSql.Append("CreateUserId=@CreateUserId,");
            strSql.Append("DeviceUrl=@DeviceUrl,");
            strSql.Append("DeviceType=@DeviceType,");
            strSql.Append("PlaceId=@PlaceId,");
            strSql.Append("DeviceModel=@DeviceModel,");
            strSql.Append("SeriaNo=@SeriaNo");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@DeviceNo", SqlDbType.NVarChar,50),
                    new SqlParameter("@DeviceName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Place", SqlDbType.NVarChar,255),
                    new SqlParameter("@CreateDate", SqlDbType.DateTime),
                    new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DeviceUrl", SqlDbType.NVarChar,256),
                    new SqlParameter("@DeviceType", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DeviceModel", SqlDbType.NVarChar,50),
                    new SqlParameter("@SeriaNo",SqlDbType.NVarChar,50),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.DeviceNo;
            parameters[3].Value = model.DeviceName;
            parameters[4].Value = model.Place;
            parameters[5].Value = model.CreateDate;
            parameters[6].Value = model.CreateUserId;
            parameters[7].Value = model.DeviceUrl;
            parameters[8].Value = model.DeviceType;
            parameters[9].Value = model.PlaceId;
            parameters[10].Value = model.DeviceModel;
            parameters[11].Value = model.SeriaNo;
            parameters[12].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("delete from ecb_MenJin where DeviceId=@ID");
            strSql.AppendLine("delete from ecb_Device where ID=@ID");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Device ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Device GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,ColumnId,ColumnPath,DeviceNo,DeviceName,Place,CreateDate,CreateUserId,DeviceUrl,DeviceType,PlaceId,DeviceModel,SeriaNo from ecb_Device ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.ecb_Device model = new ECB.PC.Model.ecb_Device();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Device DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_Device model = new ECB.PC.Model.ecb_Device();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["DeviceNo"] != null)
                {
                    model.DeviceNo = row["DeviceNo"].ToString();
                }
                if (row["DeviceName"] != null)
                {
                    model.DeviceName = row["DeviceName"].ToString();
                }
                if (row["Place"] != null)
                {
                    model.Place = row["Place"].ToString();
                }
                if (row["CreateDate"] != null && row["CreateDate"].ToString() != "")
                {
                    model.CreateDate = DateTime.Parse(row["CreateDate"].ToString());
                }
                if (row["CreateUserId"] != null && row["CreateUserId"].ToString() != "")
                {
                    model.CreateUserId = new Guid(row["CreateUserId"].ToString());
                }
                if (row["DeviceUrl"] != null)
                {
                    model.DeviceUrl = row["DeviceUrl"].ToString();
                }
                if (row["DeviceType"] != null && row["DeviceType"].ToString() != "")
                {
                    model.DeviceType = int.Parse(row["DeviceType"].ToString());
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
                if (row["DeviceModel"] != null)
                {
                    model.DeviceModel = row["DeviceModel"].ToString();
                }
                if (row["SeriaNo"] != null)
                {
                    model.SeriaNo = row["SeriaNo"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,ColumnId,ColumnPath,DeviceNo,DeviceName,Place,CreateDate,CreateUserId,DeviceUrl,DeviceType,PlaceId,DeviceModel,SeriaNo ");
            strSql.Append(" FROM ecb_Device ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,ColumnId,ColumnPath,DeviceNo,DeviceName,Place,CreateDate,CreateUserId,DeviceUrl,DeviceType,PlaceId,DeviceModel,SeriaNo ");
            strSql.Append(" FROM ecb_Device ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Device ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_Device T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Device";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_Device GetModel(int columnId, string deviceNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,ColumnId,ColumnPath,DeviceNo,DeviceName,Place,CreateDate,CreateUserId,DeviceUrl,DeviceType,PlaceId,DeviceModel,SeriaNo from ecb_Device ");
            strSql.Append(" where ColumnId=@ColumnId and DeviceNo=@DeviceNo ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@DeviceNo", SqlDbType.NVarChar,50),
            };
            parameters[0].Value = columnId;
            parameters[1].Value = deviceNo;

            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        #endregion  ExtensionMethod
    }
}

