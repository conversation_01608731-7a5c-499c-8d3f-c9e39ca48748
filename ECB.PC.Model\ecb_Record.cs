﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 校历信息
	/// </summary>
	[Serializable]
	public partial class ecb_Record
	{
		public ecb_Record()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _title;
		private string _remark;
		private DateTime _stadate;
		private DateTime _enddate;
		private Guid _termid;
		private Guid _creator;
		private DateTime _createtime;
		/// <summary>
		/// 事件编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnID
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 标题
		/// </summary>
		public string Title
		{
			set{ _title=value;}
			get{return _title;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Remark
		{
			set{ _remark=value;}
			get{return _remark;}
		}
		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime StaDate
		{
			set{ _stadate=value;}
			get{return _stadate;}
		}
		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime EndDate
		{
			set{ _enddate=value;}
			get{return _enddate;}
		}
		/// <summary>
		/// 学期ID
		/// </summary>
		public Guid TermId
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}

