﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_TimeTable_stu:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_TimeTable_stu
	{
		public ecb_TimeTable_stu()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _termid;
		private Guid _StudentId;
		private Guid _classid;
		private string _classtypecode;
		private string _subjectno;
		private int? _subjecttype;
		private Guid _teacherid;
		private int? _weekday;
		private int? _number;
		private DateTime? _lastedittime;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid TermID
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid StudentId
		{
			set{ _StudentId=value;}
			get{return _StudentId;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ClassTypeCode
		{
			set{ _classtypecode=value;}
			get{return _classtypecode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SubjectNo
		{
			set{ _subjectno=value;}
			get{return _subjectno;}
		}
		/// <summary>
		/// 科目类型 1选考 2学考
		/// </summary>
		public int? SubjectType
		{
			set{ _subjecttype=value;}
			get{return _subjecttype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid TeacherId
		{
			set{ _teacherid=value;}
			get{return _teacherid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Weekday
		{
			set{ _weekday=value;}
			get{return _weekday;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Number
		{
			set{ _number=value;}
			get{return _number;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

