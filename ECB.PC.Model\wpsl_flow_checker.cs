﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 审批流程审批人
    /// </summary>
    [Serializable]
    public partial class wpsl_flow_checker
    {
        public wpsl_flow_checker()
        { }
        #region Model
        private Guid _id;
        private int _columnId;
        private string _columnpath;
        private Guid _checkuserid;
        private Guid _flowid;
        private string _flowcode;
        private Guid _lasteditor;
        private DateTime? _lastedittime;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 所属地区
        /// </summary>
        public int ColumnId
        {
            set { _columnId = value; }
            get { return _columnId; }
        }
        /// <summary>
        /// 所属地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid CheckUserId
        {
            set { _checkuserid = value; }
            get { return _checkuserid; }
        }
        /// <summary>
        /// 流程组Id
        /// </summary>
        public Guid FlowId
        {
            set { _flowid = value; }
            get { return _flowid; }
        }
        /// <summary>
        /// 审核流程编号
        /// </summary>
        public string FlowCode
        {
            set { _flowcode = value; }
            get { return _flowcode; }
        }
        /// <summary>
        /// 修改人
        /// </summary>
        public Guid LastEditor
        {
            set { _lasteditor = value; }
            get { return _lasteditor; }
        }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        #endregion Model

    }
}

