﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_kq_temp_user:临时考勤人员表
	/// </summary>
	[Serializable]
	public partial class ecb_kq_temp_user
	{
		public ecb_kq_temp_user()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _atteid;
		private Guid _userid;
		private int _usertype;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 临时考勤id
		/// </summary>
		public Guid AtteId
		{
			set{ _atteid=value;}
			get{return _atteid;}
		}
		/// <summary>
		/// 考勤人员id
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 人员类型 1学生 2教师
		/// </summary>
		public int UserType
		{
			set { _usertype = value; }
			get { return _usertype; }
		}
		#endregion Model

	}
}

