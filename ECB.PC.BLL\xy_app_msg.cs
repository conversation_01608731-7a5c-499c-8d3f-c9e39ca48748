﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;
using System.Collections.Generic;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:xy_app_msg
	/// </summary>
	public partial class xy_app_msg
	{
        public xy_app_msg()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from xy_app_msg");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.xy_app_msg model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into xy_app_msg(");
            strSql.Append("id,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num,msg_title,msg_content,gt_result,gt_taskId,gt_status)");
            strSql.Append(" values (");
            strSql.Append("@id,@cateId,@catePath,@businessId,@send_time,@send_userId,@msg_type,@receive_type,@receive_num,@msg_title,@msg_content,@gt_result,@gt_taskId,@gt_status)");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@cateId", SqlDbType.NVarChar,50),
					new SqlParameter("@catePath", SqlDbType.NVarChar,50),
					new SqlParameter("@businessId", SqlDbType.NVarChar,50),
					new SqlParameter("@send_time", SqlDbType.DateTime),
					new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@msg_type", SqlDbType.Int,4),
					new SqlParameter("@receive_type", SqlDbType.Int,4),
					new SqlParameter("@receive_num", SqlDbType.Int,4),
					new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
					new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
					new SqlParameter("@gt_result", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_status", SqlDbType.NVarChar,50)};
            parameters[0].Value = model.id;
            parameters[1].Value = model.cateId;
            parameters[2].Value = model.catePath;
            parameters[3].Value = model.businessId;
            parameters[4].Value = model.send_time;
            parameters[5].Value = model.send_userId;
            parameters[6].Value = model.msg_type;
            parameters[7].Value = model.receive_type;
            parameters[8].Value = model.receive_num;
            parameters[9].Value = model.msg_title;
            parameters[10].Value = model.msg_content;
            parameters[11].Value = model.gt_result;
            parameters[12].Value = model.gt_taskId;
            parameters[13].Value = model.gt_status;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.xy_app_msg model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update xy_app_msg set ");
            strSql.Append("cateId=@cateId,");
            strSql.Append("catePath=@catePath,");
            strSql.Append("businessId=@businessId,");
            strSql.Append("send_time=@send_time,");
            strSql.Append("send_userId=@send_userId,");
            strSql.Append("msg_type=@msg_type,");
            strSql.Append("receive_type=@receive_type,");
            strSql.Append("receive_num=@receive_num,");
            strSql.Append("msg_title=@msg_title,");
            strSql.Append("msg_content=@msg_content,");
            strSql.Append("gt_result=@gt_result,");
            strSql.Append("gt_taskId=@gt_taskId,");
            strSql.Append("gt_status=@gt_status");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@cateId", SqlDbType.NVarChar,50),
					new SqlParameter("@catePath", SqlDbType.NVarChar,50),
					new SqlParameter("@businessId", SqlDbType.NVarChar,50),
					new SqlParameter("@send_time", SqlDbType.DateTime),
					new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@msg_type", SqlDbType.Int,4),
					new SqlParameter("@receive_type", SqlDbType.Int,4),
					new SqlParameter("@receive_num", SqlDbType.Int,4),
					new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
					new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
					new SqlParameter("@gt_result", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_status", SqlDbType.NVarChar,50),
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.cateId;
            parameters[1].Value = model.catePath;
            parameters[2].Value = model.businessId;
            parameters[3].Value = model.send_time;
            parameters[4].Value = model.send_userId;
            parameters[5].Value = model.msg_type;
            parameters[6].Value = model.receive_type;
            parameters[7].Value = model.receive_num;
            parameters[8].Value = model.msg_title;
            parameters[9].Value = model.msg_content;
            parameters[10].Value = model.gt_result;
            parameters[11].Value = model.gt_taskId;
            parameters[12].Value = model.gt_status;
            parameters[13].Value = model.id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from xy_app_msg ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from xy_app_msg ");
            strSql.Append(" where id in (" + idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.xy_app_msg GetModel(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 id,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num,msg_title,msg_content,gt_result,gt_taskId,gt_status from xy_app_msg ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = id;

            ECB.PC.Model.xy_app_msg model = new ECB.PC.Model.xy_app_msg();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.xy_app_msg DataRowToModel(DataRow row)
        {
            ECB.PC.Model.xy_app_msg model = new ECB.PC.Model.xy_app_msg();
            if (row != null)
            {
                if (row["id"] != null && row["id"].ToString() != "")
                {
                    model.id = new Guid(row["id"].ToString());
                }
                if (row["cateId"] != null)
                {
                    model.cateId = row["cateId"].ToString();
                }
                if (row["catePath"] != null)
                {
                    model.catePath = row["catePath"].ToString();
                }
                if (row["businessId"] != null)
                {
                    model.businessId = row["businessId"].ToString();
                }
                if (row["send_time"] != null && row["send_time"].ToString() != "")
                {
                    model.send_time = DateTime.Parse(row["send_time"].ToString());
                }
                if (row["send_userId"] != null && row["send_userId"].ToString() != "")
                {
                    model.send_userId = new Guid(row["send_userId"].ToString());
                }
                if (row["msg_type"] != null && row["msg_type"].ToString() != "")
                {
                    model.msg_type = int.Parse(row["msg_type"].ToString());
                }
                if (row["receive_type"] != null && row["receive_type"].ToString() != "")
                {
                    model.receive_type = int.Parse(row["receive_type"].ToString());
                }
                if (row["receive_num"] != null && row["receive_num"].ToString() != "")
                {
                    model.receive_num = int.Parse(row["receive_num"].ToString());
                }
                if (row["msg_title"] != null)
                {
                    model.msg_title = row["msg_title"].ToString();
                }
                if (row["msg_content"] != null)
                {
                    model.msg_content = row["msg_content"].ToString();
                }
                if (row["gt_result"] != null)
                {
                    model.gt_result = row["gt_result"].ToString();
                }
                if (row["gt_taskId"] != null)
                {
                    model.gt_taskId = row["gt_taskId"].ToString();
                }
                if (row["gt_status"] != null)
                {
                    model.gt_status = row["gt_status"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select id,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num,msg_title,msg_content,gt_result,gt_taskId,gt_status ");
            strSql.Append(" FROM xy_app_msg ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" id,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num,msg_title,msg_content,gt_result,gt_taskId,gt_status ");
            strSql.Append(" FROM xy_app_msg ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM xy_app_msg ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from xy_app_msg T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "xy_app_msg";
            parameters[1].Value = "id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 发送消息
        /// </summary>
        //public bool SendMsg(Model.xy_app_msg model_msg, List<string> checkerIds, Model.xy_Checkinfo model_apply, string url, string pcUrl)
        //{
        //    int rows = 0;
        //    Model.xy_app_msg_cate model_cate = new BLL.xy_app_msg_cate().GetModel(model_msg.cateId);
        //    using (SqlConnection conn = new SqlConnection(DbHelperSQL.Connection[0]))
        //    {
        //        conn.Open();
        //        SqlCommand cmd = new SqlCommand();
        //        cmd.Connection = conn;
        //        SqlTransaction tx = conn.BeginTransaction();
        //        cmd.Transaction = tx;
        //        try
        //        {
        //            StringBuilder strSql = new StringBuilder();
        //            strSql.Append("insert into xy_app_msg(");
        //            strSql.Append("id,msg_title,msg_content,gt_result,gt_taskId,gt_status,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num");
        //            strSql.Append(") values (");
        //            strSql.Append("@id,@msg_title,@msg_content,@gt_result,@gt_taskId,@gt_status,@cateId,@catePath,@businessId,@send_time,@send_userId,@msg_type,@receive_type,@receive_num");
        //            strSql.Append(") ");

        //            SqlParameter[] parameters = {
        //                new SqlParameter("@id", SqlDbType.UniqueIdentifier,16) ,            
        //                new SqlParameter("@msg_title", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@msg_content", SqlDbType.NVarChar,-1) ,            
        //                new SqlParameter("@gt_result", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@gt_status", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@cateId", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@catePath", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@businessId", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@send_time", SqlDbType.DateTime) ,            
        //                new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16) ,            
        //                new SqlParameter("@msg_type", SqlDbType.Int,4) ,            
        //                new SqlParameter("@receive_type", SqlDbType.Int,4) ,            
        //                new SqlParameter("@receive_num", SqlDbType.Int,4)};

        //            parameters[0].Value = model_msg.id;
        //            parameters[1].Value = model_msg.msg_title;
        //            parameters[2].Value = model_msg.msg_content;
        //            parameters[3].Value = model_msg.gt_result;
        //            parameters[4].Value = model_msg.gt_taskId;
        //            parameters[5].Value = model_msg.gt_status;
        //            parameters[6].Value = model_msg.cateId;
        //            parameters[7].Value = model_msg.catePath;
        //            parameters[8].Value = model_msg.businessId;
        //            parameters[9].Value = model_msg.send_time;
        //            parameters[10].Value = model_msg.send_userId;
        //            parameters[11].Value = model_msg.msg_type;
        //            parameters[12].Value = model_msg.receive_type;
        //            parameters[13].Value = model_msg.receive_num;
        //            PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
        //            rows = cmd.ExecuteNonQuery();
        //            cmd.Parameters.Clear();
        //            if (rows > 0)
        //            {
        //                if (checkerIds.Count > 0)
        //                {
        //                    foreach (string checkerId in checkerIds)
        //                    {

        //                        xy_auth.Model.auth_UserInfos model_user = new xy_auth.BLL.auth_UserInfos().GetModel(new Guid(checkerId));
        //                        if (model_cate != null && model_user != null)
        //                        {
        //                            string title1 = model_user.Cname + "的" + model_cate.name;
        //                            string status = "";
        //                            string formdata = "{\"content\":\"";
        //                            string msg_content = "";
        //                            status = new Common.CommonEnum().GetApplyStatus_Description(model_apply.Status);
        //                            JObject jaformdata = (JObject)JsonConvert.DeserializeObject(model_apply.FormData);
        //                            if (jaformdata.Count > 0)
        //                            {
        //                                for (int k = 0; k < (jaformdata.Count > 3 ? 3 : jaformdata.Count); k++)
        //                                {
        //                                    formdata += "" + jaformdata["" + k + ""]["label"].ToString() + ":" + jaformdata["" + k + ""]["text"].ToString() + "</br>";
        //                                }
        //                            }
        //                            formdata += "\",\"status\":\"" + status + "\"}";
        //                            msg_content = formdata;

        //                            strSql.Clear();
        //                            strSql.Append("insert into xy_app_msg_receive(");
        //                            strSql.Append("id,msgId,cateId,catePath,userId,isRead,msg_title,msg_content,send_time");
        //                            strSql.Append(") values (");
        //                            strSql.Append("@id,@msgId,@cateId,@catePath,@userId,@isRead,@msg_title,@msg_content,@send_time");
        //                            strSql.Append(") ");
        //                            SqlParameter[] parameters1 = {
        //                new SqlParameter("@id", SqlDbType.UniqueIdentifier,16) ,            
        //                new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16) ,            
        //                new SqlParameter("@cateId", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@catePath", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16) ,            
        //                new SqlParameter("@isRead", SqlDbType.Int,4) ,            
        //                new SqlParameter("@msg_title", SqlDbType.NVarChar,50) ,            
        //                new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
        //                new SqlParameter("@send_time", SqlDbType.DateTime)        
        //    };
        //                            parameters1[0].Value = Guid.NewGuid();
        //                            parameters1[1].Value = model_msg.id;
        //                            parameters1[2].Value = model_msg.cateId;
        //                            parameters1[3].Value = model_msg.catePath;
        //                            parameters1[4].Value = new Guid(checkerId);
        //                            parameters1[5].Value = 0;
        //                            parameters1[6].Value = model_user.Cname + "的" + model_cate.name;
        //                            parameters1[7].Value = msg_content;
        //                            parameters1[8].Value = model_msg.send_time;
        //                            PrepareCommand(cmd, conn, null, strSql.ToString(), parameters1);
        //                            rows = cmd.ExecuteNonQuery();
        //                            cmd.Parameters.Clear();
        //                        }

        //                    }
        //                }
        //            }
        //            tx.Commit();
        //        }
        //        catch (System.Data.SqlClient.SqlException E)
        //        {
        //            rows = 0;
        //            tx.Rollback();
        //            throw new Exception(E.Message);
        //        }
        //        finally
        //        {
        //            cmd.Dispose();
        //            conn.Close();
        //        }
        //    }
        //    if (rows > 0)
        //    {
        //        return true;
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}
        /// <summary>
        /// 发送消息，不经过审批流程
        /// </summary>
        public bool SendMsgNoAduit(Model.xy_app_msg model_msg, List<string> checkerIds, string url, string pcUrl)
        {
            int rows = 0;
            //Model.xy_app_msg_cate model_cate = new BLL.xy_app_msg_cate().GetModel(model_msg.cateId);
            //using (SqlConnection conn = new SqlConnection(DbHelperSQL.Connection[0]))
            //{
            //    conn.Open();
            //    SqlCommand cmd = new SqlCommand();
            //    cmd.Connection = conn;
            //    SqlTransaction tx = conn.BeginTransaction();
            //    cmd.Transaction = tx;
            //    try
            //    {
            //        StringBuilder strSql = new StringBuilder();
            //        strSql.Append("insert into xy_app_msg(");
            //        strSql.Append("id,msg_title,msg_content,gt_result,gt_taskId,gt_status,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num");
            //        strSql.Append(") values (");
            //        strSql.Append("@id,@msg_title,@msg_content,@gt_result,@gt_taskId,@gt_status,@cateId,@catePath,@businessId,@send_time,@send_userId,@msg_type,@receive_type,@receive_num");
            //        strSql.Append(") ");

            //        SqlParameter[] parameters = {
            //            new SqlParameter("@id", SqlDbType.UniqueIdentifier,16) ,            
            //            new SqlParameter("@msg_title", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@msg_content", SqlDbType.NVarChar,-1) ,            
            //            new SqlParameter("@gt_result", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@gt_status", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@cateId", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@catePath", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@businessId", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@send_time", SqlDbType.DateTime) ,            
            //            new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16) ,            
            //            new SqlParameter("@msg_type", SqlDbType.Int,4) ,            
            //            new SqlParameter("@receive_type", SqlDbType.Int,4) ,            
            //            new SqlParameter("@receive_num", SqlDbType.Int,4)};

            //        parameters[0].Value = model_msg.id;
            //        parameters[1].Value = model_msg.msg_title;
            //        parameters[2].Value = model_msg.msg_content;
            //        parameters[3].Value = model_msg.gt_result;
            //        parameters[4].Value = model_msg.gt_taskId;
            //        parameters[5].Value = model_msg.gt_status;
            //        parameters[6].Value = model_msg.cateId;
            //        parameters[7].Value = model_msg.catePath;
            //        parameters[8].Value = model_msg.businessId;
            //        parameters[9].Value = model_msg.send_time;
            //        parameters[10].Value = model_msg.send_userId;
            //        parameters[11].Value = model_msg.msg_type;
            //        parameters[12].Value = model_msg.receive_type;
            //        parameters[13].Value = model_msg.receive_num;
            //        PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
            //        rows = cmd.ExecuteNonQuery();
            //        cmd.Parameters.Clear();
            //        if (rows > 0)
            //        {
            //            if (checkerIds.Count > 0)
            //            {
            //                foreach (string checkerId in checkerIds)
            //                {
            //                    xy_auth.Model.auth_UserInfos model_user = new xy_auth.BLL.auth_UserInfos().GetModel(new Guid(checkerId));
            //                    if (model_cate != null && model_user != null)
            //                    {
            //                        string formdata = "";
            //                        string msg_content = "";
            //                        if (model_cate.template != "")
            //                        {
            //                            formdata = model_cate.template.Replace("#姓名#", model_user.Cname);
            //                            formdata = formdata.Replace("#部门#", new xy_auth.BLL.auth_Group().GetModel(int.Parse(model_user.GroupId.ToString())).Description);
            //                            formdata = formdata.Replace("#当前时间#", model_msg.send_time.Value.ToString("yyyy-MM-dd HH:mm"));
            //                            msg_content = "{\"content\":\"" + formdata + "\",\"url\":\"" + url + "\",\"pcUrl\":\"" + pcUrl + "\"}";
            //                        }
            //                        else
            //                        {
            //                            msg_content = "{\"content\":\"" + model_msg.msg_content + "\",\"url\":\"" + url + "\",\"pcUrl\":\"" + pcUrl + "\"}";
            //                        }
            //                        strSql.Clear();
            //                        strSql.Append("insert into xy_app_msg_receive(");
            //                        strSql.Append("id,msgId,cateId,catePath,userId,isRead,msg_title,msg_content,send_time");
            //                        strSql.Append(") values (");
            //                        strSql.Append("@id,@msgId,@cateId,@catePath,@userId,@isRead,@msg_title,@msg_content,@send_time");
            //                        strSql.Append(") ");
            //                        SqlParameter[] parameters1 = {
            //            new SqlParameter("@id", SqlDbType.UniqueIdentifier,16) ,            
            //            new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16) ,            
            //            new SqlParameter("@cateId", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@catePath", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16) ,            
            //            new SqlParameter("@isRead", SqlDbType.Int,4) ,            
            //            new SqlParameter("@msg_title", SqlDbType.NVarChar,50) ,            
            //            new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
            //            new SqlParameter("@send_time", SqlDbType.DateTime)        
            //};
            //                        parameters1[0].Value = Guid.NewGuid();
            //                        parameters1[1].Value = model_msg.id;
            //                        parameters1[2].Value = model_msg.cateId;
            //                        parameters1[3].Value = model_msg.catePath;
            //                        parameters1[4].Value = new Guid(checkerId);
            //                        parameters1[5].Value = 0;
            //                        parameters1[6].Value = model_cate.name;
            //                        parameters1[7].Value = msg_content;
            //                        parameters1[8].Value = model_msg.send_time;
            //                        PrepareCommand(cmd, conn, null, strSql.ToString(), parameters1);
            //                        rows = cmd.ExecuteNonQuery();
            //                        cmd.Parameters.Clear();
            //                    }

            //                }
            //            }
            //        }
            //        tx.Commit();
            //    }
            //    catch (System.Data.SqlClient.SqlException E)
            //    {
            //        rows = 0;
            //        tx.Rollback();
            //        throw new Exception(E.Message);
            //    }
            //    finally
            //    {
            //        cmd.Dispose();
            //        conn.Close();
            //    }
            //}
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public bool AddReciveMsg(Model.xy_app_msg_receive model_recivemsg, Guid id, int recive_num)
        {
            ArrayList array = new ArrayList();
            //新增接受表数据
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into xy_app_msg_receive(");
            strSql.Append("id,msgId,cateId,catePath,userId,isRead,msg_title,msg_content,send_time)");
            strSql.AppendFormat(" values ('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}')", Guid.NewGuid(), model_recivemsg.msgId, model_recivemsg.cateId, model_recivemsg.catePath, model_recivemsg.userId, model_recivemsg.isRead, model_recivemsg.msg_title, model_recivemsg.msg_content, model_recivemsg.send_time);
            array.Add(strSql.ToString());
            //更新上一条信息的状态
            array.Add(string.Format("update xy_app_msg_receive set isRead='{1}' where id='{0}'", id, 2));
            //更新主表的发送数目
            array.Add(string.Format("update xy_app_msg set receive_num='{1}', receive_type='{2}' where id='{0}'", model_recivemsg.msgId, recive_num + 1, 3));
            int rows = DbHelperSQL.ExecuteSqlTran(array);
            if (rows > 0)
            {
                return true;
            }
            return false;
        }
        private static void PrepareCommand(SqlCommand cmd, SqlConnection conn, SqlTransaction trans, string cmdText, SqlParameter[] cmdParms)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            cmd.Connection = conn;
            cmd.CommandText = cmdText;
            if (trans != null)
                cmd.Transaction = trans;
            cmd.CommandType = CommandType.Text;//cmdType;
            if (cmdParms != null)
            {
                foreach (SqlParameter parameter in cmdParms)
                {
                    if ((parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Input) &&
                        (parameter.Value == null))
                    {
                        parameter.Value = DBNull.Value;
                    }
                    cmd.Parameters.Add(parameter);
                }
            }
        }

        /// <summary>
        /// 发送给家长
        /// </summary>
        public bool SendMsgToPrents(Model.xy_app_msg model_msg, List<string> checkerIds, string url, string pcUrl)
        {
            int rows = 0;
            Model.xy_app_msg_cate model_cate = new BLL.xy_app_msg_cate().GetModel(model_msg.cateId);
            using (SqlConnection conn = new SqlConnection(DbHelperSQL.ConnMain[0]))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    StringBuilder strSql = new StringBuilder();
                    strSql.Append("insert into xy_app_msg(");
                    strSql.Append("id,msg_title,msg_content,gt_result,gt_taskId,gt_status,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num");
                    strSql.Append(") values (");
                    strSql.Append("@id,@msg_title,@msg_content,@gt_result,@gt_taskId,@gt_status,@cateId,@catePath,@businessId,@send_time,@send_userId,@msg_type,@receive_type,@receive_num");
                    strSql.Append(") ");

                    SqlParameter[] parameters = {
                        new SqlParameter("@id", SqlDbType.UniqueIdentifier,16) ,
                        new SqlParameter("@msg_title", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@msg_content", SqlDbType.NVarChar,-1) ,
                        new SqlParameter("@gt_result", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@gt_status", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@cateId", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@catePath", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@businessId", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@send_time", SqlDbType.DateTime) ,
                        new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16) ,
                        new SqlParameter("@msg_type", SqlDbType.Int,4) ,
                        new SqlParameter("@receive_type", SqlDbType.Int,4) ,
                        new SqlParameter("@receive_num", SqlDbType.Int,4)};

                    parameters[0].Value = model_msg.id;
                    parameters[1].Value = model_msg.msg_title;
                    parameters[2].Value = model_msg.msg_content;
                    parameters[3].Value = model_msg.gt_result;
                    parameters[4].Value = model_msg.gt_taskId;
                    parameters[5].Value = model_msg.gt_status;
                    parameters[6].Value = model_msg.cateId;
                    parameters[7].Value = model_msg.catePath;
                    parameters[8].Value = model_msg.businessId;
                    parameters[9].Value = model_msg.send_time;
                    parameters[10].Value = model_msg.send_userId;
                    parameters[11].Value = model_msg.msg_type;
                    parameters[12].Value = model_msg.receive_type;
                    parameters[13].Value = model_msg.receive_num;
                    PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
                    rows = cmd.ExecuteNonQuery();
                    cmd.Parameters.Clear();
                    if (rows > 0)
                    {
                        if (checkerIds.Count > 0)
                        {
                            foreach (string checkerId in checkerIds)
                            {
                                if (model_cate != null)
                                {
                                    string msg_content = "";
                                    msg_content = "{\"content\":\"" + model_msg.msg_content + "\",\"url\":\"" + url + "\",\"pcUrl\":\"" + pcUrl + "\"}";
                                    strSql.Clear();
                                    strSql.Append("insert into xy_app_msg_receive(");
                                    strSql.Append("id,msgId,cateId,catePath,userId,isRead,msg_title,msg_content,send_time");
                                    strSql.Append(") values (");
                                    strSql.Append("@id,@msgId,@cateId,@catePath,@userId,@isRead,@msg_title,@msg_content,@send_time");
                                    strSql.Append(") ");
                                    SqlParameter[] parameters1 = {
                        new SqlParameter("@id", SqlDbType.UniqueIdentifier,16) ,
                        new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16) ,
                        new SqlParameter("@cateId", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@catePath", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16) ,
                        new SqlParameter("@isRead", SqlDbType.Int,4) ,
                        new SqlParameter("@msg_title", SqlDbType.NVarChar,50) ,
                        new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
                        new SqlParameter("@send_time", SqlDbType.DateTime)
            };
                                    parameters1[0].Value = Guid.NewGuid();
                                    parameters1[1].Value = model_msg.id;
                                    parameters1[2].Value = model_msg.cateId;
                                    parameters1[3].Value = model_msg.catePath;
                                    parameters1[4].Value = new Guid(checkerId);
                                    parameters1[5].Value = 0;
                                    parameters1[6].Value = model_cate.name;
                                    parameters1[7].Value = msg_content;
                                    parameters1[8].Value = model_msg.send_time;
                                    PrepareCommand(cmd, conn, null, strSql.ToString(), parameters1);
                                    rows = cmd.ExecuteNonQuery();
                                    cmd.Parameters.Clear();
                                }

                            }
                        }
                    }
                    tx.Commit();
                }
                catch (System.Data.SqlClient.SqlException E)
                {
                    rows = 0;
                    tx.Rollback();
                    throw new Exception(E.Message);
                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                }
            }
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public bool SendInterfereMsg(List<Model.xy_app_msg> msgList, List<Model.xy_app_msg_receive> msgReceiveList)
        {
            int rows = 0;
            using (SqlConnection conn = new SqlConnection(DbHelperSQL.ConnMain[0]))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;

                try
                {

                    StringBuilder strSql = new StringBuilder();
                    for (int i = 0; i < msgList.Count; i++)
                    {
                        // 插入主表 xy_app_msg
                        Model.xy_app_msg model_msg = msgList[i];
                        strSql.Append("insert into xy_app_msg(");
                        strSql.Append("id,msg_title,msg_content,gt_result,gt_taskId,gt_status,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num");
                        strSql.Append(") values (");
                        strSql.Append("@id,@msg_title,@msg_content,@gt_result,@gt_taskId,@gt_status,@cateId,@catePath,@businessId,@send_time,@send_userId,@msg_type,@receive_type,@receive_num");
                        strSql.Append(")");

                        SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
                    new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
                    new SqlParameter("@gt_result", SqlDbType.NVarChar,50),
                    new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50),
                    new SqlParameter("@gt_status", SqlDbType.NVarChar,50),
                    new SqlParameter("@cateId", SqlDbType.NVarChar,50),
                    new SqlParameter("@catePath", SqlDbType.NVarChar,50),
                    new SqlParameter("@businessId", SqlDbType.NVarChar,50),
                    new SqlParameter("@send_time", SqlDbType.DateTime),
                    new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@msg_type", SqlDbType.Int,4),
                    new SqlParameter("@receive_type", SqlDbType.Int,4),
                    new SqlParameter("@receive_num", SqlDbType.Int,4)
                };

                        parameters[0].Value = model_msg.id;
                        parameters[1].Value = model_msg.msg_title;
                        parameters[2].Value = model_msg.msg_content;
                        parameters[3].Value = model_msg.gt_result;
                        parameters[4].Value = model_msg.gt_taskId;
                        parameters[5].Value = model_msg.gt_status;
                        parameters[6].Value = model_msg.cateId;
                        parameters[7].Value = model_msg.catePath;
                        parameters[8].Value = model_msg.businessId;
                        parameters[9].Value = model_msg.send_time;
                        parameters[10].Value = model_msg.send_userId;
                        parameters[11].Value = model_msg.msg_type;
                        parameters[12].Value = model_msg.receive_type;
                        parameters[13].Value = model_msg.receive_num;

                        PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
                        rows = cmd.ExecuteNonQuery();
                        cmd.Parameters.Clear();

                        if (rows <= 0) continue;
                    }
                    if (msgReceiveList != null && msgReceiveList.Count > 0)
                    {
                        for (int j = 0; j < msgReceiveList.Count; j++)
                        {
                            Model.xy_app_msg_receive model_receivemsg = msgReceiveList[j];
                            strSql.Clear();
                            strSql.Append("insert into xy_app_msg_receive(");
                            strSql.Append("id,msgId,cateId,catePath,userId,isRead,msg_title,msg_content,send_time");
                            strSql.Append(") values (");
                            strSql.Append("@id,@msgId,@cateId,@catePath,@userId,@isRead,@msg_title,@msg_content,@send_time");
                            strSql.Append(")");

                            SqlParameter[] parameters1 = {
                            new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
                            new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16),
                            new SqlParameter("@cateId", SqlDbType.NVarChar,50),
                            new SqlParameter("@catePath", SqlDbType.NVarChar,50),
                            new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16),
                            new SqlParameter("@isRead", SqlDbType.Int,4),
                            new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
                            new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
                            new SqlParameter("@send_time", SqlDbType.DateTime)
                        };

                            parameters1[0].Value = Guid.NewGuid();
                            parameters1[1].Value = model_receivemsg.msgId;
                            parameters1[2].Value = model_receivemsg.cateId;
                            parameters1[3].Value = model_receivemsg.catePath;
                            parameters1[4].Value = model_receivemsg.userId;
                            parameters1[5].Value = 0;
                            parameters1[6].Value = model_receivemsg.msg_title;
                            parameters1[7].Value = model_receivemsg.msg_content;
                            parameters1[8].Value = model_receivemsg.send_time;

                            PrepareCommand(cmd, conn, null, strSql.ToString(), parameters1);
                            rows = cmd.ExecuteNonQuery();
                            cmd.Parameters.Clear();
                        }
                    }

                    tx.Commit();
                }
                catch (System.Data.SqlClient.SqlException E)
                {
                    rows = 0;
                    tx.Rollback();
                    throw new Exception(E.Message);
                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                }
            }

            return rows > 0;
        }
        #endregion  ExtensionMethod
    }
}

