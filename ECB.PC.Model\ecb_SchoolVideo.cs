﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 学校校宣视频表
	/// </summary>
	[Serializable]
	public partial class ecb_SchoolVideo
	{
		public ecb_SchoolVideo()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _name;
		private string _path;
		private int _istop;
		private DateTime _createdate;
		private Guid _createuserid;
        private int? _sort;
        private int? _type;
        private decimal? _size;
        private int? _ispass;
        private Guid _ispassby;
        private DateTime? _ispassdate;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区Id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 视频名称
		/// </summary>
		public string Name
		{
			set{ _name=value;}
			get{return _name;}
		}
		/// <summary>
		/// 视频路径
		/// </summary>
		public string Path
		{
			set{ _path=value;}
			get{return _path;}
		}
		/// <summary>
		/// 0不置顶 1置顶 置顶时在班牌播放
		/// </summary>
		public int IsTop
		{
			set{ _istop=value;}
			get{return _istop;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateDate
		{
			set{ _createdate=value;}
			get{return _createdate;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
        /// <summary>
		/// 
		/// </summary>
		public int? Sort
		{
			set{ _sort=value;}
			get{return _sort;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Type
		{
			set{ _type=value;}
			get{return _type;}
		}
        /// <summary>
        /// 视频大小
        /// </summary>
        public decimal? Size
        {
            set { _size = value; }
            get { return _size; }
        }
        /// <summary>
		/// 是否通过审核
		/// </summary>
		public int? IsPass
        {
            set { _ispass = value; }
            get { return _ispass; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid IsPassBy
        {
            set { _ispassby = value; }
            get { return _ispassby; }
        }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? IsPassDate
        {
            set { _ispassdate = value; }
            get { return _ispassdate; }
        }
        #endregion Model

    }
}

