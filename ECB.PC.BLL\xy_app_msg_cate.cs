﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:xy_app_msg_cate
	/// </summary>
	public partial class xy_app_msg_cate
	{
        public xy_app_msg_cate()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from xy_app_msg_cate");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.NVarChar,50)			};
            parameters[0].Value = id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.xy_app_msg_cate model)
        {
            int rowsAffected = 0;
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into xy_app_msg_cate(");
            strSql.Append("id,name,template,pId,icon,color,isShow,setting,pageUrl,msgPcUrl,msgAppUrl)");
            strSql.Append(" values (");
            strSql.Append("@id,@name,@template,@pId,@path,@depth,@orderId,@icon,@color,@isShow,@setting,@pageUrl,@msgPcUrl,@msgAppUrl)");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.NVarChar,50),
					new SqlParameter("@name", SqlDbType.NVarChar,50),
					new SqlParameter("@template", SqlDbType.NVarChar,-1),
					new SqlParameter("@pId", SqlDbType.NVarChar,50),
					new SqlParameter("@icon", SqlDbType.NVarChar,50),
					new SqlParameter("@color", SqlDbType.NVarChar,50),
					new SqlParameter("@isShow", SqlDbType.Int,4),
					new SqlParameter("@setting", SqlDbType.NVarChar,-1),
                    new SqlParameter("@pageUrl",SqlDbType.NVarChar,255),
                    new SqlParameter("@msgPcUrl",SqlDbType.NVarChar,255),
                    new SqlParameter("@msgAppUrl",SqlDbType.NVarChar,255)
                                        };
            parameters[0].Value = model.id;
            parameters[1].Value = model.name;
            parameters[2].Value = model.template;
            parameters[3].Value = model.pId;
            parameters[4].Value = model.icon;
            parameters[5].Value = model.color;
            parameters[6].Value = model.isShow;
            parameters[7].Value = model.setting;
            parameters[8].Value = model.pageUrl;
            parameters[9].Value = model.msgPcUrl;
            parameters[10].Value = model.msgAppUrl;
            object obj = DbHelperSQL.RunProcedure("UP_xy_app_msg_cate_ADD", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.xy_app_msg_cate model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update xy_app_msg_cate set ");
            strSql.Append("name=@name,");
            strSql.Append("template=@template,");
            strSql.Append("pId=@pId,");
            strSql.Append("path=@path,");
            strSql.Append("depth=@depth,");
            strSql.Append("orderId=@orderId,");
            strSql.Append("icon=@icon,");
            strSql.Append("color=@color,");
            strSql.Append("isShow=@isShow,");
            strSql.Append("setting=@setting,");
            strSql.Append("pageUrl=@pageUrl,");
            strSql.Append("msgPcUrl=@msgPcUrl,");
            strSql.Append("msgAppUrl=@msgAppUrl");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@name", SqlDbType.NVarChar,50),
					new SqlParameter("@template", SqlDbType.NVarChar,-1),
					new SqlParameter("@pId", SqlDbType.NVarChar,50),
					new SqlParameter("@path", SqlDbType.NVarChar,50),
					new SqlParameter("@depth", SqlDbType.Int,4),
					new SqlParameter("@orderId", SqlDbType.Int,4),
					new SqlParameter("@icon", SqlDbType.NVarChar,50),
					new SqlParameter("@color", SqlDbType.NVarChar,50),
					new SqlParameter("@isShow", SqlDbType.Int,4),
					new SqlParameter("@setting", SqlDbType.NVarChar,-1),
                    new SqlParameter("@pageUrl",SqlDbType.NVarChar,255),
                    new SqlParameter("@msgPcUrl",SqlDbType.NVarChar,255),
                    new SqlParameter("@msgAppUrl",SqlDbType.NVarChar,255),
					new SqlParameter("@id", SqlDbType.NVarChar,50)};
            parameters[0].Value = model.name;
            parameters[1].Value = model.template;
            parameters[2].Value = model.pId;
            parameters[3].Value = model.path;
            parameters[4].Value = model.depth;
            parameters[5].Value = model.orderId;
            parameters[6].Value = model.icon;
            parameters[7].Value = model.color;
            parameters[8].Value = model.isShow;
            parameters[9].Value = model.setting;
            parameters[10].Value = model.pageUrl;
            parameters[11].Value = model.msgPcUrl;
            parameters[12].Value = model.msgAppUrl;
            parameters[13].Value = model.id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from xy_app_msg_cate ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.NVarChar,50)			};
            parameters[0].Value = id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from xy_app_msg_cate ");
            strSql.Append(" where id in (" + idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.xy_app_msg_cate GetModel(string id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 id,name,template,pId,path,depth,orderId,icon,color,isShow,setting,pageUrl,msgPcUrl,msgAppUrl from xy_app_msg_cate ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.NVarChar,50)			};
            parameters[0].Value = id;

            ECB.PC.Model.xy_app_msg_cate model = new ECB.PC.Model.xy_app_msg_cate();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.xy_app_msg_cate DataRowToModel(DataRow row)
        {
            ECB.PC.Model.xy_app_msg_cate model = new ECB.PC.Model.xy_app_msg_cate();
            if (row != null)
            {
                if (row["id"] != null)
                {
                    model.id = row["id"].ToString();
                }
                if (row["name"] != null)
                {
                    model.name = row["name"].ToString();
                }
                if (row["template"] != null)
                {
                    model.template = row["template"].ToString();
                }
                if (row["pId"] != null)
                {
                    model.pId = row["pId"].ToString();
                }
                if (row["path"] != null)
                {
                    model.path = row["path"].ToString();
                }
                if (row["depth"] != null && row["depth"].ToString() != "")
                {
                    model.depth = int.Parse(row["depth"].ToString());
                }
                if (row["orderId"] != null && row["orderId"].ToString() != "")
                {
                    model.orderId = int.Parse(row["orderId"].ToString());
                }
                if (row["icon"] != null)
                {
                    model.icon = row["icon"].ToString();
                }
                if (row["color"] != null)
                {
                    model.color = row["color"].ToString();
                }
                if (row["isShow"] != null && row["isShow"].ToString() != "")
                {
                    model.isShow = int.Parse(row["isShow"].ToString());
                }
                if (row["setting"] != null)
                {
                    model.setting = row["setting"].ToString();
                }
                if (row["pageUrl"] != null)
                {
                    model.pageUrl = row["pageUrl"].ToString();
                }
                if (row["msgPcUrl"] != null)
                {
                    model.msgPcUrl = row["msgPcUrl"].ToString();
                }
                if (row["msgAppUrl"] != null)
                {
                    model.msgAppUrl = row["msgAppUrl"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select id,name,template,pId,path,depth,orderId,icon,color,isShow,setting,pageUrl,msgPcUrl,msgAppUrl ");
            strSql.Append(" FROM xy_app_msg_cate ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder, string[] ConnStr = null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" id,name,template,pId,path,depth,orderId,icon,color,isShow,setting,pageUrl,msgPcUrl,msgAppUrl ");
            strSql.Append(" FROM xy_app_msg_cate ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString(), ConnStr);
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM xy_app_msg_cate ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from xy_app_msg_cate T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "xy_app_msg_cate";
            parameters[1].Value = "id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        //上移
        public object MoveUp(string Id)
        {
            SqlParameter[] parameter = { new SqlParameter("@Id", Id) };
            return DbHelperSQL.RunProcedure("UP_xy_app_msg_cate_moveup", parameter);
        }
        //下移
        public object MoveDown(string Id)
        {
            SqlParameter[] parameter = { new SqlParameter("@Id", Id) };
            return DbHelperSQL.RunProcedure("UP_xy_app_msg_cate_movedown", parameter);
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Del(string Id)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.NVarChar,50)			};
            parameters[0].Value = Id;

            DbHelperSQL.RunProcedure("UP_xy_app_msg_cate_Delete", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public string GetMaxModuleId(string pId,string[] ConnStr = null)
        {
            StringBuilder strSql = new StringBuilder();
            SqlParameter[] parameters = {
					new SqlParameter("@pId", SqlDbType.NVarChar,50)
			};
            parameters[0].Value = pId;

            strSql.Append("select MAX(Id) FROM xy_app_msg_cate where pId=@pId");
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), parameters, ConnStr);
            if (obj == null)
            {
                return "";
            }
            else
            {
                return obj.ToString();
            }

        }
        #endregion  ExtensionMethod
	}
}

