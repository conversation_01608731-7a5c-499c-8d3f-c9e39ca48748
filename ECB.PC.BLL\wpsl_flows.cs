﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:wpsl_flows
    /// </summary>
    public partial class wpsl_flows
    {

        public wpsl_flows()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from wpsl_flows");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.wpsl_flows model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into wpsl_flows(");
            strSql.Append("Id,ColumnId,ColumnPath,Name,TypeCode,IsEnable,CreateTime,LastEditor)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@Name,@TypeCode,@IsEnable,@CreateTime,@LastEditor)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@Name", SqlDbType.NVarChar,100),
                    new SqlParameter("@TypeCode", SqlDbType.Int,4),
                    new SqlParameter("@IsEnable", SqlDbType.Bit,1),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.Name;
            parameters[4].Value = model.TypeCode;
            parameters[5].Value = model.IsEnable;
            parameters[6].Value = model.CreateTime;
            parameters[7].Value = model.LastEditor;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.wpsl_flows model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update wpsl_flows set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Name=@Name,");
            strSql.Append("TypeCode=@TypeCode,");
            strSql.Append("IsEnable=@IsEnable,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("LastEditor=@LastEditor");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@Name", SqlDbType.NVarChar,100),
                    new SqlParameter("@TypeCode", SqlDbType.Int,4),
                    new SqlParameter("@IsEnable", SqlDbType.Bit,1),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.Name;
            parameters[3].Value = model.TypeCode;
            parameters[4].Value = model.IsEnable;
            parameters[5].Value = model.CreateTime;
            parameters[6].Value = model.LastEditor;
            parameters[7].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from wpsl_flows ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from wpsl_flows ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.wpsl_flows GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Name,TypeCode,IsEnable,CreateTime,LastEditor from wpsl_flows ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.wpsl_flows model = new ECB.PC.Model.wpsl_flows();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.wpsl_flows DataRowToModel(DataRow row)
        {
            ECB.PC.Model.wpsl_flows model = new ECB.PC.Model.wpsl_flows();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["TypeCode"] != null && row["TypeCode"].ToString() != "")
                {
                    model.TypeCode = int.Parse(row["TypeCode"].ToString());
                }
                if (row["IsEnable"] != null && row["IsEnable"].ToString() != "")
                {
                    if ((row["IsEnable"].ToString() == "1") || (row["IsEnable"].ToString().ToLower() == "true"))
                    {
                        model.IsEnable = true;
                    }
                    else
                    {
                        model.IsEnable = false;
                    }
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["LastEditor"] != null && row["LastEditor"].ToString() != "")
                {
                    model.LastEditor = new Guid(row["LastEditor"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Name,TypeCode,IsEnable,CreateTime,LastEditor ");
            strSql.Append(" FROM wpsl_flows ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder, bool isMain = false)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,Name,TypeCode,IsEnable,CreateTime,LastEditor ");
            strSql.Append(" FROM wpsl_flows ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString(), isMain ? DbHelperSQL.ConnMain : null);
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM wpsl_flows ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from wpsl_flows T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "wpsl_flows";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 停用流程
        /// </summary>
        /// <param name="ColumnId"></param>
        /// <param name="TypeCode">流程类型</param>
        /// <returns></returns>
        public bool StopFlows(int ColumnId, int TypeCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("update wpsl_flows set IsEnable=0 where ColumnId=@ColumnId AND TypeCode=@TypeCode;");
            SqlParameter[] parameters = { new SqlParameter("@ColumnId", SqlDbType.Int, 4), new SqlParameter("@TypeCode", SqlDbType.Int, 4) };
            parameters[0].Value = ColumnId;
            parameters[1].Value = TypeCode;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除关联数据
        /// </summary>
        /// 
        public bool DeleteAllByFlowId(Guid FlowId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("delete from wpsl_flows where Id=@FlowId;");
            strSql.AppendLine("delete FROM wpsl_flow_detail where FlowId=@FlowId;");
            strSql.AppendLine("delete FROM wpsl_flow_checker where FlowId=@FlowId;");
            SqlParameter[] parameters = { new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier, 16) };
            parameters[0].Value = FlowId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsbyFlowIdCode(Guid FlowId, string FlowCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from wpsl_flow_detail");
            strSql.Append(" where FlowId=@FlowId and FlowCode=@FlowCode ");
            SqlParameter[] parameters = {
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode",SqlDbType.NVarChar,10)
                                        };
            parameters[0].Value = FlowId;
            parameters[1].Value = FlowCode;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <param name="typeCode">类型code</param>
        /// <returns></returns>
        public ECB.PC.Model.wpsl_flows GetModel(int columnId, int typeCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Name,TypeCode,IsEnable,CreateTime,LastEditor from wpsl_flows ");
            strSql.Append(" where ColumnId=@ColumnId and TypeCode=@TypeCode and IsEnable=1 ");
            SqlParameter[] parameters = {
               new SqlParameter("@ColumnId", SqlDbType.Int, 4),
               new SqlParameter("@TypeCode", SqlDbType.Int, 4)};
            parameters[0].Value = columnId;
            parameters[1].Value = (int)typeCode;
            ECB.PC.Model.wpsl_flows model = new ECB.PC.Model.wpsl_flows();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        #endregion  ExtensionMethod
    }
}
