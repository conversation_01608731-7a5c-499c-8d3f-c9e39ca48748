﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// 荣誉附件类
    /// </summary>
    [Serializable]
    public partial class ecb_class_honor_attachement
    {

        public ecb_class_honor_attachement()
        { }
        #region Model
        private Guid _id;
        private Guid _honorcode;
        private string _filename;
        private string _filepath;
        private DateTime? _uploaddate;
        private int? _schoolcolumnid;
        private string _schoolcolumnpath;
        /// <summary>
        /// 主键ID
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 荣誉Code
        /// </summary>
        public Guid HonorCode
        {
            set { _honorcode = value; }
            get { return _honorcode; }
        }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName
        {
            set { _filename = value; }
            get { return _filename; }
        }
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath
        {
            set { _filepath = value; }
            get { return _filepath; }
        }
        /// <summary>
        /// 上传日期
        /// </summary>
        public DateTime? UploadDate
        {
            set { _uploaddate = value; }
            get { return _uploaddate; }
        }
        /// <summary>
        /// 学校ColumnID
        /// </summary>
        public int? SchoolColumnID
        {
            set { _schoolcolumnid = value; }
            get { return _schoolcolumnid; }
        }
        /// <summary>
        /// 学校ColumnPath
        /// </summary>
        public string SchoolColumnPath
        {
            set { _schoolcolumnpath = value; }
            get { return _schoolcolumnpath; }
        }
        #endregion Model
    }
}
