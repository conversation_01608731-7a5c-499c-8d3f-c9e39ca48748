﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 表扬表
	/// </summary>
	[Serializable]
	public partial class ecb_Praise
	{
		public ecb_Praise()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _typeid;
		private int _praiselevel;
		private Guid _studentid;
		private string _praisedesc;
		private string _imgpath;
		private Guid _classid;
		private Guid _gradeid;
		private Guid _termid;
		private Guid _createuserid;
		private DateTime _createtime;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 类型ID
		/// </summary>
		public Guid TypeId
		{
			set{ _typeid=value;}
			get{return _typeid;}
		}
		/// <summary>
		/// 0 班级 1校级
		/// </summary>
		public int PraiseLevel
		{
			set{ _praiselevel=value;}
			get{return _praiselevel;}
		}
		/// <summary>
		/// 受表扬人
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 表扬内容
		/// </summary>
		public string PraiseDesc
		{
			set{ _praisedesc=value;}
			get{return _praisedesc;}
		}
		/// <summary>
		/// 图片 |分割
		/// </summary>
		public string ImgPath
		{
			set{ _imgpath=value;}
			get{return _imgpath;}
		}
		/// <summary>
		/// 班级ID
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 年级ID
		/// </summary>
		public Guid GradeId
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 学期ID
		/// </summary>
		public Guid TermId
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}

