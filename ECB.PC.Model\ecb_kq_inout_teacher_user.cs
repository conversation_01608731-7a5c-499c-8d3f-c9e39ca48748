﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_kq_inout_teacher_user:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_kq_inout_teacher_user
	{
		public ecb_kq_inout_teacher_user()
		{}
		#region Model
		private Guid _id;
		private Guid _kqteachersid;
		private int _columnid;
		private string _columnpath;
        public int  DeptId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 考勤人员
		/// </summary>
		public Guid KQTeachersId
		{
			set{ _kqteachersid=value;}
			get{return _kqteachersid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		#endregion Model

	}
}

