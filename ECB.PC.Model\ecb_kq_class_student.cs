﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 学生课堂考勤记录表
    /// </summary>
    [Serializable]
    public partial class ecb_kq_class_student
    {
        public ecb_kq_class_student()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _userid;
        private DateTime _recorddate;
        private int? _weekday;
        private string _kqrecords;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 考勤人Id
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 考勤记录时间
        /// </summary>
        public DateTime RecordDate
        {
            set { _recorddate = value; }
            get { return _recorddate; }
        }
        /// <summary>
        /// 周几 1-7
        /// </summary>
        public int? WeekDay
        {
            set { _weekday = value; }
            get { return _weekday; }
        }
        /// <summary>
        /// 考勤详情
        /// </summary>
        public string KQRecords
        {
            set { _kqrecords = value; }
            get { return _kqrecords; }
        }
        #endregion Model

    }
}

