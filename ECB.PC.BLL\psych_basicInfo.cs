﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;
using ECB.PC.Model;
using YunEdu.Common;
using System.Linq;
using Newtonsoft.Json;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:psych_basicInfo
	/// </summary>
	public partial class psych_basicInfo
	{
		public psych_basicInfo()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from psych_basicInfo");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(), parameters);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.psych_basicInfo model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into psych_basicInfo(");
			strSql.Append("Id,ColumnId,ColumnPath,StudentId,FamilyStructure,FamilyStructureExt,FamilyEconomics,EducationMethod,ParentRelation,FamilyAtmosphere,FamilyAtmosphereExt,Accommodation,AccommodationExt,ZDJB,ZDJB_Ext,LongMedicationUse,LongMedicationUseExt,PhysicalLimitations,JWXLZD,JWXLZD_Ext,SuicidalReport,SuicidalReportExt,AbnormalBehavior,AbnormalBehaviorExt,AbnormalStatus,DSTeacherId,LastEditBy,LastEditTime,EducationMethodExt,ParentRelationExt)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@FamilyStructure,@FamilyStructureExt,@FamilyEconomics,@EducationMethod,@ParentRelation,@FamilyAtmosphere,@FamilyAtmosphereExt,@Accommodation,@AccommodationExt,@ZDJB,@ZDJB_Ext,@LongMedicationUse,@LongMedicationUseExt,@PhysicalLimitations,@JWXLZD,@JWXLZD_Ext,@SuicidalReport,@SuicidalReportExt,@AbnormalBehavior,@AbnormalBehaviorExt,@AbnormalStatus,@DSTeacherId,@LastEditBy,@LastEditTime,@EducationMethodExt,@ParentRelationExt)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@FamilyStructure", SqlDbType.NVarChar,4),
					new SqlParameter("@FamilyStructureExt", SqlDbType.NVarChar,100),
					new SqlParameter("@FamilyEconomics", SqlDbType.NVarChar,4),
					new SqlParameter("@EducationMethod", SqlDbType.NVarChar,4),
					new SqlParameter("@ParentRelation", SqlDbType.NVarChar,4),
					new SqlParameter("@FamilyAtmosphere", SqlDbType.NVarChar,4),
					new SqlParameter("@FamilyAtmosphereExt", SqlDbType.NVarChar,200),
					new SqlParameter("@Accommodation", SqlDbType.NVarChar,4),
					new SqlParameter("@AccommodationExt", SqlDbType.NVarChar,200),
					new SqlParameter("@ZDJB", SqlDbType.NVarChar,4),
					new SqlParameter("@ZDJB_Ext", SqlDbType.NVarChar,200),
					new SqlParameter("@LongMedicationUse", SqlDbType.NVarChar,4),
					new SqlParameter("@LongMedicationUseExt", SqlDbType.NVarChar,200),
					new SqlParameter("@PhysicalLimitations", SqlDbType.NVarChar,4),
					new SqlParameter("@JWXLZD", SqlDbType.NVarChar,4),
					new SqlParameter("@JWXLZD_Ext", SqlDbType.NVarChar,200),
					new SqlParameter("@SuicidalReport", SqlDbType.NVarChar,4),
					new SqlParameter("@SuicidalReportExt", SqlDbType.NVarChar,200),
					new SqlParameter("@AbnormalBehavior", SqlDbType.NVarChar,4),
					new SqlParameter("@AbnormalBehaviorExt", SqlDbType.NVarChar,200),
					new SqlParameter("@AbnormalStatus", SqlDbType.NVarChar,4),
					new SqlParameter("@DSTeacherId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime),
					new SqlParameter("@EducationMethodExt", SqlDbType.NVarChar,200),
					new SqlParameter("@ParentRelationExt", SqlDbType.NVarChar,200)};
			parameters[0].Value = model.Id = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.StudentId;
			parameters[4].Value = model.FamilyStructure;
			parameters[5].Value = model.FamilyStructureExt;
			parameters[6].Value = model.FamilyEconomics;
			parameters[7].Value = model.EducationMethod;
			parameters[8].Value = model.ParentRelation;
			parameters[9].Value = model.FamilyAtmosphere;
			parameters[10].Value = model.FamilyAtmosphereExt;
			parameters[11].Value = model.Accommodation;
			parameters[12].Value = model.AccommodationExt;
			parameters[13].Value = model.ZDJB;
			parameters[14].Value = model.ZDJB_Ext;
			parameters[15].Value = model.LongMedicationUse;
			parameters[16].Value = model.LongMedicationUseExt;
			parameters[17].Value = model.PhysicalLimitations;
			parameters[18].Value = model.JWXLZD;
			parameters[19].Value = model.JWXLZD_Ext;
			parameters[20].Value = model.SuicidalReport;
			parameters[21].Value = model.SuicidalReportExt;
			parameters[22].Value = model.AbnormalBehavior;
			parameters[23].Value = model.AbnormalBehaviorExt;
			parameters[24].Value = model.AbnormalStatus;
			if (model.DSTeacherId == Guid.Empty)
			{
				parameters[25].Value = DBNull.Value;
			}
			else
			{
				parameters[25].Value = model.DSTeacherId;
			}
			parameters[26].Value = model.LastEditBy;
			parameters[27].Value = model.LastEditTime;
			parameters[28].Value = model.EducationMethodExt;
			parameters[29].Value = model.ParentRelationExt;
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.psych_basicInfo model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update psych_basicInfo set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("StudentId=@StudentId,");
			strSql.Append("FamilyStructure=@FamilyStructure,");
			strSql.Append("FamilyStructureExt=@FamilyStructureExt,");
			strSql.Append("FamilyEconomics=@FamilyEconomics,");
			strSql.Append("EducationMethod=@EducationMethod,");
			strSql.Append("ParentRelation=@ParentRelation,");
			strSql.Append("FamilyAtmosphere=@FamilyAtmosphere,");
			strSql.Append("FamilyAtmosphereExt=@FamilyAtmosphereExt,");
			strSql.Append("Accommodation=@Accommodation,");
			strSql.Append("AccommodationExt=@AccommodationExt,");
			strSql.Append("ZDJB=@ZDJB,");
			strSql.Append("ZDJB_Ext=@ZDJB_Ext,");
			strSql.Append("LongMedicationUse=@LongMedicationUse,");
			strSql.Append("LongMedicationUseExt=@LongMedicationUseExt,");
			strSql.Append("PhysicalLimitations=@PhysicalLimitations,");
			strSql.Append("JWXLZD=@JWXLZD,");
			strSql.Append("JWXLZD_Ext=@JWXLZD_Ext,");
			strSql.Append("SuicidalReport=@SuicidalReport,");
			strSql.Append("SuicidalReportExt=@SuicidalReportExt,");
			strSql.Append("AbnormalBehavior=@AbnormalBehavior,");
			strSql.Append("AbnormalBehaviorExt=@AbnormalBehaviorExt,");
			strSql.Append("AbnormalStatus=@AbnormalStatus,");
			strSql.Append("DSTeacherId=@DSTeacherId,");
			strSql.Append("LastEditBy=@LastEditBy,");
			strSql.Append("LastEditTime=@LastEditTime,");
			strSql.Append("EducationMethodExt=@EducationMethodExt,");
			strSql.Append("ParentRelationExt=@ParentRelationExt");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@FamilyStructure", SqlDbType.NVarChar,4),
					new SqlParameter("@FamilyStructureExt", SqlDbType.NVarChar,100),
					new SqlParameter("@FamilyEconomics", SqlDbType.NVarChar,4),
					new SqlParameter("@EducationMethod", SqlDbType.NVarChar,4),
					new SqlParameter("@ParentRelation", SqlDbType.NVarChar,4),
					new SqlParameter("@FamilyAtmosphere", SqlDbType.NVarChar,4),
					new SqlParameter("@FamilyAtmosphereExt", SqlDbType.NVarChar,200),
					new SqlParameter("@Accommodation", SqlDbType.NVarChar,4),
					new SqlParameter("@AccommodationExt", SqlDbType.NVarChar,200),
					new SqlParameter("@ZDJB", SqlDbType.NVarChar,4),
					new SqlParameter("@ZDJB_Ext", SqlDbType.NVarChar,200),
					new SqlParameter("@LongMedicationUse", SqlDbType.NVarChar,4),
					new SqlParameter("@LongMedicationUseExt", SqlDbType.NVarChar,200),
					new SqlParameter("@PhysicalLimitations", SqlDbType.NVarChar,4),
					new SqlParameter("@JWXLZD", SqlDbType.NVarChar,4),
					new SqlParameter("@JWXLZD_Ext", SqlDbType.NVarChar,200),
					new SqlParameter("@SuicidalReport", SqlDbType.NVarChar,4),
					new SqlParameter("@SuicidalReportExt", SqlDbType.NVarChar,200),
					new SqlParameter("@AbnormalBehavior", SqlDbType.NVarChar,4),
					new SqlParameter("@AbnormalBehaviorExt", SqlDbType.NVarChar,200),
					new SqlParameter("@AbnormalStatus", SqlDbType.NVarChar,4),
					new SqlParameter("@DSTeacherId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime),
					new SqlParameter("@EducationMethodExt", SqlDbType.NVarChar,200),
					new SqlParameter("@ParentRelationExt", SqlDbType.NVarChar,200),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.StudentId;
			parameters[3].Value = model.FamilyStructure;
			parameters[4].Value = model.FamilyStructureExt;
			parameters[5].Value = model.FamilyEconomics;
			parameters[6].Value = model.EducationMethod;
			parameters[7].Value = model.ParentRelation;
			parameters[8].Value = model.FamilyAtmosphere;
			parameters[9].Value = model.FamilyAtmosphereExt;
			parameters[10].Value = model.Accommodation;
			parameters[11].Value = model.AccommodationExt;
			parameters[12].Value = model.ZDJB;
			parameters[13].Value = model.ZDJB_Ext;
			parameters[14].Value = model.LongMedicationUse;
			parameters[15].Value = model.LongMedicationUseExt;
			parameters[16].Value = model.PhysicalLimitations;
			parameters[17].Value = model.JWXLZD;
			parameters[18].Value = model.JWXLZD_Ext;
			parameters[19].Value = model.SuicidalReport;
			parameters[20].Value = model.SuicidalReportExt;
			parameters[21].Value = model.AbnormalBehavior;
			parameters[22].Value = model.AbnormalBehaviorExt;
			parameters[23].Value = model.AbnormalStatus;
			parameters[24].Value = model.DSTeacherId;
			parameters[25].Value = model.LastEditBy;
			parameters[26].Value = model.LastEditTime;
			parameters[27].Value = model.EducationMethodExt;
			parameters[28].Value = model.ParentRelationExt;
			parameters[29].Value = model.Id;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from psych_basicInfo ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = Id;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from psych_basicInfo ");
			strSql.Append(" where Id in (" + Idlist + ")  ");
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.psych_basicInfo GetModel(Guid Id)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,FamilyStructure,FamilyStructureExt,FamilyEconomics,EducationMethod,ParentRelation,FamilyAtmosphere,FamilyAtmosphereExt,Accommodation,AccommodationExt,ZDJB,ZDJB_Ext,LongMedicationUse,LongMedicationUseExt,PhysicalLimitations,JWXLZD,JWXLZD_Ext,SuicidalReport,SuicidalReportExt,AbnormalBehavior,AbnormalBehaviorExt,AbnormalStatus,DSTeacherId,LastEditBy,LastEditTime,EducationMethodExt,ParentRelationExt from psych_basicInfo ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = Id;

			ECB.PC.Model.psych_basicInfo model = new ECB.PC.Model.psych_basicInfo();
			DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.psych_basicInfo DataRowToModel(DataRow row)
		{
			ECB.PC.Model.psych_basicInfo model = new ECB.PC.Model.psych_basicInfo();
			if (row != null)
			{
				if (row["Id"] != null && row["Id"].ToString() != "")
				{
					model.Id = new Guid(row["Id"].ToString());
				}
				if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
				{
					model.ColumnId = int.Parse(row["ColumnId"].ToString());
				}
				if (row["ColumnPath"] != null)
				{
					model.ColumnPath = row["ColumnPath"].ToString();
				}
				if (row["StudentId"] != null && row["StudentId"].ToString() != "")
				{
					model.StudentId = new Guid(row["StudentId"].ToString());
				}
				if (row["FamilyStructure"] != null)
				{
					model.FamilyStructure = row["FamilyStructure"].ToString();
				}
				if (row["FamilyStructureExt"] != null)
				{
					model.FamilyStructureExt = row["FamilyStructureExt"].ToString();
				}
				if (row["FamilyEconomics"] != null)
				{
					model.FamilyEconomics = row["FamilyEconomics"].ToString();
				}
				if (row["EducationMethod"] != null)
				{
					model.EducationMethod = row["EducationMethod"].ToString();
				}
				if (row["ParentRelation"] != null)
				{
					model.ParentRelation = row["ParentRelation"].ToString();
				}
				if (row["FamilyAtmosphere"] != null)
				{
					model.FamilyAtmosphere = row["FamilyAtmosphere"].ToString();
				}
				if (row["FamilyAtmosphereExt"] != null)
				{
					model.FamilyAtmosphereExt = row["FamilyAtmosphereExt"].ToString();
				}
				if (row["Accommodation"] != null)
				{
					model.Accommodation = row["Accommodation"].ToString();
				}
				if (row["AccommodationExt"] != null)
				{
					model.AccommodationExt = row["AccommodationExt"].ToString();
				}
				if (row["ZDJB"] != null)
				{
					model.ZDJB = row["ZDJB"].ToString();
				}
				if (row["ZDJB_Ext"] != null)
				{
					model.ZDJB_Ext = row["ZDJB_Ext"].ToString();
				}
				if (row["LongMedicationUse"] != null)
				{
					model.LongMedicationUse = row["LongMedicationUse"].ToString();
				}
				if (row["LongMedicationUseExt"] != null)
				{
					model.LongMedicationUseExt = row["LongMedicationUseExt"].ToString();
				}
				if (row["PhysicalLimitations"] != null)
				{
					model.PhysicalLimitations = row["PhysicalLimitations"].ToString();
				}
				if (row["JWXLZD"] != null)
				{
					model.JWXLZD = row["JWXLZD"].ToString();
				}
				if (row["JWXLZD_Ext"] != null)
				{
					model.JWXLZD_Ext = row["JWXLZD_Ext"].ToString();
				}
				if (row["SuicidalReport"] != null)
				{
					model.SuicidalReport = row["SuicidalReport"].ToString();
				}
				if (row["SuicidalReportExt"] != null)
				{
					model.SuicidalReportExt = row["SuicidalReportExt"].ToString();
				}
				if (row["AbnormalBehavior"] != null)
				{
					model.AbnormalBehavior = row["AbnormalBehavior"].ToString();
				}
				if (row["AbnormalBehaviorExt"] != null)
				{
					model.AbnormalBehaviorExt = row["AbnormalBehaviorExt"].ToString();
				}
				if (row["AbnormalStatus"] != null)
				{
					model.AbnormalStatus = row["AbnormalStatus"].ToString();
				}
				if (row["DSTeacherId"] != null && row["DSTeacherId"].ToString() != "")
				{
					model.DSTeacherId = new Guid(row["DSTeacherId"].ToString());
				}
				if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
				{
					model.LastEditBy = new Guid(row["LastEditBy"].ToString());
				}
				if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
				{
					model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
				}
				if (row["EducationMethodExt"] != null)
				{
					model.EducationMethodExt = row["EducationMethodExt"].ToString();
				}
				if (row["ParentRelationExt"] != null)
				{
					model.ParentRelationExt = row["ParentRelationExt"].ToString();
				}
			}
			return model;
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,StudentId,FamilyStructure,FamilyStructureExt,FamilyEconomics,EducationMethod,ParentRelation,FamilyAtmosphere,FamilyAtmosphereExt,Accommodation,AccommodationExt,ZDJB,ZDJB_Ext,LongMedicationUse,LongMedicationUseExt,PhysicalLimitations,JWXLZD,JWXLZD_Ext,SuicidalReport,SuicidalReportExt,AbnormalBehavior,AbnormalBehaviorExt,AbnormalStatus,DSTeacherId,LastEditBy,LastEditTime,EducationMethodExt,ParentRelationExt ");
			strSql.Append(" FROM psych_basicInfo ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,StudentId,FamilyStructure,FamilyStructureExt,FamilyEconomics,EducationMethod,ParentRelation,FamilyAtmosphere,FamilyAtmosphereExt,Accommodation,AccommodationExt,ZDJB,ZDJB_Ext,LongMedicationUse,LongMedicationUseExt,PhysicalLimitations,JWXLZD,JWXLZD_Ext,SuicidalReport,SuicidalReportExt,AbnormalBehavior,AbnormalBehaviorExt,AbnormalStatus,DSTeacherId,LastEditBy,LastEditTime,EducationMethodExt,ParentRelationExt ");
			strSql.Append(" FROM psych_basicInfo ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM psych_basicInfo ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from psych_basicInfo T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_basicInfo";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 获取教师列表
		/// </summary>
		/// <param name="columnId">栏目ID</param>
		/// <returns>教师数据集</returns>
		public DataSet GetTeacherList(int columnId)
		{
			string sql = @"SELECT u.UserInfoID as Id, u.TName as Name, 
                        (SELECT COUNT(1) FROM psych_basicInfo pb WHERE pb.DSTeacherId = u.UserInfoID) as StudentCount
                        FROM UserInfos u 
                        WHERE u.ColumnId = @ColumnId AND u.UserTypeID = 3 AND (u.IsDelete = 0 OR u.IsDelete IS NULL)
                        ORDER BY u.TName";

			SqlParameter[] parameters = {
				new SqlParameter("@ColumnId", columnId)
			};

			return DbHelperSQL.Query(sql, parameters);
		}

		/// <summary>
		/// 获取学生列表
		/// </summary>
		/// <param name="columnId">栏目ID</param>
		/// <param name="classId">班级ID</param>
		/// <returns>学生数据集</returns>
		public DataSet GetStudentList(int columnId, string classId,string type)
		{
			string sql = $@"SELECT 
                        s.ID as StudentId, 
                        s.StudentName as Name, 
                        {(type == "bg" ? "BGTeacherId" : "DSTeacherId")} as TeacherId,
                        u.TName as TeacherName
                        FROM JC_StudentInfos s
                        LEFT JOIN psych_basicInfo pb ON s.ID = pb.StudentId
                        LEFT JOIN UserInfos u ON pb.{(type == "bg" ? "BGTeacherId" : "DSTeacherId")} = u.UserID
                        WHERE s.ClassID = @ClassId AND s.ColumnId = @ColumnId
                        ORDER BY s.StudentName";

			SqlParameter[] parameters = {
				new SqlParameter("@ClassId", classId),
				new SqlParameter("@ColumnId", columnId)
			};

			return DbHelperSQL.Query(sql, parameters);
		}

		/// <summary>
		/// 获取教师的学生列表
		/// </summary>
		/// <param name="teacherId">教师ID</param>
		/// <returns>学生数据集</returns>
		public DataSet GetTeacherStudents(string where)
		{
			string sql = $@"SELECT s.ID as StudentId, s.StudentName as Name, c.ClassName
                        FROM JC_StudentInfos s 
                        INNER JOIN psych_basicInfo pb ON s.ID = pb.StudentId 
                        LEFT JOIN JC_ClassInfos c ON s.ClassID = c.ID
                        WHERE {where}
                        ORDER BY c.ClassName, s.StudentName";

			return DbHelperSQL.Query(sql);
		}


		/// <summary>
		/// 更新学生导师
		/// </summary>
		/// <param name="studentId">学生ID</param>
		/// <param name="teacherId">教师ID</param>
		/// <param name="columnId">栏目ID</param>
		/// <param name="userId">操作用户ID</param>
		/// <returns>操作结果</returns>
		public bool UpdateStudentTeacher(string studentId, string teacherId, int columnId, string userId,string type)
		{
			string sql = $"UPDATE psych_basicInfo SET {(type=="bg"? "BGTeacherId" : "DSTeacherId")} = @TeacherId, LastEditBy = @UserId, LastEditTime = GETDATE() WHERE StudentId = @StudentId AND ColumnId = @ColumnId";

			SqlParameter[] parameters = {
				new SqlParameter("@TeacherId", teacherId),
				new SqlParameter("@StudentId", studentId),
				new SqlParameter("@ColumnId", columnId),
				new SqlParameter("@UserId", userId)
			};

			return DbHelperSQL.ExecuteSql(sql, parameters) > 0;
		}

		/// <summary>
		/// 创建学生基本信息记录
		/// </summary>
		/// <param name="studentId">学生ID</param>
		/// <param name="teacherId">教师ID</param>
		/// <param name="columnId">栏目ID</param>
		/// <param name="columnPath">栏目路径</param>
		/// <param name="userId">操作用户ID</param>
		/// <returns>操作结果</returns>
		public bool CreateStudentInfo(string studentId, string teacherId, int columnId, string columnPath, string userId,string type)
		{
			string sql = $@"INSERT INTO psych_basicInfo 
                        (Id, ColumnId, ColumnPath, StudentId, FamilyStructure, FamilyStructureExt, 
                        FamilyEconomics, EducationMethod, ParentRelation, FamilyAtmosphere, 
                        FamilyAtmosphereExt, Accommodation, AccommodationExt, ZDJB, ZDJB_Ext, 
                        LongMedicationUse, LongMedicationUseExt, PhysicalLimitations, JWXLZD, 
                        JWXLZD_Ext, SuicidalReport, SuicidalReportExt, AbnormalBehavior, 
                        AbnormalBehaviorExt, AbnormalStatus, {(type == "bg" ? "BGTeacherId" : "DSTeacherId")}, LastEditBy, LastEditTime)
                        VALUES
                        (NEWID(), @ColumnId, @ColumnPath, @StudentId, N'', N'', 
                        N'', N'', N'', N'', 
                        N'', N'', N'', N'', N'', 
                        N'', N'', N'', N'', 
                        N'', N'', N'', N'', 
                        N'', N'', @TeacherId, @UserId, GETDATE())";

			SqlParameter[] parameters = {
				new SqlParameter("@ColumnId", columnId),
				new SqlParameter("@ColumnPath", columnPath),
				new SqlParameter("@StudentId", studentId),
				new SqlParameter("@TeacherId", teacherId),
				new SqlParameter("@UserId", userId)
			};

			return DbHelperSQL.ExecuteSql(sql, parameters) > 0;
		}

		/// <summary>
		/// 获取学生所在班级路径
		/// </summary>
		/// <param name="studentId">学生ID</param>
		/// <returns>班级路径</returns>
		public string GetStudentColumnPath(string studentId)
		{
			string sql = "SELECT ColumnPath FROM JC_StudentInfos WHERE ID = @StudentId";

			SqlParameter[] parameters = {
				new SqlParameter("@StudentId", studentId)
			};

			object result = DbHelperSQL.GetSingle(sql, parameters);
			return result != null ? result.ToString() : "";
		}

		/// <summary>
		/// 清除教师的所有学生分配
		/// </summary>
		/// <param name="teacherId">教师ID</param>
		/// <returns>操作结果</returns>
		public bool ClearTeacherStudents(string teacherId,string type="")
		{
			string sql = $"UPDATE psych_basicInfo SET {(type == "bg" ? "BGTeacherId" : "DSTeacherId")} = NULL WHERE {(type == "bg" ? "BGTeacherId" : "DSTeacherId")} = @TeacherId";

			SqlParameter[] parameters = {
				new SqlParameter("@TeacherId", teacherId)
			};

			return DbHelperSQL.ExecuteSql(sql, parameters) > 0;
		}

		/// <summary>
		/// 批量为学生分配导师
		/// </summary>
		/// <param name="teacherId">教师ID</param>
		/// <param name="studentIds">学生ID列表</param>
		/// <param name="columnId">栏目ID</param>
		/// <param name="userId">操作用户ID</param>
		/// <returns>成功分配的数量</returns>
		public int AssignTeacherToStudents(string teacherId, List<string> studentIds, int columnId, string userId,string type)
		{
			int successCount = 0;

			foreach (string studentId in studentIds)
			{
				if (GetRecordCount($"StudentId = '{studentId}' AND ColumnId = {columnId}" )>0)
				{
					// 更新已存在记录
					if (UpdateStudentTeacher(studentId, teacherId, columnId, userId, type))
					{
						successCount++;
					}
				}
				else
				{
					// 获取学生所在的班级路径
					string columnPath = GetStudentColumnPath(studentId);

					// 创建新记录
					if (CreateStudentInfo(studentId, teacherId, columnId, columnPath, userId, type))
					{
						successCount++;
					}
				}
			}

			return successCount;
		}

		/// <summary>
		/// 获取学生心理信息
		/// </summary>
		public DataSet GetStudentPsychInfo(string studentId)
		{
			StringBuilder psychSql = new StringBuilder();
			psychSql.Append("SELECT p.*, d1.ItemText AS ZDJBText, d2.ItemText AS LongMedicationUseText, ");
			psychSql.Append("d3.ItemText AS PhysicalLimitationsText, d4.ItemText AS JWXLZDText ");
			psychSql.Append("FROM psych_basicInfo p ");
			psychSql.Append("LEFT JOIN psych_dict d1 ON p.ZDJB = d1.ItemValue AND d1.DictType = 'ZDJB' ");
			psychSql.Append("LEFT JOIN psych_dict d2 ON p.LongMedicationUse = d2.ItemValue AND d2.DictType = 'LongMedicationUse' ");
			psychSql.Append("LEFT JOIN psych_dict d3 ON p.PhysicalLimitations = d3.ItemValue AND d3.DictType = 'PhysicalLimitations' ");
			psychSql.Append("LEFT JOIN psych_dict d4 ON p.JWXLZD = d4.ItemValue AND d4.DictType = 'JWXLZD' ");
			psychSql.Append("WHERE p.StudentId = @StudentId");

			SqlParameter[] psychParams = {
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier)
				};
			psychParams[0].Value = new Guid(studentId);

			return DbHelperSQL.Query(psychSql.ToString(), psychParams);
		}

		/// <summary>
		/// 获取学生导师配置名单
		/// </summary>
		/// <param name="columnId">地区ID</param>
		/// <returns>学生导师配置数据</returns>
		public DataSet GetStudentTeacherConfigList_ds(int columnId)
		{
			string sql = @"SELECT c.ClassName as '班级',
	                        b.StudentName as '学生姓名',
	                        ISNULL(t.TName, '') as '导师姓名'
	                        FROM JC_StudentInfos b
	                        LEFT JOIN psych_basicInfo a ON b.ID=a.StudentId
	                        LEFT JOIN JC_ClassInfos c ON b.ClassID=c.ID
	                        LEFT JOIN UserInfos t ON a.DSTeacherId=t.UserID
	                        WHERE b.ColumnId=@ColumnId and IsGraduation<>1
	                        ORDER BY c.GradeId,c.OrderId,b.StudentName";

			SqlParameter[] parameters = {
				new SqlParameter("@ColumnId", SqlDbType.Int)
			};
			parameters[0].Value = columnId;

			return DbHelperSQL.Query(sql, parameters);
		}

		/// <summary>
		/// 获取学生行政包干配置名单
		/// </summary>
		/// <param name="columnId">地区ID</param>
		/// <returns>学生导师配置数据</returns>
		public DataSet GetStudentTeacherConfigList_bg(int columnId)
		{
			string sql = @"SELECT c.ClassName as '班级',
	                        b.StudentName as '学生姓名',
	                        ISNULL(t.TName, '') as '行政包干'
							FROM JC_StudentInfos b
	                        LEFT JOIN psych_basicInfo a ON b.ID=a.StudentId
	                        LEFT JOIN JC_ClassInfos c ON b.ClassID=c.ID
	                        LEFT JOIN UserInfos t ON a.BGTeacherId=t.UserID
	                        WHERE b.ColumnId=@ColumnId and IsGraduation<>1
	                        ORDER BY c.GradeId,c.OrderId,b.StudentName";

			SqlParameter[] parameters = {
				new SqlParameter("@ColumnId", SqlDbType.Int)
			};
			parameters[0].Value = columnId;

			return DbHelperSQL.Query(sql, parameters);
		}
		/// <summary>
		/// 得到基本信息实体对象
		/// </summary>
		/// <param name="studentId">学生id</param>
		/// <returns><see cref="psych_basicInfo"/>对象</returns>
		public Model.psych_basicInfo GetModelByStudentId(Guid studentId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,FamilyStructure,FamilyStructureExt,FamilyEconomics,EducationMethod,ParentRelation,FamilyAtmosphere,FamilyAtmosphereExt,Accommodation,AccommodationExt,ZDJB,ZDJB_Ext,LongMedicationUse,LongMedicationUseExt,PhysicalLimitations,JWXLZD,JWXLZD_Ext,SuicidalReport,SuicidalReportExt,AbnormalBehavior,AbnormalBehaviorExt,AbnormalStatus,DSTeacherId,LastEditBy,LastEditTime,EducationMethodExt,ParentRelationExt  from psych_basicInfo ");
			strSql.Append(" where StudentId=@StudentId ");
			SqlParameter[] parameters = {
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16)
			};
			parameters[0].Value = studentId;
			DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		/// 获取异常状态统计数据
		/// 功能：统计指定地区和时间范围内学生的异常状态分布
		/// 关键逻辑：关联学生信息表进行权限过滤，按异常状态分组统计
		/// </summary>
		/// <param name="nColumnId">地区ID</param>
		/// <param name="strGradeId">年级ID（可选）</param>
		/// <param name="strClassId">班级ID（可选）</param>
		/// <returns>异常状态统计数据表</returns>
		public DataTable GetAbnormalStatistics(int nColumnId, string strGradeId = "", string strClassId = "")
		{
			string strSql = @"
                SELECT p.AbnormalStatus AS StatusValue, COUNT(p.Id) AS Count
                FROM psych_basicInfo p
                INNER JOIN JC_StudentInfos s ON p.StudentId = s.ID
                WHERE s.ColumnId = " + nColumnId;

			// 添加年级过滤条件
			if (!string.IsNullOrEmpty(strGradeId))
			{
				strSql += " AND s.GradeID = '" + strGradeId + "'";
			}

			// 添加班级过滤条件
			if (!string.IsNullOrEmpty(strClassId))
			{
				strSql += " AND s.ClassID = '" + strClassId + "'";
			}

			strSql += @"
                AND p.AbnormalStatus IS NOT NULL AND p.AbnormalStatus <> ''
                GROUP BY p.AbnormalStatus";

			DataSet ds = DbHelperSQL.Query(strSql);
			return ds != null && ds.Tables.Count > 0 ? ds.Tables[0] : new DataTable();
		}

		/// <summary>
		/// 获取学生跟进总统计子表的查询条件
		/// </summary>
		/// <param name="category">跟进类别</param>
		/// <param name="columnId">学校id</param>
		/// <param name="dteBegin">开始时间</param>
		/// <param name="dteEnd">结束时间</param>
		/// <param name="teacherName">教师姓名</param>
		/// <returns></returns>
		private string GetStudentGJTotalQueryTableWhere(string category, int columnId, DateTime? dteBegin, DateTime? dteEnd, string teacherName)
		{
			StringBuilder where = new StringBuilder();
			string timeField = "";
			string teacherNameField = "";
			switch (category)
			{
				case "HomeViist": // 家访
					timeField = "t.InterviewTime";
					teacherNameField = "p.CName";
					break;
				case "Interview":// 访谈
					timeField = "t.InterviewTime";
					teacherNameField = "p.CName";
					break;
				case "PsychTest":// 心理测评
					timeField = "m.CreateTime";
					teacherNameField = "p.CName";
					break;
				case "WeekDiary":// 周记
					timeField = "t.CreateTime";
					teacherNameField = "p.CName";
					break;
				default:
					return "where 1=2";
			}
			where.Append($"where t.ColumnID={columnId}");
			// 开始时间筛选             
			if (dteBegin != null)
			{
				where.Append($" and {timeField}>='{dteBegin:yyyy-MM-dd}'");
			}
			// 结束时间筛选
			if (dteEnd != null)
			{
				where.Append($" and {timeField}<'{dteEnd.Value.AddDays(1):yyyy-MM-dd}'");
			}
			// 跟进人筛选
			if (!string.IsNullOrEmpty(teacherName))
			{
				where.Append($" and {teacherNameField} like '%{DataSecurity.FilteSQLStr(teacherName)}%'");
			}
			return where.ToString();
		}

		/// <summary>
		/// 获取学生跟进总统计
		/// </summary>
		/// <param name="columnId">学校id</param>
		/// <param name="gradeId">年级Id</param>
		/// <param name="classId">班级Id</param>
		/// <param name="dteBegin">开始时间</param>
		/// <param name="dteEnd">结束时间</param>
		/// <param name="studentName">学生姓名</param>
		/// <param name="teacherName">教师姓名</param>
		/// <returns></returns>
		public DataSet GetStudentGJTotalStatistics(int columnId, Guid gradeId, Guid classId, DateTime? dteBegin, DateTime? dteEnd, string studentName, string teacherName)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.AppendLine("select g.Category,Count(g.Id) Num");
			strSql.AppendLine(" from (");
			strSql.AppendLine($"select Id,p.ColumnId,t.StudentId,'HomeViist' Category from psych_home_visit t left join aspnet_Membership p on t.UserId=p.UserId {GetStudentGJTotalQueryTableWhere("HomeViist", columnId, dteBegin, dteEnd, teacherName)} and t.HomeVisitContent is not null");
			strSql.AppendLine("union all");
			strSql.AppendLine($"select Id,p.ColumnId,t.StudentId,'Interview' Category from psych_interview t left join aspnet_Membership p on t.UserId=p.UserId {GetStudentGJTotalQueryTableWhere("Interview", columnId, dteBegin, dteEnd, teacherName)}");
			strSql.AppendLine("union all");
			strSql.AppendLine($"select t.Id,p.ColumnId,t.StudentId,'PsychTest' Category from psych_test_result t left join psych_test m on t.Id=m.Id left join aspnet_Membership p on m.CreatorId=p.UserId {GetStudentGJTotalQueryTableWhere("PsychTest", columnId, dteBegin, dteEnd, teacherName)}");
			strSql.AppendLine("union all");
			strSql.AppendLine($"select Id,p.ColumnId,t.StudentId,'WeekDiary' Category from psych_week_diary_abnormal t left join aspnet_Membership p on t.CreatorId=p.UserId {GetStudentGJTotalQueryTableWhere("WeekDiary", columnId, dteBegin, dteEnd, teacherName)}");
			strSql.AppendLine(") g left join JC_StudentInfos b on g.StudentId=b.ID ");
			strSql.AppendLine($"where g.ColumnId={columnId}");
			// 年级筛选
			if (gradeId != Guid.Empty)
			{
				strSql.AppendLine($" and b.GradeID='{gradeId}'");
			}
			// 班级筛选
			if (classId != Guid.Empty)
			{
				strSql.Append($" and b.ClassID='{classId}'");
			}
			if (!string.IsNullOrEmpty(studentName))
			{
				strSql.Append($" AND (b.StudentName LIKE '%{DataSecurity.FilteSQLStr(studentName)}%')");
			}
			strSql.AppendLine("group by g.Category");
			//strSql.AppendLine(") a");

			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取跟进统计子表的查询条件
		/// </summary>
		/// <param name="gjCategory">跟进类别</param>       
		/// <param name="columnId">学校id</param>
		/// <param name="gradeId">年级Id</param>
		///  <param name="classId">班级Id</param>
		/// <param name="dteBegin">开始时间</param>
		/// <param name="dteEnd">结束时间</param>
		/// <param name="teacherName">教师姓名</param>
		/// <returns></returns>
		private string GetTeacherGJTotalQueryTableWhere(string gjCategory, int columnId, Guid gradeId, Guid classId, DateTime? dteBegin, DateTime? dteEnd, string teacherName)
		{
			StringBuilder where = new StringBuilder();
			string timeField = "";
			string teacherNameField = "";
			switch (gjCategory)
			{
				case "HomeViist": // 家访
					timeField = "t.InterviewTime";
					teacherNameField = "p.CName";
					break;
				case "Interview":// 访谈
					timeField = "t.InterviewTime";
					teacherNameField = "p.CName";
					break;
				case "PsychTest":// 心理测评
					timeField = "t.CreateTime";
					teacherNameField = "p.CName";
					break;
				case "WeekDiary":// 周记
					timeField = "t.CreateTime";
					teacherNameField = "p.CName";
					break;
				default:
					return "where 1=2";
			}
			where.Append($"where t.ColumnID={columnId}");
			// 开始时间筛选             
			if (dteBegin != null)
			{
				where.Append($" and {timeField}>='{dteBegin:yyyy-MM-dd}'");
			}
			// 结束时间筛选
			if (dteEnd != null)
			{
				where.Append($" and {timeField}<'{dteEnd.Value.AddDays(1):yyyy-MM-dd}'");
			}
			// 年级筛选
			if (gradeId != Guid.Empty)
			{
				where.Append($" and st.GradeID='{gradeId}'");
			}
			// 班级筛选
			if (classId != Guid.Empty)
			{
				where.Append($" and st.ClassID='{classId}'");
			}
			// 跟进人筛选
			if (!string.IsNullOrEmpty(teacherName))
			{
				where.Append($" and {teacherNameField} like '%{DataSecurity.FilteSQLStr(teacherName)}%'");
			}
			return where.ToString();
		}

		/// <summary>
		/// 获取教师跟进统计
		/// </summary>
		/// <param name="columnId">学校id</param>
		/// <param name="gradeId">年级Id</param>
		///  <param name="classId">班级Id</param>
		/// <param name="dteBegin">开始时间</param>
		/// <param name="dteEnd">结束时间</param>
		/// <param name="teacherName">教师姓名</param>
		/// <returns></returns>
		public DataSet GetTeacherGJTotalStatistics(int columnId, Guid gradeId, Guid classId, DateTime? dteBegin, DateTime? dteEnd, string teacherName)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.AppendLine("select g.Category,Count(g.Id) Num");
			strSql.AppendLine(" from (");
			strSql.AppendLine($"select t.Id,p.ColumnId,'HomeViist' Category from psych_home_visit t left join aspnet_Membership p on t.UserId=p.UserId left join JC_StudentInfos st on t.StudentId=st.ID {GetTeacherGJTotalQueryTableWhere("HomeViist", columnId, gradeId, classId, dteBegin, dteEnd, teacherName)} and t.HomeVisitContent is not null");
			strSql.AppendLine("union all");
			strSql.AppendLine($"select t.Id,p.ColumnId,'Interview' Category from psych_interview t left join aspnet_Membership p on t.UserId=p.UserId left join JC_StudentInfos st on t.StudentId=st.ID {GetTeacherGJTotalQueryTableWhere("Interview", columnId, gradeId, classId, dteBegin, dteEnd, teacherName)}");
			strSql.AppendLine("union all");
			//strSql.AppendLine($"select t.Id,p.ColumnId,'PsychTest' Category from psych_test t left join aspnet_Membership p on t.CreatorId=p.UserId left join JC_StudentInfos st on t.StudentId=st.ID {GetTeacherGJTotalQueryTableWhere("PsychTest", columnId, dteBegin, dteEnd, teacherName)}");
			//strSql.AppendLine("union all");
			strSql.AppendLine($"select t.Id,p.ColumnId,'WeekDiary' Category from psych_week_diary_abnormal t left join aspnet_Membership p on t.CreatorId=p.UserId left join JC_StudentInfos st on t.StudentId=st.ID {GetTeacherGJTotalQueryTableWhere("WeekDiary", columnId, gradeId, classId, dteBegin, dteEnd, teacherName)}");
			strSql.AppendLine(") g ");
			strSql.AppendLine($"where g.ColumnId={columnId}");
			strSql.AppendLine("group by g.Category");

			return DbHelperSQL.Query(strSql.ToString());
		}
		/// <summary>
		/// 更新基础信息
		/// </summary>
		/// <param name="model">健康档案信息</param>
		/// <param name="modelWarningRecord">预警信息</param>
		/// <param name="msgList">消息列表</param>
		/// <param name="receiveList">消息接收列表</param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		public bool UpdateTran(Model.psych_basicInfo model, Model.psych_warning_record modelWarningRecord, List<Model.xy_app_msg> msgList, List<Model.xy_app_msg_receive> receiveList)
		{
			int rows = 0;
			//添加事物
			using (SqlConnection conn = new SqlConnection(DbHelperSQL.ConnMain[0]))
			{
				conn.Open();
				SqlCommand cmd = new SqlCommand();
				cmd.Connection = conn;
				SqlTransaction tx = conn.BeginTransaction();
				try
				{
					StringBuilder strSql = new StringBuilder();
					StringBuilder strInfos = new StringBuilder();
					SqlParameter[] parameters = {
							new SqlParameter("@StudentId",SqlDbType.UniqueIdentifier,16)
						};
					parameters[0].Value = model.StudentId;
					// 删除消息接收列表
					strSql.Append($"delete from xy_app_msg_receive where msgId in (select Id from xy_app_msg where businessId in (select cast(Id as nvarchar(255)) from psych_warning_record a where a.StudentId=@StudentId and a.SourceType={(int)CommonEnum.WarningSourceType.Info}))");
					SqlParameter[] paramDelete = {
							new SqlParameter("@StudentId",SqlDbType.UniqueIdentifier,16)
						};
					paramDelete[0].Value = model.StudentId;
					DbHelperSQL.PrepareCommand(cmd, conn, tx, strSql.ToString(), parameters, strInfos);
					rows = cmd.ExecuteNonQuery();
					cmd.Parameters.Clear();
					// 删除消息主表记录
					strSql.Clear();
					strSql.Append($"delete from xy_app_msg where businessId in (select cast(Id as nvarchar(255)) from psych_warning_record a where a.StudentId=@StudentId and a.SourceType={(int)CommonEnum.WarningSourceType.Info})");
					DbHelperSQL.PrepareCommand(cmd, conn, tx, strSql.ToString(), parameters, strInfos);
					rows = cmd.ExecuteNonQuery();
					cmd.Parameters.Clear();
					// 删除预警记录
					strSql.Clear();
					strSql.Append($"delete from psych_warning_record where StudentId=@StudentId and SourceType={(int)CommonEnum.WarningSourceType.Info}");
					DbHelperSQL.PrepareCommand(cmd, conn, tx, strSql.ToString(), parameters, strInfos);
					rows = cmd.ExecuteNonQuery();
					cmd.Parameters.Clear();

					// 需要预警
					if (modelWarningRecord != null)
					{
						strSql.Append("insert into psych_warning_record(");
						strSql.Append("Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,WarningStatus,CreateTime,Creator,IsInterfere)");
						strSql.Append(" values (");
						strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@SourceType,@SourceId,@WarningStatus,@CreateTime,@Creator,@IsInterfere)");
						parameters = new SqlParameter[] {
								new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
								new SqlParameter("@ColumnId", SqlDbType.Int,4),
								new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
								new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
								new SqlParameter("@SourceType", SqlDbType.Int,4),
								new SqlParameter("@SourceId", SqlDbType.UniqueIdentifier,16),
								new SqlParameter("@WarningStatus", SqlDbType.NVarChar,2),
								new SqlParameter("@CreateTime", SqlDbType.DateTime),
								new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
								new SqlParameter("@IsInterfere",SqlDbType.Int,4)};
						parameters[0].Value = modelWarningRecord.Id;
						parameters[1].Value = modelWarningRecord.ColumnId;
						parameters[2].Value = modelWarningRecord.ColumnPath;
						parameters[3].Value = modelWarningRecord.StudentId;
						parameters[4].Value = modelWarningRecord.SourceType;
						parameters[5].Value = modelWarningRecord.SourceId;
						parameters[6].Value = modelWarningRecord.WarningStatus;
						parameters[7].Value = modelWarningRecord.CreateTime;
						parameters[8].Value = modelWarningRecord.Creator;
						parameters[9].Value = modelWarningRecord.IsInterfere;
						DbHelperSQL.PrepareCommand(cmd, conn, tx, strSql.ToString(), parameters, strInfos);
						rows = cmd.ExecuteNonQuery();
						cmd.Parameters.Clear();
						// 插入预警记录成功
						if (rows > 0 && msgList != null)
						{
							// 插入消息主表
							foreach (var item in msgList)
							{
								strSql.Clear();
								strSql.Append("insert into xy_app_msg(");
								strSql.Append("id,msg_title,msg_content,gt_result,gt_taskId,gt_status,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num");
								strSql.Append(") values (");
								strSql.Append("@id,@msg_title,@msg_content,@gt_result,@gt_taskId,@gt_status,@cateId,@catePath,@businessId,@send_time,@send_userId,@msg_type,@receive_type,@receive_num");
								strSql.Append(") ");
								parameters = new SqlParameter[] {
									new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
									new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
									new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
									new SqlParameter("@gt_result", SqlDbType.NVarChar,50),
									new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50),
									new SqlParameter("@gt_status", SqlDbType.NVarChar,50),
									new SqlParameter("@cateId", SqlDbType.NVarChar,50),
									new SqlParameter("@catePath", SqlDbType.NVarChar,50),
									new SqlParameter("@businessId", SqlDbType.NVarChar,50),
									new SqlParameter("@send_time", SqlDbType.DateTime),
									new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16),
									new SqlParameter("@msg_type", SqlDbType.Int,4),
									new SqlParameter("@receive_type", SqlDbType.Int,4),
									new SqlParameter("@receive_num", SqlDbType.Int,4)};

								parameters[0].Value = item.id;
								parameters[1].Value = item.msg_title;
								parameters[2].Value = item.msg_content;
								parameters[3].Value = item.gt_result;
								parameters[4].Value = item.gt_taskId;
								parameters[5].Value = item.gt_status;
								parameters[6].Value = item.cateId;
								parameters[7].Value = item.catePath;
								parameters[8].Value = item.businessId;
								parameters[9].Value = item.send_time;
								parameters[10].Value = item.send_userId;
								parameters[11].Value = item.msg_type;
								parameters[12].Value = item.receive_type;
								parameters[13].Value = item.receive_num;
								DbHelperSQL.PrepareCommand(cmd, conn, tx, strSql.ToString(), parameters, strInfos);
								rows = cmd.ExecuteNonQuery();
								cmd.Parameters.Clear();
								if (rows <= 0) break; // 插入失败直接跳出循环
							}
							if (rows > 0 && receiveList != null)
							{
								// 插入消息副表
								foreach (var item in receiveList)
								{
									strSql.Clear();
									strSql.Append("insert into xy_app_msg_receive(");
									strSql.Append("id,msgId,cateId,catePath,userId,isRead,msg_title,msg_content,send_time");
									strSql.Append(") values (");
									strSql.Append("@id,@msgId,@cateId,@catePath,@userId,@isRead,@msg_title,@msg_content,@send_time");
									strSql.Append(")");
									parameters = new SqlParameter[] {
											new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
											new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16),
											new SqlParameter("@cateId", SqlDbType.NVarChar,50),
											new SqlParameter("@catePath", SqlDbType.NVarChar,50),
											new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16),
											new SqlParameter("@isRead", SqlDbType.Int,4),
											new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
											new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
											new SqlParameter("@send_time", SqlDbType.DateTime)
										};
									parameters[0].Value = Guid.NewGuid();
									parameters[1].Value = item.msgId;
									parameters[2].Value = item.cateId;
									parameters[3].Value = item.catePath;
									parameters[4].Value = item.userId;
									parameters[5].Value = 0;
									parameters[6].Value = item.msg_title;
									parameters[7].Value = item.msg_content;
									parameters[8].Value = item.send_time;

									DbHelperSQL.PrepareCommand(cmd, conn, tx, strSql.ToString(), parameters, strInfos);
									rows = cmd.ExecuteNonQuery();
									cmd.Parameters.Clear();
									if (rows <= 0) break; // 插入失败直接跳出循环
								}
							}
						}
					}

					tx.Commit();
				}
				catch (Exception e)
				{
					rows = 0;
					tx.Rollback();
					throw new Exception(e.Message);
				}
				finally
				{
					cmd.Dispose();
					conn.Close();
				}
			}
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		#endregion  ExtensionMethod
	}
}