﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 奖品管理
	/// </summary>
	[Serializable]
	public partial class zhsz_prize
	{
		public zhsz_prize()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _prizecode;
		private string _prizename;
		private decimal _redeempoint;
		private string _imgs;
		private int _isshelves;
		private int _stocknum;
		private Guid _lasteditby;
		private DateTime _lastedittime;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 奖品编码 唯一
		/// </summary>
		public string PrizeCode
		{
			set{ _prizecode=value;}
			get{return _prizecode;}
		}
		/// <summary>
		/// 奖品名称
		/// </summary>
		public string PrizeName
		{
			set{ _prizename=value;}
			get{return _prizename;}
		}
		/// <summary>
		/// 兑换积分
		/// </summary>
		public decimal RedeemPoint
		{
			set{ _redeempoint=value;}
			get{return _redeempoint;}
		}
		/// <summary>
		/// 奖品图片 多个图竖线分割
		/// </summary>
		public string Imgs
		{
			set{ _imgs=value;}
			get{return _imgs;}
		}
		/// <summary>
		/// 是否上架 0未上架 1上架
		/// </summary>
		public int IsShelves
		{
			set{ _isshelves=value;}
			get{return _isshelves;}
		}
		/// <summary>
		/// 库存数量
		/// </summary>
		public int StockNum
		{
			set{ _stocknum=value;}
			get{return _stocknum;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

