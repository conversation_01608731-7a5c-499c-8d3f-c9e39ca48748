﻿﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// custom_Reward_record:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class custom_Reward_record
	{
		public custom_Reward_record()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _userid;
		private DateTime? _rewardtime;
		private string _rewardname;
		private string _rewardlevel;
		private string _toawardsunit;
		private Guid _creatorid;
		private DateTime? _createtime;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? RewardTime
		{
			set{ _rewardtime=value;}
			get{return _rewardtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RewardName
		{
			set{ _rewardname=value;}
			get{return _rewardname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RewardLevel
		{
			set{ _rewardlevel=value;}
			get{return _rewardlevel;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ToAawardsUnit
		{
			set{ _toawardsunit=value;}
			get{return _toawardsunit;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid CreatorId
		{
			set{ _creatorid=value;}
			get{return _creatorid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}
