﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 校历事件
	/// </summary>
	[Serializable]
	public partial class ecb_Event
	{
		public ecb_Event()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _recordid;
		private DateTime _stadate;
		private DateTime? _enddate;
		private string _remark;
		private int _type;
		private Guid _creator;
		private DateTime _createtime;
		/// <summary>
		/// 事件编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnID
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 校历ID
		/// </summary>
		public Guid RecordId
		{
			set{ _recordid=value;}
			get{return _recordid;}
		}
		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime StaDate
		{
			set{ _stadate=value;}
			get{return _stadate;}
		}
		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime ?EndDate
		{
			set{ _enddate=value;}
			get{return _enddate;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Remark
		{
			set{ _remark=value;}
			get{return _remark;}
		}
        /// <summary>
        /// 假期类型  1放假 2调休 3其他
        /// </summary>
        public int Type
		{
			set{ _type = value;}
			get{return _type; }
		}
		/// <summary>
		/// 创建人ID
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}

