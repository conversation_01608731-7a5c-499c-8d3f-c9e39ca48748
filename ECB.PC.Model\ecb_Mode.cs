﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 留言表
	/// </summary>
	[Serializable]
	public partial class ecb_Mode
	{
		public ecb_Mode()
		{}
		#region Model
		private Guid _id;
		private string _modenam;
		private string _modepath;
		private int _modetype;
		private int _modelevel;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 模式名称
		/// </summary>
		public string ModeNam
		{
			set{ _modenam=value;}
			get{return _modenam;}
		}
		/// <summary>
		/// 模式地址
		/// </summary>
		public string ModePath
		{
			set{ _modepath=value;}
			get{return _modepath;}
		}
		/// <summary>
		/// 模式类型
		/// </summary>
		public int ModeType
		{
			set{ _modetype=value;}
			get{return _modetype;}
		}
		/// <summary>
		/// 模式级别
		/// </summary>
		public int ModeLevel
		{
			set{ _modelevel=value;}
			get{return _modelevel;}
		}
		#endregion Model

	}
}

