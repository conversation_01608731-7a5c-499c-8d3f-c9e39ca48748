﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_reboot_rule:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_reboot_rule
	{
		public ecb_reboot_rule()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private string _rulename;
		private string _ids;
		private DateTime? _lastedittime;
		private Guid _lasteditor;
        public bool IsUpdate;//自定义字段 用于事务判断
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区Id
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 规则名
		/// </summary>
		public string RuleName
		{
			set{ _rulename=value;}
			get{return _rulename;}
		}
		/// <summary>
		/// 适用对象，班牌类型，年级，班级以|分隔
		/// </summary>
		public string Ids
		{
			set{ _ids=value;}
			get{return _ids;}
		}
		/// <summary>
		/// 更新时间
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		/// <summary>
		/// 更新人
		/// </summary>
		public Guid LastEditor
		{
			set{ _lasteditor=value;}
			get{return _lasteditor;}
		}
		#endregion Model

	}
}

