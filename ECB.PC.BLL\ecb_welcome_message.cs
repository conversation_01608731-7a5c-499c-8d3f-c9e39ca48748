﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;
using System.Collections;

namespace ECB.PC.BLL
{
    public partial class ecb_welcome_message
    {
        public ecb_welcome_message()
        { }
        #region  Method

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from [ecb_welcome_message]");
            strSql.Append(" where Id=@Id ");

            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_welcome_message model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into [ecb_welcome_message] (");
            strSql.Append("Id,ColumnId,ColumnPath,MessageContent,IsEnable,StarTime,EndTime,Type,SendType,Ids,IsIndex,Weeks)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@MessageContent,@IsEnable,@StarTime,@EndTime,@Type,@SendType,@Ids,@IsIndex,@Weeks)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@MessageContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@IsEnable", SqlDbType.Int,4),
                    new SqlParameter("@StarTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@Type", SqlDbType.Int,4),
                    new SqlParameter("@SendType", SqlDbType.Int,4),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@IsIndex", SqlDbType.Int,4),
                     new SqlParameter("@Weeks", SqlDbType.NVarChar,-1)
            };
            if (model.Id == Guid.Empty)
            {
                model.Id = Guid.NewGuid();
            }
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.MessageContent;
            parameters[4].Value = model.IsEnable;
            parameters[5].Value = model.StarTime;
            parameters[6].Value = model.EndTime;
            parameters[7].Value = model.Type;
            parameters[8].Value = model.SendType;
            parameters[9].Value = model.Ids;
            parameters[10].Value = model.IsIndex;
            parameters[11].Value = model.Weeks;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_welcome_message model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_welcome_message set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("MessageContent=@MessageContent,");
            strSql.Append("IsEnable=@IsEnable,");
            strSql.Append("StarTime=@StarTime,");
            strSql.Append("EndTime=@EndTime,");
            strSql.Append("Type=@Type,");
            strSql.Append("SendType=@SendType,");
            strSql.Append("Ids=@Ids,");
            strSql.Append("IsIndex=@IsIndex,");
            strSql.Append("Weeks=@Weeks");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@MessageContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@IsEnable", SqlDbType.Int,4),
                    new SqlParameter("@StarTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@Type", SqlDbType.Int,4),
                    new SqlParameter("@SendType", SqlDbType.Int,4),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsIndex", SqlDbType.Int,4),
                    new SqlParameter("@Weeks", SqlDbType.NVarChar,-1)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.MessageContent;
            parameters[3].Value = model.IsEnable;
            parameters[4].Value = model.StarTime;
            parameters[5].Value = model.EndTime;
            parameters[6].Value = model.Type;
            parameters[7].Value = model.SendType;
            parameters[8].Value = model.Ids;
            parameters[9].Value = model.Id;
            parameters[10].Value = model.IsIndex;
            parameters[11].Value = model.Weeks;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from [ecb_welcome_message] ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_welcome_message GetModel(string ColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,MessageContent,IsEnable,StarTime,EndTime,Type,SendType,Ids,IsIndex,Weeks");
            strSql.Append(" FROM ecb_welcome_message ");
            strSql.Append(" where ColumnPath=@ColumnPath ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500)};
            parameters[0].Value = ColumnPath;

            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        public Model.ecb_welcome_message DataRowToModel(DataRow row)
        {
            Model.ecb_welcome_message model = new Model.ecb_welcome_message();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["MessageContent"] != null)
                {
                    model.MessageContent = row["MessageContent"].ToString();
                }
                if (row["IsEnable"] != null && row["IsEnable"].ToString() != "")
                {
                    model.IsEnable = int.Parse(row["IsEnable"].ToString());
                }
                if (row["StarTime"] != null && row["StarTime"].ToString() != "")
                {
                    model.StarTime = DateTime.Parse(row["StarTime"].ToString());
                }
                if (row["EndTime"] != null && row["EndTime"].ToString() != "")
                {
                    model.EndTime = DateTime.Parse(row["EndTime"].ToString());
                }
                if (row["Type"] != null && row["Type"].ToString() != "")
                {
                    model.Type = int.Parse(row["Type"].ToString());
                }
                if (row["SendType"] != null && row["SendType"].ToString() != "")
                {
                    model.SendType = int.Parse(row["SendType"].ToString());
                }
                if (row["Ids"] != null)
                {
                    model.Ids = row["Ids"].ToString();
                }
                if (row["IsIndex"] != null && row["IsIndex"].ToString() != "")
                {
                    model.IsIndex = int.Parse(row["IsIndex"].ToString());
                }
                if (row["Weeks"] != null)
                {
                    model.Weeks = row["Weeks"].ToString();
                }
            }
            return model;
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM [ecb_welcome_message] ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        #endregion  Method
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string ColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from [ecb_welcome_message]");
            strSql.Append(" where ColumnPath=@ColumnPath ");

            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500)};
            parameters[0].Value = ColumnPath;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        public Model.ecb_welcome_message GetModel(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,MessageContent,IsEnable,StarTime,EndTime,Type,SendType,Ids,IsIndex,Weeks");
            strSql.Append(" FROM ecb_welcome_message ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = Id;

            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_welcome_message ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 通过id找展示时间端
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public DataTable GetTimes(Guid id)
        {
            string sql = $"select * from ecb_welcome_times where OutId='{id}'";
            return DbHelperSQL.Query(sql).Tables[0];
        }

        public void AddTimes(Guid id, string times)
        {
            ArrayList list=new ArrayList();
            list.Add($"delete ecb_welcome_times where [OutId]='{id}'");
            foreach (string item in times.Split('|'))
            {
                string[] time = item.Split('-');
                if (time.Length == 2&&DateTime.TryParse(DateTime.Now.Date.ToString("yyyy-MM-dd ")+ time[0],out DateTime beginDate) && DateTime.TryParse(DateTime.Now.Date.ToString("yyyy-MM-dd ")+ time[1], out DateTime endDate))
                {
                    list.Add($"INSERT INTO [ecb_welcome_times] values('{id}','{beginDate.ToString("HH:mm")}','{endDate.ToString("HH:mm")}')");
                }
            }
            if (list.Count>1)
            {
                int rows = DbHelperSQL.ExecuteSqlTran(list);
            }
        }
    }
}
