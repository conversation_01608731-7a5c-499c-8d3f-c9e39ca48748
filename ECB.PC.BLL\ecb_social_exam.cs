﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_social_exam
    /// </summary>
    public partial class ecb_social_exam
    {
        public ecb_social_exam()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_social_exam");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_social_exam model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_social_exam(");
            strSql.Append("Id,ColumnID,ColumnPath,ExamName,Subtitle,ExamSta,ExamEnd,ShowSta,ShowEnd,Mark,ModelType,RuleId)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnID,@ColumnPath,@ExamName,@Subtitle,@ExamSta,@ExamEnd,@ShowSta,@ShowEnd,@Mark,@ModelType,@RuleId)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@ExamName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Subtitle", SqlDbType.NVarChar,50),
                    new SqlParameter("@ExamSta", SqlDbType.DateTime),
                    new SqlParameter("@ExamEnd", SqlDbType.DateTime),
                    new SqlParameter("@ShowSta", SqlDbType.DateTime),
                    new SqlParameter("@ShowEnd", SqlDbType.DateTime),
                    new SqlParameter("@Mark", SqlDbType.NVarChar,255),
                    new SqlParameter("@ModelType", SqlDbType.NVarChar,10),
                    new SqlParameter("@RuleId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnID;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ExamName;
            parameters[4].Value = model.Subtitle;
            parameters[5].Value = model.ExamSta;
            parameters[6].Value = model.ExamEnd;
            parameters[7].Value = model.ShowSta;
            parameters[8].Value = model.ShowEnd;
            parameters[9].Value = model.Mark;
            parameters[10].Value = model.ModelType;
            parameters[11].Value = model.RuleId;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_social_exam model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_social_exam set ");
            strSql.Append("ColumnID=@ColumnID,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ExamName=@ExamName,");
            strSql.Append("Subtitle=@Subtitle,");
            strSql.Append("ExamSta=@ExamSta,");
            strSql.Append("ExamEnd=@ExamEnd,");
            strSql.Append("ShowSta=@ShowSta,");
            strSql.Append("ShowEnd=@ShowEnd,");
            strSql.Append("Mark=@Mark,");
            strSql.Append("ModelType=@ModelType,");
            strSql.Append("RuleId=@RuleId");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@ExamName", SqlDbType.NVarChar,50),
                    new SqlParameter("@Subtitle", SqlDbType.NVarChar,50),
                    new SqlParameter("@ExamSta", SqlDbType.DateTime),
                    new SqlParameter("@ExamEnd", SqlDbType.DateTime),
                    new SqlParameter("@ShowSta", SqlDbType.DateTime),
                    new SqlParameter("@ShowEnd", SqlDbType.DateTime),
                    new SqlParameter("@Mark", SqlDbType.NVarChar,255),
                    new SqlParameter("@ModelType", SqlDbType.NVarChar,10),
                    new SqlParameter("@RuleId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnID;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.ExamName;
            parameters[3].Value = model.Subtitle;
            parameters[4].Value = model.ExamSta;
            parameters[5].Value = model.ExamEnd;
            parameters[6].Value = model.ShowSta;
            parameters[7].Value = model.ShowEnd;
            parameters[8].Value = model.Mark;
            parameters[9].Value = model.ModelType;
            parameters[10].Value = model.RuleId;
            parameters[11].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" delete from ecb_social_exam ");
            strSql.Append(" where Id=@Id ");
            //同步删除社会化考试考场
            strSql.Append("delete from ecb_social_place ");
            strSql.Append(" where ExamId=@Id ");
            //同步删除社会化生成的开关机规则
            strSql.Append("delete from ecb_reboot_once ");
            strSql.Append(" where Id=@Id ");
            //同步删除社会化生成的开关机时间
            strSql.Append("delete from ecb_reboot_time ");
            strSql.Append(" where RuleId=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_social_exam ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_social_exam GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnID,ColumnPath,ExamName,Subtitle,ExamSta,ExamEnd,ShowSta,ShowEnd,Mark,ModelType,RuleId from ecb_social_exam ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_social_exam model = new ECB.PC.Model.ecb_social_exam();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_social_exam DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_social_exam model = new ECB.PC.Model.ecb_social_exam();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnID"] != null && row["ColumnID"].ToString() != "")
                {
                    model.ColumnID = int.Parse(row["ColumnID"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ExamName"] != null)
                {
                    model.ExamName = row["ExamName"].ToString();
                }
                if (row["Subtitle"] != null)
                {
                    model.Subtitle = row["Subtitle"].ToString();
                }
                if (row["ExamSta"] != null && row["ExamSta"].ToString() != "")
                {
                    model.ExamSta = DateTime.Parse(row["ExamSta"].ToString());
                }
                if (row["ExamEnd"] != null && row["ExamEnd"].ToString() != "")
                {
                    model.ExamEnd = DateTime.Parse(row["ExamEnd"].ToString());
                }
                if (row["ShowSta"] != null && row["ShowSta"].ToString() != "")
                {
                    model.ShowSta = DateTime.Parse(row["ShowSta"].ToString());
                }
                if (row["ShowEnd"] != null && row["ShowEnd"].ToString() != "")
                {
                    model.ShowEnd = DateTime.Parse(row["ShowEnd"].ToString());
                }
                if (row["Mark"] != null)
                {
                    model.Mark = row["Mark"].ToString();
                }
                if (row["ModelType"] != null)
                {
                    model.ModelType = row["ModelType"].ToString();
                }
                if (row["RuleId"] != null && row["RuleId"].ToString() != "")
                {
                    model.RuleId = new Guid(row["RuleId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnID,ColumnPath,ExamName,Subtitle,ExamSta,ExamEnd,ShowSta,ShowEnd,Mark,ModelType ,RuleId");
            strSql.Append(" FROM ecb_social_exam ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnID,ColumnPath,ExamName,Subtitle,ExamSta,ExamEnd,ShowSta,ShowEnd,Mark,ModelType,RuleId ");
            strSql.Append(" FROM ecb_social_exam ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_social_exam ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_social_exam T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_social_exam";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

