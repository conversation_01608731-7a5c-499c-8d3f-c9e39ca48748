﻿﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
using System.Collections;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:custom_Reward_record
	/// </summary>
	public partial class custom_Reward_record
	{
		public custom_Reward_record()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from custom_Reward_record");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.custom_Reward_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into custom_Reward_record(");
			strSql.Append("ID,ColumnId,ColumnPath,UserId,RewardTime,RewardName,RewardLevel,ToAawardsUnit,CreatorId,CreateTime)");
			strSql.Append(" values (");
			strSql.Append("@ID,@ColumnId,@ColumnPath,@UserId,@RewardTime,@RewardName,@RewardLevel,@ToAawardsUnit,@CreatorId,@CreateTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@RewardTime", SqlDbType.DateTime),
					new SqlParameter("@RewardName", SqlDbType.NVarChar,250),
					new SqlParameter("@RewardLevel", SqlDbType.NVarChar,250),
					new SqlParameter("@ToAawardsUnit", SqlDbType.NVarChar,50),
					new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime)};
			parameters[0].Value = model.ID;
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.UserId;
			parameters[4].Value = model.RewardTime;
			parameters[5].Value = model.RewardName;
			parameters[6].Value = model.RewardLevel;
			parameters[7].Value = model.ToAawardsUnit;
			parameters[8].Value = model.CreatorId;
			parameters[9].Value = model.CreateTime;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.custom_Reward_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update custom_Reward_record set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("UserId=@UserId,");
			strSql.Append("RewardTime=@RewardTime,");
			strSql.Append("RewardName=@RewardName,");
			strSql.Append("RewardLevel=@RewardLevel,");
			strSql.Append("ToAawardsUnit=@ToAawardsUnit,");
			strSql.Append("CreatorId=@CreatorId,");
			strSql.Append("CreateTime=@CreateTime");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@RewardTime", SqlDbType.DateTime),
					new SqlParameter("@RewardName", SqlDbType.NVarChar,250),
					new SqlParameter("@RewardLevel", SqlDbType.NVarChar,250),
					new SqlParameter("@ToAawardsUnit", SqlDbType.NVarChar,50),
					new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.UserId;
			parameters[3].Value = model.RewardTime;
			parameters[4].Value = model.RewardName;
			parameters[5].Value = model.RewardLevel;
			parameters[6].Value = model.ToAawardsUnit;
			parameters[7].Value = model.CreatorId;
			parameters[8].Value = model.CreateTime;
			parameters[9].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Reward_record ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Reward_record ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Reward_record GetModel(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,ColumnId,ColumnPath,UserId,RewardTime,RewardName,RewardLevel,ToAawardsUnit,CreatorId,CreateTime from custom_Reward_record ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.custom_Reward_record model=new ECB.PC.Model.custom_Reward_record();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Reward_record DataRowToModel(DataRow row)
		{
			ECB.PC.Model.custom_Reward_record model=new ECB.PC.Model.custom_Reward_record();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["UserId"]!=null && row["UserId"].ToString()!="")
				{
					model.UserId= new Guid(row["UserId"].ToString());
				}
				if(row["RewardTime"]!=null && row["RewardTime"].ToString()!="")
				{
					model.RewardTime=DateTime.Parse(row["RewardTime"].ToString());
				}
				if(row["RewardName"]!=null)
				{
					model.RewardName=row["RewardName"].ToString();
				}
				if(row["RewardLevel"]!=null)
				{
					model.RewardLevel=row["RewardLevel"].ToString();
				}
				if(row["ToAawardsUnit"]!=null)
				{
					model.ToAawardsUnit=row["ToAawardsUnit"].ToString();
				}
				if(row["CreatorId"]!=null && row["CreatorId"].ToString()!="")
				{
					model.CreatorId= new Guid(row["CreatorId"].ToString());
				}
				if(row["CreateTime"]!=null && row["CreateTime"].ToString()!="")
				{
					model.CreateTime=DateTime.Parse(row["CreateTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,ColumnId,ColumnPath,UserId,RewardTime,RewardName,RewardLevel,ToAawardsUnit,CreatorId,CreateTime ");
			strSql.Append(" FROM custom_Reward_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,ColumnId,ColumnPath,UserId,RewardTime,RewardName,RewardLevel,ToAawardsUnit,CreatorId,CreateTime ");
			strSql.Append(" FROM custom_Reward_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM custom_Reward_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from custom_Reward_record T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "custom_Reward_record";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 添加获奖记录到事务列表
		/// </summary>
		/// <param name="model">获奖记录模型</param>
		/// <param name="sqlList">SQL事务列表</param>
		public void Add(ECB.PC.Model.custom_Reward_record model, ArrayList sqlList)
		{
			string sql = $@"INSERT INTO custom_Reward_record(ID,ColumnId,ColumnPath,UserId,RewardTime,RewardName,RewardLevel,ToAawardsUnit,CreatorId,CreateTime)
                           VALUES('{model.ID}',{model.ColumnId},'{model.ColumnPath}','{model.UserId}','{model.RewardTime:yyyy-MM-dd HH:mm:ss}','{model.RewardName}','{model.RewardLevel}','{model.ToAawardsUnit}','{model.CreatorId}','{model.CreateTime:yyyy-MM-dd HH:mm:ss}')";
			sqlList.Add(sql);
		}

		/// <summary>
		/// 执行SQL事务
		/// </summary>
		/// <param name="sqlList">SQL语句列表</param>
		/// <returns>执行结果</returns>
		public bool ExecuteSqlTran(ArrayList sqlList)
		{
			return DbHelperSQL.ExecuteSqlTran(sqlList) > 0;
		}

		#endregion  ExtensionMethod
	}
}
