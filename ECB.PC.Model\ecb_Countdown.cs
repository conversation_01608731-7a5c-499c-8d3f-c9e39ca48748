﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_Countdown:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_Countdown
	{
		public ecb_Countdown()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _title;
		private string _remark;
		private string _placeids;
		private int _sendtype;
		private DateTime _begintime;
		private DateTime _endtime;
		private Guid _creator;
		private DateTime _CreateTime;
        public string Template { get; set; }
        /// <summary>
        /// id
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 标题
		/// </summary>
		public string Title
		{
			set{ _title=value;}
			get{return _title;}
		}
		/// <summary>
		/// 描述
		/// </summary>
		public string Remark
        {
			set{ _remark=value;}
			get{return _remark;}
		}
		/// <summary>
		/// 场地名称
		/// </summary>
		public string PlaceIds
		{
			set{ _placeids=value;}
			get{return _placeids; }
		}
		/// <summary>
		/// 班牌类型
		/// </summary>
		public int SendType
		{
			set{ _sendtype=value;}
			get{return _sendtype;}
		}
		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime BeginTime
		{
			set{ _begintime=value;}
			get{return _begintime;}
		}
		/// <summary>
		/// 截止时间
		/// </summary>
		public DateTime EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _CreateTime=value;}
			get{return _CreateTime;}
		}
		#endregion Model

	}
}

