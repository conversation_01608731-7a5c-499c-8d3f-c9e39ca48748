﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理预警配置
	/// </summary>
	[Serializable]
	public partial class psych_warning_config
	{
		public psych_warning_config()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _warningname;
		private string _warningcode;
		private decimal _scorelimit;
		private string _color;
		/// <summary>
		/// 主键
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 预警名称
		/// </summary>
		public string WarningName
		{
			set{ _warningname=value;}
			get{return _warningname;}
		}
		/// <summary>
		/// 预警编号
		/// </summary>
		public string WarningCode
		{
			set{ _warningcode=value;}
			get{return _warningcode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal ScoreLimit
		{
			set{ _scorelimit=value;}
			get{return _scorelimit;}
		}
		/// <summary>
		/// 颜色
		/// </summary>
		public string Color
		{
			set{ _color=value;}
			get{return _color;}
		}
		#endregion Model

	}
}