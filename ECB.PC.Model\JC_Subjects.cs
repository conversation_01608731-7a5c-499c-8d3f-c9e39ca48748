﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 任课老师表
	/// </summary>
	[Serializable]
	public partial class JC_Subjects
	{
		public JC_Subjects()
		{}
		#region Model
		private Guid _id;
		private string _teacherno;
		private int? _columnid;
		private string _columnpath;
		private Guid _termid;
		private Guid _gradeid;
		private Guid _classid;
		private string _subjectcode;
		private string _statuscode;
		private DateTime? _createdate;
		private DateTime? _lasteditdate;
		private Guid _lasteditby;
		private string _tname;
		private string _schoolyear;
		public Guid TeacherId { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 教师唯一编号
		/// </summary>
		public string TeacherNo
		{
			set{ _teacherno=value;}
			get{return _teacherno;}
		}
		/// <summary>
		/// 学校ID
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 学期编号
		/// </summary>
		public Guid TermID
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 年级编号
		/// </summary>
		public Guid GradeID
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 班级编号
		/// </summary>
		public Guid ClassID
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 科目
		/// </summary>
		public string SubjectCode
		{
			set{ _subjectcode=value;}
			get{return _subjectcode;}
		}
		/// <summary>
		/// 状态
		/// </summary>
		public string StatusCode
		{
			set{ _statuscode=value;}
			get{return _statuscode;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime? CreateDate
		{
			set{ _createdate=value;}
			get{return _createdate;}
		}
		/// <summary>
		/// 最近修改时间
		/// </summary>
		public DateTime? LastEditDate
		{
			set{ _lasteditdate=value;}
			get{return _lasteditdate;}
		}
		/// <summary>
		/// 最近修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 教师名称
		/// </summary>
		public string TName
		{
			set{ _tname=value;}
			get{return _tname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SchoolYear
		{
			set{ _schoolyear=value;}
			get{return _schoolyear;}
		}
		#endregion Model

	}
}

