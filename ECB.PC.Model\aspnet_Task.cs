﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 任务系统表
    /// </summary>
    [Serializable]
    public partial class aspnet_Task
    {
        public aspnet_Task()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _batchid;
        private int _tasktype;
        private int _businesstype;
        private string _taskcontent;
        private DateTime? _excutetime;
        private DateTime? _starttime;
        private DateTime? _finishedtime;
        private int? _status;
        private string _errormessage;
        private string _taskresult;
        private DateTime? _createTime;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 批次Id
        /// </summary>
        public Guid BatchId
        {
            set { _batchid = value; }
            get { return _batchid; }
        }
        /// <summary>
        /// 任务类型枚举：1 短信2 服务号推送 3 企业号推送 4 站内消息推送
        /// </summary>
        public int TaskType
        {
            set { _tasktype = value; }
            get { return _tasktype; }
        }
        /// <summary>
        /// 业务类型枚举：1 请假推送 2 请假修改办公状态 3 教师异常考勤推送 4 学生异常考勤推送 … 
        /// </summary>
        public int BusinessType
        {
            set { _businesstype = value; }
            get { return _businesstype; }
        }
        /// <summary>
        /// 任务内容 Json字符串
        /// </summary>
        public string TaskContent
        {
            set { _taskcontent = value; }
            get { return _taskcontent; }
        }
        /// <summary>
        /// 任务执行时间
        /// </summary>
        public DateTime? ExcuteTime
        {
            set { _excutetime = value; }
            get { return _excutetime; }
        }
        /// <summary>
        /// 任务实际开始执行时间
        /// </summary>
        public DateTime? StartTime
        {
            set { _starttime = value; }
            get { return _starttime; }
        }
        /// <summary>
        /// 任务完成时间
        /// </summary>
        public DateTime? FinishedTime
        {
            set { _finishedtime = value; }
            get { return _finishedtime; }
        }
        /// <summary>
        /// 完成状态枚举：1成功 0失败
        /// </summary>
        public int? Status
        {
            set { _status = value; }
            get { return _status; }
        }
        /// <summary>
        /// 错误描述
        /// </summary>
        public string ErrorMessage
        {
            set { _errormessage = value; }
            get { return _errormessage; }
        }
        /// <summary>
        /// 执行反馈
        /// </summary>
        public string TaskResult
        {
            set { _taskresult = value; }
            get { return _taskresult; }
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime
        {
            set { _createTime = value; }
            get { return _createTime; }
        }
        #endregion Model

    }
}

