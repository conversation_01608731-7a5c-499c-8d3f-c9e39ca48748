﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;
using System.Linq;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_warning_record
    /// </summary>
    public partial class psych_warning_record
    {
        public psych_warning_record()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_warning_record");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.psych_warning_record model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_warning_record(");
            strSql.Append("Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,WarningStatus,CreateTime,Creator,IsInterfere)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@SourceType,@SourceId,@WarningStatus,@CreateTime,@Creator,@IsInterfere)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SourceType", SqlDbType.Int,4),
                    new SqlParameter("@SourceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@WarningStatus", SqlDbType.NVarChar,2),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsInterfere",SqlDbType.Int,4)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.SourceType;
            parameters[5].Value = model.SourceId;
            parameters[6].Value = model.WarningStatus;
            parameters[7].Value = model.CreateTime;
            parameters[8].Value = model.Creator;
            parameters[9].Value = model.IsInterfere;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.psych_warning_record model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_warning_record set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("SourceType=@SourceType,");
            strSql.Append("SourceId=@SourceId,");
            strSql.Append("WarningStatus=@WarningStatus,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("Creator=@Creator,");
            strSql.Append("IsInterfere=@IsInterfere");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SourceType", SqlDbType.Int,4),
                    new SqlParameter("@SourceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@WarningStatus", SqlDbType.NVarChar,2),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsInterfere",SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.StudentId;
            parameters[3].Value = model.SourceType;
            parameters[4].Value = model.SourceId;
            parameters[5].Value = model.WarningStatus;
            parameters[6].Value = model.CreateTime;
            parameters[7].Value = model.Creator;
            parameters[8].Value = model.IsInterfere;
            parameters[9].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_warning_record ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_warning_record ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.psych_warning_record GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,WarningStatus,CreateTime,Creator,IsInterfere from psych_warning_record ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.psych_warning_record model = new Model.psych_warning_record();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.psych_warning_record DataRowToModel(DataRow row)
        {
            Model.psych_warning_record model = new Model.psych_warning_record();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["SourceType"] != null && row["SourceType"].ToString() != "")
                {
                    model.SourceType = int.Parse(row["SourceType"].ToString());
                }
                if (row["SourceId"] != null && row["SourceId"].ToString() != "")
                {
                    model.SourceId = new Guid(row["SourceId"].ToString());
                }
                if (row["WarningStatus"] != null)
                {
                    model.WarningStatus = row["WarningStatus"].ToString();
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["Creator"] != null && row["Creator"].ToString() != "")
                {
                    model.Creator = new Guid(row["Creator"].ToString());
                }
                if (row["IsInterfere"] != null && row["IsInterfere"].ToString() != "")
                {
                    model.IsInterfere = int.Parse(row["IsInterfere"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,WarningStatus,CreateTime,Creator,IsInterfere ");
            strSql.Append(" FROM psych_warning_record ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,WarningStatus,CreateTime,Creator,IsInterfere ");
            strSql.Append(" FROM psych_warning_record ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere, string[] ConStr = null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_warning_record ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), ConStr);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_warning_record T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_warning_record";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 获取预警记录统计数据
        /// 功能：统计预警记录，按来源类型和预警状态分组
        /// 关键逻辑：关联学生信息表进行权限过滤，处理访谈信息的特殊来源类型（3开头的都归为3）
        /// </summary>
        /// <param name="strWhereClause">where条件字符串，包含地区、年级、班级、时间等过滤条件</param>
        /// <returns>预警统计数据表，包含SourceType、WarningStatus、Count字段</returns>
        public DataTable GetWarningStatistics(string strWhereClause)
        {
            string strSql = @"
                SELECT
                    CASE
                        WHEN w.SourceType LIKE '3%' THEN 3
                        ELSE w.SourceType
                    END AS SourceType,
                    w.WarningStatus,
                    COUNT(w.Id) AS Count
                FROM psych_warning_record w
                INNER JOIN JC_StudentInfos s ON w.StudentId = s.ID
                WHERE " + strWhereClause + @"
                GROUP BY
                    CASE
                        WHEN w.SourceType LIKE '3%' THEN 3
                        ELSE w.SourceType
                    END,
                    w.WarningStatus";

            DataSet ds = DbHelperSQL.Query(strSql);
            return ds != null && ds.Tables.Count > 0 ? ds.Tables[0] : new DataTable();
        }
        /// <summary>
        /// 获取记录以及等级颜色
        /// </summary>
        /// <param name="SourceId"></param>
        /// <param name="SourceType"></param>
        /// <returns></returns>
        public DataSet GetListBySourceId(Guid SourceId, CommonEnum.WarningSourceType SourceType)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select top 1 a.*,b.WarningName,b.Color ");
            strSql.Append(" FROM psych_warning_record a left join psych_warning_config b on a.WarningStatus = b.WarningCode AND a.ColumnId=b.ColumnId");
            strSql.Append(" where a.SourceId = @SourceId and a.SourceType = @SourceType");
            strSql.Append("  order by a.CreateTime desc");
            SqlParameter[] parameters = {
                    new SqlParameter("@SourceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SourceType", SqlDbType.Int,4)
            };
            parameters[0].Value = SourceId;
            parameters[1].Value = (int)SourceType;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 是否存在记录
        /// </summary>
        /// <param name="studentId">学生id</param>
        /// <param name="sourceId">来源id</param>
        /// <param name="date">日期</param>
        /// <returns></returns>
        public bool Exists(Guid studentId, Guid sourceId, DateTime date)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_warning_record");
            strSql.Append(" where StudentId=@StudentId and SourceId=@SourceId and CreateTime>=@BeginTime and CreateTime<@EndTime ");
            SqlParameter[] parameters = {
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SourceId",SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@BeginTime",SqlDbType.Date),
                    new SqlParameter("@EndTime",SqlDbType.Date),
            };
            parameters[0].Value = studentId;
            parameters[1].Value = sourceId;
            parameters[2].Value = date.Date;
            parameters[3].Value = date.AddDays(1).Date;
            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        /// <param name="studentId">学生id</param>
        /// <param name="sourceId">来源id</param>
        /// <param name="date">日期</param>
        /// <param name="warningStatus">预警等级</param>
        /// <returns></returns>
        public Model.psych_warning_record GetModel(Guid studentId, Guid sourceId, DateTime date, string warningStatus)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,SourceType,SourceId,WarningStatus,CreateTime,Creator,IsInterfere from psych_warning_record ");
            strSql.Append(" where StudentId=@StudentId and SourceId=@SourceId and CreateTime>=@BeginTime and CreateTime<@EndTime and WarningStatus=@WarningStatus ");
            SqlParameter[] parameters = {
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SourceId",SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@BeginTime",SqlDbType.Date),
                    new SqlParameter("@EndTime",SqlDbType.Date),
                    new SqlParameter("@WarningStatus",SqlDbType.NVarChar,4)
            };
            parameters[0].Value = studentId;
            parameters[1].Value = sourceId;
            parameters[2].Value = date.Date;
            parameters[3].Value = date.AddDays(1).Date;
            parameters[4].Value = warningStatus;
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取要提醒的心理教师
        /// </summary>
        /// <param name="columnId">学校Id</param>
        /// <param name="classId">班级Id</param>
        /// <returns></returns>
        public List<Guid> GetNoticePsychTeacher(int columnId, Guid classId)
        {
            List<Guid> userIds = new List<Guid>();
            BLL.JC_GradeLeader bllGradeLeader = new JC_GradeLeader();
            // 找到对应负责的心理教师
            DataTable data = bllGradeLeader.GetList($"SchoolColumnID={columnId} and DutyTypeCode=5 and ClassId='{classId}'").Tables[0];
            if (data != null && data.Rows.Count > 0)
            {
                userIds = data.AsEnumerable().Select(row => row.Field<Guid>("GradeLeaderId")).ToList();
            }
            return userIds;
        }

        /// <summary>
        /// 获取要提醒的用户
        /// </summary>
        /// <param name="columnId">学校Id</param>
        /// <param name="classId">班级Id</param>
        /// <returns></returns>
        public List<Guid> GetNoticeUsers(int columnId, Guid classId, Guid studentId)
        {
            List<Guid> userIds = new List<Guid>();
            BLL.JC_GradeLeader bllGradeLeader = new JC_GradeLeader();
            BLL.psych_basicInfo bllBasicInfo = new psych_basicInfo();
            // 找到学生对应的导师
            Model.psych_basicInfo modelBasicInfo = bllBasicInfo.GetModelByStudentId(studentId);
            if (modelBasicInfo != null && modelBasicInfo.DSTeacherId != Guid.Empty)
            {
                userIds.Add(modelBasicInfo.DSTeacherId);
            }
            // 找到班主任
            BLL.JC_ClassInfos bllClassInfo = new JC_ClassInfos();
            DataTable dtHeadTeacher = GetHeadtecher(classId).Tables[0];
            if (dtHeadTeacher != null && dtHeadTeacher.Rows.Count > 0)
            {
                var headTeacherIds = (from DataRow row in dtHeadTeacher.Rows select row.Field<Guid>("UserId")).ToList();
                userIds.AddRange(headTeacherIds);
            }
            // 找年级组长             
            DataTable dtGradeLeader = bllGradeLeader.GetList($"SchoolColumnID={columnId} and DutyTypeCode=5 and ClassId='{classId}'").Tables[0];
            if (dtGradeLeader != null && dtGradeLeader.Rows.Count > 0)
            {
                var gradeLeaderIds = (from DataRow row in dtGradeLeader.Rows select row.Field<Guid>("GradeLeaderId")).ToList();
                userIds.AddRange(gradeLeaderIds);
            }
            // 找所有的校级领导
            YunEdu.BLL.aspnet_Roles bllRoles = new YunEdu.BLL.aspnet_Roles();
            YunEdu.Model.aspnet_Roles modelRoles = bllRoles.GetModel(YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin.ToString(), columnId);
            YunEdu.BLL.aspnet_Membership bllMembership = new YunEdu.BLL.aspnet_Membership();
            DataTable dtSchoolAdmins = GetListByRole(modelRoles.RoleId);
            if (dtSchoolAdmins != null && dtSchoolAdmins.Rows.Count > 0)
            {
                var schoolAdminIds = (from DataRow row in dtSchoolAdmins.Rows select row.Field<Guid>("UserId")).ToList();
                userIds.AddRange(schoolAdminIds);
            }
            return userIds;
        }
        public DataTable GetListByRole(Guid RoleId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendFormat(" select * from aspnet_Membership a left join aspnet_UsersInRoles b on a.UserId=b.UserId where b.RoleId='{0}' and a.MobilePhone<>'D46E8A4711DFAF65' ", RoleId);
            return DbHelperSQL.Query(strSql.ToString()).Tables[0];
        }
        /// <summary>
        /// 获取班主任信息
        /// </summary>
        public DataSet GetHeadtecher(Guid ClassID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT b.UserId,b.CName,b.MobilePhone,b.Photo FROM JC_ClassInfos a LEFT JOIN aspnet_Membership b ON a.TeacherNo = b.TeacherNo");
            strSql.Append(" WHERE a.ID='" + ClassID + "' AND b.UserId IS NOT NULL");
            return DbHelperSQL.Query(strSql.ToString());
        }
        public DataTable GetWarningStat(string strWhereClause)
        {
            string strSql = @"
                SELECT
                    t.WarningStatus,
                    COUNT(t.Id) AS Count
                FROM psych_warning_record t
                INNER JOIN JC_StudentInfos s ON t.StudentId = s.ID
                WHERE " + strWhereClause + @"
                GROUP BY
                    t.WarningStatus";

            DataSet ds = DbHelperSQL.Query(strSql);
            return ds != null && ds.Tables.Count > 0 ? ds.Tables[0] : new DataTable();
        }

        #endregion  ExtensionMethod
    }
}