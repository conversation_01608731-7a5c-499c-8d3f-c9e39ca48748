﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 学生档案目录表
	/// </summary>
	[Serializable]
	public partial class DA_Catalog
	{
		public DA_Catalog()
		{}
		#region Model
		private Guid _id;
		private int? _schoolcolumnid;
		private string _schoolcolumnpath;
		private Guid _studentid;
		private Guid _termid;
		private Guid _gradeid;
		private Guid _classid;
		private string _schoolyear;
		private string _edustagecode;
		private string _termtype;
		private string _name;
		private string _gradecode;
		private DateTime? _createtime;
		private DateTime? _updatetime;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校ID
		/// </summary>
		public int? SchoolColumnId
		{
			set{ _schoolcolumnid=value;}
			get{return _schoolcolumnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string SchoolColumnPath
		{
			set{ _schoolcolumnpath=value;}
			get{return _schoolcolumnpath;}
		}
		/// <summary>
		/// 学生ID
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 学期ID
		/// </summary>
		public Guid TermId
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 年级Id
		/// </summary>
		public Guid GradeId
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 班级Id
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学年
		/// </summary>
		public string SchoolYear
		{
			set{ _schoolyear=value;}
			get{return _schoolyear;}
		}
		/// <summary>
		/// 教育阶段
		/// </summary>
		public string EduStageCode
		{
			set{ _edustagecode=value;}
			get{return _edustagecode;}
		}
		/// <summary>
		/// 学期Type
		/// </summary>
		public string TermType
		{
			set{ _termtype=value;}
			get{return _termtype;}
		}
		/// <summary>
		/// 节点名称
		/// </summary>
		public string Name
		{
			set{ _name=value;}
			get{return _name;}
		}
		/// <summary>
		/// 所在年级
		/// </summary>
		public string GradeCode
		{
			set{ _gradecode=value;}
			get{return _gradecode;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime? CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 更新时间
		/// </summary>
		public DateTime? UpdateTime
		{
			set{ _updatetime=value;}
			get{return _updatetime;}
		}
		#endregion Model

	}
}

