﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_Event
    /// </summary>
    public partial class ecb_Event
    {
        public ecb_Event()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_Event");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_Event model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_Event(");
            strSql.Append("Id,ColumnID,ColumnPath,RecordId,StaDate,EndDate,Remark,Type,Creator,CreateTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnID,@ColumnPath,@RecordId,@StaDate,@EndDate,@Remark,@Type,@Creator,@CreateTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@RecordId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StaDate", SqlDbType.Date,3),
                    new SqlParameter("@EndDate", SqlDbType.Date,3),
                    new SqlParameter("@Remark", SqlDbType.NVarChar,255),
                    new SqlParameter("@Type", SqlDbType.Int,4),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnID;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.RecordId;
            parameters[4].Value = model.StaDate;
            parameters[5].Value = model.EndDate;
            parameters[6].Value = model.Remark;
            parameters[7].Value = model.Type;
            parameters[8].Value = model.Creator;
            parameters[9].Value = model.CreateTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_Event model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_Event set ");
            strSql.Append("ColumnID=@ColumnID,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("RecordId=@RecordId,");
            strSql.Append("StaDate=@StaDate,");
            strSql.Append("EndDate=@EndDate,");
            strSql.Append("Remark=@Remark,");
            strSql.Append("Type=@Type,");
            strSql.Append("Creator=@Creator,");
            strSql.Append("CreateTime=@CreateTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@RecordId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StaDate", SqlDbType.Date,3),
                    new SqlParameter("@EndDate", SqlDbType.Date,3),
                    new SqlParameter("@Remark", SqlDbType.NVarChar,255),
                    new SqlParameter("@Type", SqlDbType.Int,4),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnID;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.RecordId;
            parameters[3].Value = model.StaDate;
            parameters[4].Value = model.EndDate;
            parameters[5].Value = model.Remark;
            parameters[6].Value = model.Type;
            parameters[7].Value = model.Creator;
            parameters[8].Value = model.CreateTime;
            parameters[9].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Event ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Event ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Event GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnID,ColumnPath,RecordId,StaDate,EndDate,Remark,Type,Creator,CreateTime from ecb_Event ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_Event model = new ECB.PC.Model.ecb_Event();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Event DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_Event model = new ECB.PC.Model.ecb_Event();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnID"] != null && row["ColumnID"].ToString() != "")
                {
                    model.ColumnID = int.Parse(row["ColumnID"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["RecordId"] != null && row["RecordId"].ToString() != "")
                {
                    model.RecordId = new Guid(row["RecordId"].ToString());
                }
                if (row["StaDate"] != null && row["StaDate"].ToString() != "")
                {
                    model.StaDate = DateTime.Parse(row["StaDate"].ToString());
                }
                if (row["EndDate"] != null && row["EndDate"].ToString() != "")
                {
                    model.EndDate = DateTime.Parse(row["EndDate"].ToString());
                }
                if (row["Remark"] != null)
                {
                    model.Remark = row["Remark"].ToString();
                }
                if (row["Type"] != null && row["Type"].ToString() != "")
                {
                    model.Type = int.Parse(row["Type"].ToString());
                }
                if (row["Creator"] != null && row["Creator"].ToString() != "")
                {
                    model.Creator = new Guid(row["Creator"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, string[] ConnStr=null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnID,ColumnPath,RecordId,StaDate,EndDate,Remark,Type,Creator,CreateTime ");
            strSql.Append(" FROM ecb_Event ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString(),ConnStr);
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnID,ColumnPath,RecordId,StaDate,EndDate,Remark,Type,Creator,CreateTime ");
            strSql.Append(" FROM ecb_Event ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Event ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_Event T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_Event";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_Event GetModel(Guid RecordId, DateTime date)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnID,ColumnPath,RecordId,StaDate,EndDate,Remark,Type,Creator,CreateTime from ecb_Event ");
            strSql.Append(" where RecordId=@RecordId AND ((StaDate<=@date AND EndDate>=@date AND Type!=2 ) OR (StaDate=@date AND Type=2) )  ");
            SqlParameter[] parameters = {
                    new SqlParameter("@RecordId", SqlDbType.UniqueIdentifier,16) ,
                    new SqlParameter("@date", SqlDbType.DateTime)};
            parameters[0].Value = RecordId;
            parameters[1].Value = date;

            ECB.PC.Model.ecb_Event model = new ECB.PC.Model.ecb_Event();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool DeleteByRecord(Guid RecordId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Event ");
            strSql.Append(" where RecordId=@RecordId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@RecordId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = RecordId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool Delete(Guid RecordId, string where)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_Event ");
            strSql.Append(" where  RecordId='" + RecordId + "'" + where);

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 获取指定时间的补课记录
        /// </summary>
        /// <param name="columnId">学校id</param>
        /// <param name="date">时间</param>
        /// <returns></returns>
        public ECB.PC.Model.ecb_Event GetReplaceSubject(int columnId, DateTime date)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnID,ColumnPath,RecordId,StaDate,EndDate,Remark,Type,Creator,CreateTime from ecb_Event ");
            strSql.Append(" where ColumnID=@ColumnID AND DATEDIFF(day,StaDate,@date)=0 and Type=2");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnID", SqlDbType.Int,4) ,
                    new SqlParameter("@date", SqlDbType.DateTime)};
            parameters[0].Value = columnId;
            parameters[1].Value = date;

            ECB.PC.Model.ecb_Event model = new ECB.PC.Model.ecb_Event();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 判断是否休息
        /// </summary>
        public int GetHolidayCount(int columnId, DateTime dteDay)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_Event where RecordId =(select TOP 1 ID from ecb_Record where TermId=(select TOP 1 ID from JC_TermInfos where IsCurrentTerm=1 and SchoolColumnId=" + columnId + ")) AND StaDate<='" + dteDay + "' and EndDate>='" + dteDay + "' and Type!=2");
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        #endregion  ExtensionMethod
    }
}

