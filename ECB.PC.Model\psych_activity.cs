﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// psych_activity:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class psych_activity
	{
		public psych_activity()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _title;
		private int _type;
		private int _teachernum;
		private int _studentnum;
		private string _summary;
		private string _attachmentpath;
		private DateTime _activitydate;
		private DateTime _createtime;
		private Guid _creator;
		private Guid _lasteditby;
		private DateTime? _lastedittime;
		/// <summary>
		/// id
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 活动标题
		/// </summary>
		public string Title
		{
			set{ _title=value;}
			get{return _title;}
		}
		/// <summary>
		/// 活动类型 枚举：家长学校、家长会、 万师润万心、其它活动
		/// </summary>
		public int Type
		{
			set{ _type=value;}
			get{return _type;}
		}
		/// <summary>
		/// 参与教师数
		/// </summary>
		public int TeacherNum
		{
			set{ _teachernum=value;}
			get{return _teachernum;}
		}
		/// <summary>
		/// 参与学生数
		/// </summary>
		public int StudentNum
		{
			set{ _studentnum=value;}
			get{return _studentnum;}
		}
		/// <summary>
		/// 总结
		/// </summary>
		public string Summary
		{
			set{ _summary=value;}
			get{return _summary;}
		}
		/// <summary>
		/// 附件，|线分割
		/// </summary>
		public string AttachmentPath
		{
			set{ _attachmentpath=value;}
			get{return _attachmentpath;}
		}
		/// <summary>
		/// 活动日期
		/// </summary>
		public DateTime ActivityDate
		{
			set{ _activitydate=value;}
			get{return _activitydate;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

