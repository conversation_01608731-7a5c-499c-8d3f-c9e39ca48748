﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理访谈信息
	/// </summary>
	[Serializable]
	public partial class psych_interview
	{
		public psych_interview()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _interviewtype;
		private Guid _studentid;
		private Guid _userid;
		private DateTime _interviewtime;
		private string _dietarystatus;
		private string _dietarystatusext;
		private string _sleepstatus;
		private string _sleepstatusext;
		private string _emotionalcharact;
		private string _emotionalcharactext;
		private string _socialsupport;
		private string _socialsupportext;
		private int _isriskofselfharm;
		private int _zwshxf_exists;
		private DateTime? _zwshxf_earliesttime;
		private string _zwshxf_frequency;
		private DateTime? _zwshxf_lasttime;
		private string _zwshxf_detail;
		private int _zwshxw_exists;
		private DateTime? _zwshxw_earliesttime;
		private string _zwshxw_frequency;
		private DateTime? _zwshxw_lasttime;
		private string _zwshxw_detail;
		private int _zsxf_exists;
		private DateTime? _zsxf_earliesttime;
		private string _zsxf_frequency;
		private DateTime? _zsxf_lasttime;
		private string _zsxf_plan;
		private string _zsxf_plantype;
		private int _zsxw_exists;
		private DateTime? _zsxw_earliesttime;
		private string _zsxw_frequency;
		private DateTime? _zsxw_lasttime;
		private string _zsxw_detail;
		private string _gjrecod;
		private Guid _lasteditby;
		private DateTime _lastedittime;

		/// <summary>
		/// 访谈图片
		/// </summary>
        public string InterViewPic { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 访谈类型
		/// </summary>
		public string InterviewType
		{
			set{ _interviewtype=value;}
			get{return _interviewtype;}
		}
		/// <summary>
		/// 学生Id
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 参与人Id
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 访谈时间
		/// </summary>
		public DateTime InterviewTime
		{
			set{ _interviewtime=value;}
			get{return _interviewtime;}
		}
		/// <summary>
		/// 饮食情况
		/// </summary>
		public string DietaryStatus
		{
			set{ _dietarystatus=value;}
			get{return _dietarystatus;}
		}
		/// <summary>
		/// 饮食情况扩展
		/// </summary>
		public string DietaryStatusExt
		{
			set{ _dietarystatusext=value;}
			get{return _dietarystatusext;}
		}
		/// <summary>
		/// 睡眠情况
		/// </summary>
		public string SleepStatus
		{
			set{ _sleepstatus=value;}
			get{return _sleepstatus;}
		}
		/// <summary>
		/// 睡眠情况扩展
		/// </summary>
		public string SleepStatusExt
		{
			set{ _sleepstatusext=value;}
			get{return _sleepstatusext;}
		}
		/// <summary>
		/// 情绪特征
		/// </summary>
		public string EmotionalCharact
		{
			set{ _emotionalcharact=value;}
			get{return _emotionalcharact;}
		}
		/// <summary>
		/// 情绪特征扩展
		/// </summary>
		public string EmotionalCharactExt
		{
			set{ _emotionalcharactext=value;}
			get{return _emotionalcharactext;}
		}
		/// <summary>
		/// 社会支持评估
		/// </summary>
		public string SocialSupport
		{
			set{ _socialsupport=value;}
			get{return _socialsupport;}
		}
		/// <summary>
		/// 社会支持评估扩展
		/// </summary>
		public string SocialSupportExt
		{
			set{ _socialsupportext=value;}
			get{return _socialsupportext;}
		}
		/// <summary>
		/// 是否有自我伤害风险
		/// </summary>
		public int IsRiskOfSelfHarm
		{
			set{ _isriskofselfharm=value;}
			get{return _isriskofselfharm;}
		}
		/// <summary>
		/// 有无自我伤害想法
		/// </summary>
		public int ZWSHXF_Exists
		{
			set{ _zwshxf_exists=value;}
			get{return _zwshxf_exists;}
		}
		/// <summary>
		/// 最早出现自伤想法的时间
		/// </summary>
		public DateTime? ZWSHXF_EarliestTime
		{
			set{ _zwshxf_earliesttime=value;}
			get{return _zwshxf_earliesttime;}
		}
		/// <summary>
		/// 出现自伤想法频率如何
		/// </summary>
		public string ZWSHXF_Frequency
		{
			set{ _zwshxf_frequency=value;}
			get{return _zwshxf_frequency;}
		}
		/// <summary>
		/// 最近一次出现该想法是什么时候
		/// </summary>
		public DateTime? ZWSHXF_LastTime
		{
			set{ _zwshxf_lasttime=value;}
			get{return _zwshxf_lasttime;}
		}
		/// <summary>
		/// 有过哪些自伤想法
		/// </summary>
		public string ZWSHXF_Detail
		{
			set{ _zwshxf_detail=value;}
			get{return _zwshxf_detail;}
		}
		/// <summary>
		/// 有无自我伤害行为
		/// </summary>
		public int ZWSHXW_Exists
		{
			set{ _zwshxw_exists=value;}
			get{return _zwshxw_exists;}
		}
		/// <summary>
		/// 最早出现自伤行为的时间
		/// </summary>
		public DateTime? ZWSHXW_EarliestTime
		{
			set{ _zwshxw_earliesttime=value;}
			get{return _zwshxw_earliesttime;}
		}
		/// <summary>
		/// 出现自伤行为频率如何
		/// </summary>
		public string ZWSHXW_Frequency
		{
			set{ _zwshxw_frequency=value;}
			get{return _zwshxw_frequency;}
		}
		/// <summary>
		/// 最近一次自我伤害是什么时候
		/// </summary>
		public DateTime? ZWSHXW_LastTime
		{
			set{ _zwshxw_lasttime=value;}
			get{return _zwshxw_lasttime;}
		}
		/// <summary>
		/// 有哪些自伤行为
		/// </summary>
		public string ZWSHXW_Detail
		{
			set{ _zwshxw_detail=value;}
			get{return _zwshxw_detail;}
		}
		/// <summary>
		/// 有无自杀想法
		/// </summary>
		public int ZSXF_Exists
		{
			set{ _zsxf_exists=value;}
			get{return _zsxf_exists;}
		}
		/// <summary>
		/// 最早出现自杀想法的时间
		/// </summary>
		public DateTime? ZSXF_EarliestTime
		{
			set{ _zsxf_earliesttime=value;}
			get{return _zsxf_earliesttime;}
		}
		/// <summary>
		/// 出现自杀想法频率如何
		/// </summary>
		public string ZSXF_Frequency
		{
			set{ _zsxf_frequency=value;}
			get{return _zsxf_frequency;}
		}
		/// <summary>
		/// 最近一次出现该想法是什么时候
		/// </summary>
		public DateTime? ZSXF_LastTime
		{
			set{ _zsxf_lasttime=value;}
			get{return _zsxf_lasttime;}
		}
		/// <summary>
		/// 是否有具体的自杀计划
		/// </summary>
		public string ZSXF_Plan
		{
			set{ _zsxf_plan=value;}
			get{return _zsxf_plan;}
		}
		/// <summary>
		/// 具体自杀计划的类别
		/// </summary>
		public string ZSXF_PlanType
		{
			set{ _zsxf_plantype=value;}
			get{return _zsxf_plantype;}
		}
		/// <summary>
		/// 有无自杀行为
		/// </summary>
		public int ZSXW_Exists
		{
			set{ _zsxw_exists=value;}
			get{return _zsxw_exists;}
		}
		/// <summary>
		/// 最早出现自杀行为的时间
		/// </summary>
		public DateTime? ZSXW_EarliestTime
		{
			set{ _zsxw_earliesttime=value;}
			get{return _zsxw_earliesttime;}
		}
		/// <summary>
		/// 出现自杀行为频率如何
		/// </summary>
		public string ZSXW_Frequency
		{
			set{ _zsxw_frequency=value;}
			get{return _zsxw_frequency;}
		}
		/// <summary>
		/// 最近一次自杀行为是什么时候
		/// </summary>
		public DateTime? ZSXW_LastTime
		{
			set{ _zsxw_lasttime=value;}
			get{return _zsxw_lasttime;}
		}
		/// <summary>
		/// 有哪些自杀行为
		/// </summary>
		public string ZSXW_Detail
		{
			set{ _zsxw_detail=value;}
			get{return _zsxw_detail;}
		}
		/// <summary>
		/// 跟进记录
		/// </summary>
		public string GJRecod
		{
			set{ _gjrecod=value;}
			get{return _gjrecod;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}