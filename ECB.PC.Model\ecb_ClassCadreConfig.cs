﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 班级班干部设置
	/// </summary>
	[Serializable]
	public partial class ecb_ClassCadreConfig
	{
		public ecb_ClassCadreConfig()
		{}
		#region Model
		private Guid _id;
        private Guid _cadreid;
		private Guid _classid;
		private Guid _studentid;
		private DateTime _createdate;
		private Guid _createuserid;
		private int _columnid;
		private string _columnpath;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 所属干部id
		/// </summary>
        public Guid CadreId
		{
            set { _cadreid = value; }
            get { return _cadreid; }
		}
		/// <summary>
		/// 所属班级
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学生id
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateDate
		{
			set{ _createdate=value;}
			get{return _createdate;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		#endregion Model

	}
}

