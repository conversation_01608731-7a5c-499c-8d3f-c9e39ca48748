﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 推送人员配置
	/// </summary>
	[Serializable]
	public partial class ecb_PushUser
	{
		public ecb_PushUser()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _userid;
		private int? _typeid;
		private string _gradecode;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 分户ID |分割
		/// </summary>
		public string UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 类型
		/// </summary>
		public int? TypeId
		{
			set{ _typeid=value;}
			get{return _typeid;}
		}
		/// <summary>
		/// 年级Code
		/// </summary>
		public string GradeCode
		{
			set{ _gradecode=value;}
			get{return _gradecode;}
		}
		#endregion Model

	}
}

