﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 教师个人课程表
	/// </summary>
	[Serializable]
	public partial class JC_TeacherTimetable
	{
		public JC_TeacherTimetable()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private string _teacherno;
		private Guid _classid;
		private string _schoolyear;
		private Guid _termid;
		private int? _weekday;
		private int? _class;
		private string _subjectcode;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 区域编号
		/// </summary>
		public int? ColumnID
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 区域路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 教师编号
		/// </summary>
		public string TeacherNo
		{
			set{ _teacherno=value;}
			get{return _teacherno;}
		}
		/// <summary>
		/// 班级编号
		/// </summary>
		public Guid ClassID
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学年
		/// </summary>
		public string SchoolYear
		{
			set{ _schoolyear=value;}
			get{return _schoolyear;}
		}
		/// <summary>
		/// 学期编号
		/// </summary>
		public Guid TermID
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 星期
		/// </summary>
		public int? Weekday
		{
			set{ _weekday=value;}
			get{return _weekday;}
		}
		/// <summary>
		/// 节次
		/// </summary>
		public int? Class
		{
			set{ _class=value;}
			get{return _class;}
		}
		/// <summary>
		/// 科目
		/// </summary>
		public string SubjectCode
		{
			set{ _subjectcode=value;}
			get{return _subjectcode;}
		}
		#endregion Model

	}
}

