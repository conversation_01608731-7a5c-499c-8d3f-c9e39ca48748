﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.DBUtility;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_setting : System.Web.UI.Page
    {

        YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();
        YunEdu.BLL.PKC_TestUsers blltestusers = new YunEdu.BLL.PKC_TestUsers();
        YunEdu.Model.PKC_TestUsers modeltestusers = new YunEdu.Model.PKC_TestUsers();
        YunEdu.Model.PKC_Rooms modelrooms = new YunEdu.Model.PKC_Rooms();
        YunEdu.BLL.PKC_Rooms bllrooms = new YunEdu.BLL.PKC_Rooms();
        YunEdu.BLL.JC_Message bllmessage = new YunEdu.BLL.JC_Message();

        string id = "";
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
            }
        }

        protected void btnInput_Click(object sender, EventArgs e)
        {
            if (id != null)
            {
                int roomnum = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "'and (RoomNum=0 or RoomNum is NULL)");
                if (roomnum == 0)
                {
                    int allnumber = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "'");
                    int amount = 0;
                    foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "RoomLayout", "TestId='" + id + "'and IsEnabled = 1", "RoomNum asc").Tables[0].Rows)
                    {
                        try
                        {
                            string layout = item["RoomLayout"].ToString();
                            string[] _tempLayouts = layout.Split('|');
                            int[] a = new int[_tempLayouts.Length];
                            for (int i = 0; i < _tempLayouts.Length; i++)
                            {
                                a[i] = Convert.ToInt32(_tempLayouts[i]);
                            }
                            for (int i = 0; i < a.Length; i++)
                            {
                                amount += a[i];
                            }
                        }
                        catch (Exception)
                        {
                            continue;
                        }
                    }


                    if (allnumber <= amount)
                    {

                        //foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "*", "TestId='" + id + "'", "AbilityValue asc").Tables[0].Rows)
                        //{
                        //    modeltestusers = blltestusers.GetModel(new Guid(item["TestUserId"].ToString()));

                        //    decimal abilityvalue = 0;
                        //    foreach (DataRow itemscore in YunEdu.Common.GetRecordByPageOrder.GetListByVW("JC_TestScores", 0, "ScoreTotal", "UserID='" + item["UserId"].ToString() + "'and TestID='" + this.ddlTests.SelectedValue + "'", "SubjectCode asc").Tables[0].Rows)
                        //    {
                        //        abilityvalue += decimal.Parse(itemscore["ScoreTotal"].ToString());
                        //    }
                        //    modeltestusers.AbilityValue = abilityvalue;
                        //    blltestusers.Update(modeltestusers);
                        //}
                        //按考试成绩编排
                        if (option1.Checked)
                        {
                            PaiKaoChang(false);
                        }
                        //按随机规则编排考场
                        else if (option2.Checked)
                        {
                            PaiKaoChang(true);
                        }
                        //按本班学生编排考场
                        else if (option3.Checked)
                        {
                            modeltests = blltests.GetModel(new Guid(id));
                            if (modeltests != null)
                            {
                                DataTable dtRoom = new YunEdu.BLL.PKC_Rooms().GetList("TestId='" + id + "' and ClassId IN (SELECT ID FROM dbo.JC_ClassInfos WHERE GradeId='" + modeltests.GradeId + "')").Tables[0];
                                //判断这场考试的考场是不是在本班教室
                                if (new YunEdu.BLL.JC_ClassInfos().GetRecordCount("GradeId='" + modeltests.GradeId + "'") <= dtRoom.Rows.Count)
                                {
                                    DateTime createTime = DateTime.Now;
                                    int _index = 1;
                                    int _number = 1;
                                    string MyClassId = "";
                                    //获得本场考试的所有学生
                                    DataTable dtTestUser = blltestusers.GetList(0, "TestId='" + id + "'", "MyClassId").Tables[0];
                                    foreach (DataRow item in dtTestUser.Rows)
                                    {
                                        DataRow[] drRooms = dtRoom.Select("ClassId='" + item["MyClassId"].ToString() + "'");
                                        if (drRooms.Length > 0)
                                        {
                                            modeltestusers = blltestusers.GetModel(new Guid(item["TestUserId"].ToString()));
                                            modeltestusers.SeatNumber = _index;
                                            modeltestusers.RoomId = new Guid(drRooms[0]["RoomId"].ToString());
                                            modeltestusers.CreateTime = createTime;
                                            modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + _number;
                                            blltestusers.Update(modeltestusers);
                                            _index++;
                                            _number++;
                                            if (MyClassId != item["MyClassId"].ToString())
                                            {
                                                _index = 1;
                                            }
                                            MyClassId = item["MyClassId"].ToString();

                                            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and RoomId='" + new Guid(drRooms[0]["RoomId"].ToString()) + "'") != 0)
                                            {
                                                //修改考场已被使用
                                                modelrooms = bllrooms.GetModel(new Guid(drRooms[0]["RoomId"].ToString()));
                                                modelrooms.IsUsing = true;
                                                bllrooms.Update(modelrooms);
                                            }
                                        }
                                        else
                                        {
                                            MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
                                            return;
                                        }
                                    }

                                }
                                else
                                {
                                    MessageBox.ResponseScript(this, "layer.msg('请先安排考试班级的考场!');");
                                    return;
                                }

                            }

                        }
                        int allCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "'");
                        int someCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>'' ");
                        int c = allCount - someCount;
                        if (c > 0)
                        {
                            MessageBox.ResponseScript(this, "layer.msg('考场编排完成！总的学生数" + allCount + "人，已排完的学生数" + someCount + "人，还有" + c + "考生未安排考场，建议全部删除考生，然后导入考生，适当增加考场或考场的布局容量重新排一次考场!');");

                        }
                        else
                        {
                          MessageBox.ResponseScript(this, "layer.msg('考场编排完成!');");
                        }

                    }
                    else
                    {
                        int lacknum = allnumber - amount;
                        MessageBox.ResponseScript(this, "layer.msg('考场座位总数小于考试人数，缺少" + lacknum + "个座位!');");
                        return;
                    }
                    this.Page.ClientScript.RegisterClientScriptBlock(this.GetType(), "msg", "<script>CloseBox();</script>");
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('存在考场未设置考场号或设置考场号为0，请进入“考场管理”为每个考场设置唯一考场号!');");
                    return;
                }
            }

            //如果选择了根据成绩来排  则根据所选考试导入考生成绩
            //进行排考场算法


        }
        #region 排考场算法

        public int ticketNum = 1;
        private void PaiKaoChang(bool IsRand)
        {
            //如果已经有排完的学生，表示已经完成了排考场
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>'' ") != 0) return;
            modeltests = blltests.GetModel(new Guid(id));
            string testtitle = modeltests.TestTitle;
            string gradeid = modeltests.GradeId.ToString();
            DateTime createTime = DateTime.Now;
            bool RoomFull = false;//判断考场是否过多

            int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 1);
            int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 2);
            int ArtOrMath;
            //1 获取考场布局
            //2 利用数据库排序
            //3 将条件排序完的第一条数据读取存放
            //4 重复2,3

            DataSet dsRooms = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "RoomId,RoomType,RoomLayout", "TestId='" + id + "' and IsEnabled = 1", "RoomNum");
            //找出最大列
            foreach (DataRow item1 in dsRooms.Tables[0].Rows)
            {
                if (RoomFull) return;
                string layout = item1["RoomLayout"].ToString();
                string s = "s";

                if ((int)item1["RoomType"] == 1)
                {
                    s = "s";
                }
                if ((int)item1["RoomType"] == 2)
                {
                    s = "c";
                }
                //找出最大列
                string[] _tempLayouts = layout.Split('|');
                int[] a = new int[_tempLayouts.Length];
                int MaxRow = 0;
                for (int i = 0; i < _tempLayouts.Length; i++)
                {
                    a[i] = Convert.ToInt32(_tempLayouts[i]);
                }
                for (int i = 0; i < _tempLayouts.Length; i++)
                {
                    if (MaxRow < a[i])
                    {
                        MaxRow = a[i];
                    }
                }

                //根据考场序号，从数据库读出一个的类型
                //对这个考场进行排布，循环多次就完成多个考场的排布
                string roomid = item1["RoomId"].ToString();



                //DataSet dsExistId = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "UserId", "TestId='" + id + "'and TicketNumber is not null");
                //if (dsExistId.Tables[0].Rows.Count != 0)
                //{
                //    foreach (DataRow item2 in dsExistId.Tables[0].Rows)
                //    {
                //        ExistId += ",'" + item2["UserId"] + "'";
                //    }
                //}
                //按上次分数编排
                if (!IsRand)
                {
                    //有文科考生，有理科考生

                    if (Art != 0 && Math != 0)//分文理
                    {
                        if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 2) != 0)
                        {//理科
                            ArtOrMath = 2;
                            if (s == "s")
                                RoomFull = PKCMathOrArtS(_tempLayouts.Length, MaxRow, roomid, createTime, RoomFull, ArtOrMath);
                            else
                                RoomFull = PKCMathOrArtC(_tempLayouts.Length, MaxRow, a, roomid, createTime, RoomFull, ArtOrMath);
                        }
                        else//文科
                        {
                            ArtOrMath = 1;
                            if (s == "s")
                                RoomFull = PKCMathOrArtS(_tempLayouts.Length, MaxRow, roomid, createTime, RoomFull, ArtOrMath);
                            else
                                RoomFull = PKCMathOrArtC(_tempLayouts.Length, MaxRow, a, roomid, createTime, RoomFull, ArtOrMath);
                        }
                    }
                    else //是不分科的或者文或理考试
                    {

                        if (s == "s")
                            RoomFull = PaiKaoChangS(_tempLayouts.Length, MaxRow, roomid, createTime, RoomFull);
                        else
                            RoomFull = PaiKaoChangC(_tempLayouts.Length, MaxRow, a, roomid, createTime, RoomFull);
                    }
                }
                else//随机排考场
                {
                    if (Art != 0 && Math != 0)//分文理
                    {
                        if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 2) != 0)
                        {//理科
                            ArtOrMath = 2;
                            if (s == "s")
                                RoomFull = PKCMathOrArtRandS(_tempLayouts.Length, MaxRow, roomid, createTime, RoomFull, ArtOrMath);
                            else
                                RoomFull = PKCMathOrArtRandC(_tempLayouts.Length, MaxRow, a, roomid, createTime, RoomFull, ArtOrMath);
                        }
                        else//文科
                        {
                            ArtOrMath = 1;
                            if (s == "s")
                                RoomFull = PKCMathOrArtRandS(_tempLayouts.Length, MaxRow, roomid, createTime, RoomFull, ArtOrMath);
                            else
                                RoomFull = PKCMathOrArtRandC(_tempLayouts.Length, MaxRow, a, roomid, createTime, RoomFull, ArtOrMath);
                        }
                    }
                    else //是不分科的或者文或理考试
                    {

                        if (s == "s")
                            RoomFull = PKCRandS(_tempLayouts.Length, MaxRow, roomid, createTime, RoomFull);
                        else
                            RoomFull = PKCRandC(_tempLayouts.Length, MaxRow, a, roomid, createTime, RoomFull);
                    }
                }
                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and RoomId='" + roomid + "'") != 0)
                {
                    //修改考场已被使用
                    modelrooms = bllrooms.GetModel(new Guid(roomid));
                    modelrooms.IsUsing = true;
                    bllrooms.Update(modelrooms);
                }

            }
        }


        public bool PaiKaoChangC(int MaxColum, int MaxRow, int[] a, string roomid, DateTime createTime, bool IsOver)
        {
            int SeatNum = 1;

            bool IsNull = false;//判断考生是否已全部排完
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];
            for (int i = 0; i < MaxColum; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一列
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");

                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = SeatNum.ToString();
                        SeatNum += 1;
                    }
                    else return IsOver = true;//所有学生都已经排完了

                    ExistId += ",'" + sum[0, 1] + "'";

                    for (int j = 1; j < MaxRow; j++)
                    {
                        if (j < a[i])//小于实际列长度，插入学生
                        {
                            DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            //如果只剩下一个班
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else//只剩下一个班
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else
                    if (i % 2 != 0)//判断偶数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow >= MaxRow - a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")//第二列第一个左侧无人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            }
                            else
                                if (sum[j - 1, 0] == "0")//上一个没有人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            }
                            else
                            {
                                if (sum[j - count, 0] == "0")//左侧没有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                               GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                                }
                                else//左侧和上一个位置都有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                                }
                            }
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                SeatNum += 1;
                                ExistId += ",'" + sum[j, 1] + "'";
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else//第一列以外的奇数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow < a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            }
                            else
                                if (sum[j - 1, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            else
                                    if (sum[j - count, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            else dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            if (dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                                if (dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        //这场考试的学生id
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;
                        blltestusers.Update(modeltestusers);
                    }
                }
                else return IsOver = true;
            }
            return IsOver;
        }
        public bool PaiKaoChangS(int MaxColum, int MaxRow, string roomid, DateTime createTime, bool IsOver)
        {
            int SeatNum = 1;//每个考场的座位号
            bool IsNull = false;
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];

            for (int i = 0; i < MaxRow; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一行
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = "" + SeatNum;
                        ExistId += ",'" + sum[0, 1] + "'";
                        SeatNum += 1;
                    }
                    else return IsOver = true;
                    for (int j = 1; j < MaxColum; j++)
                    {
                        DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ")  and SeatNumber is null", "AbilityValue desc");
                        if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum;
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
                else
                    if (i % 2 != 0)//判断偶数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null", "AbilityValue desc");
                        if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
                else//第一行以外的奇数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null", "AbilityValue desc");
                        if (ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "AbilityValue desc");
                            if (dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;

                        blltestusers.Update(modeltestusers);
                    }
                }
                else return IsOver = true;
            }
            return IsOver;
        }

        public bool PKCMathOrArtC(int MaxColum, int MaxRow, int[] a, string roomid, DateTime createTime, bool IsOver, int ArtOrMath)
        {
            int SeatNum = 1;
            bool IsNull = false;//判断考生是否已全部排完
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];
            for (int i = 0; i < MaxColum; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一列
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");

                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = SeatNum.ToString();
                        SeatNum += 1;
                    }
                    else
                    {
                        if (ArtOrMath == 2)
                        {
                            return IsOver = false;
                        }
                        else return IsOver = true;//所有学生都已经排完了
                    }


                    ExistId += ",'" + sum[0, 1] + "'";

                    for (int j = 1; j < MaxRow; j++)
                    {
                        if (j < a[i])//小于实际列长度，插入学生
                        {
                            DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            //如果只剩下一个班
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else//只剩下一个班
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    if (ArtOrMath == 2)
                                    {
                                        IsNull = true;
                                        IsOver = false;
                                        break;
                                    }
                                    else
                                    {
                                        IsNull = true;
                                        IsOver = true;
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else
                    if (i % 2 != 0)//判断偶数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow >= MaxRow - a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")//第二列第一个左侧无人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            }
                            else
                                if (sum[j - 1, 0] == "0")//上一个没有人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            }
                            else
                            {
                                if (sum[j - count, 0] == "0")//左侧没有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                               GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                                }
                                else//左侧和上一个位置都有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                                }
                            }
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                SeatNum += 1;
                                ExistId += ",'" + sum[j, 1] + "'";
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    if (ArtOrMath == 2)
                                    {
                                        IsNull = true;
                                        IsOver = false;
                                        break;
                                    }
                                    else
                                    {
                                        IsNull = true;
                                        IsOver = true;
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else//第一列以外的奇数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow < a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            }
                            else
                                if (sum[j - 1, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            else
                                    if (sum[j - count, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            else dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            if (dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                                if (dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    if (ArtOrMath == 2)
                                    {
                                        IsNull = true;
                                        IsOver = false;
                                        break;
                                    }
                                    else
                                    {
                                        IsNull = true;
                                        IsOver = true;
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        //这场考试的学生id
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;
                        blltestusers.Update(modeltestusers);
                    }
                }
                else
                {
                    if (ArtOrMath == 2)
                    {
                        return IsOver = false;
                    }
                    else
                    {
                        return IsOver = true;
                    }
                }
            }
            return IsOver;
        }
        public bool PKCMathOrArtS(int MaxColum, int MaxRow, string roomid, DateTime createTime, bool IsOver, int ArtOrMath)
        {
            int SeatNum = 1;//每个考场的座位号
            bool IsNull = false;
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];

            for (int i = 0; i < MaxRow; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一行
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = "" + SeatNum;
                        ExistId += ",'" + sum[0, 1] + "'";
                        SeatNum += 1;
                    }
                    else
                    {
                        if (ArtOrMath == 2)
                        {
                            return IsOver = false;
                        }
                        else return IsOver = true;//所有学生都已经排完了
                    }
                    for (int j = 1; j < MaxColum; j++)
                    {
                        DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ")  and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                        if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum;
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                if (ArtOrMath == 2)
                                {
                                    IsNull = true;
                                    IsOver = false;
                                    break;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                else
                    if (i % 2 != 0)//判断偶数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                        if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                if (ArtOrMath == 2)
                                {
                                    IsNull = true;
                                    IsOver = false;
                                    break;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                else//第一行以外的奇数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                        if (ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "AbilityValue desc");
                            if (dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                if (ArtOrMath == 2)
                                {
                                    IsNull = true;
                                    IsOver = false;
                                    break;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;

                        blltestusers.Update(modeltestusers);
                    }
                }
                else
                {
                    if (ArtOrMath == 2)
                    {
                        return IsOver = false;
                    }
                    else return IsOver = true;//所有学生都已经排完了
                }
            }
            return IsOver;
        }

        public bool PKCRandC(int MaxColum, int MaxRow, int[] a, string roomid, DateTime createTime, bool IsOver)
        {
            int SeatNum = 1;

            bool IsNull = false;//判断考生是否已全部排完
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];
            for (int i = 0; i < MaxColum; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一列
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");

                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = SeatNum.ToString();
                        SeatNum += 1;
                    }
                    else return IsOver = true;//所有学生都已经排完了

                    ExistId += ",'" + sum[0, 1] + "'";

                    for (int j = 1; j < MaxRow; j++)
                    {
                        if (j < a[i])//小于实际列长度，插入学生
                        {
                            DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            //如果只剩下一个班
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else//只剩下一个班
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else
                    if (i % 2 != 0)//判断偶数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow >= MaxRow - a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")//第二列第一个左侧无人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            }
                            else
                                if (sum[j - 1, 0] == "0")//上一个没有人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            }
                            else
                            {
                                if (sum[j - count, 0] == "0")//左侧没有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                               GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                                }
                                else//左侧和上一个位置都有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                                }
                            }
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                SeatNum += 1;
                                ExistId += ",'" + sum[j, 1] + "'";
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else//第一列以外的奇数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow < a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            }
                            else
                                if (sum[j - 1, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            else
                                    if (sum[j - count, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            else dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            if (dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                                if (dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        //这场考试的学生id
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;
                        blltestusers.Update(modeltestusers);
                    }
                }
                else return IsOver = true;
            }
            return IsOver;
        }
        public bool PKCRandS(int MaxColum, int MaxRow, string roomid, DateTime createTime, bool IsOver)
        {
            int SeatNum = 1;//每个考场的座位号
            bool IsNull = false;
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];

            for (int i = 0; i < MaxRow; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一行
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = "" + SeatNum;
                        ExistId += ",'" + sum[0, 1] + "'";
                        SeatNum += 1;
                    }
                    else return IsOver = true;
                    for (int j = 1; j < MaxColum; j++)
                    {
                        DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ")  and SeatNumber is null", "newid()");
                        if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum;
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
                else
                    if (i % 2 != 0)//判断偶数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null", "newid()");
                        if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
                else//第一行以外的奇数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null", "newid()");
                        if (ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null", "newid()");
                            if (dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;

                        blltestusers.Update(modeltestusers);
                    }
                }
                else return IsOver = true;
            }
            return IsOver;
        }

        public bool PKCMathOrArtRandC(int MaxColum, int MaxRow, int[] a, string roomid, DateTime createTime, bool IsOver, int ArtOrMath)
        {
            int SeatNum = 1;
            bool IsNull = false;//判断考生是否已全部排完
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];
            for (int i = 0; i < MaxColum; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一列
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");

                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = SeatNum.ToString();
                        SeatNum += 1;
                    }
                    else
                    {
                        if (ArtOrMath == 2)
                        {
                            return IsOver = false;
                        }
                        else return IsOver = true;//所有学生都已经排完了
                    }


                    ExistId += ",'" + sum[0, 1] + "'";

                    for (int j = 1; j < MaxRow; j++)
                    {
                        if (j < a[i])//小于实际列长度，插入学生
                        {
                            DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            //如果只剩下一个班
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else//只剩下一个班
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    if (ArtOrMath == 2)
                                    {
                                        IsNull = true;
                                        IsOver = false;
                                        break;
                                    }
                                    else
                                    {
                                        IsNull = true;
                                        IsOver = true;
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else
                    if (i % 2 != 0)//判断偶数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow >= MaxRow - a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")//第二列第一个左侧无人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            }
                            else
                                if (sum[j - 1, 0] == "0")//上一个没有人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            }
                            else
                            {
                                if (sum[j - count, 0] == "0")//左侧没有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                               GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                                }
                                else//左侧和上一个位置都有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                                }
                            }
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                SeatNum += 1;
                                ExistId += ",'" + sum[j, 1] + "'";
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    if (ArtOrMath == 2)
                                    {
                                        IsNull = true;
                                        IsOver = false;
                                        break;
                                    }
                                    else
                                    {
                                        IsNull = true;
                                        IsOver = true;
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else//第一列以外的奇数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow < a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            }
                            else
                                if (sum[j - 1, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            else
                                    if (sum[j - count, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            else dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            if (dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                                if (dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    if (ArtOrMath == 2)
                                    {
                                        IsNull = true;
                                        IsOver = false;
                                        break;
                                    }
                                    else
                                    {
                                        IsNull = true;
                                        IsOver = true;
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        //这场考试的学生id
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;
                        blltestusers.Update(modeltestusers);
                    }
                }
                else
                {
                    if (ArtOrMath == 2)
                    {
                        return IsOver = false;
                    }
                    else
                    {
                        return IsOver = true;
                    }
                }
            }
            return IsOver;
        }
        public bool PKCMathOrArtRandS(int MaxColum, int MaxRow, string roomid, DateTime createTime, bool IsOver, int ArtOrMath)
        {
            int SeatNum = 1;//每个考场的座位号
            bool IsNull = false;
            string ExistId = "'" + new Guid().ToString() + "'";
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];

            for (int i = 0; i < MaxRow; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一行
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = "" + SeatNum;
                        ExistId += ",'" + sum[0, 1] + "'";
                        SeatNum += 1;
                    }
                    else
                    {
                        if (ArtOrMath == 2)
                        {
                            return IsOver = false;
                        }
                        else return IsOver = true;//所有学生都已经排完了
                    }
                    for (int j = 1; j < MaxColum; j++)
                    {
                        DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ")  and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                        if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum;
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                if (ArtOrMath == 2)
                                {
                                    IsNull = true;
                                    IsOver = false;
                                    break;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                else
                    if (i % 2 != 0)//判断偶数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                        if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                if (ArtOrMath == 2)
                                {
                                    IsNull = true;
                                    IsOver = false;
                                    break;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                else//第一行以外的奇数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                        if (ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and SeatNumber is null and PropertyCode=" + ArtOrMath, "newid()");
                            if (dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                if (ArtOrMath == 2)
                                {
                                    IsNull = true;
                                    IsOver = false;
                                    break;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;

                        blltestusers.Update(modeltestusers);
                    }
                }
                else
                {
                    if (ArtOrMath == 2)
                    {
                        return IsOver = false;
                    }
                    else return IsOver = true;//所有学生都已经排完了
                }
            }
            return IsOver;
        }
        #endregion
    }
}