﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_teacher_works
    /// </summary>
    public partial class ecb_teacher_works
    {
        public ecb_teacher_works()
        { }
        #region  BasicMethod
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_teacher_works");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_teacher_works model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_teacher_works(");
            strSql.Append("Id,ColumnId,ColumnPath,UserId,UploadTime,UploadFilePath,WorksDesc,Title,UploadFileName,IsPass,IsPassBy,IsPassDate)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@UserId,@UploadTime,@UploadFilePath,@WorksDesc,@Title,@UploadFileName,@IsPass,@IsPassBy,@IsPassDate)");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@UploadTime", SqlDbType.DateTime),
					new SqlParameter("@UploadFilePath", SqlDbType.NVarChar,-1),
					new SqlParameter("@WorksDesc", SqlDbType.NVarChar,500),
					new SqlParameter("@Title", SqlDbType.NVarChar,200),
					new SqlParameter("@UploadFileName", SqlDbType.NVarChar,500),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.UserId;
            parameters[4].Value = model.UploadTime;
            parameters[5].Value = model.UploadFilePath;
            parameters[6].Value = model.WorksDesc;
            parameters[7].Value = model.Title;
            parameters[8].Value = model.UploadFileName;
            parameters[9].Value = model.IsPass;
            parameters[10].Value = model.IsPassBy;
            parameters[11].Value = model.IsPassDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_teacher_works model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_teacher_works set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("UploadTime=@UploadTime,");
            strSql.Append("UploadFilePath=@UploadFilePath,");
            strSql.Append("WorksDesc=@WorksDesc,");
            strSql.Append("Title=@Title,");
            strSql.Append("UploadFileName=@UploadFileName,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("IsPassDate=@IsPassDate");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@UploadTime", SqlDbType.DateTime),
					new SqlParameter("@UploadFilePath", SqlDbType.NVarChar,-1),
					new SqlParameter("@WorksDesc", SqlDbType.NVarChar,500),
					new SqlParameter("@Title", SqlDbType.NVarChar,200),
					new SqlParameter("@UploadFileName", SqlDbType.NVarChar,500),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.UserId;
            parameters[3].Value = model.UploadTime;
            parameters[4].Value = model.UploadFilePath;
            parameters[5].Value = model.WorksDesc;
            parameters[6].Value = model.Title;
            parameters[7].Value = model.UploadFileName;
            parameters[8].Value = model.IsPass;
            parameters[9].Value = model.IsPassBy;
            parameters[10].Value = model.IsPassDate;
            parameters[11].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_teacher_works ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_teacher_works ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_teacher_works GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,UserId,UploadTime,UploadFilePath,WorksDesc,Title,UploadFileName,IsPass,IsPassBy,IsPassDate from ecb_teacher_works ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            Model.ecb_teacher_works model = new Model.ecb_teacher_works();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_teacher_works DataRowToModel(DataRow row)
        {
            Model.ecb_teacher_works model = new Model.ecb_teacher_works();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["UploadTime"] != null && row["UploadTime"].ToString() != "")
                {
                    model.UploadTime = DateTime.Parse(row["UploadTime"].ToString());
                }
                if (row["UploadFilePath"] != null)
                {
                    model.UploadFilePath = row["UploadFilePath"].ToString();
                }
                if (row["WorksDesc"] != null)
                {
                    model.WorksDesc = row["WorksDesc"].ToString();
                }
                if (row["Title"] != null)
                {
                    model.Title = row["Title"].ToString();
                }
                if (row["UploadFileName"] != null)
                {
                    model.UploadFileName = row["UploadFileName"].ToString();
                }
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    model.IsPass = int.Parse(row["IsPass"].ToString());
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,UserId,UploadTime,UploadFilePath,WorksDesc,Title,UploadFileName,IsPass,IsPassBy,IsPassDate ");
            strSql.Append(" FROM ecb_teacher_works ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,UserId,UploadTime,UploadFilePath,WorksDesc,Title,UploadFileName ,IsPass,IsPassBy,IsPassDate");
            strSql.Append(" FROM ecb_teacher_works ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_teacher_works ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_teacher_works T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_teacher_works";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}
