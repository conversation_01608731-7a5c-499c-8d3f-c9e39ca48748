﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_time_config:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_time_config
	{
		public ecb_time_config()
		{}
		#region Model
		private Guid _id;
		private Guid _templateid;
		private int _weekday;
		private int _number;
		private DateTime _starttime;
		private DateTime _endtime;
		private int _lessontype;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 所属配置表ID
		/// </summary>
		public Guid TemplateId
		{
			set{ _templateid=value;}
			get{return _templateid;}
		}
		/// <summary>
		/// 周次
		/// </summary>
		public int Weekday
		{
			set{ _weekday=value;}
			get{return _weekday;}
		}
		/// <summary>
		/// 课节
		/// </summary>
		public int Number
		{
			set{ _number=value;}
			get{return _number;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime StartTime
		{
			set{ _starttime=value;}
			get{return _starttime;}
		}
		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 课节类型
		/// </summary>
		public int LessonType
		{
			set{ _lessontype=value;}
			get{return _lessontype;}
		}
		#endregion Model

	}
}

