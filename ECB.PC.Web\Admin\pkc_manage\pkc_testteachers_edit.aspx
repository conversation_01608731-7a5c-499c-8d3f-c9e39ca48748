﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="pkc_testteachers_edit.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_testteachers_edit" StylesheetTheme="Admin_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>监考教师</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script>
        function Reload() {
            parent.window.location.reload();
            parent.$.colorbox.close();
        }
    </script>
    
</head>
<body>
   <form id="form1" runat="server">
        <div id="container">
            <div id="sidebar-tab">
                <div id="tab-title">
                </div>
                <div id="tab-content">
                    <div id="list">
                        <table id="infoTable" cellspacing="1" cellpadding="3" border="0" align="center" style="width: 99%;" class="margin-bottom-bar">
                            <tbody>
                                <tr>
                                    <td height="25" width="30%" align="right" class="left_title_1">
                                        <span class="red">*</span>监考教师：
                                    </td>
                                    <td>
                                        <asp:HiddenField ID="hfldTeacher" runat="server" />
                                        <asp:TextBox ID="txtTeachers" runat="server" Enabled="False" Width="100px"></asp:TextBox>
                                        <asp:Panel ID="pnlxuanze" runat="server"><a id="aBtn" runat="server" onclick="SelectTeacher()" href="javascript:void(0)">选择</a></asp:Panel>                       
                                    </td>
                                </tr>
                                <tr>
                                    <td height="25" width="30%" align="right" class="left_title_2">
                                        <span class="red">*</span>监考职位：
                                    </td>
                                    <td>
                                         <asp:DropDownList ID="ddlPosition" runat="server" Width="120px"></asp:DropDownList>
                                    </td>
                                </tr>
                               
                      <%--          <tr>
                                    <td height="25" width="30%" align="right" class="left_title_1">
                                        <span class="red"></span>监考场次数量：
                                    </td>
                                    <td>
                                        <asp:TextBox ID="txtJianKaoCount" runat="server" Width="100px"></asp:TextBox>
                                    </td>
                                </tr>--%>
                                              
                            </tbody>
                        </table>
                        <div class="bottom-bar">
                            <asp:Button ID="btnAdd" runat="server" Text="提交" CssClass="btnGreen" OnClick="btnAddTeacher_Click"  OnClientClick="return check();" />         
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <script type="text/javascript" charset="utf-8">
        //
        //选择教师
        function SelectTeacher() {
            layer.open({
                type: 2,
                title: '选择',
                area: ['800px', '480px'],
                content: '/Admin/jc_infos/JC_Teacher_view.aspx?NAME=txtTeachers&VALUE=hfldTeacher&ID=' + <%=modelAreaUser.ColumnID %>
                });
        }
    </script>
</body>
</html>
