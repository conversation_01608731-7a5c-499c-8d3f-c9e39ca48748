﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_kq_class_student
    /// </summary>
    public partial class ecb_kq_class_student
    {
        public ecb_kq_class_student()
        { }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return DbHelperSQL.GetMaxID("ColumnId", "ecb_kq_class_student");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int ColumnId, Guid UserId, DateTime RecordDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_kq_class_student");
            strSql.Append(" where ColumnId=@ColumnId and UserId=@UserId and RecordDate=@RecordDate ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)           };
            parameters[0].Value = ColumnId;
            parameters[1].Value = UserId;
            parameters[2].Value = RecordDate;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_kq_class_student model, DateTime date)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //如果请假时间大于等于当前时间 操作当日表
            if (Convert.ToDateTime(date.ToString("yyyy-MM-dd")) >= Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd")))
            {
                tabName = "ecb_kq_class_student";
            }
            else
            {
                tabName = "ecb_kq_class_student_history";
            }
            strSql.Append("insert into " + tabName + "(");
            strSql.Append("Id,ColumnId,ColumnPath,UserId,RecordDate,WeekDay,KQRecords)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@UserId,@RecordDate,@WeekDay,@KQRecords)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3),
                    new SqlParameter("@WeekDay", SqlDbType.Int,4),
                    new SqlParameter("@KQRecords", SqlDbType.NVarChar,-1)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.UserId;
            parameters[4].Value = model.RecordDate;
            parameters[5].Value = model.WeekDay;
            parameters[6].Value = model.KQRecords;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_kq_class_student model, DateTime date)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //如果请假时间大于等于当前时间 操作当日表
            if (Convert.ToDateTime(date.ToString("yyyy-MM-dd")) >= Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd")))
            {
                tabName = "ecb_kq_class_student";
            }
            else
            {
                tabName = "ecb_kq_class_student_history";
            }
            strSql.Append("update " + tabName + " set ");
            strSql.Append("Id=@Id,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("WeekDay=@WeekDay,");
            strSql.Append("KQRecords=@KQRecords");
            strSql.Append(" where ColumnId=@ColumnId and UserId=@UserId and RecordDate=@RecordDate ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
                    new SqlParameter("@WeekDay", SqlDbType.Int,4),
                    new SqlParameter("@KQRecords", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.WeekDay;
            parameters[3].Value = model.KQRecords;
            parameters[4].Value = model.ColumnId;
            parameters[5].Value = model.UserId;
            parameters[6].Value = model.RecordDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int ColumnId, Guid UserId, DateTime RecordDate)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_class_student ");
            strSql.Append(" where ColumnId=@ColumnId and UserId=@UserId and RecordDate=@RecordDate ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)           };
            parameters[0].Value = ColumnId;
            parameters[1].Value = UserId;
            parameters[2].Value = RecordDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_class_student GetModel(int ColumnId, Guid UserId, DateTime RecordDate)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,UserId,RecordDate,WeekDay,KQRecords from ecb_kq_class_student ");
            strSql.Append(" where ColumnId=@ColumnId and UserId=@UserId and RecordDate=@RecordDate ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)           };
            parameters[0].Value = ColumnId;
            parameters[1].Value = UserId;
            parameters[2].Value = RecordDate;

            ECB.PC.Model.ecb_kq_class_student model = new ECB.PC.Model.ecb_kq_class_student();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_class_student DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_kq_class_student model = new ECB.PC.Model.ecb_kq_class_student();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["RecordDate"] != null && row["RecordDate"].ToString() != "")
                {
                    model.RecordDate = DateTime.Parse(row["RecordDate"].ToString());
                }
                if (row["WeekDay"] != null && row["WeekDay"].ToString() != "")
                {
                    model.WeekDay = int.Parse(row["WeekDay"].ToString());
                }
                if (row["KQRecords"] != null)
                {
                    model.KQRecords = row["KQRecords"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,UserId,RecordDate,WeekDay,KQRecords ");
            strSql.Append(" FROM ecb_kq_class_student ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,UserId,RecordDate,WeekDay,KQRecords ");
            strSql.Append(" FROM ecb_kq_class_student ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_kq_class_student ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.RecordDate desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_kq_class_student T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_class_student";
			parameters[1].Value = "RecordDate";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 学生打卡(希沃无地点判断)
        /// </summary>
        /// <param name="columnId"></param>
        /// <param name="userId"></param>
        /// <param name="Photo"></param>
        /// <param name="ClassID"></param>
        /// <param name="SignTime"></param>
        /// <returns></returns>
        public DataSet StudentSubmitAttendance(int columnId, Guid userId, string Photo,Guid ClassID,DateTime SignTime)
        {
            SqlParameter[] parameters = {
                new SqlParameter("@UserId",SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@columnId",SqlDbType.Int),
                new SqlParameter("@Photo",SqlDbType.NVarChar,500),
                 new SqlParameter("@ClassId",SqlDbType.UniqueIdentifier,16),
                 new SqlParameter("@SignTime",SqlDbType.DateTime)
            };
            parameters[0].Value = userId;
            parameters[1].Value = columnId;
            parameters[2].Value = Photo;
            parameters[3].Value = ClassID;
            parameters[4].Value = SignTime;
            try
            {
                return DbHelperSQL.RunProcedureReturnMoreDateTable("UP_StudentClassAttendance_Seewo", parameters);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetOpenJson(string FileName, string tabName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  " + FileName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ClassNum '节次' ,SUM(CASE WHEN b.[Status] = 1 THEN 1 ELSE 0 END) '正常',SUM(CASE WHEN b.[Status] = 2 THEN 1 ELSE 0 END) '迟到',SUM(CASE WHEN b.[Status] = 3 THEN 1 ELSE 0 END) '缺卡',SUM(CASE WHEN b.[Status] = 4 THEN 1 ELSE 0 END) '请假'");
            strSql.Append(" FROM (select UserId,RecordDate,KQRecords from ecb_kq_class_student union all select UserId,RecordDate,KQRecords from ecb_kq_class_student_history) a cross apply openjson(a.KQRecords) with(ClassId uniqueidentifier,[Status] INT,ClassNum INT,SignTime DATETIME,BeginTime DATETIME) as b left join JC_ClassInfos c on b.ClassId=c.ID where BeginTime<=GETDATE() ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }

            strSql.Append(" group by ClassNum order by ClassNum ");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select UserId as ID,StudentName 'UserName',COUNT(1) total");
            strSql.Append(" FROM (select UserId,RecordDate,KQRecords from ecb_kq_class_student union all select UserId,RecordDate,KQRecords from ecb_kq_class_student_history) a cross apply openjson(a.KQRecords) with(ClassId uniqueidentifier,[Status] INT,ClassNum INT,SignTime DATETIME,BeginTime DATETIME) as b left join JC_StudentInfos c on a.UserId=c.ID   where BeginTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" group by UserId,StudentName order by total desc");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 导出班级课堂考勤统计
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet ExecelOut(string where)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ClassId,ClassName,'第'+CAST( ClassNum as nvarchar)+'节' '节次' ,SUM(CASE WHEN b.[Status] = 1 THEN 1 ELSE 0 END) '正常(人)',SUM(CASE WHEN b.[Status] = 2 THEN 1 ELSE 0 END) '迟到(人)',SUM(CASE WHEN b.[Status] = 3 THEN 1 ELSE 0 END) '缺卡(人)',SUM(CASE WHEN b.[Status] = 4 THEN 1 ELSE 0 END) '请假(人)'");
            strSql.Append(" FROM (select UserId,RecordDate,KQRecords from ecb_kq_class_student union all select UserId,RecordDate,KQRecords from ecb_kq_class_student_history) a cross apply openjson(a.KQRecords) with(ClassId uniqueidentifier,[Status] INT,ClassNum INT,SignTime DATETIME,BeginTime DATETIME) as b LEFT JOIN JC_ClassInfos D ON b.ClassId=D.ID where BeginTime<=GETDATE() ");
            if (where.Trim() != "")
            {
                strSql.Append(" and " + where);
            }

            strSql.Append(" group by ClassNum,b.ClassId,ClassName,OrderId order by OrderId,ClassName,ClassNum  ");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        ///  修改考勤状态
        /// </summary>
        /// <param name="columnId">地区</param>
        /// <param name="day">日期</param>
        /// <param name="classNum">课节数量</param>
        /// <param name="preStatus">改之前的状态</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <param name="classIds">筛选的班级id</param>
        /// <returns></returns>
        public bool ModifyStatus(int columnId, DateTime day, int preStatus, int toStatus, Guid gradeId, Guid classId)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //修改时间
            if (day.Date == DateTime.Now.Date)
            {
                tabName = "ecb_kq_class_student";
            }
            else
            {
                tabName = "ecb_kq_class_student_history";
            }
            strSql.AppendLine("UPDATE " + tabName + " SET KQRecords = (");
            strSql.AppendLine("SELECT b.SubjectCode,b.ClassId,b.ClassNum,b.Photo,b.PlaceId,b.LeaveId,b.BeginTime,b.OverTime,");
            strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN @ToStatus ELSE b.[Status] END [Status],");
            if (toStatus == 1)
            {
                // 更改为正常
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN DATEADD(mi,-1,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 2)
            {
                // 更改为迟到
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN DATEADD(mi,2,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN 120 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 3)
            {
                // 更改为缺卡
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN '" + day.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
            }

            strSql.AppendLine(" FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords)");
            strSql.AppendLine("WITH (SubjectCode NVARCHAR(2),[Status] INT,ClassId UNIQUEIDENTIFIER,ClassNum INT,SignTime DATETIME,Photo NVARCHAR(250),PlaceId UNIQUEIDENTIFIER,LeaveId UNIQUEIDENTIFIER,LateTime INT,BeginTime DATETIME,OverTime DATETIME) b");
            strSql.AppendLine("WHERE a.ColumnId=@ColumnId AND a.RecordDate=@Date AND a.UserId=" + tabName + ".UserId FOR json PATH");
            strSql.AppendLine(") WHERE ColumnId=@ColumnId AND RecordDate=@Date");
            //修改 班级考勤
            if (classId != Guid.Empty)
            {
                strSql.AppendLine("AND EXISTS (");
                strSql.AppendLine("SELECT b.ClassId FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords) WITH (ClassId UNIQUEIDENTIFIER) b");
                strSql.AppendLine("WHERE a.RecordDate=@Date AND a.UserId=" + tabName + ".UserId AND b.ClassId ='" + classId + "'");
                strSql.AppendLine(")");
            }
            //修改 年级考勤
            else if (gradeId != Guid.Empty)
            {
                strSql.AppendLine("AND EXISTS (");
                strSql.AppendLine("SELECT b.ClassId FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords) WITH (ClassId UNIQUEIDENTIFIER) b");
                strSql.AppendLine("WHERE a.RecordDate=@Date AND a.UserId=" + tabName + ".UserId AND b.ClassId in (select ID from JC_ClassInfos where GradeId='"+ gradeId + "')");
                strSql.AppendLine(")");
            }
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@PreStatus",SqlDbType.Int,4),
                new SqlParameter("@ToStatus",SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = day;
            parameters[2].Value = preStatus;
            parameters[3].Value = toStatus;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }
        /// <summary>
        ///  修改考勤状态
        /// </summary>
        /// <param name="columnId">地区</param>
        /// <param name="day">日期</param>
        /// <param name="classNum">课节数量</param>
        /// <param name="preStatus">改之前的状态</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <param name="classIds">筛选的班级id</param>
        /// <returns></returns>
        public bool UpdateTeacherAttendClassRecord(int columnId, DateTime day, int preStatus, int toStatus)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //修改时间
            if (day.Date == DateTime.Now.Date)
            {
                tabName = "ecb_kq_class_teacher";
            }
            else
            {
                tabName = "ecb_kq_class_teacher_history";
            }
            strSql.AppendLine("UPDATE " + tabName + " SET KQRecords = (");
            strSql.AppendLine("SELECT b.ClassId,b.PlaceId,b.ClassNum,b.SubjectCode,b.Photo,b.IsSubstitute,b.SubstituteUserId,b.BeginTime,b.OverTime,");
            strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN @ToStatus ELSE b.[Status] END [Status],");
            if (toStatus == 1)
            {
                // 更改为正常
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN DATEADD(mi,-1,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 2)
            {
                // 更改为迟到
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN DATEADD(mi,2,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN 120 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 3)
            {
                // 更改为缺卡
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN '" + day.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.BeginTime<=GETDATE() AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
            }

            strSql.AppendLine(" FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords)");
            strSql.AppendLine("WITH (ClassId UNIQUEIDENTIFIER,PlaceId UNIQUEIDENTIFIER,ClassNum INT,SubjectCode NVARCHAR(2),[Status] INT,SignTime DATETIME,Photo NVARCHAR(250),LateTime INT,IsSubstitute INT,SubstituteUserId UNIQUEIDENTIFIER,BeginTime DATETIME,OverTime DATETIME) b");
            strSql.AppendLine("WHERE a.ColumnId=@ColumnId AND a.RecordDate=@Date AND a.UserId=" + tabName + ".UserId FOR json PATH");
            strSql.AppendLine(") WHERE ColumnId=@ColumnId AND RecordDate=@Date");

            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@PreStatus",SqlDbType.Int,4),
                new SqlParameter("@ToStatus",SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = day;
            parameters[2].Value = preStatus;
            parameters[3].Value = toStatus;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }
        /// <summary>
        /// 导出学生班级课堂考勤统计
        /// </summary>
        public DataSet ExecelDetailInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select StudentName '姓名',CONVERT(varchar(100),SignTime, 23) as '日期',ClassName '班级', DictText '科目',ClassNum '课节',case when Status = 2  then '迟到' when Status = 3 then '缺卡' when Status = 4 then '请假' else '' end '考勤状态',CASE WHEN Status=3 THEN '' ELSE CONVERT(varchar(100),SignTime, 120) END  AS '考勤时间'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_student union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_class_student_history ) a cross apply openjson(KQRecords) with(ClassId UNIQUEIDENTIFIER,[Status] INT,ClassNum INT,SubjectCode INT,SignTime DATETIME,BeginTime DATETIME) as b LEFT JOIN JC_StudentInfos C ON a.UserId=C.ID left join JC_ClassInfos d on b.ClassId=d.ID left join Site_Dictionary e on b.SubjectCode=e.DictValue and e.DictTypeId=28 AND e.ColumnId IN (0,a.ColumnId) where BeginTime<=GETDATE()  AND  Status<>1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" ORDER BY RecordDate,OrderId,ClassNum,SignTime, StudentName ");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        ///  修改课堂考勤状态
        /// </summary>
        /// <param name="columnId">地区</param>
        /// <param name="day">日期</param>
        /// <param name="classNum">课节数量</param>
        /// <param name="preStatus">改之前的状态</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <param name="classIds">筛选的班级id</param>
        /// <returns></returns>
        public bool ModifyClassStatus(Guid userId, DateTime day, int classNum, int toStatus)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //修改时间
            if (day.Date == DateTime.Now.Date)
            {
                tabName = "ecb_kq_class_student";
            }
            else
            {
                tabName = "ecb_kq_class_student_history";
            }
            strSql.AppendLine("UPDATE " + tabName + " SET KQRecords = (");
            strSql.AppendLine("SELECT b.SubjectCode,b.ClassId,b.ClassNum,b.Photo,b.PlaceId,b.LeaveId,b.BeginTime,b.OverTime,");
            strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN @ToStatus ELSE b.[Status] END [Status],");
            if (toStatus == 1)
            {
                // 更改为正常
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN DATEADD(mi,-1,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN 0 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 2)
            {
                // 更改为迟到
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN DATEADD(mi,2,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN 120 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 3)
            {
                // 更改为缺卡
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN '" + day.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN 0 ELSE b.LateTime END LateTime");
            }

            strSql.AppendLine(" FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords)");
            strSql.AppendLine("WITH (SubjectCode NVARCHAR(2),[Status] INT,ClassId UNIQUEIDENTIFIER,ClassNum INT,SignTime DATETIME,Photo NVARCHAR(250),PlaceId UNIQUEIDENTIFIER,LeaveId UNIQUEIDENTIFIER,LateTime INT,BeginTime DATETIME,OverTime DATETIME) b");
            strSql.AppendLine("WHERE a.UserId=@UserId AND a.RecordDate=@Date FOR json PATH");
            strSql.AppendLine(") WHERE UserId=@UserId AND RecordDate=@Date");

            SqlParameter[] parameters = {
                new SqlParameter("@UserId",SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@ToStatus",SqlDbType.Int,4),
                new SqlParameter("@ClassNum",SqlDbType.Int,4)
            };
            parameters[0].Value = userId;
            parameters[1].Value = day;
            parameters[2].Value = toStatus;
            parameters[3].Value = classNum;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }
        /// <summary>
        ///  修改考勤状态
        /// </summary>
        /// <param name="day">日期</param>
        /// <param name="classNum">课节数量</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <returns></returns>
        public bool UpdateTeacherClassAttend(Guid UserId, DateTime day, int ClassNum, int toStatus)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //修改时间
            if (day.Date == DateTime.Now.Date)
            {
                tabName = "ecb_kq_class_teacher";
            }
            else
            {
                tabName = "ecb_kq_class_teacher_history";
            }
            strSql.AppendLine("UPDATE " + tabName + " SET KQRecords = (");
            strSql.AppendLine("SELECT b.ClassId,b.PlaceId,b.ClassNum,b.SubjectCode,b.Photo,b.IsSubstitute,b.SubstituteUserId,b.BeginTime,b.OverTime,");
            strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN @ToStatus ELSE b.[Status] END [Status],");
            if (toStatus == 1)
            {
                // 更改为正常
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN DATEADD(mi,-1,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN 0 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 2)
            {
                // 更改为迟到
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN DATEADD(mi,2,b.BeginTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN 120 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 3)
            {
                // 更改为缺卡
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN '" + day.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN b.ClassNum=@ClassNum THEN 0 ELSE b.LateTime END LateTime");
            }

            strSql.AppendLine(" FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords)");
            strSql.AppendLine("WITH (ClassId UNIQUEIDENTIFIER,PlaceId UNIQUEIDENTIFIER,ClassNum INT,SubjectCode NVARCHAR(2),[Status] INT,SignTime DATETIME,Photo NVARCHAR(250),LateTime INT,IsSubstitute INT,SubstituteUserId UNIQUEIDENTIFIER,BeginTime DATETIME,OverTime DATETIME) b");
            strSql.AppendLine("WHERE a.UserId=@UserId AND a.RecordDate=@Date FOR json PATH");
            strSql.AppendLine(") WHERE UserId=@UserId AND RecordDate=@Date");

            SqlParameter[] parameters = {
                new SqlParameter("@UserId",SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@ToStatus",SqlDbType.Int,4),
                new SqlParameter("@ClassNum",SqlDbType.Int,4)
            };
            parameters[0].Value = UserId;
            parameters[1].Value = day;
            parameters[2].Value = toStatus;
            parameters[3].Value = ClassNum;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }


        /// <summary>
        /// 删除指定日期之后的课堂考勤数据,不包括历史数据
        /// </summary>
        /// <param name="UserId">学生id</param>
        /// <param name="RecordDate">日期</param>
        /// <returns></returns>
        public bool Delete(Guid UserId, DateTime RecordDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_class_student");
            strSql.Append(" where UserId=@UserId AND RecordDate>@RecordDate");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16) ,
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = UserId;
            parameters[1].Value = RecordDate;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

