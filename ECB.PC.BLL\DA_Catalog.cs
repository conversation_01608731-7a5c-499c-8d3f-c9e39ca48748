﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:DA_Catalog
    /// </summary>
    public partial class DA_Catalog
    {
      
        public DA_Catalog()
        { }

        #region  Method

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            int result = DbHelperSQL.RunProcedure("UP_DA_Catalog_Exists", parameters, out rowsAffected);
            if (result == 1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.DA_Catalog model)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@EduStageCode", SqlDbType.NVarChar,4),
					new SqlParameter("@TermType", SqlDbType.NVarChar,1),
					new SqlParameter("@Name", SqlDbType.NVarChar,100),
					new SqlParameter("@GradeCode", SqlDbType.NVarChar,10),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@UpdateTime", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.SchoolColumnId;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.StudentId;
            if (model.TermId == Guid.Empty)
            {
                parameters[4].Value = DBNull.Value;
            }
            else
            {
                parameters[4].Value = model.TermId;
            }
            if (model.GradeId == Guid.Empty)
            {
                parameters[5].Value = DBNull.Value;
            }
            else
            {
                parameters[5].Value = model.GradeId;
            }
            if (model.ClassId == Guid.Empty)
            {
                parameters[6].Value = DBNull.Value;
            }
            else
            {
                parameters[6].Value = model.ClassId;
            }

            parameters[7].Value = model.SchoolYear;
            parameters[8].Value = model.EduStageCode;
            parameters[9].Value = model.TermType;
            parameters[10].Value = model.Name;
            parameters[11].Value = model.GradeCode;
            parameters[12].Value = model.CreateTime;
            parameters[13].Value = model.UpdateTime;

            DbHelperSQL.RunProcedure("UP_DA_Catalog_ADD", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.DA_Catalog model)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@EduStageCode", SqlDbType.NVarChar,4),
					new SqlParameter("@TermType", SqlDbType.NVarChar,1),
					new SqlParameter("@Name", SqlDbType.NVarChar,100),
					new SqlParameter("@GradeCode", SqlDbType.NVarChar,10),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@UpdateTime", SqlDbType.DateTime)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.SchoolColumnId;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.StudentId;
            if (model.TermId == Guid.Empty)
            {
                parameters[4].Value = DBNull.Value;
            }
            else
            {
                parameters[4].Value = model.TermId;
            }
            if (model.GradeId == Guid.Empty)
            {
                parameters[5].Value = DBNull.Value;
            }
            else
            {
                parameters[5].Value = model.GradeId;
            }
            if (model.ClassId == Guid.Empty)
            {
                parameters[6].Value = DBNull.Value;
            }
            else
            {
                parameters[6].Value = model.ClassId;
            }
            parameters[7].Value = model.SchoolYear;
            parameters[8].Value = model.EduStageCode;
            parameters[9].Value = model.TermType;
            parameters[10].Value = model.Name;
            parameters[11].Value = model.GradeCode;
            parameters[12].Value = model.CreateTime;
            parameters[13].Value = model.UpdateTime;

            DbHelperSQL.RunProcedure("UP_DA_Catalog_Update", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            DbHelperSQL.RunProcedure("UP_DA_Catalog_Delete", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from DA_Catalog ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_Catalog GetModel(Guid ID)
        {
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            ECB.PC.Model.DA_Catalog model = new ECB.PC.Model.DA_Catalog();
            DataSet ds = DbHelperSQL.RunProcedure("UP_DA_Catalog_GetModel", parameters, "ds");
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_Catalog DataRowToModel(DataRow row)
        {
            ECB.PC.Model.DA_Catalog model = new ECB.PC.Model.DA_Catalog();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["SchoolColumnId"] != null && row["SchoolColumnId"].ToString() != "")
                {
                    model.SchoolColumnId = int.Parse(row["SchoolColumnId"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["TermId"] != null && row["TermId"].ToString() != "")
                {
                    model.TermId = new Guid(row["TermId"].ToString());
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
                if (row["EduStageCode"] != null)
                {
                    model.EduStageCode = row["EduStageCode"].ToString();
                }
                if (row["TermType"] != null)
                {
                    model.TermType = row["TermType"].ToString();
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["GradeCode"] != null)
                {
                    model.GradeCode = row["GradeCode"].ToString();
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["UpdateTime"] != null && row["UpdateTime"].ToString() != "")
                {
                    model.UpdateTime = DateTime.Parse(row["UpdateTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,SchoolColumnId,SchoolColumnPath,StudentId,TermId,GradeId,ClassId,SchoolYear,EduStageCode,TermType,Name,GradeCode,CreateTime,UpdateTime ");
            strSql.Append(" FROM DA_Catalog ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,SchoolColumnId,SchoolColumnPath,StudentId,TermId,GradeId,ClassId,SchoolYear,EduStageCode,TermType,Name,GradeCode,CreateTime,UpdateTime ");
            strSql.Append(" FROM DA_Catalog ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM DA_Catalog ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from DA_Catalog T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        #endregion  Method

        #region  MethodEx
        #endregion  MethodEx
    }
}

