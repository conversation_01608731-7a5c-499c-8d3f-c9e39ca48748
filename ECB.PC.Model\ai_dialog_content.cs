﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// AI对话内容
    /// </summary>
    [Serializable]
    public partial class ai_dialog_content
    {
        public ai_dialog_content()
        { }
        #region Model
        private Guid _id;
        private Guid _dialogid;
        private int _role;
        private Guid _userid;
        private string _contents;
        private DateTime _createtime;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 对话Id
        /// </summary>
        public Guid DialogId
        {
            set { _dialogid = value; }
            get { return _dialogid; }
        }
        /// <summary>
        /// 身份 枚举：0 system 1 assistant 2 user
        /// </summary>
        public int Role
        {
            set { _role = value; }
            get { return _role; }
        }
        /// <summary>
        /// 用户Id 系统是0000
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 内容
        /// </summary>
        public string Contents
        {
            set { _contents = value; }
            get { return _contents; }
        }
        /// <summary>
        /// 创建时间
        ///
        /// </summary>
        public DateTime CreateTime
        {
            set { _createtime = value; }
            get { return _createtime; }
        }
        #endregion Model

    }
}

