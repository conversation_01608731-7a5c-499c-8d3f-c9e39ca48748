﻿<%@ Page Language="C#" AutoEventWireup="true" StylesheetTheme="Admin_Default" EnableEventValidation="false" CodeBehind="pkc_tests_edit.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_tests_edit" %>

<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>添加考试</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
     <script src="/js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style>
        span {
            color: gray;
        }
    </style>
    <script>
        function setCheckedAll(obj, targetId) {
            var checked = $(obj).attr("checked");
            $('#' + targetId).find('input[type="checkbox"]').attr("checked", checked)
            console.log(checked)
        }

        function showMessage(success, content, ico) {
            var _index = parent.layer.alert(content, {
                icon: ico,
                shade: ['0.1', '#000'],
                time: success == "true" ? 1000 : 0,
                end: function () {
                    if (success == "true") {
						parent.__doPostBack('btnSearch', '1'); 
						parent.layer.close(parent.layer.getFrameIndex(window.name));
                    }
                }
            });
        }
	</script>

</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <div id="sidebar-tab">
                <div id="tab-title">
                </div>
                <div id="tab-content">
                    <div id="list">
                        <div style="display: block" id="div1">
                            <table id="infoTable" cellspacing="1" cellpadding="3" border="0" align="center" style="width: 99%;" class="margin-bottom-bar">
                                <tbody>
                                    <tr>
                                        <td height="25" width="20%" align="right" class="left_title_2">
                                            <span class="red">*</span>考试名称：
                                        </td>
                                        <td>
                                            <input class="input-normal control-text" type="text" runat="server" id="txtTestTitle" />
                                        </td>
                                    </tr>
                                    <asp:Panel ID="pnlAdd" runat="server">
                                        <tr>
                                            <td height="25" width="20%" align="right" class="left_title_1">
                                                <span class="red">*</span>考试对象：
                                            </td>
                                            <td>
                                                <asp:Panel ID="pnlArea" runat="server">
                                                    <asp:Panel ID="pnlSchool" runat="server">
                                                        <asp:DropDownList ID="S1" runat="server" EnableViewState="True">
                                                            <asp:ListItem Value="" Selected="True">==请选择地市==</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:DropDownList ID="S2" runat="server" EnableViewState="True">
                                                            <asp:ListItem Value="" Selected="True">==请选择县区==</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:DropDownList ID="S3" runat="server" EnableViewState="True" ValidationGroup="1">
                                                            <asp:ListItem Value="" Selected="True">==请选择学校==</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:DropDownList ID="ddlChildSchool" runat="server" EnableViewState="True" Style="display: none;">
                                                            <asp:ListItem Value="" Selected="True">==请选附属学校==</asp:ListItem>
                                                        </asp:DropDownList>
                                                    </asp:Panel>
                                                    <asp:DropDownList ID="ddlGrade" runat="server"  OnSelectedIndexChanged="ddlGrade_SelectedIndexChanged" AutoPostBack="true"></asp:DropDownList>
                                                    <asp:DropDownList ID="ddlPropertyCode" runat="server" Width="120px"  OnSelectedIndexChanged="DDLPropertyCode_SelectedIndexChanged" AutoPostBack="true"></asp:DropDownList>
                                                    <asp:Panel ID="PnlClass" runat="server">
                                                        <asp:CheckBoxList ID="chkClassList" runat="server" RepeatDirection="Horizontal" RepeatColumns="5"></asp:CheckBoxList>
                                                        <label runat="server" id="chkClassAll" style="margin: 3px;" visible="false">
                                                            <input type="checkbox" onclick="setCheckedAll(this,'chkClassList')" />全选</label>
                                                    </asp:Panel>
                                                </asp:Panel>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title_2"><span class="red">*</span>科目：</td>
                                            <td>
                                                <asp:CheckBoxList ID="chklSubject" runat="server" RepeatDirection="Horizontal" RepeatColumns="5"></asp:CheckBoxList>
                                                <label runat="server" id="chkSubjectAll" style="margin: 3px;" visible="false">
                                                    <input type="checkbox" onclick="setCheckedAll(this,'chklSubject')" />全选</label>
                                            </td>
                                        </tr>
                                    </asp:Panel>
                                    <asp:Panel ID="pnlSyncTest" runat="server">
                                        <tr>
                                            <td height="25" width="20%" align="right" class="left_title_1"><span class="red"></span>同步到考试管理：</td>
                                            <td>
                                                <input type="checkbox" value="启用" id="ckbSyncTest" runat="server">
                                                <span class="auxiliary-text">选择“是”则在考试管理中同步此考试信息</span>
                                            </td>
                                        </tr>
                                    </asp:Panel>
                                    <tr>
                                        <td height="25" width="20%" align="right" class="left_title_2">
                                            <span class="red">*</span>考试开始时间：
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txtBeginDate" autocomplete="off" runat="server" Width="200px" onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'txtEndDate\')}',dateFmt:'yyyy-MM-dd HH:mm'})" CssClass="Wdate"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td height="25" width="20%" align="right" class="left_title_1">
                                            <span class="red">*</span>考试结束时间：
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txtEndDate" autocomplete="off" runat="server" Width="200px" onclick="WdatePicker({minDate:'#F{$dp.$D(\'txtBeginDate\')}',dateFmt:'yyyy-MM-dd HH:mm'})" CssClass="Wdate"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr style="display: none;">
                                        <td height="25" width="20%" align="right" class="left_title_2"><span class="red"></span>是否可查：</td>
                                        <td>
                                            <input type="checkbox" checked="checked" value="启用" id="ckbIsEnabled" runat="server">
                                            <span class="auxiliary-text">选择“是”则学生、家长可在系统平台查看排考场的结果</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td height="25" width="20%" align="right" class="left_title_2">考试说明：</td>
                                        <td>
                                            <asp:TextBox ID="txtRemark" runat="server" Width="400px" Height="80" TextMode="MultiLine"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <asp:Panel ID="pnlImport" runat="server">
                                        <tr>
                                            <td class="left_title_1"><span class="red"></span>是否导入考生：</td>
                                            <td>
                                                <input type="checkbox" value="导入" id="ckbImport" runat="server" checked="checked">
                                                <span class="auxiliary-text">选择“是”则立即导入本次考试考生，导入考生需要点时间，请耐心等待！</span>
                                            </td>
                                        </tr>
                                    </asp:Panel>
                                </tbody>
                            </table>
                            <div class="bottom-bar">
                                <asp:Button ID="btnSubmit" runat="server" Text="提交" CssClass="btnGreen" OnClientClick="return check();" OnClick="btnAddTest_Click" />
                                <input id="btnClose" type="button" value="关闭" class="btnGray" onclick="CloseBox()"  />
                            </div>
                        </div>
                        <div style="display: none;" id="loading">
                            处理中……
                        </div>
                    </div>
                    <asp:HiddenField ID="hidSchoolId" runat="server" Value="" />
                    <asp:HiddenField ID="hidChildSchool" runat="server" Value="" />
                    <asp:HiddenField ID="hidGradeId" runat="server" />
                </div>
            </div>
        </div>
    </form>

    <div class="demo-content">
        <div class="row">
            <script type="text/javascript">
                //以下是实现级联菜单
                // 市
                function select1() {
                    $.ajax(
                        {
                            type: "post",
                            url: "/admin/ajax/getSchool.ashx",
                            data: { "type": "province" },
                            success: function (msg) {
                                for (var i = 0; i < msg.length; i++) {
                                    $("#S1").append("<option value=" + msg[i].ProvinceID + ">" + msg[i].ProvinceName + "</option>");
                                }
                                select2();
                            }
                        })
                };
                // 县区
                function select2() {
                    $("#S2").html("");
                    $("#S2").append("<option value=\"\" selected=\"selected\">==请选择县区==</option>");
                    $.ajax(
                        {
                            type: "post",
                            url: "/admin/ajax/getSchool.ashx",
                            data: { "type": "city", "provinceID": $('#S1').val() },
                            success: function (msg) {
                                for (var i = 0; i < msg.length; i++) {
                                    $("#S2").append("<option value=" + msg[i].CityID + ">" + msg[i].CityName + "</option>");
                                }
                                select3();
                            }
                        })
                };
                // 学校
                function select3() {
                    $("#S3").html("");
                    $("#S3").append("<option value=\"\" selected=\"selected\">==请选择学校==</option>");
                    $.ajax(
                        {
                            type: "post",
                            url: "/admin/ajax/getSchool.ashx",
                            data: { "type": "district", "cityID": $('#S2').val() },
                            success: function (msg) {
                                for (var i = 0; i < msg.length; i++) {
                                    $("#S3").append("<option value=" + msg[i].DistrictID + ">" + msg[i].DistrictName + "</option>");
                                }
                                selectChildSchool();
                            }
                        })
                };
                // 附属学校
                function selectChildSchool() {
                    $("#ddlChildSchool").html("");
                    $("#ddlChildSchool").append("<option value=\"\" selected=\"selected\">==请选择附属学校==</option>");
                    $.ajax(
                        {
                            type: "post",
                            url: "/admin/ajax/getSchool.ashx",
                            data: { "type": "childSchool", "schoolId": $('#S3').val() },
                            success: function (msg) {
                                for (var i = 0; i < msg.length; i++) {
                                    $("#ddlChildSchool").append("<option value=" + msg[i].SchoolId + ">" + msg[i].SchoolName + "</option>");
                                }
                                if (msg.length > 0) {
                                    $('#ddlChildSchool').css("display", "inline-block");
                                }
                                else {
                                    $('#ddlChildSchool').css("display", "none");
                                }
                            }
                        });
                    selectGrade();
                };
                function selectGrade() {
                    $('#ddlGrade').html("");
                    $('#ddlGrade').append("<option value=\"\" selected=\"selected\">==请选择年级==</option>");
                    $.ajax({
                        type: "post",
                        dataType: "json",
                        url: "/admin/ajax/getSchool.ashx",
                        data: { "type": "getGradeByTerm", "termId": $('#ddlTerm').val() },
                        success: function (msg) {
                            for (var i = 0; i < msg.length; i++) {
                                $("#ddlGrade").append("<option value=" + msg[i].ID + ">" + msg[i].GradeName + "</option>");
                            }
                            selectPropertyCode();
                        }
                    });
                };

                function selectPropertyCode() {
                    $('#ddlPropertyCode').html("");
                    $('#ddlPropertyCode').append("<option value=\"\" selected=\"selected\">==请选择==</option>");
                    $.ajax({
                        type: "post",
                        dataType: "json",
                        url: "/admin/ajax/getSchool.ashx",
                        data: { "type": "getPropertyCode", "gradeId": $('#ddlGrade').val() },
                        success: function (msg) {
                            for (var i = 0; i < msg.length; i++) {
                                $("#ddlPropertyCode").append("<option value=" + msg[i].ID + ">" + msg[i].PropertyCode + "</option>");
                            }
                        }
                    });
                };
                $(document).ready(function () {
                    //级联菜单
                    $('#S1').bind("change", select2);
                    $('#S2').bind("change", select3);
                    $('#S3').bind("change", selectChildSchool);
                    $('#ddlChildSchool').bind("change", selectGrade);
                    $('#ddlGrade').bind("change", selectPropertyCode);
                    // 年级
                    $('#ddlGrade').change(function () {
                        $('#ddlPropertyCode').val("");

                        $('#hidGradeId').val($('#ddlGrade').val());
                    });
                    //  第一次加载判断是否有附属学校，有则显示
                    if ($("#ddlChildSchool").children().length > 1) {
                        $('#ddlChildSchool').css("display", "inline-block");
                    }
                    // 每次变动都给隐藏域赋值
                    $('#S1').change(function () {
                        $('#hidChildSchool').val("");
                        $('#hidSchoolId').val($('#S1').val());
                    });

                    $('#S2').change(function () {
                        $('#hidChildSchool').val("");
                        if ($('#S2').val() != "") {
                            $('#hidSchoolId').val($('#S2').val());
                        }
                        else {
                            if ($('#S1').length > 0 && $('#S1').val() != "") {
                                $('#hidSchoolId').val($('#S1').val());
                            }
                            else {
                                $('#hidSchoolId').val("");
                            }
                        }
                    });

                    $('#S3').change(function () {
                        $('#hidChildSchool').val("");
                        if ($('#S3').val() != "") {
                            $('#hidSchoolId').val($('#S3').val());
                        }
                        else {
                            if ($('#S2').length > 0 && $('#S2').val() != "") {
                                $('#hidSchoolId').val($('#S2').val());
                            }
                            else if ($('#S1').length > 0 && $('#S1').val() != "") {
                                $('#hidSchoolId').val($('#S1').val());
                            }
                            else {
                                $('#hidSchoolId').val("");
                            }
                        }
                    });
                    $('#ddlChildSchool').change(function () {
                        $('#hidChildSchool').val("");
                        if ($('#ddlChildSchool').length > 0 && $('#ddlChildSchool').val() != "") {
                            // 给学校隐藏域赋值
                            if ($('#hidSchoolId').length > 0) {
                                $('#hidSchoolId').val($('#S3').val());
                            }
                            // 给附属学校隐藏域赋值
                            $('#hidChildSchool').val($('#ddlChildSchool').val());
                        }
                        else if ($('#S3').length > 0 && $('#S3').val() != "") {
                            $('#hidSchoolId').val($('#S3').val());
                        }
                        else if ($('#S2').length > 0 && $('#S2').val() != "") {
                            $('#hidSchoolId').val($('#S2').val());
                        }
                        else if ($('#S1').length > 0 && $('#S1').val() != "") {
                            $('#hidSchoolId').val($('#S1').val());
                        }
                        else {
                            $('#hidSchoolId').val("");
                        }
                    });
                    //结束级联菜单                   
                });
            </script>
            <script type="text/javascript">
                function check() {
                    if (!$('#txtTestTitle').val()) {
                        layer.msg("请填写考试名称");
                        $('#txtTestTitle').focus();
                        return false;
                    }

                    if (!$('#txtBeginDate').val()) {
                        layer.msg("请填写考试开始时间");
                        $('#txtBeginDate').focus();
                        return false;
                    }
                    if (!$('#txtEndDate').val()) {
                        layer.msg("请填写考试结束时间");
                        $('#txtEndDate').focus();
                        return false;
                    }
                    //if (!$('#txtRemark').val()) {
                    //    alert("请填写考试说明");
                    //    $('#txtRemark').focus();
                    //    return false;
                    //}
                    $("#div1").hide().addClass();
                    $("#loading").show().addClass();
                }
            </script>
        </div>
    </div>
</body>
</html>

