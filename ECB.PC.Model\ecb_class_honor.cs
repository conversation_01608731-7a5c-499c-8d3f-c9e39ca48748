﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_class_honor:班级荣誉类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_class_honor
    {
        public ecb_class_honor()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private Guid _classid;
        private string _awardsname;
        private string _typecode;
        private string _levecode;
        private string _rankcode;
        private DateTime? _awardsdate;
        private string _awardsdesc;
        private DateTime? _creatdate;
        private DateTime? _lasteditdate;
        private Guid _lasteditby;
        private int? _is_show;
        private int? _is_top;
        private int? _ispass;
        private Guid _ispassby;
        private DateTime? _ispassdate;
        /// <summary>
        /// ID
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int? ColumnID
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 班级ID
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 荣誉名称
        /// </summary>
        public string AwardsName
        {
            set { _awardsname = value; }
            get { return _awardsname; }
        }
        /// <summary>
        /// 荣誉类型
        /// </summary>
        public string TypeCode
        {
            set { _typecode = value; }
            get { return _typecode; }
        }
        /// <summary>
        /// 荣誉级别
        /// </summary>
        public string LeveCode
        {
            set { _levecode = value; }
            get { return _levecode; }
        }
        /// <summary>
        /// 荣誉等级
        /// </summary>
        public string RankCode
        {
            set { _rankcode = value; }
            get { return _rankcode; }
        }
        /// <summary>
        /// 获奖日期
        /// </summary>
        public DateTime? AwardsDate
        {
            set { _awardsdate = value; }
            get { return _awardsdate; }
        }
        /// <summary>
        /// 获奖描述
        /// </summary>
        public string AwardsDesc
        {
            set { _awardsdesc = value; }
            get { return _awardsdesc; }
        }
        /// <summary>
        /// 获奖日期
        /// </summary>
        public DateTime? CreatDate
        {
            set { _creatdate = value; }
            get { return _creatdate; }
        }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? LastEditDate
        {
            set { _lasteditdate = value; }
            get { return _lasteditdate; }
        }
        /// <summary>
        /// 修改人
        /// </summary>
        public Guid LastEditBy
        {
            set { _lasteditby = value; }
            get { return _lasteditby; }
        }
        /// <summary>
        /// 是否展示
        /// </summary>
        public int? is_show
        {
            set { _is_show = value; }
            get { return _is_show; }
        }
        /// <summary>
        /// 是否置顶
        /// </summary>
        public int? is_top
        {
            set { _is_top = value; }
            get { return _is_top; }
        }
        /// <summary>
		/// 是都通过审核
		/// </summary>
		public int? IsPass
        {
            set { _ispass = value; }
            get { return _ispass; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid IsPassBy
        {
            set { _ispassby = value; }
            get { return _ispassby; }
        }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? IsPassDate
        {
            set { _ispassdate = value; }
            get { return _ispassdate; }
        }
        #endregion Model

    }
}