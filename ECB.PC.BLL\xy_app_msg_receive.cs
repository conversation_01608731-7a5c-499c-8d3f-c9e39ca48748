﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;//Please add references
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:xy_app_msg_receive
    /// </summary>
    public partial class xy_app_msg_receive
    {
        public xy_app_msg_receive()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from xy_app_msg_receive");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.xy_app_msg_receive model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into xy_app_msg_receive(");
            strSql.Append("id,msgId,cateId,catePath,userId,isRead,read_time,msg_title,msg_content,send_time,push_time,gt_result,gt_taskId,gt_status)");
            strSql.Append(" values (");
            strSql.Append("@id,@msgId,@cateId,@catePath,@userId,@isRead,@read_time,@msg_title,@msg_content,@send_time,@push_time,@gt_result,@gt_taskId,@gt_status)");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@cateId", SqlDbType.NVarChar,50),
					new SqlParameter("@catePath", SqlDbType.NVarChar,50),
					new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@isRead", SqlDbType.Int,4),
					new SqlParameter("@read_time", SqlDbType.DateTime),
					new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
					new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
					new SqlParameter("@send_time", SqlDbType.DateTime),
					new SqlParameter("@push_time", SqlDbType.DateTime),
					new SqlParameter("@gt_result", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_status", SqlDbType.NVarChar,50)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.msgId;
            parameters[2].Value = model.cateId;
            parameters[3].Value = model.catePath;
            parameters[4].Value = model.userId;
            parameters[5].Value = model.isRead;
            parameters[6].Value = model.read_time;
            parameters[7].Value = model.msg_title;
            parameters[8].Value = model.msg_content;
            parameters[9].Value = model.send_time;
            parameters[10].Value = model.push_time;
			parameters[11].Value = model.gt_result;
			parameters[12].Value = model.gt_taskId;
			parameters[13].Value = model.gt_status;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.xy_app_msg_receive model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update xy_app_msg_receive set ");
            strSql.Append("msgId=@msgId,");
            strSql.Append("cateId=@cateId,");
            strSql.Append("catePath=@catePath,");
            strSql.Append("userId=@userId,");
            strSql.Append("isRead=@isRead,");
            strSql.Append("read_time=@read_time,");
            strSql.Append("msg_title=@msg_title,");
            strSql.Append("msg_content=@msg_content,");
            strSql.Append("send_time=@send_time,");
            strSql.Append("push_time=@push_time,");
            strSql.Append("gt_result=@gt_result,");
            strSql.Append("gt_taskId=@gt_taskId,");
            strSql.Append("gt_status=@gt_status");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@cateId", SqlDbType.NVarChar,50),
					new SqlParameter("@catePath", SqlDbType.NVarChar,50),
					new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@isRead", SqlDbType.Int,4),
					new SqlParameter("@read_time", SqlDbType.DateTime),
					new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
					new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
					new SqlParameter("@send_time", SqlDbType.DateTime),
					new SqlParameter("@push_time", SqlDbType.DateTime),
					new SqlParameter("@gt_result", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50),
					new SqlParameter("@gt_status", SqlDbType.NVarChar,50),
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.msgId;
            parameters[1].Value = model.cateId;
            parameters[2].Value = model.catePath;
            parameters[3].Value = model.userId;
            parameters[4].Value = model.isRead;
            parameters[5].Value = model.read_time;
            parameters[6].Value = model.msg_title;
            parameters[7].Value = model.msg_content;
            parameters[8].Value = model.send_time;
            parameters[9].Value = model.push_time;
            parameters[10].Value = model.gt_result;
            parameters[11].Value = model.gt_taskId;
            parameters[12].Value = model.gt_status;
            parameters[13].Value = model.id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from xy_app_msg_receive ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from xy_app_msg_receive ");
            strSql.Append(" where id in (" + idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.xy_app_msg_receive GetModel(Guid id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 id,msgId,cateId,catePath,userId,isRead,read_time,msg_title,msg_content,send_time,push_time,gt_result,gt_taskId,gt_status from xy_app_msg_receive ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = id;

            ECB.PC.Model.xy_app_msg_receive model = new ECB.PC.Model.xy_app_msg_receive();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.xy_app_msg_receive DataRowToModel(DataRow row)
        {
            ECB.PC.Model.xy_app_msg_receive model = new ECB.PC.Model.xy_app_msg_receive();
            if (row != null)
            {
                if (row["id"] != null && row["id"].ToString() != "")
                {
                    model.id = new Guid(row["id"].ToString());
                }
                if (row["msgId"] != null && row["msgId"].ToString() != "")
                {
                    model.msgId = new Guid(row["msgId"].ToString());
                }
                if (row["cateId"] != null)
                {
                    model.cateId = row["cateId"].ToString();
                }
                if (row["catePath"] != null)
                {
                    model.catePath = row["catePath"].ToString();
                }
                if (row["userId"] != null && row["userId"].ToString() != "")
                {
                    model.userId = new Guid(row["userId"].ToString());
                }
                if (row["isRead"] != null && row["isRead"].ToString() != "")
                {
                    model.isRead = int.Parse(row["isRead"].ToString());
                }
                if (row["read_time"] != null && row["read_time"].ToString() != "")
                {
                    model.read_time = DateTime.Parse(row["read_time"].ToString());
                }
                if (row["msg_title"] != null)
                {
                    model.msg_title = row["msg_title"].ToString();
                }
                if (row["msg_content"] != null)
                {
                    model.msg_content = row["msg_content"].ToString();
                }
                if (row["send_time"] != null && row["send_time"].ToString() != "")
                {
                    model.send_time = DateTime.Parse(row["send_time"].ToString());
                }
                if (row["push_time"] != null && row["push_time"].ToString() != "")
                {
                    model.push_time = DateTime.Parse(row["push_time"].ToString());
                }
                if (row["gt_result"] != null)
                {
                    model.gt_result = row["gt_result"].ToString();
                }
                if (row["gt_taskId"] != null)
                {
                    model.gt_taskId = row["gt_taskId"].ToString();
                }
                if (row["gt_status"] != null)
                {
                    model.gt_status = row["gt_status"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select id,msgId,cateId,catePath,userId,isRead,read_time,msg_title,msg_content,send_time,push_time,gt_result,gt_taskId,gt_status ");
            strSql.Append(" FROM xy_app_msg_receive ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" id,msgId,cateId,catePath,userId,isRead,read_time,msg_title,msg_content,send_time,push_time,gt_result,gt_taskId,gt_status ");
            strSql.Append(" FROM xy_app_msg_receive ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM xy_app_msg_receive ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from xy_app_msg_receive T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "xy_app_msg_receive";
            parameters[1].Value = "id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        public bool AddReciveMsg(Model.xy_app_msg_receive model_recivemsg, Guid id, int? recive_num)
        {
            ArrayList array = new ArrayList();
            //新增接受表数据
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into xy_app_msg_receive(");
            strSql.Append("id,msgId,cateId,catePath,userId,isRead,msg_title,msg_content,send_time)");
            strSql.AppendFormat(" values ('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}')", Guid.NewGuid(), model_recivemsg.msgId, model_recivemsg.cateId, model_recivemsg.catePath, model_recivemsg.userId, model_recivemsg.isRead, model_recivemsg.msg_title, model_recivemsg.msg_content, model_recivemsg.send_time);
            array.Add(strSql.ToString());
            //更新上一条信息的状态
            array.Add(string.Format("update xy_app_msg_receive set isRead='{1}' where id='{0}'", id, 2));
            //更新主表的发送数目
            array.Add(string.Format("update xy_app_msg set receive_num='{1}', receive_type='{2}' where id='{0}'", model_recivemsg.msgId, recive_num + 1, 3));
            int rows = DbHelperSQL.ExecuteSqlTran(array);
            if (rows > 0)
            {
                return true;
            }
            return false;
        }
        #endregion  ExtensionMethod
        /// <summary>
        /// 获取阅读状态
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public DataSet GetRead(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.* FROM xy_app_msg_receive a LEFT JOIN dbo.xy_app_msg b ON a.msgId=b.id");
            strSql.Append("  WHERE b.businessId='" + Id + "'");
            return DbHelperSQL.Query(strSql.ToString());
        }


        /// <summary>
        /// 新增消息推送
        /// </summary>
        /// <param name="app_msg"></param>
        /// <param name="receive">记录表</param>
        /// <returns></returns>
        public bool Addappmsg(List<Model.xy_app_msg> app_msg, List<Model.xy_app_msg_receive> receive)
        {
            int rows = 0;
            BLL.xy_app_msg bll_appmsg = new BLL.xy_app_msg();
            using (SqlConnection conn = new SqlConnection(DbHelperSQL.ConnMain[0]))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    StringBuilder strSql = new StringBuilder();
                    if (app_msg.Count > 0)
                    {
                        foreach (Model.xy_app_msg model in app_msg)
                        {
                            strSql.Clear();
                            strSql.Append("insert into xy_app_msg(");
                            strSql.Append("id,cateId,catePath,businessId,send_time,send_userId,msg_type,receive_type,receive_num,msg_title,msg_content,gt_result,gt_taskId,gt_status)");
                            strSql.Append(" values (");
                            strSql.Append("@id,@cateId,@catePath,@businessId,@send_time,@send_userId,@msg_type,@receive_type,@receive_num,@msg_title,@msg_content,@gt_result,@gt_taskId,@gt_status)");
                            SqlParameter[] parameters = {
                            new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
                            new SqlParameter("@cateId", SqlDbType.NVarChar,50),
                            new SqlParameter("@catePath", SqlDbType.NVarChar,50),
                            new SqlParameter("@businessId", SqlDbType.NVarChar,50),
                            new SqlParameter("@send_time", SqlDbType.DateTime),
                            new SqlParameter("@send_userId", SqlDbType.UniqueIdentifier,16),
                            new SqlParameter("@msg_type", SqlDbType.Int,4),
                            new SqlParameter("@receive_type", SqlDbType.Int,4),
                            new SqlParameter("@receive_num", SqlDbType.Int,4),
                            new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
                            new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
                            new SqlParameter("@gt_result", SqlDbType.NVarChar,50),
                            new SqlParameter("@gt_taskId", SqlDbType.NVarChar,50),
                            new SqlParameter("@gt_status", SqlDbType.NVarChar,50)};
                            parameters[0].Value = model.id;
                            parameters[1].Value = model.cateId;
                            parameters[2].Value = model.catePath;
                            parameters[3].Value = model.businessId;
                            parameters[4].Value = model.send_time;
                            parameters[5].Value = model.send_userId;
                            parameters[6].Value = model.msg_type;
                            parameters[7].Value = model.receive_type;
                            parameters[8].Value = model.receive_num;
                            parameters[9].Value = model.msg_title;
                            parameters[10].Value = model.msg_content;
                            parameters[11].Value = model.gt_result;
                            parameters[12].Value = model.gt_taskId;
                            parameters[13].Value = model.gt_status;

                            PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
                            rows = cmd.ExecuteNonQuery();
                            cmd.Parameters.Clear();
                        }
                    }

                    if (rows > 0)
                    {
                        if (receive.Count > 0)
                        {
                            foreach (Model.xy_app_msg_receive item in receive)
                            {
                                strSql.Clear();
                                strSql.Append("insert into xy_app_msg_receive(");
                                strSql.Append("id,msgId,cateId,catePath,userId,isRead,read_time,msg_title,msg_content,send_time,push_time)");
                                strSql.Append(" values (");
                                strSql.Append("@id,@msgId,@cateId,@catePath,@userId,@isRead,@read_time,@msg_title,@msg_content,@send_time,@push_time)");
                                SqlParameter[] viewerParam = {
                                new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
                                new SqlParameter("@msgId", SqlDbType.UniqueIdentifier,16),
                                new SqlParameter("@cateId", SqlDbType.NVarChar,50),
                                new SqlParameter("@catePath", SqlDbType.NVarChar,50),
                                new SqlParameter("@userId", SqlDbType.UniqueIdentifier,16),
                                new SqlParameter("@isRead", SqlDbType.Int,4),
                                new SqlParameter("@read_time", SqlDbType.DateTime),
                                new SqlParameter("@msg_title", SqlDbType.NVarChar,50),
                                new SqlParameter("@msg_content", SqlDbType.NVarChar,-1),
                                new SqlParameter("@send_time", SqlDbType.DateTime),
                                new SqlParameter("@push_time",SqlDbType.DateTime)};
                                viewerParam[0].Value = Guid.NewGuid();
                                viewerParam[1].Value = item.msgId;
                                viewerParam[2].Value = item.cateId;
                                viewerParam[3].Value = item.catePath;
                                viewerParam[4].Value = item.userId;
                                viewerParam[5].Value = item.isRead;
                                viewerParam[6].Value = item.read_time;
                                viewerParam[7].Value = item.msg_title;
                                viewerParam[8].Value = item.msg_content;
                                viewerParam[9].Value = item.send_time;
                                viewerParam[10].Value = item.push_time;
                                PrepareCommand(cmd, conn, null, strSql.ToString(), viewerParam);
                                cmd.ExecuteNonQuery();
                                cmd.Parameters.Clear();
                            }

                        }
                    }
                    tx.Commit();
                }
                catch (System.Data.SqlClient.SqlException E)
                {
                    rows = 0;
                    tx.Rollback();
                    throw new Exception(E.Message);
                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                }
            }
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        private static void PrepareCommand(SqlCommand cmd, SqlConnection conn, SqlTransaction trans, string cmdText, SqlParameter[] cmdParms)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            cmd.Connection = conn;
            cmd.CommandText = cmdText;
            if (trans != null)
                cmd.Transaction = trans;
            cmd.CommandType = CommandType.Text;//cmdType;
            if (cmdParms != null)
            {
                foreach (SqlParameter parameter in cmdParms)
                {
                    if ((parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Input) &&
                        (parameter.Value == null))
                    {
                        parameter.Value = DBNull.Value;
                    }
                    cmd.Parameters.Add(parameter);
                }
            }
        }

        /// <summary>
        /// 获取未读消息数量
        /// </summary>
        /// <param name="businessId">业务id</param>
        /// <returns></returns>
        public int GetNoReadCount(string businessId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM xy_app_msg_receive a LEFT JOIN xy_app_msg b ON a.msgId = b.id ");
            strSql.Append(" where businessId=@businessId and isRead=0 ");
            SqlParameter[] parameters = new SqlParameter[] { new SqlParameter("@businessId", SqlDbType.NVarChar, 50) };
            parameters[0].Value = businessId;
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 获取已读消息数量
        /// </summary>
        /// <param name="businessId">业务id</param>
        /// <returns></returns>
        public int GetReadedCount(string businessId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM xy_app_msg_receive a LEFT JOIN xy_app_msg b ON a.msgId = b.id ");
            strSql.Append(" where businessId=@businessId and isRead=1 ");
            SqlParameter[] parameters = new SqlParameter[] { new SqlParameter("@businessId", SqlDbType.NVarChar, 50) };
            parameters[0].Value = businessId;
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
    }
}

