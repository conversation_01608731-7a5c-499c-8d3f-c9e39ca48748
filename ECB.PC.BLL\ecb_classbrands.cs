﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    public partial class ecb_ClassBrands
    {
        public ecb_ClassBrands()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_ClassBrands");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_ClassBrands model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_classbrands(");
            strSql.Append("Id,ColumnId,ColumnPath,SeriaNo,ClassId,Name,LastEditTime,Version,BrandsType,LastOpenTime,IsOnline,OnlineTime,IsTest,PlaceId,FaceModelVersion,IndexModel)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@SeriaNo,@ClassId,@Name,@LastEditTime,@Version,@BrandsType,@LastOpenTime,@IsOnline,@OnlineTime,@IsTest,@PlaceId,@FaceModelVersion,@IndexModel)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,200),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,200),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@Version", SqlDbType.NVarChar,200),
                    new SqlParameter("@BrandsType", SqlDbType.Int,4),
                    new SqlParameter("@LastOpenTime", SqlDbType.DateTime),
                    new SqlParameter("@IsOnline", SqlDbType.Int,4),
                    new SqlParameter("@OnlineTime", SqlDbType.Decimal,9),
                    new SqlParameter("@IsTest", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FaceModelVersion", SqlDbType.NVarChar,50),
                    new SqlParameter("@IndexModel", SqlDbType.NVarChar,50)
            };
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.SeriaNo;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.Name;
            parameters[6].Value = model.LastEditTime;
            parameters[7].Value = model.Version;
            parameters[8].Value = model.BrandsType;
            parameters[9].Value = model.LastOpenTime;
            parameters[10].Value = model.IsOnline;
            parameters[11].Value = model.OnlineTime;
            parameters[12].Value = model.IsTest;
            parameters[13].Value = model.PlaceId;
            parameters[14].Value = model.FaceModelVersion;
            parameters[15].Value = model.IndexModel;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_ClassBrands model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_classbrands set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("SeriaNo=@SeriaNo,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("Name=@Name,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("Version=@Version,");
            strSql.Append("BrandsType=@BrandsType,");
            strSql.Append("LastOpenTime=@LastOpenTime,");
            strSql.Append("IsOnline=@IsOnline,");
            strSql.Append("OnlineTime=@OnlineTime,");
            strSql.Append("IsTest=@IsTest,");
            strSql.Append("PlaceId=@PlaceId,");
            strSql.Append("FaceModelVersion=@FaceModelVersion,");
            strSql.Append("IndexModel=@IndexModel");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,200),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,200),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@Version", SqlDbType.NVarChar,200),
                    new SqlParameter("@BrandsType", SqlDbType.Int,4),
                    new SqlParameter("@LastOpenTime", SqlDbType.DateTime),
                    new SqlParameter("@IsOnline", SqlDbType.Int,4),
                    new SqlParameter("@OnlineTime", SqlDbType.Decimal,9),
                    new SqlParameter("@IsTest", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FaceModelVersion", SqlDbType.NVarChar,50),
                    new SqlParameter("@IndexModel", SqlDbType.NVarChar,50)
            };
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.SeriaNo;
            parameters[3].Value = model.ClassId;
            parameters[4].Value = model.Name;
            parameters[5].Value = model.LastEditTime;
            parameters[6].Value = model.Version;
            parameters[7].Value = model.BrandsType;
            parameters[8].Value = model.LastOpenTime;
            parameters[9].Value = model.IsOnline;
            parameters[10].Value = model.OnlineTime;
            parameters[11].Value = model.IsTest;
            parameters[12].Value = model.Id;
            parameters[13].Value = model.PlaceId;
            parameters[14].Value = model.FaceModelVersion;
            parameters[15].Value = model.IndexModel;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_ClassBrands ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_ClassBrands ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_ClassBrands GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,SeriaNo,ClassId,Name,LastEditTime,Version,BrandsType,LastOpenTime,IsOnline,OnlineTime,IsTest,PlaceId,FaceModelVersion,IndexModel from ecb_classbrands ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.ecb_ClassBrands model = new Model.ecb_ClassBrands();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_ClassBrands DataRowToModel(DataRow row)
        {
            Model.ecb_ClassBrands model = new Model.ecb_ClassBrands();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["SeriaNo"] != null)
                {
                    model.SeriaNo = row["SeriaNo"].ToString();
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
                if (row["Version"] != null)
                {
                    model.Version = row["Version"].ToString();
                }
                if (row["BrandsType"] != null && row["BrandsType"].ToString() != "")
                {
                    model.BrandsType = int.Parse(row["BrandsType"].ToString());
                }
                if (row["LastOpenTime"] != null && row["LastOpenTime"].ToString() != "")
                {
                    model.LastOpenTime = DateTime.Parse(row["LastOpenTime"].ToString());
                }
                if (row["IsOnline"] != null && row["IsOnline"].ToString() != "")
                {
                    model.IsOnline = int.Parse(row["IsOnline"].ToString());
                }
                if (row["OnlineTime"] != null && row["OnlineTime"].ToString() != "")
                {
                    model.OnlineTime = decimal.Parse(row["OnlineTime"].ToString());
                }
                if (row["IsTest"] != null && row["IsTest"].ToString() != "")
                {
                    model.IsTest = int.Parse(row["IsTest"].ToString());
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
                if (row["FaceModelVersion"] != null)
                {
                    model.FaceModelVersion = row["FaceModelVersion"].ToString();
                }
                if (row["IndexModel"] != null)
                {
                    model.IndexModel = row["IndexModel"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,SeriaNo,ClassId,Name,LastEditTime,Version,BrandsType,LastOpenTime,IsOnline,OnlineTime,IsTest,PlaceId,FaceModelVersion,IndexModel ");
            strSql.Append(" FROM ecb_classbrands ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,SeriaNo,ClassId,Name,LastEditTime,Version,BrandsType,LastOpenTime,IsOnline,OnlineTime,IsTest,PlaceId,FaceModelVersion,IndexModel ");
            strSql.Append(" FROM ecb_classbrands ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_ClassBrands ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_ClassBrands T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_ClassBrands";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获取当前学校当前学年年级
        /// </summary>
        /// <param name="strTime">当前学年</param>
        /// <returns></returns>
        public DataSet GetGradeList(string SchoolColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM dbo.JC_GradeInfos ");
            if (SchoolColumnPath.Trim() != "")
            {
                strSql.Append(" where SchoolYear='" + getNowYear(SchoolColumnPath) + "' AND SchoolColumnPath='" + SchoolColumnPath + "'");
            }
            strSql.Append(" order by OrderId");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取当前学年班级
        /// </summary>
        /// <param name="SchoolColumnPath"></param>
        /// <param name="GradeID"></param>
        /// <returns></returns>
        public DataSet GetClassList(string SchoolColumnPath, Guid GradeID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM dbo.JC_ClassInfos ");
            if (GradeID != null)
            {
                strSql.Append(" where  SchoolColumnPath='" + SchoolColumnPath + "' AND SchoolYear='" + getNowYear(SchoolColumnPath) + "' AND GradeId='" + GradeID + "'");
            }
            strSql.Append(" order by OrderId");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 得到一个对象
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public Model.ecb_ClassBrands GetModel(string SeriaNo)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,SeriaNo,ClassId,Name,LastEditTime,Version,BrandsType,LastOpenTime,IsOnline,OnlineTime,IsTest,PlaceId,FaceModelVersion,IndexModel from ecb_ClassBrands ");
            strSql.Append(" where SeriaNo=@SeriaNo ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,200)            };
            parameters[0].Value = SeriaNo;

            Model.ecb_ClassBrands model = new Model.ecb_ClassBrands();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string SeriaNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_ClassBrands");
            strSql.Append(" where SeriaNo=@SeriaNo ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SeriaNo", SqlDbType.NVarChar,200)            };
            parameters[0].Value = SeriaNo;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 查看班级表中是否存在班级
        /// </summary>
        public bool ExistsClass(string ClassName, string SchoolColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from JC_ClassInfos");
            strSql.Append(" where ClassName=@ClassName and SchoolColumnPath=@SchoolColumnPath and SchoolYear='" + getNowYear(SchoolColumnPath) + "' ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassName", SqlDbType.NVarChar,20),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,500)
                                        };
            parameters[0].Value = ClassName;
            parameters[1].Value = SchoolColumnPath;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取当前学年
        /// </summary>
        /// <returns></returns>
        public static string getNowYear(string SchoolColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            string strTime = "";
            strSql.Append("select  * from dbo.JC_TermInfos ");
            if (SchoolColumnPath != null)
            {
                strSql.Append(" where  SchoolColumnPath='" + SchoolColumnPath + "' AND IsCurrentTerm=1 ");
            }
            DataTable dtSchoolYear = DbHelperSQL.Query(strSql.ToString()).Tables[0];
            if (dtSchoolYear.Rows.Count > 0)
            {
                strTime = dtSchoolYear.Rows[0]["SchoolYear"].ToString();
            }
            return strTime;
        }
        /// <summary>
        /// 班级是否存在班牌
        /// </summary>
        /// <param name="ClassId"></param>
        /// <returns></returns>
        public bool IsExistsClass(Guid ClassId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_ClassBrands");
            strSql.Append(" where ClassId=@ClassId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)         };
            parameters[0].Value = ClassId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 是否存在相同班牌序列号
        /// </summary>
        /// <param name="SerialNo">班牌序列号</param>
        /// <returns></returns>
        public bool IsExistsSerialNo(string SerialNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_ClassBrands");
            strSql.Append(" where SeriaNo=@SerialNo ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SerialNo", SqlDbType.NVarChar,200)           };
            parameters[0].Value = SerialNo;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 获取班牌列表-班级名称 连表
        /// </summary>
        /// <param name="strTime">当前学年</param>
        /// <returns></returns>
        public DataSet GetBrandsList(int columnId, string gradeId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.Id,ColumnId,ColumnPath,SeriaNo,ClassId,ClassName Name,LastEditTime,Version,BrandsType,LastOpenTime,IsOnline,OnlineTime,IsTest  FROM dbo.ecb_classbrands a INNER JOIN dbo.JC_ClassInfos b ON a.ClassId=b.ID ");
            strSql.Append(" where  ColumnId='" + columnId + "' AND GradeId='" + gradeId + "'");
            strSql.Append(" order by SeriaNo");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取班牌列表-班级名称 连表
        /// </summary>
        /// <param name="strTime">当前学年</param>
        /// <returns></returns>
        public DataSet GetBrandsList(string tableName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select *  FROM  " + tableName);

            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by SeriaNo");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_ClassBrands GetModelByPlaceId(Guid placeId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,SeriaNo,ClassId,Name,LastEditTime,Version,BrandsType,LastOpenTime,IsOnline,OnlineTime,IsTest,PlaceId,FaceModelVersion,IndexModel from ecb_classbrands ");
            strSql.Append(" where PlaceId=@PlaceId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = placeId;

            Model.ecb_ClassBrands model = new Model.ecb_ClassBrands();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool UpdateIndexModel(string indexModel, string where)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_ClassBrands set IndexModel='" + indexModel + "'");
            if (!string.IsNullOrEmpty(where))
            {
                strSql.Append(" where " + where);
            }
            else
            {
                strSql.Append(" where 1=2");
            }

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetEcbList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.Id,a.ColumnId,a.ColumnPath,a.SeriaNo,a.ClassId,a.BrandsType,a.PlaceId,");
            strSql.Append("case when isnull(b.ClassName,'')<>'' then b.ClassName ");
            strSql.Append("when isnull(c.RoomName,'')<>'' then c.RoomName ");
            strSql.Append("when isnull(d.Name,'')<>'' then d.Name ");
            strSql.Append("else a.Name end as Name,");
            strSql.Append("case when ISNULL(b.OrderId,'')<>'' then b.OrderId else 99 end as OrderId ");
            strSql.Append("FROM ecb_classbrands a left join JC_ClassInfos b on a.ClassId=b.ID left join ecb_FunctionRoom c on a.PlaceId=c.PlaceId left join ecb_loupai_info d on a.PlaceId=d.PlaceId");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by OrderId");
            return DbHelperSQL.Query(strSql.ToString());
        }


        public DataSet GetEcbPlaceList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select DISTINCT  PlaceId FROM ecb_classbrands ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(int columnId, int brandType)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select distinct PlaceId,Name FROM ecb_classbrands where ColumnId=@ColumnId and BrandsType=@BrandsType");
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId", SqlDbType.Int,4),
                new SqlParameter("@BrandsType",SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = brandType;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        #endregion  ExtensionMethod
    }
}
