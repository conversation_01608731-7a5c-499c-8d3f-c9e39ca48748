﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 流动红旗历史记录
	/// </summary>
	[Serializable]
	public partial class ecb_RedFlag
	{
		public ecb_RedFlag()
		{}
				#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _classid;
		private Guid _termid;
		private DateTime _createtime;
		private bool _isenabled;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 班级ID
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学期ID
		/// </summary>
		public Guid TermId
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 是否启用
		/// </summary>
		public bool IsEnabled
		{
			set{ _isenabled=value;}
			get{return _isenabled;}
		}
		#endregion Model

	}
}

