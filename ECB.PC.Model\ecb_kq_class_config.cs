﻿
using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 上课考勤配置表
    /// </summary>
    [Serializable]
    public partial class ecb_kq_class_config
    {
        public ecb_kq_class_config()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private int _startpoint;
        private int _iscontinuoussignonce;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区编号
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 上课前多少分钟开始打卡
        /// </summary>
        public int StartPoint
        {
            set { _startpoint = value; }
            get { return _startpoint; }
        }
        /// <summary>
        /// 连堂课是否只打一次卡
        /// </summary>
        public int IsContinuousSignOnce
        {
            set { _iscontinuoussignonce = value; }
            get { return _iscontinuoussignonce; }
        }
        #endregion Model

    }
}

