﻿
using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 教师上课考勤记录表
    /// </summary>
    [Serializable]
    public partial class ecb_kq_class_teacher
    {
        public ecb_kq_class_teacher()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _userid;
        private DateTime? _signtime;
        private Guid _classid;
        private string _subjectcode;
        private int _status;
        private int _issubstitute;
        private int _classnum;
        public string Photo;
        public Guid _placeid;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 考勤人
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// Datetime
        /// </summary>
        public DateTime? SignTime
        {
            set { _signtime = value; }
            get { return _signtime; }
        }
        /// <summary>
        /// 班级
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 课程
        /// </summary>
        public string SubjectCode
        {
            set { _subjectcode = value; }
            get { return _subjectcode; }
        }
        /// <summary>
        /// 考勤状态
        /// </summary>
        public int Status
        {
            set { _status = value; }
            get { return _status; }
        }
        /// <summary>
        /// IsSubstitute
        /// </summary>
        public int IsSubstitute
        {
            set { _issubstitute = value; }
            get { return _issubstitute; }
        }
        /// <summary>
        /// 课节数
        /// </summary>
        public int ClassNum
        {
            get { return _classnum; }
            set { _classnum = value; }
        }
        /// <summary>
        /// 考勤场地
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        #endregion Model

    }
}

