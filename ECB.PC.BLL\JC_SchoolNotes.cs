﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:JC_SchoolNotes
    /// </summary>
    public partial class JC_SchoolNotes
    {
        public JC_SchoolNotes()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from JC_SchoolNotes");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.JC_SchoolNotes model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into JC_SchoolNotes(");
            strSql.Append("ID,SchoolColumnID,SchoolColumnPath,NoteTitle,NoteContent,NoteDate,UserID,NoteType,IsRemind,IsEnabled,SendType,SchoolYear,TermID,Attachment,SendArea,Creator,Ids,IsPass,IsPassBy,IsPassDate)");
            strSql.Append(" values (");
            strSql.Append("@ID,@SchoolColumnID,@SchoolColumnPath,@NoteTitle,@NoteContent,@NoteDate,@UserID,@NoteType,@IsRemind,@IsEnabled,@SendType,@SchoolYear,@TermID,@Attachment,@SendArea,@Creator,@Ids,@IsPass,@IsPassBy,@IsPassDate)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@NoteTitle", SqlDbType.NVarChar,255),
                    new SqlParameter("@NoteContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@NoteDate", SqlDbType.DateTime),
                    new SqlParameter("@UserID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@NoteType", SqlDbType.Int,4),
                    new SqlParameter("@IsRemind", SqlDbType.Bit,1),
                    new SqlParameter("@IsEnabled", SqlDbType.Bit,1),
                    new SqlParameter("@SendType", SqlDbType.Int,4),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Attachment", SqlDbType.NVarChar,-1),
                    new SqlParameter("@SendArea", SqlDbType.NVarChar,50),
                    new SqlParameter("@Creator", SqlDbType.NVarChar,50),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.SchoolColumnID;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.NoteTitle;
            parameters[4].Value = model.NoteContent;
            parameters[5].Value = model.NoteDate;
            parameters[6].Value = model.UserID;
            parameters[7].Value = model.NoteType;
            parameters[8].Value = model.IsRemind;
            parameters[9].Value = model.IsEnabled;
            parameters[10].Value = model.SendType;
            parameters[11].Value = model.SchoolYear;
            parameters[12].Value = model.TermID;
            parameters[13].Value = model.Attachment;
            parameters[14].Value = model.SendArea;
            parameters[15].Value = model.Creator;
            parameters[16].Value = model.Ids;
            parameters[17].Value = model.IsPass;
            parameters[18].Value = model.IsPassBy;
            parameters[19].Value = model.IsPassDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.JC_SchoolNotes model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update JC_SchoolNotes set ");
            strSql.Append("SchoolColumnID=@SchoolColumnID,");
            strSql.Append("SchoolColumnPath=@SchoolColumnPath,");
            strSql.Append("NoteTitle=@NoteTitle,");
            strSql.Append("NoteContent=@NoteContent,");
            strSql.Append("NoteDate=@NoteDate,");
            strSql.Append("UserID=@UserID,");
            strSql.Append("NoteType=@NoteType,");
            strSql.Append("IsRemind=@IsRemind,");
            strSql.Append("IsEnabled=@IsEnabled,");
            strSql.Append("SendType=@SendType,");
            strSql.Append("SchoolYear=@SchoolYear,");
            strSql.Append("TermID=@TermID,");
            strSql.Append("Attachment=@Attachment,");
            strSql.Append("SendArea=@SendArea,");
            strSql.Append("Creator=@Creator,");
            strSql.Append("Ids=@Ids,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("IsPassDate=@IsPassDate");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@NoteTitle", SqlDbType.NVarChar,255),
                    new SqlParameter("@NoteContent", SqlDbType.NVarChar,-1),
                    new SqlParameter("@NoteDate", SqlDbType.DateTime),
                    new SqlParameter("@UserID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@NoteType", SqlDbType.Int,4),
                    new SqlParameter("@IsRemind", SqlDbType.Bit,1),
                    new SqlParameter("@IsEnabled", SqlDbType.Bit,1),
                    new SqlParameter("@SendType", SqlDbType.Int,4),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Attachment", SqlDbType.NVarChar,-1),
                    new SqlParameter("@SendArea", SqlDbType.NVarChar,50),
                    new SqlParameter("@Creator", SqlDbType.NVarChar,50),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.SchoolColumnID;
            parameters[1].Value = model.SchoolColumnPath;
            parameters[2].Value = model.NoteTitle;
            parameters[3].Value = model.NoteContent;
            parameters[4].Value = model.NoteDate;
            parameters[5].Value = model.UserID;
            parameters[6].Value = model.NoteType;
            parameters[7].Value = model.IsRemind;
            parameters[8].Value = model.IsEnabled;
            parameters[9].Value = model.SendType;
            parameters[10].Value = model.SchoolYear;
            parameters[11].Value = model.TermID;
            parameters[12].Value = model.Attachment;
            parameters[13].Value = model.SendArea;
            parameters[14].Value = model.Creator;
            parameters[15].Value = model.Ids;
            parameters[16].Value = model.IsPass;
            parameters[17].Value = model.IsPassBy;
            parameters[18].Value = model.IsPassDate;
            parameters[19].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            DbHelperSQL.RunProcedure("UP_JC_SchoolNotes_Delete", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_SchoolNotes ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_SchoolNotes GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,SchoolColumnID,SchoolColumnPath,NoteTitle,NoteContent,NoteDate,UserID,NoteType,IsRemind,IsEnabled,SendType,SchoolYear,TermID,Attachment,SendArea,Creator,Ids,IsPass,IsPassBy,IsPassDate from JC_SchoolNotes ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.JC_SchoolNotes model = new ECB.PC.Model.JC_SchoolNotes();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_SchoolNotes DataRowToModel(DataRow row)
        {
            ECB.PC.Model.JC_SchoolNotes model = new ECB.PC.Model.JC_SchoolNotes();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["SchoolColumnID"] != null && row["SchoolColumnID"].ToString() != "")
                {
                    model.SchoolColumnID = int.Parse(row["SchoolColumnID"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["NoteTitle"] != null)
                {
                    model.NoteTitle = row["NoteTitle"].ToString();
                }
                if (row["NoteContent"] != null)
                {
                    model.NoteContent = row["NoteContent"].ToString();
                }
                if (row["NoteDate"] != null && row["NoteDate"].ToString() != "")
                {
                    model.NoteDate = DateTime.Parse(row["NoteDate"].ToString());
                }
                if (row["UserID"] != null && row["UserID"].ToString() != "")
                {
                    model.UserID = new Guid(row["UserID"].ToString());
                }
                if (row["NoteType"] != null && row["NoteType"].ToString() != "")
                {
                    model.NoteType = int.Parse(row["NoteType"].ToString());
                }
                if (row["IsRemind"] != null && row["IsRemind"].ToString() != "")
                {
                    if ((row["IsRemind"].ToString() == "1") || (row["IsRemind"].ToString().ToLower() == "true"))
                    {
                        model.IsRemind = true;
                    }
                    else
                    {
                        model.IsRemind = false;
                    }
                }
                if (row["IsEnabled"] != null && row["IsEnabled"].ToString() != "")
                {
                    if ((row["IsEnabled"].ToString() == "1") || (row["IsEnabled"].ToString().ToLower() == "true"))
                    {
                        model.IsEnabled = true;
                    }
                    else
                    {
                        model.IsEnabled = false;
                    }
                }
                if (row["SendType"] != null && row["SendType"].ToString() != "")
                {
                    model.SendType = int.Parse(row["SendType"].ToString());
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
                if (row["TermID"] != null && row["TermID"].ToString() != "")
                {
                    model.TermID = new Guid(row["TermID"].ToString());
                }
                if (row["Attachment"] != null)
                {
                    model.Attachment = row["Attachment"].ToString();
                }
                if (row["SendArea"] != null)
                {
                    model.SendArea = row["SendArea"].ToString();
                }
                if (row["Creator"] != null)
                {
                    model.Creator = row["Creator"].ToString();
                }
                if (row["Ids"] != null)
                {
                    model.Ids = row["Ids"].ToString();
                }
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    model.IsPass = int.Parse(row["IsPass"].ToString());
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,SchoolColumnID,SchoolColumnPath,NoteTitle,NoteContent,NoteDate,UserID,NoteType,IsRemind,IsEnabled,SendType,SchoolYear,TermID,Attachment,SendArea,Creator,Ids,IsPass,IsPassBy,IsPassDate ");
            strSql.Append(" FROM JC_SchoolNotes ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,SchoolColumnID,SchoolColumnPath,NoteTitle,NoteContent,NoteDate,UserID,NoteType,IsRemind,IsEnabled,SendType,SchoolYear,TermID,Attachment,SendArea,Creator,Ids,IsPass,IsPassBy,IsPassDate ");
            strSql.Append(" FROM JC_SchoolNotes ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_SchoolNotes ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_SchoolNotes T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "JC_SchoolNotes";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetFamilyInfosByStudent(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.*  ");
            strSql.Append(" FROM JC_FamilyInfos a LEFT JOIN dbo.JC_StudentFamily b ON a.ID=b.FamilyId LEFT JOIN dbo.JC_StudentInfos c ON b.StudentId=c.ID ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetStudentInfosByFamily(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.*  ");
            strSql.Append(" FROM JC_StudentInfos a LEFT JOIN dbo.JC_StudentFamily b ON a.ID=b.StudentId LEFT JOIN dbo.JC_FamilyInfos c ON b.FamilyId=c.ID ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

