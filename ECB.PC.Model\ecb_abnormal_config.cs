﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_abnormal_config:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_abnormal_config
    {
        public ecb_abnormal_config()
        { }
        #region Model
        private Guid _id;
        private string _columnid;
        private string _columnpath;
        private decimal? _leave;
        private decimal? _timetable;
        private decimal? _device;
        private decimal? _attendance;
        private DateTime? _lastedittime;
        private Guid _lasteditor;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 区域ID
        /// </summary>
        public string ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 区域Path
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 请假
        /// </summary>
        public decimal? Leave
        {
            set { _leave = value; }
            get { return _leave; }
        }
        /// <summary>
        /// 上次课表
        /// </summary>
        public decimal? TimeTable
        {
            set { _timetable = value; }
            get { return _timetable; }
        }
        /// <summary>
        /// 设备
        /// </summary>
        public decimal? Device
        {
            set { _device = value; }
            get { return _device; }
        }
        /// <summary>
        /// 考勤
        /// </summary>
        public decimal? Attendance
        {
            set { _attendance = value; }
            get { return _attendance; }
        }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        /// <summary>
        /// 更新人
        /// </summary>
        public Guid LastEditor
        {
            set { _lasteditor = value; }
            get { return _lasteditor; }
        }
        #endregion Model

    }
}

