﻿using System;
using System.Data;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_tests_edit : YunEdu.Authority.AdminCommonJC
    {
        YunEdu.BLL.PKC_Tests bllPKC_Tests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modelPKC_Tests = new YunEdu.Model.PKC_Tests();
        YunEdu.BLL.JC_StudentInfos bllStudentinfos = new YunEdu.BLL.JC_StudentInfos();
        YunEdu.Model.JC_StudentInfos modelstudentinfos = new YunEdu.Model.JC_StudentInfos();
        YunEdu.BLL.PKC_TestUsers bllPKC_TestUsers = new YunEdu.BLL.PKC_TestUsers();
        YunEdu.BLL.PKC_Subjects bllPKC_Subjects = new YunEdu.BLL.PKC_Subjects();
        YunEdu.Model.PKC_Subjects modelPKC_Subjects = new YunEdu.Model.PKC_Subjects();
        YunEdu.BLL.JC_ClassInfos bllClass = new YunEdu.BLL.JC_ClassInfos();
        YunEdu.BLL.JC_Tests bllTest = new YunEdu.BLL.JC_Tests();
        YunEdu.Model.JC_Tests modelTests = new YunEdu.Model.JC_Tests();
        BLL.Site_Dictionary bllDictionary = new BLL.Site_Dictionary();
        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();
        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();
        string id;

        protected void Page_Load(object sender, EventArgs e)
        {
            // 要编辑的考试id
            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
            }
            GetArea();
            if (!Page.IsPostBack)
            {
                DefaultDataBound();
            }
        }

        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }

        /// <summary>
        /// 编辑时绑定数据
        /// </summary>
        private void DefaultDataBound()
        {
            if (string.IsNullOrEmpty(id))
            {
                BindPropertyCode("");
                return;
            }
            modelPKC_Tests = bllPKC_Tests.GetModel(new Guid(id));

            if (modelPKC_Tests != null)
            {
                this.pnlAdd.Enabled = false;
                this.pnlSyncTest.Visible = false;
                this.pnlImport.Visible = false;
                this.txtTestTitle.Value = modelPKC_Tests.TestTitle;
                this.txtRemark.Text = modelPKC_Tests.TestInstruction;
                this.txtBeginDate.Text = modelPKC_Tests.TestStartDate.Value.ToString("yyyy-MM-dd HH:mm");
                this.txtEndDate.Text = modelPKC_Tests.TestEndDate.Value.ToString("yyyy-MM-dd HH:mm");
                this.ckbIsEnabled.Checked = modelPKC_Tests.IsEnabled;
                this.ddlGrade.SelectedValue = this.hidGradeId.Value = modelPKC_Tests.GradeId.ToString();

                string columnpath = modelPKC_Tests.ColumnPath;
                string[] _columnpath = columnpath.Split('|');
                switch (_columnpath.Length)
                {
                    //case 1:

                    //    break;
                    case 2:
                        this.S1.SelectedValue = _columnpath[1];
                        break;
                    case 3:
                        this.S1.SelectedValue = _columnpath[1];
                        this.S2.SelectedValue = _columnpath[1] + "|" + _columnpath[2];
                        break;
                    case 4:
                        this.S1.SelectedValue = _columnpath[1];
                        this.S2.SelectedValue = _columnpath[1] + "|" + _columnpath[2];
                        this.S3.SelectedValue = _columnpath[1] + "|" + _columnpath[2] + "|" + _columnpath[3];
                        break;
                    case 5:
                        this.S1.SelectedValue = _columnpath[1];
                        this.S2.SelectedValue = _columnpath[1] + "|" + _columnpath[2];
                        this.S3.SelectedValue = _columnpath[1] + "|" + _columnpath[2] + "|" + _columnpath[3];
                        this.ddlChildSchool.SelectedValue = _columnpath[4];
                        break;
                }
                BindPropertyCode(modelPKC_Tests.GradeId.ToString(), modelPKC_Tests.PropertyCode);
                BindClassInfos(modelPKC_Tests.GradeId.ToString(), modelPKC_Tests.PropertyCode, modelPKC_Tests.ClassId);
                BindSubject(modelPKC_Tests.GradeId.ToString(), modelPKC_Tests.PropertyCode, modelPKC_Tests.TestSubjectCode);
            }
        }

        /// <summary>
        /// 绑定年级信息
        /// </summary>
        /// <param name="schoolId">学校ID</param>
        /// <param name="selectedValue"></param>
        private void BindGradeInfos(string schoolid, string selectedValue = "")
        {
            YunEdu.BLL.JC_GradeInfos bllGrade = new YunEdu.BLL.JC_GradeInfos();
            DataSet dsGradeInfos = new DataSet();
            // 管理员
            if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.SchoolAdmin).ToString()) ||
                RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
                RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
                RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()))//全部显示
            {
                dsGradeInfos = new YunEdu.BLL.JC_GradeInfos().GetGradeBySchoolYear(schoolid, ModelTermInfos(schoolid).SchoolYear);
            }
            else if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsGradeInfos = new YunEdu.BLL.JC_Subjects().GetTeachGrade(Int32.Parse(schoolid), UserName);
            }
            else if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModel(new Guid(UserId));
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsGradeInfos = new YunEdu.BLL.JC_GradeInfos().GetGradeBySchoolYear(schoolid, ModelTermInfos(schoolid).SchoolYear);
            }
            // 绑定年级信息列表
            if (dsGradeInfos != null && dsGradeInfos.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlGrade, dsGradeInfos.Tables[0], "", "GradeName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    ddlGrade.SelectedValue = selectedValue;
                }
            }
        }

        /// <summary>
        /// 绑定班级信息
        /// </summary>
        /// <param name="gradeId"></param>
        /// <param name="selectedValue"></param>
        private void BindClassInfos(string gradeId, string propertyCode, string selectedValue = "")
        {
            // 清空当前的项目
            chkClassList.Items.Clear();
            chkClassAll.Visible = false;
            // 判断是否有选择年级id
            if (string.IsNullOrEmpty(gradeId) || !Guid.TryParse(gradeId, out Guid guidGradeId) || guidGradeId == Guid.Empty)
            {
                return;
            }
            // 如果班级类型code是3，则选择全部 ，1 文科，2理科  3同科
            if (propertyCode.Equals("3"))
            {
                propertyCode = "1,2,3";
            }
            // 判断是否有选择类型
            BLL.JC_ClassInfos bllClass = new BLL.JC_ClassInfos();
            DataSet dsClass = new DataSet();
            if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()) ||
                RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
                RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
                RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", guidGradeId.ToString()), "OrderId");
            }
            else if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsClass = new YunEdu.BLL.JC_Subjects().GetTeachClass(modelAreaUser.ColumnID, UserName, guidGradeId.ToString());
            }
            else if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }
            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(UserName);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", guidGradeId.ToString()), "OrderId");
            }
            // 判断是否有班级数据
            if (dsClass != null && dsClass.Tables.Count > 0)
            {
                foreach (DataRow item in dsClass.Tables[0].Rows)
                {
                    var chkClassItem = new ListItem
                    {
                        Text = item["ClassName"].ToString(),
                        Value = item["ID"].ToString()
                    };
                    // 判断是否有设置选中值
                    if (string.IsNullOrEmpty(selectedValue))
                    {
                        chkClassItem.Selected = item["ClassPropertyCode"] != null && propertyCode.Contains(item["ClassPropertyCode"].ToString());
                    }
                    else
                    {
                        chkClassItem.Selected = selectedValue.Contains(item["ID"].ToString());
                    }
                    chkClassList.Items.Add(chkClassItem);
                }
                // 设置全选按钮可见
                chkClassAll.Visible = string.IsNullOrEmpty(id);
            }
        }

        /// <summary>
        /// 绑定科目列表
        /// </summary>
        /// <param name="gradeId">年级id</param>
        /// <param name="propertyCode">班级类型：文科，理科，同科</param>
        private void BindSubject(string gradeId, string propertyCode, string selectedValue = "")
        {
            selectedValue = $",{selectedValue},";
            chklSubject.Items.Clear();
            chkSubjectAll.Visible = false;
            // 判断是否有选择年级id
            if (string.IsNullOrEmpty(gradeId) || !Guid.TryParse(gradeId, out Guid guidGradeId) || guidGradeId == Guid.Empty)
            {
                chklSubject.Items.Clear();
                return;
            }
            // 如果是选择全部，则将文科，理科，同科全部选中
            if (propertyCode.Equals("3"))
            {
                propertyCode = "1,2,3";
            }
            // 判断是否有选择年级
            if (guidGradeId != Guid.Empty)
            {
                chklSubject.DataTextField = "DictText";
                chklSubject.DataValueField = "SubjectCode";
                string tableName = "dbo.Site_Dictionary a inner join dbo.JC_GradeSubjectConfig b on a.DictValue=b.SubjectCode";
                // 如果不是选择全部
                if (propertyCode.Equals("1") || propertyCode.Equals("2"))
                {
                    // 筛选出指定类型的班级和同科的班级
                    tableName += $"and (b.PropertyCode='{DataSecurity.FilteSQLStr(propertyCode)}' or b.PropertyCode='3')";
                }
                tableName += $" inner join dbo.JC_GradeInfos c on b.GradeCode=c.GradeCode and b.ColumnId=c.SchoolColumnId and c.ID='{guidGradeId}' and c.SchoolColumnId={modelAreaUser.ColumnID}";
                DataTable data = GetRecordByPageOrder.GetModel(tableName, "SubjectCode,DictText,b.PropertyCode", "a.DictTypeId=28 and a.ColumnId in (0," + modelAreaUser.ColumnID + ")").Tables[0];
                // 判断是否有取到数据
                if (data != null && data.Rows.Count > 0)
                {
                    foreach (DataRow item in data.Rows)
                    {
                        var chkSubjectItem = new ListItem
                        {
                            Text = item["DictText"].ToString(),
                            Value = item["SubjectCode"].ToString()
                        };
                        // 判断是否有设置选中值
                        if (string.IsNullOrEmpty(selectedValue))
                        {
                            chkSubjectItem.Selected = item["PropertyCode"] != null && propertyCode.Contains(item["PropertyCode"].ToString());
                        }
                        else
                        {
                            chkSubjectItem.Selected = selectedValue.Contains($",{item["SubjectCode"]},");
                        }
                        chklSubject.Items.Add(chkSubjectItem);
                    }
                    // 新增操作时，设置全选按钮可见
                    chkSubjectAll.Visible = string.IsNullOrEmpty(id);
                }
            }
        }

        /// <summary>
        /// 绑定班级性质类型下拉框
        /// </summary>
        /// <param name="gradeId">年级id</param>
        private void BindPropertyCode(string gradeId, string selectedValue = "")
        {
            DataTable data = null;
            // 标识是否有同科
            bool hasTongKe = false;
            // 判断是否有年级      
            if (!string.IsNullOrEmpty(gradeId) && Guid.TryParse(gradeId, out Guid guidGradeId))
            {
                // 获取到字典数据
                data = bllDictionary.GetList($"DictTypeId=48 and DictValue in (select  distinct ClassPropertyCode from dbo.JC_ClassInfos where GradeId='{guidGradeId}')").Tables[0];
                foreach (DataRow item in data.Rows)
                {
                    if (item["DictValue"].ToString() == "3")
                    {
                        hasTongKe = true;
                        item["DictText"] = "全年级";
                        item["DictValue"] = "3";

                    }
                }
                // 如果没有同科数据，添加一个同科（全年级）
                if (!hasTongKe)
                {
                    DataRow dr = data.NewRow();
                    dr["DictText"] = "全年级";
                    dr["DictValue"] = "3";
                    data.Rows.Add(dr);
                }
            }
            GetRecordByPageOrder.BindDropDownList(ddlPropertyCode, data, "", "DictText", "DictValue", "请选择", "");
            ddlPropertyCode.SelectedValue = selectedValue;
        }

        #region 加载区域信息
        private void GetArea()
        {
            //设区市，绑定，同时，根据用户所处级别，选定当前选择项
            //任何级别的用户都要显示市级
            //hidSchoolId.value默认显示用户所在县区学校
            string strColumnPath = "";

            if (!IsPostBack)
            {
                strColumnPath = modelAreaUser.ColumnPath;
                hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
            }
            else
            {
                if (string.IsNullOrEmpty(hidSchoolId.Value))
                {
                    strColumnPath = modelAreaUser.ColumnPath;
                    hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
                }
                else
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                    if (modelArea == null)
                    {
                        return;
                    }
                    else
                    {
                        strColumnPath = modelArea.ColumnPath;
                    }
                }
            }
            if (string.IsNullOrEmpty(strColumnPath))
            {
                return;
            }
            string[] place = strColumnPath.Split('|');
            bllArea.BindAreaDropDown(S1, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S2, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S3, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(ddlChildSchool, "", IsPostBack, "", "请选择市");
            GetRecordByPageOrder.BindDropDownList(ddlGrade, null, "", "GradeName", "ID", "请选择年级");
            //GetRecordByPageOrder.BindDropDownList(ddlPropertyCode, null, "", "DictText", "DictValue", "请选择");
            switch (place.Length)
            {
                case 1:
                    // 通过省查处市级
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, "", "请选择市");
                    break;
                case 2:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, "", "请选择县区");
                    break;
                case 3:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, "", "请选择学校");
                    break;
                case 4:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, "", "请选择学校");
                    if (ddlChildSchool.Items.Count > 1)
                    {
                        ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                        if (!string.IsNullOrEmpty(hidChildSchool.Value))
                        {
                            ddlChildSchool.SelectedValue = hidChildSchool.Value;
                        }
                    }
                    BindGradeInfos(place[3], hidGradeId.Value);
                    break;
                case 5:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    pnlSchool.Visible = false;
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, place[4], "请选择附属学校");
                    ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                    BindGradeInfos(place[4], hidGradeId.Value);
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    break;
                default:
                    break;
            }
        }
        #endregion

        #region 事件

        protected void DDLPropertyCode_SelectedIndexChanged(object sender, EventArgs e)
        {
            BindClassInfos(ddlGrade.SelectedValue, ddlPropertyCode.SelectedValue);
            BindSubject(ddlGrade.SelectedValue, ddlPropertyCode.SelectedValue);
        }

        protected void ddlGrade_SelectedIndexChanged(object sender, EventArgs e)
        {
            BindPropertyCode(hidGradeId.Value);
            // 绑定班级信息
            BindClassInfos(ddlGrade.SelectedValue, ddlPropertyCode.SelectedValue);
            // 绑定科目信息
            BindSubject(ddlGrade.SelectedValue, ddlPropertyCode.SelectedValue);
        }

        protected void btnAddTest_Click(object sender, EventArgs e)
        {
            // 保存操作类型
            string actionType = "add";
            // 判断是否有id，有则是编辑操作
            if (id != "" && id != null)
            {
                actionType = "edit";
                modelPKC_Tests = bllPKC_Tests.GetModel(new Guid(id));
            }
            else
            {
                modelPKC_Tests = new YunEdu.Model.PKC_Tests();
                modelPKC_Tests.TestId = Guid.NewGuid();
            }
            // 部分参数获取
            string _TestTitle = StringHelper.FilterIllegalWords(txtTestTitle.Value);
            string _TestInstruction = StringHelper.FilterIllegalWords(txtRemark.Text);
            string _TestStartDate = txtBeginDate.Text;
            string _TestEndDate = txtEndDate.Text;
            string _PropertyCode = ddlPropertyCode.SelectedValue;
            if (_PropertyCode == "")
            {
                _PropertyCode = "3";
            }
            // 给考试对象赋值
            modelPKC_Tests.TestTitle = _TestTitle;
            modelPKC_Tests.TestInstruction = _TestInstruction;
            modelPKC_Tests.TestStartDate = DateTime.Parse(_TestStartDate);
            modelPKC_Tests.TestEndDate = DateTime.Parse(_TestEndDate);
            modelPKC_Tests.IsEnabled = ckbIsEnabled.Checked;
            modelPKC_Tests.PropertyCode = _PropertyCode;

            // 判断是否是添加操作
            if (actionType.Equals("add"))
            {
                // 判断是否有选择附属学校
                if (!string.IsNullOrEmpty(hidChildSchool.Value))
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                }
                else if (!string.IsNullOrEmpty(hidSchoolId.Value)) // 判断是否有选择学校
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                }
                // 判断是否有获取到学校信息
                if (modelArea == null)
                {
                    MessageBox.ResponseScript(this, "layer.msg('请选择学校!');");
                    return;
                }
                modelPKC_Tests.ColumnId = modelArea.ColumnID;
                modelPKC_Tests.ColumnPath = modelArea.ColumnPath;
                var modelTerm = ModelTermInfos(modelAreaUser.ColumnID.ToString());
                if (modelTerm == null)
                {
                    MessageBox.ResponseScript(this, "layer.msg('当前学校未配置学期信息，请前往学期管理配置学期信息!');");
                    return;
                }
                modelPKC_Tests.TermId = modelTerm.ID;
                // 年级
                if (!string.IsNullOrEmpty(ddlGrade.SelectedValue))
                {
                    modelPKC_Tests.GradeId = new Guid(ddlGrade.SelectedValue);
                }
                // 班级
                var classIds = from ListItem a in chkClassList.Items where a.Selected select a.Value;
                if (!classIds.Any())
                {
                    MessageBox.ResponseScript(this, "layer.msg('请选择参加考试的班级!');");
                    return;
                }
                modelPKC_Tests.ClassId = string.Join("|", classIds);
                // 考试科目
                string checkStr = string.Empty; ;
                for (int i = 0; i < chklSubject.Items.Count; i++)
                {
                    if (chklSubject.Items[i].Selected == true)
                    {
                        //根据所选科目创建考试科目表
                        modelPKC_Subjects = new YunEdu.Model.PKC_Subjects();
                        modelPKC_Subjects.TestId = modelPKC_Tests.TestId;
                        modelPKC_Subjects.SubjectCode = chklSubject.Items[i].Value;
                        modelPKC_Subjects.SubjectName = chklSubject.Items[i].Text;
                        modelPKC_Subjects.CreateTime = DateTime.Now;
                        modelPKC_Subjects.SchoolYear = modelTerm.SchoolYear;
                        string gradecode = GetRecordByPageOrder.GetModelField("JC_GradeInfos", "GradeCode", "ID='" + modelPKC_Tests.GradeId + "'");
                        string propertycode = GetRecordByPageOrder.GetModelField("JC_GradeSubjectConfig", "PropertyCode", "ColumnId='" + modelPKC_Tests.ColumnId + "' and GradeCode='" + gradecode + "' and SubjectCode='" + modelPKC_Subjects.SubjectCode + "'");
                        modelPKC_Subjects.PropertyCode = propertycode;
                        bllPKC_Subjects.Add(modelPKC_Subjects);
                        checkStr += chklSubject.Items[i].Value + ",";
                    }
                }
                if (string.IsNullOrEmpty(checkStr))
                {
                    MessageBox.ResponseScript(this, "layer.msg('请选择考试科目!');");
                    return;
                }
                checkStr = checkStr.Substring(0, checkStr.LastIndexOf(','));
                modelPKC_Tests.TestSubjectCode = checkStr;
                // 判断是否同步到考试管理
                if (ckbSyncTest.Checked)
                {
                    modelTests = new YunEdu.Model.JC_Tests();
                    modelTests.ID = Guid.NewGuid();
                    modelTests.PkcTestId = modelPKC_Tests.TestId;
                    modelTests.SchoolColumnId = modelPKC_Tests.ColumnId;
                    modelTests.SchoolColumnPath = modelPKC_Tests.ColumnPath;
                    modelTests.SchoolYear = modelTerm.SchoolYear;
                    modelTests.GradeID = modelPKC_Tests.GradeId;
                    modelTests.TermID = modelTerm.ID;
                    modelTests.PropertyCode = _PropertyCode;
                    modelTests.TestName = _TestTitle;
                    modelTests.BeginDate = modelPKC_Tests.TestStartDate;
                    modelTests.EndDate = modelPKC_Tests.TestEndDate;
                    modelTests.Remark = _TestInstruction;
                    modelTests.TestSubjectCode = checkStr;

                    foreach (var item in classIds)
                    {
                        Guid classId = new Guid(item);
                        int TestCount = bllTest.GetRecordCount($"PkcTestId='{modelTests.PkcTestId}' AND ClassID='{classId}'");
                        if (TestCount == 0)
                        {
                            modelTests.ClassID = classId;
                            bllTest.Add(modelTests);
                        }
                    }
                }
                // 判断是否导入学生
                if (ckbImport.Checked)
                {
                    foreach (var item in classIds)
                    {
                        Guid classId = new Guid(item);
                        YunEdu.Model.JC_ClassInfos modelClass = bllClass.GetModel(classId);
                        try
                        {
                            foreach (DataRow itemuser in GetRecordByPageOrder.GetListByVW("JC_StudentInfos", 0, "*", $"ClassID='{classId}'", "CreateDate desc").Tables[0].Rows)
                            {
                                modelstudentinfos = bllStudentinfos.GetModel(new Guid(itemuser["ID"].ToString()));
                                if (modelstudentinfos != null)
                                {
                                    YunEdu.Model.PKC_TestUsers modelTestUsers = new YunEdu.Model.PKC_TestUsers();
                                    modelTestUsers.TestId = modelPKC_Tests.TestId;
                                    modelTestUsers.UserId = modelstudentinfos.ID;
                                    modelTestUsers.MyClassId = modelstudentinfos.ClassID;
                                    modelTestUsers.PropertyCode = modelClass.ClassPropertyCode;
                                    modelTestUsers.AbilityValue = 0;
                                    modelTestUsers.SchoolYear = modelTerm.SchoolYear;
                                    bllPKC_TestUsers.Add(modelTestUsers);
                                }
                            }
                        }
                        catch (Exception)
                        {
                            continue;
                        }
                    }
                }
                modelPKC_Tests.TestAddDate = DateTime.Now;
                modelPKC_Tests.SchoolYear = modelTerm.SchoolYear;
                if (bllPKC_Tests.Add(modelPKC_Tests))
                {
                    MessageBox.ResponseScript(this, "showMessage('true','添加成功!请进入第2步：科目管理!',1);");
                }
            }
            else
            {
                if (bllPKC_Tests.Update(modelPKC_Tests))
                {
                    MessageBox.ResponseScript(this, "showMessage('true','编辑成功!',1);");
                }
            }

        }

        // 返回
        protected void btnBack_Click(object sender, EventArgs e)
        {
            Response.Redirect("pkc_tests_list.aspx?MenuId=" + Request.QueryString["MenuId"]);
        }

        #endregion
    }
}