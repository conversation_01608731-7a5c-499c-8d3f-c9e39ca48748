﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 作业提交情况
	/// </summary>
	[Serializable]
	public partial class JC_Homework_NoPost
	{
		public JC_Homework_NoPost()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _gradeid;
		private Guid _classid;
		private Guid _homeworkid;
		private Guid _userid;
		private DateTime _createtime;
		private string _remark;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校ColumnID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校ColumnPath
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 年级ID
		/// </summary>
		public Guid GradeId
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 班级ID
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 作业ID
		/// </summary>
		public Guid HomeworkID
		{
			set{ _homeworkid=value;}
			get{return _homeworkid;}
		}
		/// <summary>
		/// 学生ID
		/// </summary>
		public Guid UserID
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 操作时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 完成说明
		/// </summary>
		public string Remark
		{
			set{ _remark=value;}
			get{return _remark;}
		}
		#endregion Model

	}
}

