﻿
using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 学生作品表
    /// </summary>
    [Serializable]
    public partial class DA_XS_StudentWorks
    {
        public DA_XS_StudentWorks()
        { }
        #region Model
        private Guid _id;
        private int? _schoolcolumnid;
        private string _schoolcolumnpath;
        private string _schoolyear;
        private Guid _termid;
        private Guid _gradeid;
        private Guid _classid;
        private Guid _studentid;
        private string _studentname;
        private string _workname;
        private string _description;
        private string _teachercomment;
        private bool _isshowinclass;
        private bool _isshowinschool;
        private bool _teachercheck;
        private Guid _teachercheckuser;
        private DateTime? _teachercheckdate;
        private bool _schoolcheck;
        private Guid _schoolcheckuser;
        private DateTime? _schoolcheckdate;
        private int? _agreecount;
        private int? _disagreecount;
        private DateTime? _createtime;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 学校ID
        /// </summary>
        public int? SchoolColumnId
        {
            set { _schoolcolumnid = value; }
            get { return _schoolcolumnid; }
        }
        /// <summary>
        /// 学校路径
        /// </summary>
        public string SchoolColumnPath
        {
            set { _schoolcolumnpath = value; }
            get { return _schoolcolumnpath; }
        }
        /// <summary>
        /// 学年
        /// </summary>
        public string SchoolYear
        {
            set { _schoolyear = value; }
            get { return _schoolyear; }
        }
        /// <summary>
        /// 学期id
        /// </summary>
        public Guid TermId
        {
            set { _termid = value; }
            get { return _termid; }
        }
        /// <summary>
        /// 年级id
        /// </summary>
        public Guid GradeId
        {
            set { _gradeid = value; }
            get { return _gradeid; }
        }
        /// <summary>
        /// 班级ID
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        /// <summary>
        /// 学生ID
        /// </summary>
        public Guid StudentId
        {
            set { _studentid = value; }
            get { return _studentid; }
        }
        /// <summary>
        /// 学生名称
        /// </summary>
        public string StudentName
        {
            set { _studentname = value; }
            get { return _studentname; }
        }
        /// <summary>
        /// 作品名称
        /// </summary>
        public string WorkName
        {
            set { _workname = value; }
            get { return _workname; }
        }
        /// <summary>
        /// 作品描述
        /// </summary>
        public string Description
        {
            set { _description = value; }
            get { return _description; }
        }
        /// <summary>
        /// 教师评价
        /// </summary>
        public string TeacherComment
        {
            set { _teachercomment = value; }
            get { return _teachercomment; }
        }
        /// <summary>
        /// 是否在班级页展示
        /// </summary>
        public bool IsShowInClass
        {
            set { _isshowinclass = value; }
            get { return _isshowinclass; }
        }
        /// <summary>
        /// 是否在学校页展示
        /// </summary>
        public bool IsShowInSchool
        {
            set { _isshowinschool = value; }
            get { return _isshowinschool; }
        }
        /// <summary>
        /// 教师审核结果
        /// </summary>
        public bool TeacherCheck
        {
            set { _teachercheck = value; }
            get { return _teachercheck; }
        }
        /// <summary>
        /// 教师审核人
        /// </summary>
        public Guid TeacherCheckUser
        {
            set { _teachercheckuser = value; }
            get { return _teachercheckuser; }
        }
        /// <summary>
        /// 教师审核日期
        /// </summary>
        public DateTime? TeacherCheckDate
        {
            set { _teachercheckdate = value; }
            get { return _teachercheckdate; }
        }
        /// <summary>
        /// 学校审核结果
        /// </summary>
        public bool SchoolCheck
        {
            set { _schoolcheck = value; }
            get { return _schoolcheck; }
        }
        /// <summary>
        /// 学校审核人
        /// </summary>
        public Guid SchoolCheckUser
        {
            set { _schoolcheckuser = value; }
            get { return _schoolcheckuser; }
        }
        /// <summary>
        /// 学校审核日期
        /// </summary>
        public DateTime? SchoolCheckDate
        {
            set { _schoolcheckdate = value; }
            get { return _schoolcheckdate; }
        }
        /// <summary>
        /// 赞
        /// </summary>
        public int? AgreeCount
        {
            set { _agreecount = value; }
            get { return _agreecount; }
        }
        /// <summary>
        /// 踩
        /// </summary>
        public int? DisagreeCount
        {
            set { _disagreecount = value; }
            get { return _disagreecount; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? CreateTime
        {
            set { _createtime = value; }
            get { return _createtime; }
        }
        #endregion Model

    }
}

