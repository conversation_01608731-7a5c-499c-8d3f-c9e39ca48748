﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_ClassCadre
    /// </summary>
    public partial class ecb_ClassCadre
    {
        public ecb_ClassCadre()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_ClassCadre");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取 最大的排序ID
        /// </summary>
        public int GetMaxOrderId(int ColumnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select MAX(OrderId) from ecb_ClassCadre");
            strSql.Append(" where ColumnId=@ColumnId ");
            SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4)			};
            parameters[0].Value = ColumnId;
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_ClassCadre model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_ClassCadre(");
            strSql.Append("ID,CadreName,Remark,OrderId,CreateDate,CreateUserId,ColumnId,ColumnPath,ClassId)");
            strSql.Append(" values (");
            strSql.Append("@ID,@CadreName,@Remark,@OrderId,@CreateDate,@CreateUserId,@ColumnId,@ColumnPath,@ClassId)");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CadreName", SqlDbType.NVarChar,50),
					new SqlParameter("@Remark", SqlDbType.NVarChar,-1),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
					new SqlParameter("@CreateDate", SqlDbType.DateTime),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.CadreName;
            parameters[2].Value = model.Remark;
            parameters[3].Value = model.OrderId;
            parameters[4].Value = model.CreateDate;
            parameters[5].Value = model.CreateUserId;
            parameters[6].Value = model.ColumnId;
            parameters[7].Value = model.ColumnPath;
            parameters[8].Value = model.ClassId;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_ClassCadre model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_ClassCadre set ");
            strSql.Append("CadreName=@CadreName,");
            strSql.Append("Remark=@Remark,");
            strSql.Append("OrderId=@OrderId,");
            strSql.Append("CreateDate=@CreateDate,");
            strSql.Append("CreateUserId=@CreateUserId,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ClassId=@ClassId");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@CadreName", SqlDbType.NVarChar,50),
					new SqlParameter("@Remark", SqlDbType.NVarChar,-1),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
					new SqlParameter("@CreateDate", SqlDbType.DateTime),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.CadreName;
            parameters[1].Value = model.Remark;
            parameters[2].Value = model.OrderId;
            parameters[3].Value = model.CreateDate;
            parameters[4].Value = model.CreateUserId;
            parameters[5].Value = model.ColumnId;
            parameters[6].Value = model.ColumnPath;
            parameters[7].Value = model.ClassId;
            parameters[8].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_ClassCadre ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_ClassCadre ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_ClassCadre GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,CadreName,Remark,OrderId,CreateDate,CreateUserId,ColumnId,ColumnPath,ClassId from ecb_ClassCadre ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            ECB.PC.Model.ecb_ClassCadre model = new ECB.PC.Model.ecb_ClassCadre();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_ClassCadre DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_ClassCadre model = new ECB.PC.Model.ecb_ClassCadre();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["CadreName"] != null)
                {
                    model.CadreName = row["CadreName"].ToString();
                }
                if (row["Remark"] != null)
                {
                    model.Remark = row["Remark"].ToString();
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["OrderId"] != null && row["OrderId"].ToString() != "")
                {
                    model.OrderId = int.Parse(row["OrderId"].ToString());
                }
                if (row["CreateDate"] != null && row["CreateDate"].ToString() != "")
                {
                    model.CreateDate = DateTime.Parse(row["CreateDate"].ToString());
                }
                if (row["CreateUserId"] != null && row["CreateUserId"].ToString() != "")
                {
                    model.CreateUserId = new Guid(row["CreateUserId"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,CadreName,Remark,OrderId,CreateDate,CreateUserId,ColumnId,ColumnPath,ClassId ");
            strSql.Append(" FROM ecb_ClassCadre ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,CadreName,Remark,OrderId,CreateDate,CreateUserId,ColumnId,ColumnPath,ClassId ");
            strSql.Append(" FROM ecb_ClassCadre ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_ClassCadre ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_ClassCadre T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_ClassCadre";
            parameters[1].Value = "ID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 班级是否存在记录 
        /// </summary>
        public bool ExistsByClass(Guid ClassId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_ClassCadre");
            strSql.Append(" where ClassId=@ClassId ");
            SqlParameter[] parameters = {
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ClassId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsByName(string CadreName, int ColumnId, Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_ClassCadre");
            strSql.Append(" where CadreName=@CadreName and ColumnId=@ColumnId and ID!=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@CadreName", SqlDbType.NVarChar,50),	new SqlParameter("@ColumnId", SqlDbType.Int,4),	new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = CadreName;
            parameters[1].Value = ColumnId;
            parameters[2].Value = ID;
            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 删除数据  包括班干部 职务 成员
        /// </summary>
        public bool DeleteCadre(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_ClassCadre ");
            strSql.Append(" where ID=@ID ");
            strSql.Append(" delete from ecb_ClassCadreConfig ");
            strSql.Append("  where cadreId=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod

       
    }
}

