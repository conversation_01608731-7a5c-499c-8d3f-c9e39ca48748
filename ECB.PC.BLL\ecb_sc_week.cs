﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_sc_week
	/// </summary>
	public partial class ecb_sc_week
	{
            public ecb_sc_week()
            { }
            #region  BasicMethod

            /// <summary>
            /// 是否存在该记录
            /// </summary>
            public bool Exists(Guid Id)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("select count(1) from ecb_sc_week");
                strSql.Append(" where Id=@Id ");
                SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
                parameters[0].Value = Id;

                return DbHelperSQL.Exists(strSql.ToString(), parameters);
            }
            /// <summary>
            /// 增加一条数据
            /// </summary>
            public bool Add(ECB.PC.Model.ecb_sc_week model)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("insert into ecb_sc_week(");
                strSql.Append("Id,ColumnId,ColumnPath,TermId,StaDate,EndDate,WeekNum,IsSingle)");
                strSql.Append(" values (");
                strSql.Append("@Id,@ColumnId,@ColumnPath,@TermId,@StaDate,@EndDate,@WeekNum,@IsSingle)");
                SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StaDate", SqlDbType.Date,3),
                    new SqlParameter("@EndDate", SqlDbType.Date,3),
                    new SqlParameter("@WeekNum", SqlDbType.Int,4),
                    new SqlParameter("@IsSingle", SqlDbType.Int,4)};
                parameters[0].Value = Guid.NewGuid();
                parameters[1].Value = model.ColumnId;
                parameters[2].Value = model.ColumnPath;
                parameters[3].Value = model.TermId;
                parameters[4].Value = model.StaDate;
                parameters[5].Value = model.EndDate;
                parameters[6].Value = model.WeekNum;
                parameters[7].Value = model.IsSingle;

                int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            /// <summary>
            /// 更新一条数据
            /// </summary>
            public bool Update(ECB.PC.Model.ecb_sc_week model)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("update ecb_sc_week set ");
                strSql.Append("ColumnId=@ColumnId,");
                strSql.Append("ColumnPath=@ColumnPath,");
                strSql.Append("TermId=@TermId,");
                strSql.Append("StaDate=@StaDate,");
                strSql.Append("EndDate=@EndDate,");
                strSql.Append("WeekNum=@WeekNum,");
                strSql.Append("IsSingle=@IsSingle");
                strSql.Append(" where Id=@Id ");
                SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StaDate", SqlDbType.Date,3),
                    new SqlParameter("@EndDate", SqlDbType.Date,3),
                    new SqlParameter("@WeekNum", SqlDbType.Int,4),
                    new SqlParameter("@IsSingle", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
                parameters[0].Value = model.ColumnId;
                parameters[1].Value = model.ColumnPath;
                parameters[2].Value = model.TermId;
                parameters[3].Value = model.StaDate;
                parameters[4].Value = model.EndDate;
                parameters[5].Value = model.WeekNum;
                parameters[6].Value = model.IsSingle;
                parameters[7].Value = model.Id;

                int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }

            /// <summary>
            /// 删除一条数据
            /// </summary>
            public bool Delete(Guid Id)
            {

                StringBuilder strSql = new StringBuilder();
                strSql.Append("delete from ecb_sc_week ");
                strSql.Append(" where Id=@Id ");
                SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
                parameters[0].Value = Id;

                int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            /// <summary>
            /// 批量删除数据
            /// </summary>
            public bool DeleteList(string Idlist)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("delete from ecb_sc_week ");
                strSql.Append(" where Id in (" + Idlist + ")  ");
                int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }


            /// <summary>
            /// 得到一个对象实体
            /// </summary>
            public ECB.PC.Model.ecb_sc_week GetModel(Guid Id)
            {

                StringBuilder strSql = new StringBuilder();
                strSql.Append("select  top 1 Id,ColumnId,ColumnPath,TermId,StaDate,EndDate,WeekNum,IsSingle from ecb_sc_week ");
                strSql.Append(" where Id=@Id ");
                SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
                parameters[0].Value = Id;

                ECB.PC.Model.ecb_sc_week model = new ECB.PC.Model.ecb_sc_week();
                DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
                if (ds.Tables[0].Rows.Count > 0)
                {
                    return DataRowToModel(ds.Tables[0].Rows[0]);
                }
                else
                {
                    return null;
                }
            }


            /// <summary>
            /// 得到一个对象实体
            /// </summary>
            public ECB.PC.Model.ecb_sc_week DataRowToModel(DataRow row)
            {
                ECB.PC.Model.ecb_sc_week model = new ECB.PC.Model.ecb_sc_week();
                if (row != null)
                {
                    if (row["Id"] != null && row["Id"].ToString() != "")
                    {
                        model.Id = new Guid(row["Id"].ToString());
                    }
                    if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                    {
                        model.ColumnId = int.Parse(row["ColumnId"].ToString());
                    }
                    if (row["ColumnPath"] != null)
                    {
                        model.ColumnPath = row["ColumnPath"].ToString();
                    }
                    if (row["TermId"] != null && row["TermId"].ToString() != "")
                    {
                        model.TermId = new Guid(row["TermId"].ToString());
                    }
                    if (row["StaDate"] != null && row["StaDate"].ToString() != "")
                    {
                        model.StaDate = DateTime.Parse(row["StaDate"].ToString());
                    }
                    if (row["EndDate"] != null && row["EndDate"].ToString() != "")
                    {
                        model.EndDate = DateTime.Parse(row["EndDate"].ToString());
                    }
                    if (row["WeekNum"] != null && row["WeekNum"].ToString() != "")
                    {
                        model.WeekNum = int.Parse(row["WeekNum"].ToString());
                    }
                    if (row["IsSingle"] != null && row["IsSingle"].ToString() != "")
                    {
                        model.IsSingle = int.Parse(row["IsSingle"].ToString());
                    }
                }
                return model;
            }

            /// <summary>
            /// 获得数据列表
            /// </summary>
            public DataSet GetList(string strWhere)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("select Id,ColumnId,ColumnPath,TermId,StaDate,EndDate,WeekNum,IsSingle ");
                strSql.Append(" FROM ecb_sc_week ");
                if (strWhere.Trim() != "")
                {
                    strSql.Append(" where " + strWhere);
                }
                return DbHelperSQL.Query(strSql.ToString());
            }

            /// <summary>
            /// 获得前几行数据
            /// </summary>
            public DataSet GetList(int Top, string strWhere, string filedOrder)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("select ");
                if (Top > 0)
                {
                    strSql.Append(" top " + Top.ToString());
                }
                strSql.Append(" Id,ColumnId,ColumnPath,TermId,StaDate,EndDate,WeekNum,IsSingle ");
                strSql.Append(" FROM ecb_sc_week ");
                if (strWhere.Trim() != "")
                {
                    strSql.Append(" where " + strWhere);
                }
                strSql.Append(" order by " + filedOrder);
                return DbHelperSQL.Query(strSql.ToString());
            }

            /// <summary>
            /// 获取记录总数
            /// </summary>
            public int GetRecordCount(string strWhere)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("select count(1) FROM ecb_sc_week ");
                if (strWhere.Trim() != "")
                {
                    strSql.Append(" where " + strWhere);
                }
                object obj = DbHelperSQL.GetSingle(strSql.ToString());
                if (obj == null)
                {
                    return 0;
                }
                else
                {
                    return Convert.ToInt32(obj);
                }
            }
            /// <summary>
            /// 分页获取数据列表
            /// </summary>
            public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("SELECT * FROM ( ");
                strSql.Append(" SELECT ROW_NUMBER() OVER (");
                if (!string.IsNullOrEmpty(orderby.Trim()))
                {
                    strSql.Append("order by T." + orderby);
                }
                else
                {
                    strSql.Append("order by T.Id desc");
                }
                strSql.Append(")AS Row, T.*  from ecb_sc_week T ");
                if (!string.IsNullOrEmpty(strWhere.Trim()))
                {
                    strSql.Append(" WHERE " + strWhere);
                }
                strSql.Append(" ) TT");
                strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
                return DbHelperSQL.Query(strSql.ToString());
            }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_sc_week";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        /// <param name="date">指定日期</param>
        /// <returns></returns>
        public ECB.PC.Model.ecb_sc_week GetModel(int columnId, DateTime date)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,TermId,StaDate,EndDate,WeekNum,IsSingle from ecb_sc_week ");
            strSql.Append(" where ColumnId=@ColumnId and @date>=StaDate and @date<=EndDate");
            SqlParameter[] parameters = {
                new SqlParameter ("@ColumnId",SqlDbType.Int,4),
                new SqlParameter("@date", SqlDbType.Date,3)};
            parameters[0].Value = columnId;
            parameters[1].Value = date;
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool DeleteAll(Guid TermId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_sc_week ");
            strSql.Append(" where TermId=@TermId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = TermId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
    }

