﻿﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// custom_Working_record:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class custom_Working_record
	{
		public custom_Working_record()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _userid;
		private string _workname;
		private string _workcontent;
		private DateTime? _begindate;
		private DateTime? _enddate;
		private string _dept;
		private string _depttype;
		private int? _weeknum;
		private string _mark;
		private DateTime? _createtime;
		private Guid _creator;
		/// <summary>
		/// 主键ID
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 教师ID
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 工作名称
		/// </summary>
		public string WorkName
		{
			set{ _workname=value;}
			get{return _workname;}
		}
		/// <summary>
		/// 工作内容
		/// </summary>
		public string WorkContent
		{
			set{ _workcontent=value;}
			get{return _workcontent;}
		}
		/// <summary>
		/// 开始日期
		/// </summary>
		public DateTime? BeginDate
		{
			set{ _begindate=value;}
			get{return _begindate;}
		}
		/// <summary>
		/// 结束日期
		/// </summary>
		public DateTime? EndDate
		{
			set{ _enddate=value;}
			get{return _enddate;}
		}
		/// <summary>
		/// 部门
		/// </summary>
		public string Dept
		{
			set{ _dept=value;}
			get{return _dept;}
		}
		/// <summary>
		/// 部门类别
		/// </summary>
		public string DeptType
		{
			set{ _depttype=value;}
			get{return _depttype;}
		}
		/// <summary>
		/// 周数
		/// </summary>
		public int? WeekNum
		{
			set{ _weeknum=value;}
			get{return _weeknum;}
		}
		/// <summary>
		/// 标记
		/// </summary>
		public string Mark
		{
			set{ _mark=value;}
			get{return _mark;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime? Createtime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 创建者
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		#endregion Model

	}
}
