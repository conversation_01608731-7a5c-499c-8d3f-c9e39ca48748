﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace ECB.PC.Model
{
    /// <summary>
    /// 人员去向状态设置记录
    /// </summary>
    [Serializable]
    public partial class ecb_officeStatus_OperationRecord
    {
        public ecb_officeStatus_OperationRecord()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _userid;
        private int _status;
        private Guid _creatoruserid;
        private DateTime _createtime;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 枚举：到校、离校、请假、外出、上课、开会、休息、休假、请勿打扰 从1开始
        /// </summary>
        public int Status
        {
            set { _status = value; }
            get { return _status; }
        }
        /// <summary>
        /// 
        /// </summary>
        public Guid CreatorUserId
        {
            set { _creatoruserid = value; }
            get { return _creatoruserid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime CreateTime
        {
            set { _createtime = value; }
            get { return _createtime; }
        }
        #endregion Model

    }
    /// <summary>
    /// 数据库任务内容类
    /// </summary>
    public class DbTaskContent
    {
        /// <summary>
        /// /sql语句
        /// </summary>
        public string Sql { get; set; }
        /// <summary>
        /// sql参数
        /// </summary>
        public List<SqlParam> SqlParameters { get; set; }
        /// <summary>
        /// 是否存储过程
        /// </summary>
        public bool IsProcedure { get; set; }

    }
    /// <summary>
    /// sql参数
    /// </summary>
    public class SqlParam
    {
        /// <summary>
        /// 参数名称    
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 参数值
        /// </summary>
        public object Value { get; set; }
        /// <summary>
        /// 参数类型
        /// </summary>
        public SqlDbType SqlDbType { get; set; }
    }

}

