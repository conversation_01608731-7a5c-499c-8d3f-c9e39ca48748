﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// AI模型配置
    /// </summary>
    [Serializable]
    public partial class ai_config
    {
        public ai_config()
        { }
        #region Model
        private Guid _id;
        private string _model;
        private string _apiurl;
        private string _token;
        private string _modelconfig;
        private int _maxdialognum = 15;
        private int _dialogendtime = 5;
        private int _historydialognum = 10;
        private int _isEnable = 0;
        public string ModelName { get; set; }
        /// <summary>
        /// 主键
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 模型 唯一
        /// </summary>
        public string Model
        {
            set { _model = value; }
            get { return _model; }
        }
        /// <summary>
        /// 模型接口
        /// </summary>
        public string ApiUrl
        {
            set { _apiurl = value; }
            get { return _apiurl; }
        }
        /// <summary>
        /// 接口凭据 加密
        /// </summary>
        public string Token
        {
            set { _token = value; }
            get { return _token; }
        }
        /// <summary>
        /// 模型配置
        /// </summary>
        public string ModelConfig
        {
            set { _modelconfig = value; }
            get { return _modelconfig; }
        }
        /// <summary>
        /// 最大对话轮数
        /// </summary>
        public int MaxDialogNum
        {
            set { _maxdialognum = value; }
            get { return _maxdialognum; }
        }
        /// <summary>
        /// 班牌对话结束时间
        /// </summary>
        public int DialogEndTime
        {
            set { _dialogendtime = value; }
            get { return _dialogendtime; }
        }
        /// <summary>
        /// 保留会话记录个数
        /// </summary>
        public int HistoryDialogNum
        {
            set { _historydialognum = value; }
            get { return _historydialognum; }
        }
        /// <summary>
        /// 是否启用
        /// </summary>
        public int IsEnable
        {
            set { _isEnable = value; }
            get { return _isEnable; }
        }
        #endregion Model
    }
}

