﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// xy_app_msg_cate:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class xy_app_msg_cate
	{
		public xy_app_msg_cate()
		{}
		#region Model
		private string _id;
		private string _name;
		private string _template;
		private string _pid;
		private string _path;
		private int? _depth;
		private int? _orderid;
		private string _icon;
		private string _color;
		private int? _isshow;
		private string _setting;
		private string _pageurl;
		private string _msgpcurl;
		private string _msgappurl;
		/// <summary>
		/// 
		/// </summary>
		public string id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 类别名称
		/// </summary>
		public string name
		{
			set{ _name=value;}
			get{return _name;}
		}
		/// <summary>
		/// 模版
		/// </summary>
		public string template
		{
			set{ _template=value;}
			get{return _template;}
		}
		/// <summary>
		/// 上级ID（顶级为0）
		/// </summary>
		public string pId
		{
			set{ _pid=value;}
			get{return _pid;}
		}
		/// <summary>
		/// 路径
		/// </summary>
		public string path
		{
			set{ _path=value;}
			get{return _path;}
		}
		/// <summary>
		/// 层级
		/// </summary>
		public int? depth
		{
			set{ _depth=value;}
			get{return _depth;}
		}
		/// <summary>
		/// 排序
		/// </summary>
		public int? orderId
		{
			set{ _orderid=value;}
			get{return _orderid;}
		}
		/// <summary>
		/// 图标（为空则显示name）
		/// </summary>
		public string icon
		{
			set{ _icon=value;}
			get{return _icon;}
		}
		/// <summary>
		/// 颜色
		/// </summary>
		public string color
		{
			set{ _color=value;}
			get{return _color;}
		}
		/// <summary>
		/// 0不显示1总是显示2有相关信息才显示
		/// </summary>
		public int? isShow
		{
			set{ _isshow=value;}
			get{return _isshow;}
		}
		/// <summary>
		/// 配置json格式
		/// </summary>
		public string setting
		{
			set{ _setting=value;}
			get{return _setting;}
		}
		/// <summary>
		/// 模块链接地址
		/// </summary>
		public string pageUrl
		{
			set{ _pageurl=value;}
			get{return _pageurl;}
		}
		/// <summary>
		/// Pc端推送跳转链接
		/// </summary>
		public string msgPcUrl
		{
			set{ _msgpcurl=value;}
			get{return _msgpcurl;}
		}
		/// <summary>
		/// App端推送跳转链接
		/// </summary>
		public string msgAppUrl
		{
			set{ _msgappurl=value;}
			get{return _msgappurl;}
		}
		#endregion Model

	}
}

