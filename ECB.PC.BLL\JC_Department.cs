﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using YunEdu.DBUtility;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:JC_Department
    /// </summary>
    public partial class JC_Department
    {
        public JC_Department()
        { }
        #region  Method

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return DbHelperSQL.GetMaxID("ID", "JC_Department");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int ID)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.Int,4)
            };
            parameters[0].Value = ID;

            int result = DbHelperSQL.RunProcedure("UP_JC_Department_Exists", parameters, out rowsAffected);
            if (result == 1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.JC_Department model)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.Int,4),
                    new SqlParameter("@DeptName", SqlDbType.NVarChar,10),
                    new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@Columnpath", SqlDbType.NVarChar,100),
                    new SqlParameter("@DeptPhone", SqlDbType.NVarChar,20),
                    new SqlParameter("@DeptDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ChildsNum", SqlDbType.Int,4),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier)
                    };
            parameters[0].Direction = ParameterDirection.Output;
            parameters[1].Value = model.DeptName;
            parameters[2].Value = model.ParentId;
            parameters[3].Value = model.ColumnId;
            parameters[4].Value = model.Columnpath;
            parameters[5].Value = model.DeptPhone;
            parameters[6].Value = model.DeptDesc;
            parameters[7].Value = model.ChildsNum;
            parameters[8].Value = model.OrderId;
            parameters[9].Value = model.PlaceId;

            DbHelperSQL.RunProcedure("UP_JC_Department_ADD", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 添加部门并获取当前添加部门的部门ID
        /// </summary>
        /// <param name="model">部门</param>
        /// <param name="deptId">生产的部门ID</param>
        /// <returns></returns>
        public bool Add(ECB.PC.Model.JC_Department model, ref int deptId)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.Int,4),
                    new SqlParameter("@DeptName", SqlDbType.NVarChar,10),
                    new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@Columnpath", SqlDbType.NVarChar,100),
                    new SqlParameter("@DeptPhone", SqlDbType.NVarChar,20),
                    new SqlParameter("@DeptDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ChildsNum", SqlDbType.Int,4),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier)
                    };
            parameters[0].Direction = ParameterDirection.Output;
            parameters[1].Value = model.DeptName;
            parameters[2].Value = model.ParentId;
            parameters[3].Value = model.ColumnId;
            parameters[4].Value = model.Columnpath;
            parameters[5].Value = model.DeptPhone;
            parameters[6].Value = model.DeptDesc;
            parameters[7].Value = model.ChildsNum;
            if (model.OrderId == null)
            {
                model.OrderId = 0;
            }
            parameters[8].Value = model.OrderId;
            parameters[9].Value = model.PlaceId;

            DbHelperSQL.RunProcedure("UP_JC_Department_ADD", parameters, out rowsAffected);
            if (parameters[0].Value != null)
            {
                deptId = int.Parse(parameters[0].Value.ToString());
            }
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.JC_Department model)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.Int,4),
                    new SqlParameter("@DeptName", SqlDbType.NVarChar,10),
                    new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@Columnpath", SqlDbType.NVarChar,100),
                    new SqlParameter("@DeptPhone", SqlDbType.NVarChar,20),
                    new SqlParameter("@DeptDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ChildsNum", SqlDbType.Int,4),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.DeptName;
            parameters[2].Value = model.ParentId;
            parameters[3].Value = model.ColumnId;
            parameters[4].Value = model.Columnpath;
            parameters[5].Value = model.DeptPhone;
            parameters[6].Value = model.DeptDesc;
            parameters[7].Value = model.ChildsNum;
            parameters[8].Value = model.OrderId;
            parameters[9].Value = model.PlaceId;
            DbHelperSQL.RunProcedure("UP_JC_Department_Update", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int ID)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.Int,4)
            };
            parameters[0].Value = ID;

            DbHelperSQL.RunProcedure("UP_JC_Department_Delete", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Department ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Department GetModel(int ID)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.Int,4)
            };
            parameters[0].Value = ID;

            ECB.PC.Model.JC_Department model = new ECB.PC.Model.JC_Department();
            DataSet ds = DbHelperSQL.RunProcedure("UP_JC_Department_GetModel", parameters, "ds");
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Department DataRowToModel(DataRow row)
        {
            ECB.PC.Model.JC_Department model = new ECB.PC.Model.JC_Department();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = int.Parse(row["ID"].ToString());
                }
                if (row["DeptName"] != null)
                {
                    model.DeptName = row["DeptName"].ToString();
                }
                if (row["ParentId"] != null && row["ParentId"].ToString() != "")
                {
                    model.ParentId = int.Parse(row["ParentId"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["Columnpath"] != null)
                {
                    model.Columnpath = row["Columnpath"].ToString();
                }
                if (row["DeptPhone"] != null)
                {
                    model.DeptPhone = row["DeptPhone"].ToString();
                }
                if (row["DeptDesc"] != null)
                {
                    model.DeptDesc = row["DeptDesc"].ToString();
                }
                if (row["ChildsNum"] != null && row["ChildsNum"].ToString() != "")
                {
                    model.ChildsNum = int.Parse(row["ChildsNum"].ToString());
                }
                if (row["OrderId"] != null && row["OrderId"].ToString() != "")
                {
                    model.OrderId = int.Parse(row["OrderId"].ToString());
                }
                if (row["DeptDepth"] != null && row["DeptDepth"].ToString() != "")
                {
                    model.DeptDepth = int.Parse(row["DeptDepth"].ToString());
                }
                if (row["DeptPath"] != null && row["DeptPath"].ToString() != "")
                {
                    model.DeptPath = row["DeptPath"].ToString();
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = Guid.Parse(row["PlaceId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,DeptName,ParentId,ColumnId,Columnpath,DeptPhone,DeptDesc,ChildsNum,OrderId,DeptDepth,DeptPath ");
            strSql.Append(" FROM JC_Department ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,DeptName,ParentId,ColumnId,Columnpath,DeptPhone,DeptDesc,ChildsNum,OrderId,DeptDepth,DeptPath ");
            strSql.Append(" FROM JC_Department ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_Department ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_Department T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "JC_Department";
            parameters[1].Value = "ID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  Method
        #region  MethodEx

        /// <summary>
        /// 根据columnId删除部门
        /// </summary>
        /// <param name="columnId"></param>
        /// <returns></returns>
        public bool DeleteByColumnId(int columnId)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@columnId", SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            string _sql = @"DELETE JC_TeacherDept WHERE DeptId IN (SELECT id FROM JC_Department WHERE ColumnId=@columnId)
            DELETE JC_Department WHERE ColumnId=@columnId";
            DbHelperSQL.ExecuteSql(_sql, parameters);
            return true;
        }

        /// <summary>
        /// 根据columnId获取部门列表
        /// </summary>
        /// <param name="columnId"></param>
        /// <returns></returns>
        public DataSet GetList(int columnId)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@columnId", SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            string _sql = "select ID,DeptName from JC_Department where columnid=@columnId order by orderid";
            DataSet _dsDepts = DbHelperSQL.Query(_sql, parameters);
            return _dsDepts;
        }

        /// <summary>
        /// 根据columnId获取部门列表
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <param name="parentId">父级id</param>
        /// <returns></returns>
        public DataSet GetList(int columnId, int parentId)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@columnId", SqlDbType.Int,4),
                    new SqlParameter("@ParentId", SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = parentId;
            string _sql = "select ID,DeptName,DeptDepth,DeptPath,ParentId from JC_Department where columnid=@columnId and ParentId=@ParentId order by orderid";
            DataSet _dsDepts = DbHelperSQL.Query(_sql, parameters);
            return _dsDepts;
        }

        /// <summary>
        /// 根据columnId获取部门列表
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <param name="depth">深度</param>
        /// <returns></returns>
        public DataSet GetListByDepth(int columnId, int depth)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@columnId", SqlDbType.Int,4),
                    new SqlParameter("@DeptDepth", SqlDbType.Int,4)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = depth;
            string _sql = "select ID,DeptName,DeptDepth,DeptPath,ParentId from JC_Department where columnid=@columnId and DeptDepth=@DeptDepth order by orderid";
            DataSet _dsDepts = DbHelperSQL.Query(_sql, parameters);
            return _dsDepts;
        }
        #endregion  MethodEx
    }
}

