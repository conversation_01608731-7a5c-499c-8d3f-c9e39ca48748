﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 学生心理基础档案信息
    /// </summary>
    [Serializable]
    public partial class psych_basicInfo
    {
        public psych_basicInfo()
        { }
        #region Model
        private Guid _id;
        private int _columnid;
        private string _columnpath;
        private Guid _studentid;
        private string _familystructure = "";
        private string _familystructureext = "";
        private string _familyeconomics = "";
        private string _educationmethod = "";
        private string _parentrelation = "";
        private string _familyatmosphere = "";
        private string _familyatmosphereext = "";
        private string _accommodation = "";
        private string _accommodationext = "";
        private string _zdjb = "";
        private string _zdjb_ext = "";
        private string _longmedicationuse = "";
        private string _longmedicationuseext = "";
        private string _physicallimitations = "";
        private string _jwxlzd = "";
        private string _jwxlzd_ext = "";
        private string _suicidalreport = "";
        private string _suicidalreportext = "";
        private string _abnormalbehavior = "";
        private string _abnormalbehaviorext = "";
        private string _abnormalstatus = "";
        private Guid _dsteacherid;
        private Guid _lasteditby;
        private DateTime _lastedittime;
		public string EducationMethodExt;
		public string ParentRelationExt;
		/// <summary>
		/// 行政包干教师ID
		/// </summary>
		public Guid BGTeacherId { get; set; }
		/// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区id
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区id路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 学生Id
        /// </summary>
        public Guid StudentId
        {
            set { _studentid = value; }
            get { return _studentid; }
        }
        /// <summary>
        /// 家庭结构
        /// </summary>
        public string FamilyStructure
        {
            set { _familystructure = value; }
            get { return _familystructure; }
        }
        /// <summary>
        /// 家庭结构扩展
        /// </summary>
        public string FamilyStructureExt
        {
            set { _familystructureext = value; }
            get { return _familystructureext; }
        }
        /// <summary>
        /// 家庭经济状况
        /// </summary>
        public string FamilyEconomics
        {
            set { _familyeconomics = value; }
            get { return _familyeconomics; }
        }
        /// <summary>
        /// 家庭教育方法
        /// </summary>
        public string EducationMethod
        {
            set { _educationmethod = value; }
            get { return _educationmethod; }
        }
        /// <summary>
        /// 父母关系
        /// </summary>
        public string ParentRelation
        {
            set { _parentrelation = value; }
            get { return _parentrelation; }
        }
        /// <summary>
        /// 家庭氛围
        /// </summary>
        public string FamilyAtmosphere
        {
            set { _familyatmosphere = value; }
            get { return _familyatmosphere; }
        }
        /// <summary>
        /// 家庭氛围扩展
        /// </summary>
        public string FamilyAtmosphereExt
        {
            set { _familyatmosphereext = value; }
            get { return _familyatmosphereext; }
        }
        /// <summary>
        /// 住宿情况
        /// </summary>
        public string Accommodation
        {
            set { _accommodation = value; }
            get { return _accommodation; }
        }
        /// <summary>
        /// 住宿情况扩展
        /// </summary>
        public string AccommodationExt
        {
            set { _accommodationext = value; }
            get { return _accommodationext; }
        }
        /// <summary>
        /// 重大或慢性疾病
        /// </summary>
        public string ZDJB
        {
            set { _zdjb = value; }
            get { return _zdjb; }
        }
        /// <summary>
        /// 重大或慢性疾病扩展
        /// </summary>
        public string ZDJB_Ext
        {
            set { _zdjb_ext = value; }
            get { return _zdjb_ext; }
        }
        /// <summary>
        /// 长期用药情况
        /// </summary>
        public string LongMedicationUse
        {
            set { _longmedicationuse = value; }
            get { return _longmedicationuse; }
        }
        /// <summary>
        /// 长期用药情况扩展
        /// </summary>
        public string LongMedicationUseExt
        {
            set { _longmedicationuseext = value; }
            get { return _longmedicationuseext; }
        }
        /// <summary>
        /// 体能限制
        /// </summary>
        public string PhysicalLimitations
        {
            set { _physicallimitations = value; }
            get { return _physicallimitations; }
        }
        /// <summary>
        /// 既往心理诊断
        /// </summary>
        public string JWXLZD
        {
            set { _jwxlzd = value; }
            get { return _jwxlzd; }
        }
        /// <summary>
        /// 既往心理诊断扩展
        /// </summary>
        public string JWXLZD_Ext
        {
            set { _jwxlzd_ext = value; }
            get { return _jwxlzd_ext; }
        }
        /// <summary>
        /// 近期自伤/自杀倾向报告
        /// </summary>
        public string SuicidalReport
        {
            set { _suicidalreport = value; }
            get { return _suicidalreport; }
        }
        /// <summary>
        /// 近期自伤/自杀倾向报告扩展
        /// </summary>
        public string SuicidalReportExt
        {
            set { _suicidalreportext = value; }
            get { return _suicidalreportext; }
        }
        /// <summary>
        /// 异常行为
        /// </summary>
        public string AbnormalBehavior
        {
            set { _abnormalbehavior = value; }
            get { return _abnormalbehavior; }
        }
        /// <summary>
        /// 异常行为扩展
        /// </summary>
        public string AbnormalBehaviorExt
        {
            set { _abnormalbehaviorext = value; }
            get { return _abnormalbehaviorext; }
        }
        /// <summary>
        /// 异常状态
        /// </summary>
        public string AbnormalStatus
        {
            set { _abnormalstatus = value; }
            get { return _abnormalstatus; }
        }
        /// <summary>
        /// 导师
        /// </summary>
        public Guid DSTeacherId
        {
            set { _dsteacherid = value; }
            get { return _dsteacherid; }
        }
        /// <summary>
        /// 最后修改人
        /// </summary>
        public Guid LastEditBy
        {
            set { _lasteditby = value; }
            get { return _lasteditby; }
        }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastEditTime
        {
            set { _lastedittime = value; }
            get { return _lastedittime; }
        }
        #endregion Model

    }
}