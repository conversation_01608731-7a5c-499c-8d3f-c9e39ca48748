﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// psych_config:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class psych_config
	{
		public psych_config()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private DateTime? _beginedittime;
		private DateTime? _endedittime;
		private DateTime? _lastedittime;
		private Guid _lasteditor;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? BeginEditTime
		{
			set{ _beginedittime=value;}
			get{return _beginedittime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? EndEditTime
		{
			set{ _endedittime=value;}
			get{return _endedittime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid LastEditor
		{
			set{ _lasteditor=value;}
			get{return _lasteditor;}
		}
		#endregion Model

	}
}

