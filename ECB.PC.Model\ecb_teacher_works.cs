﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_teacher_works:教师个人作品类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_teacher_works
    {
        public ecb_teacher_works()
        { }
        #region Model
        private Guid _id;
        private int? _columnid;
        private string _columnpath;
        private Guid _userid;
        private DateTime? _uploadtime;
        private string _uploadfilepath;
        private string _worksdesc;
        private string _title;
        private string _uploadfilename;
        private int? _ispass;
        private Guid _ispassby;
        private DateTime? _ispassdate;
        /// <summary>
        /// 主键Id
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区Id
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 用户Id
        /// </summary>
        public Guid UserId
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime? UploadTime
        {
            set { _uploadtime = value; }
            get { return _uploadtime; }
        }
        /// <summary>
        /// 上传文件路径
        /// </summary>
        public string UploadFilePath
        {
            set { _uploadfilepath = value; }
            get { return _uploadfilepath; }
        }
        /// <summary>
        /// 作品描述
        /// </summary>
        public string WorksDesc
        {
            set { _worksdesc = value; }
            get { return _worksdesc; }
        }
        /// <summary>
        /// 标题
        /// </summary>
        public string Title
        {
            set { _title = value; }
            get { return _title; }
        }
        /// <summary>
        /// 上传文件名
        /// </summary>
        public string UploadFileName
        {
            set { _uploadfilename = value; }
            get { return _uploadfilename; }
        }
        /// <summary>
		/// 是否通过审核
		/// </summary>
		public int? IsPass
        {
            set { _ispass = value; }
            get { return _ispass; }
        }
        /// <summary>
        /// 审核人
        /// </summary>
        public Guid IsPassBy
        {
            set { _ispassby = value; }
            get { return _ispassby; }
        }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? IsPassDate
        {
            set { _ispassdate = value; }
            get { return _ispassdate; }
        }
        #endregion Model

    }
}