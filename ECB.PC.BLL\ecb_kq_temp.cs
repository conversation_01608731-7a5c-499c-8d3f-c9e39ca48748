﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_kq_temp
    /// </summary>
    public partial class ecb_kq_temp
    {
        public ecb_kq_temp()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_kq_temp");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_kq_temp model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_kq_temp(");
            strSql.Append("Id,ColumnId,ColumnPath,EventName,BrandsType,PlaceId,StaDate,EndDate,StaTime,EndTime,TempType,WeekDays)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@EventName,@BrandsType,@PlaceId,@StaDate,@EndDate,@StaTime,@EndTime,@TempType,@WeekDays)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@EventName", SqlDbType.NVarChar,50),
                    new SqlParameter("@BrandsType", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StaDate", SqlDbType.DateTime),
                    new SqlParameter("@EndDate", SqlDbType.DateTime),
                    new SqlParameter("@StaTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@TempType", SqlDbType.Int,4),
                    new SqlParameter("@WeekDays", SqlDbType.NVarChar,50)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.EventName;
            parameters[4].Value = model.BrandsType;
            parameters[5].Value = model.PlaceId;
            parameters[6].Value = model.StaDate;
            parameters[7].Value = model.EndDate;
            parameters[8].Value = model.StaTime;
            parameters[9].Value = model.EndTime;
            parameters[10].Value = model.TempType;
            parameters[11].Value = model.WeekDays;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_kq_temp model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_kq_temp set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("EventName=@EventName,");
            strSql.Append("BrandsType=@BrandsType,");
            strSql.Append("PlaceId=@PlaceId,");
            strSql.Append("StaDate=@StaDate,");
            strSql.Append("EndDate=@EndDate,");
            strSql.Append("StaTime=@StaTime,");
            strSql.Append("EndTime=@EndTime,");
            strSql.Append("TempType=@TempType,");
            strSql.Append("WeekDays=@WeekDays");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@EventName", SqlDbType.NVarChar,50),
                    new SqlParameter("@BrandsType", SqlDbType.Int,4),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@StaDate", SqlDbType.DateTime),
                    new SqlParameter("@EndDate", SqlDbType.DateTime),
                    new SqlParameter("@StaTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@TempType", SqlDbType.Int,4),
                    new SqlParameter("@WeekDays", SqlDbType.NVarChar,50),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.EventName;
            parameters[3].Value = model.BrandsType;
            parameters[4].Value = model.PlaceId;
            parameters[5].Value = model.StaDate;
            parameters[6].Value = model.EndDate;
            parameters[7].Value = model.StaTime;
            parameters[8].Value = model.EndTime;
            parameters[9].Value = model.TempType;
            parameters[10].Value = model.WeekDays;
            parameters[11].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_temp ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_temp ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_temp GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,EventName,BrandsType,PlaceId,StaDate,EndDate,StaTime,EndTime,TempType,WeekDays from ecb_kq_temp ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_kq_temp model = new ECB.PC.Model.ecb_kq_temp();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_temp DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_kq_temp model = new ECB.PC.Model.ecb_kq_temp();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["EventName"] != null)
                {
                    model.EventName = row["EventName"].ToString();
                }
                if (row["BrandsType"] != null && row["BrandsType"].ToString() != "")
                {
                    model.BrandsType = int.Parse(row["BrandsType"].ToString());
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
                if (row["StaDate"] != null && row["StaDate"].ToString() != "")
                {
                    model.StaDate = DateTime.Parse(row["StaDate"].ToString());
                }
                if (row["EndDate"] != null && row["EndDate"].ToString() != "")
                {
                    model.EndDate = DateTime.Parse(row["EndDate"].ToString());
                }
                if (row["StaTime"] != null && row["StaTime"].ToString() != "")
                {
                    model.StaTime = DateTime.Parse(row["StaTime"].ToString());
                }
                if (row["EndTime"] != null && row["EndTime"].ToString() != "")
                {
                    model.EndTime = DateTime.Parse(row["EndTime"].ToString());
                }
                if (row["TempType"] != null && row["TempType"].ToString() != "")
                {
                    model.TempType = int.Parse(row["TempType"].ToString());
                }
                if (row["WeekDays"] != null)
                {
                    model.WeekDays = row["WeekDays"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,EventName,BrandsType,PlaceId,StaDate,EndDate,StaTime,EndTime,TempType,WeekDays ");
            strSql.Append(" FROM ecb_kq_temp ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,EventName,BrandsType,PlaceId,StaDate,EndDate,StaTime,EndTime,TempType,WeekDays ");
            strSql.Append(" FROM ecb_kq_temp ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_kq_temp ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_kq_temp T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_temp";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod
        /// <summary>
        /// 删除一条数据，删除人员配置，考勤记录
        /// </summary>
        public bool DeleteAll(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_temp ");
            strSql.Append(" where Id=@Id ");
            strSql.Append("delete from ecb_kq_temp_user ");
            strSql.Append(" where AtteId=@Id ");
            strSql.Append("delete from ecb_kq_temp_record ");
            strSql.Append(" where AtteId=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 获取临时考勤统计
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetTempKq(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.AtteId,b.EventName '事件名称',cast(SignTime as date) '日期',SUM(case when Status<>3 then 1 else 0 end) '正常(人)',SUM(case when Status = 3 then 1 else 0 end) '缺卡(人)' from ecb_kq_temp_record a left join ecb_kq_temp b on a.AtteId = b.Id");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" group by cast(SignTime as date),a.AtteId,EventName order by a.AtteId,'日期' desc");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取临时考勤统计详情
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetTempKqDetail(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select c.StudentName,d.TName,SignTime,ISNULL(c.StudentName,d.TName) Name,e.Name PlaceName,a.BrandsType,UserType from ecb_kq_temp_record a left join ecb_kq_temp b on a.AtteId = b.Id left join JC_StudentInfos c on a.UserId = c.ID  left join UserInfos d on a.UserId = d.UserID left join ecb_place e on a.PlaceId=e.Id");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by UserType desc,SignTime,Name");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 个人临时考勤异常统计
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet ExecelOutTempInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  c.UserId ,CName  '姓名',SUM(CASE WHEN a.[Status] = 2 THEN 1 ELSE 0 END) '迟到(次)',SUM(CASE WHEN a.[Status] = 3 THEN 1 ELSE 0 END) '缺卡(次)'\r\n            FROM ecb_kq_temp_record a left join ecb_kq_temp b on a.AtteId = b.Id  left join aspnet_Membership c on a.UserId=c.UserId");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" group by c.UserId,CName order by CName");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

