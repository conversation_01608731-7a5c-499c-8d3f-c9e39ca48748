﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;
using System.Collections;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_XinLi_timetable
    /// </summary>
    public partial class ecb_XinLi_timetable
    {
        public ecb_XinLi_timetable()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_XinLi_timetable");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_XinLi_timetable model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_XinLi_timetable(");
            strSql.Append("Id,ColumnId,ColumnPath,Weekday,Number,StartTime,EndTime,TeacherId,CreateUserId,CreateTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@Weekday,@Number,@StartTime,@EndTime,@TeacherId,@CreateUserId,@CreateTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@Weekday", SqlDbType.Int,4),
                    new SqlParameter("@Number", SqlDbType.Int,4),
                    new SqlParameter("@StartTime", SqlDbType.Time,5),
                    new SqlParameter("@EndTime", SqlDbType.Time,5),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.Weekday;
            parameters[4].Value = model.Number;
            parameters[5].Value = model.StartTime.ToShortTimeString();
            parameters[6].Value = model.EndTime.ToShortTimeString();
            parameters[7].Value = model.TeacherId;
            parameters[8].Value = model.CreateUserId;
            parameters[9].Value = model.CreateTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_XinLi_timetable model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_XinLi_timetable set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Weekday=@Weekday,");
            strSql.Append("Number=@Number,");
            strSql.Append("StartTime=@StartTime,");
            strSql.Append("EndTime=@EndTime,");
            strSql.Append("TeacherId=@TeacherId,");
            strSql.Append("CreateUserId=@CreateUserId,");
            strSql.Append("CreateTime=@CreateTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@Weekday", SqlDbType.Int,4),
                    new SqlParameter("@Number", SqlDbType.Int,4),
                    new SqlParameter("@StartTime", SqlDbType.Time,5),
                    new SqlParameter("@EndTime", SqlDbType.Time,5),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.Weekday;
            parameters[3].Value = model.Number;
            parameters[4].Value = model.StartTime.ToShortTimeString();
            parameters[5].Value = model.EndTime.ToShortTimeString();
            parameters[6].Value = model.TeacherId;
            parameters[7].Value = model.CreateUserId;
            parameters[8].Value = model.CreateTime;
            parameters[9].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_XinLi_timetable ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_XinLi_timetable ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_XinLi_timetable GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Weekday,Number,StartTime,EndTime,TeacherId,CreateUserId,CreateTime from ecb_XinLi_timetable ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_XinLi_timetable model = new ECB.PC.Model.ecb_XinLi_timetable();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_XinLi_timetable DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_XinLi_timetable model = new ECB.PC.Model.ecb_XinLi_timetable();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Weekday"] != null && row["Weekday"].ToString() != "")
                {
                    model.Weekday = int.Parse(row["Weekday"].ToString());
                }
                if (row["Number"] != null && row["Number"].ToString() != "")
                {
                    model.Number = int.Parse(row["Number"].ToString());
                }
                if (row["StartTime"] != null && row["StartTime"].ToString() != "")
                {
                    model.StartTime = DateTime.Parse(row["StartTime"].ToString());
                }
                if (row["EndTime"] != null && row["EndTime"].ToString() != "")
                {
                    model.EndTime = DateTime.Parse(row["EndTime"].ToString());
                }
                if (row["TeacherId"] != null && row["TeacherId"].ToString() != "")
                {
                    model.TeacherId = new Guid(row["TeacherId"].ToString());
                }
                if (row["CreateUserId"] != null && row["CreateUserId"].ToString() != "")
                {
                    model.CreateUserId = new Guid(row["CreateUserId"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Weekday,Number,StartTime,EndTime,TeacherId,CreateUserId,CreateTime ");
            strSql.Append(" FROM ecb_XinLi_timetable ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,Weekday,Number,StartTime,EndTime,TeacherId,CreateUserId,CreateTime ");
            strSql.Append(" FROM ecb_XinLi_timetable ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere, string[] ConnStr=null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_XinLi_timetable ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString(), ConnStr);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_XinLi_timetable T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_XinLi_timetable";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(string fileName, string tabName, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select " + fileName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }
        public int AddTranList(ArrayList list)
        {
            return DbHelperSQL.ExecuteSqlTran(list);
        }
        #endregion  ExtensionMethod
    }
}

