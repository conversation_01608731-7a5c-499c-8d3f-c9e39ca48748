﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_jishiyuan_timetable
	/// </summary>
	public partial class ecb_jishiyuan_timetable
	{
		public ecb_jishiyuan_timetable()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_jishiyuan_timetable");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_jishiyuan_timetable model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_jishiyuan_timetable(");
			strSql.Append("Id,ColumnId,ColumnPath,CourseDate,WeekNum,ClassNum,LesionBeginTime,LesionEndTime,CourseName,TeacherName,ClassName,PlaceId)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@CourseDate,@WeekNum,@ClassNum,@LesionBeginTime,@LesionEndTime,@CourseName,@TeacherName,@ClassName,@PlaceId)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,200),
					new SqlParameter("@CourseDate", SqlDbType.Date,3),
					new SqlParameter("@WeekNum", SqlDbType.Int,4),
					new SqlParameter("@ClassNum", SqlDbType.Int,4),
					new SqlParameter("@LesionBeginTime", SqlDbType.DateTime,50),
					new SqlParameter("@LesionEndTime", SqlDbType.DateTime,50),
					new SqlParameter("@CourseName", SqlDbType.NVarChar,50),
					new SqlParameter("@TeacherName", SqlDbType.NVarChar,50),
					new SqlParameter("@ClassName", SqlDbType.NVarChar,50),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.CourseDate;
			parameters[4].Value = model.WeekNum;
			parameters[5].Value = model.ClassNum;
			parameters[6].Value = model.LesionBeginTime;
			parameters[7].Value = model.LesionEndTime;
			parameters[8].Value = model.CourseName;
			parameters[9].Value = model.TeacherName;
			parameters[10].Value = model.ClassName;
			parameters[11].Value = Guid.NewGuid();

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_jishiyuan_timetable model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_jishiyuan_timetable set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("CourseDate=@CourseDate,");
			strSql.Append("WeekNum=@WeekNum,");
			strSql.Append("ClassNum=@ClassNum,");
			strSql.Append("LesionBeginTime=@LesionBeginTime,");
			strSql.Append("LesionEndTime=@LesionEndTime,");
			strSql.Append("CourseName=@CourseName,");
			strSql.Append("TeacherName=@TeacherName,");
			strSql.Append("ClassName=@ClassName,");
			strSql.Append("PlaceId=@PlaceId");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,200),
					new SqlParameter("@CourseDate", SqlDbType.Date,3),
					new SqlParameter("@WeekNum", SqlDbType.Int,4),
					new SqlParameter("@ClassNum", SqlDbType.Int,4),
					new SqlParameter("@LesionBeginTime", SqlDbType.DateTime,50),
					new SqlParameter("@LesionEndTime", SqlDbType.DateTime,50),
					new SqlParameter("@CourseName", SqlDbType.NVarChar,50),
					new SqlParameter("@TeacherName", SqlDbType.NVarChar,50),
					new SqlParameter("@ClassName", SqlDbType.NVarChar,50),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.CourseDate;
			parameters[3].Value = model.WeekNum;
			parameters[4].Value = model.ClassNum;
			parameters[5].Value = model.LesionBeginTime;
			parameters[6].Value = model.LesionEndTime;
			parameters[7].Value = model.CourseName;
			parameters[8].Value = model.TeacherName;
			parameters[9].Value = model.ClassName;
			parameters[10].Value = model.PlaceId;
			parameters[11].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_jishiyuan_timetable ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_jishiyuan_timetable ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_jishiyuan_timetable GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,CourseDate,WeekNum,ClassNum,LesionBeginTime,LesionEndTime,CourseName,TeacherName,ClassName,PlaceId from ecb_jishiyuan_timetable ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_jishiyuan_timetable model=new ECB.PC.Model.ecb_jishiyuan_timetable();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_jishiyuan_timetable DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_jishiyuan_timetable model=new ECB.PC.Model.ecb_jishiyuan_timetable();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["CourseDate"]!=null && row["CourseDate"].ToString()!="")
				{
					model.CourseDate=DateTime.Parse(row["CourseDate"].ToString());
				}
				if(row["WeekNum"]!=null && row["WeekNum"].ToString()!="")
				{
					model.WeekNum=int.Parse(row["WeekNum"].ToString());
				}
				if(row["ClassNum"]!=null && row["ClassNum"].ToString()!="")
				{
					model.ClassNum=int.Parse(row["ClassNum"].ToString());
				}
				if (row["LesionBeginTime"] != null && row["LesionBeginTime"].ToString() != "")
				{
					model.LesionBeginTime = DateTime.Parse(row["LesionBeginTime"].ToString());
				}
				if (row["LesionEndTime"] != null && row["LesionEndTime"].ToString() != "")
				{
					model.LesionEndTime = DateTime.Parse(row["LesionEndTime"].ToString());
				}
				if (row["CourseName"]!=null)
				{
					model.CourseName=row["CourseName"].ToString();
				}
				if(row["TeacherName"]!=null)
				{
					model.TeacherName=row["TeacherName"].ToString();
				}
				if(row["ClassName"]!=null)
				{
					model.ClassName=row["ClassName"].ToString();
				}
				if(row["PlaceId"]!=null && row["PlaceId"].ToString()!="")
				{
					model.PlaceId= new Guid(row["PlaceId"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,CourseDate,WeekNum,ClassNum,LesionBeginTime,LesionEndTime,CourseName,TeacherName,ClassName,PlaceId ");
			strSql.Append(" FROM ecb_jishiyuan_timetable ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,CourseDate,WeekNum,ClassNum,LesionBeginTime,LesionEndTime,CourseName,TeacherName,ClassName,PlaceId ");
			strSql.Append(" FROM ecb_jishiyuan_timetable ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_jishiyuan_timetable ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_jishiyuan_timetable T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_jishiyuan_timetable";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod
		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetCount(string tblName, string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(DISTINCT a.Id) FROM "+ tblName );
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		/// <param name="tblName">表名</param> 
		/// <param name="PageSize">每页的大小</param>
		/// <param name="PageIndex">当前要显示的页码</param>
		/// <param name="FieldShow">以逗号分隔的要显示的字段列表,如果为空,则显示所有字段</param>
		/// <param name="strWhere">查询条件</param>
		/// <param name="orderConditon">以逗号分隔的排序字段列表</param>
		/// <returns>DataSet</returns>
		/// <summary> 
		/// 根据RowID分页
		/// </summary>
		public DataSet GetList(string tblName, int PageSize, int PageIndex, string ShowField, string strWhere, string OrderField, string[] ConnStr = null)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.NVarChar, -1),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@ShowField", SqlDbType.NVarChar,1000),
					new SqlParameter("@OrderField", SqlDbType.NVarChar,1000),
					new SqlParameter("@strWhere", SqlDbType.NVarChar,-1)
					};
			parameters[0].Value = tblName;
			parameters[1].Value = PageSize;
			parameters[2].Value = PageIndex;
			parameters[3].Value = ShowField;
			parameters[4].Value = OrderField;
			parameters[5].Value = strWhere;
			return DbHelperSQL.RunProcedure("UP_GetListByPage_Distinct", parameters, tblName, 3600, ConnStr);
		}

		/// <summary>
		/// 获取最大节数
		/// </summary>
		public int GetMaxNumber(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select MAX(ClassNum) from ecb_jishiyuan_timetable   ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 获取最大日期数
		/// </summary>
		public int GetMaxWeek(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select MAX(WeekNum) from ecb_jishiyuan_timetable   ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 获取场地课表
		/// </summary>
		/// <param name="TermID">学期编号</param>
		/// <param name="ClassId">班级ID</param>
		/// <returns></returns>
		public DataTable GetPlaceTimetable( Guid PlaceId)
		{
			StringBuilder strSql = new StringBuilder();

			strSql.Append("select a.CourseDate, a.ClassNum,a.WeekNum, a.CourseName,a.ClassName,a.TeacherName,a.LesionBeginTime,a.LesionEndTime,f.Name,case when (select  COUNT(*) from ecb_jishiyuan_timetable TT where TT.PlaceId=a.PlaceId  and TT.WeekNum=a.WeekNum and TT.ClassNum=a.ClassNum and TT.CourseDate=a.CourseDate)>1 then '(合)' else '' end as isHeBan ");
			strSql.Append("  from ecb_jishiyuan_timetable a");
			strSql.Append(" left join ecb_Place f on a.PlaceId=f.ID ");
			strSql.Append(" where a.WeekNum<=7 and  a.PlaceId='" + PlaceId + "' AND a.LesionBeginTime IS NOT NULL  ORDER BY a.WeekNum, a.ClassNum,a.CourseName,a.ClassName");
			DataTable dtResult = DbHelperSQL.Query(strSql.ToString()).Tables[0];
			return dtResult;
		}
		#endregion  ExtensionMethod
	}
}

