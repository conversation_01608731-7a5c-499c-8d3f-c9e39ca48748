﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;
using System.Collections;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_test
    /// </summary>
    public partial class psych_test
    {
        public psych_test()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_test");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.psych_test model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_test(");
            strSql.Append("Id,ColumnId,ColumnPath,Title,CreatorId,CreateTime,IsConfirm)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@Title,@CreatorId,@CreateTime,@IsConfirm)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@Title", SqlDbType.NVarChar,150),
                    new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@IsConfirm", SqlDbType.Int,4)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.Title;
            parameters[4].Value = model.CreatorId;
            parameters[5].Value = model.CreateTime;
            parameters[6].Value = model.IsConfirm;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 增加一条数据（使用事务）
        /// </summary>
        public bool Add(ECB.PC.Model.psych_test model, ArrayList sqlList)
        {
            // 使用字符串插值格式构建SQL语句
            string sql = $@"INSERT INTO psych_test(Id,ColumnId,ColumnPath,Title,CreatorId,CreateTime,IsConfirm) 
                           VALUES('{model.Id}',{model.ColumnId},
                           '{model.ColumnPath}','{YunEdu.Common.DataSecurity.FilteSQLStrAll(model.Title)}',
                           '{model.CreatorId}','{model.CreateTime}',
                           {model.IsConfirm})";

            // 将SQL语句添加到事务列表中
            sqlList.Add(sql);
            
            // 返回生成的ID以便后续使用
            return true;
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.psych_test model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_test set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Title=@Title,");
            strSql.Append("CreatorId=@CreatorId,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("IsConfirm=@IsConfirm");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@Title", SqlDbType.NVarChar,150),
                    new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@IsConfirm", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.Title;
            parameters[3].Value = model.CreatorId;
            parameters[4].Value = model.CreateTime;
            parameters[5].Value = model.IsConfirm;
            parameters[6].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_test ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_test ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_test GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Title,CreatorId,CreateTime,IsConfirm from psych_test ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.psych_test model = new ECB.PC.Model.psych_test();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_test DataRowToModel(DataRow row)
        {
            ECB.PC.Model.psych_test model = new ECB.PC.Model.psych_test();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Title"] != null)
                {
                    model.Title = row["Title"].ToString();
                }
                if (row["CreatorId"] != null && row["CreatorId"].ToString() != "")
                {
                    model.CreatorId = new Guid(row["CreatorId"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["IsConfirm"] != null && row["IsConfirm"].ToString() != "")
                {
                    model.IsConfirm = int.Parse(row["IsConfirm"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Title,CreatorId,CreateTime,IsConfirm ");
            strSql.Append(" FROM psych_test ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,Title,CreatorId,CreateTime,IsConfirm ");
            strSql.Append(" FROM psych_test ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_test ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_test T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_test";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 执行事务
        /// </summary>
        public bool ExecuteSqlTran(ArrayList list)
        {
            int rows = DbHelperSQL.ExecuteSqlTran(list);
            if (rows > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取学生测试结果的统计信息
        /// </summary>
        /// <param name="testId">测试ID</param>
        /// <returns>学生数量和预警人数</returns>
        public static Dictionary<string, int> GetWarningStatistics(Guid testId)
        {
            Dictionary<string, int> result = new Dictionary<string, int>();
            result["studentCount"] = 0;
            result["warningCount"] = 0;
            
            try
            {
                // 查询学生数量和预警人数
                string sql = @"
                    SELECT 
                        COUNT(1) AS StudentCount,
                        SUM(CASE WHEN WarningStatus IS NOT NULL AND WarningStatus <> '' THEN 1 ELSE 0 END) AS WarningCount 
                    FROM psych_test_result 
                    WHERE Id = @TestId";
                
                SqlParameter[] parameters = {
                    new SqlParameter("@TestId", SqlDbType.UniqueIdentifier)
                };
                parameters[0].Value = testId;
                
                DataSet ds = DbHelperSQL.Query(sql, parameters);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    DataRow row = ds.Tables[0].Rows[0];
                    result["studentCount"] = row["StudentCount"] != DBNull.Value ? Convert.ToInt32(row["StudentCount"]) : 0;
                    result["warningCount"] = row["WarningCount"] != DBNull.Value ? Convert.ToInt32(row["WarningCount"]) : 0;
                }
            }
            catch(Exception ex)
            {
                
            }
            
            return result;
        }

        #endregion  ExtensionMethod
    }
}