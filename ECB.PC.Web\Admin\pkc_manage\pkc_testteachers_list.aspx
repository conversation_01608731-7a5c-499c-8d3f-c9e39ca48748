﻿<%@ Page Language="C#" AutoEventWireup="true" StylesheetTheme="Admin_Default" EnableEventValidation="false" CodeBehind="pkc_testteachers_list.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_testteachers_list" %>

<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>监考老师信息管理</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script type="text/javascript">
        function Reload() {
            parent.window.location.reload();
            parent.$.colorbox.close();
        };
    </script>

</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <div style="display: block" id="div1">
                <asp:Panel ID="pnlAdminTop" CssClass="search-bar" runat="server">
                    显示：<asp:DropDownList ID="dorpShowSize" runat="server">
                        <asp:ListItem Value="10">10</asp:ListItem>
                        <asp:ListItem Value="15">15</asp:ListItem>
                        <asp:ListItem Value="20">20</asp:ListItem>
                        <asp:ListItem Value="50">50</asp:ListItem>
                        <asp:ListItem Value="80">80</asp:ListItem>
                        <asp:ListItem Value="100">100</asp:ListItem>
                    </asp:DropDownList>
                    <%--  考场：<asp:DropDownList ID="ddlRoom" runat="server" CssClass="input"></asp:DropDownList>--%>
                 监考职位：<asp:DropDownList ID="dropPosition" runat="server">
                     <asp:ListItem Value="0" Selected="True">=请选择=</asp:ListItem>
                     <asp:ListItem Value="1">监考员</asp:ListItem>
                     <asp:ListItem Value="2">巡考员</asp:ListItem>
                 </asp:DropDownList>
                    查询：<asp:DropDownList ID="dropType" runat="server">
                        <asp:ListItem Value="0">=请选择=</asp:ListItem>
                        <asp:ListItem Value="1" Selected="True">教师姓名</asp:ListItem>
                        <asp:ListItem Value="2">教师编号</asp:ListItem>
                    </asp:DropDownList>
                    <asp:TextBox ID="inputName" runat="server"></asp:TextBox>
                    <asp:Button ID="btnSearch" runat="server" Text="查 找" CssClass="btn" OnClick="btnSearch_Click" ValidationGroup="1" />
                    <asp:Label ID="Label1" runat="server" Text=""></asp:Label>
                </asp:Panel>
                <asp:GridView ID="gvTestTeachersList" runat="server" Width="100%" CellPadding="4" SkinID="gridviewSkin1" EmptyDataText="未找到信息"
                    BorderWidth="1px" DataKeyNames="TestTeacherId"
                    AutoGenerateColumns="False" RowStyle-HorizontalAlign="Center" OnRowDataBound="gvTestTeachersList_RowDataBound" OnRowCommand="gvTestTeachersList_RowCommand" >
                    <Columns>
                        <asp:TemplateField HeaderText="选择">
                            <ItemTemplate>
                                <label class="chkItem">
                                <asp:CheckBox ID="chkItem1" runat="server" />
                                <asp:HiddenField ID="hidID" runat="server" Value='<%# Eval("TestTeacherId") %>' />
                                   </label>
                            </ItemTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30" />
                        </asp:TemplateField>
                        <asp:BoundField DataField="TName" HeaderText="教师姓名" SortExpression="TName">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="UserNo" HeaderText="教师编号" SortExpression="UserNo">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="TeachingSubject" HeaderText="当前任教学科" SortExpression="TeachingSubject">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="Position" HeaderText="监考职位" SortExpression="Position">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="JianKaoCount" HeaderText="监考场次数量" SortExpression="RoomNum">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:TemplateField HeaderText="操作">
                            <ItemTemplate>             
                        <%--        <asp:Button ID="hlEdit" runat="server" CssClass="btnGray" Text="编辑" OnClientClick='<%#"return ShowEdit(this,\""+Eval("TestTeacherId")+"\");" %>' />--%>
                                <asp:Button ID="btnDelete" runat="server" Text="删除" CssClass="btnRed light" CommandName="Del" OnClientClick="return CheckDo('',this.id.replace(/_/g, '$'));" />
                            </ItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" />
                        </asp:TemplateField>
                    </Columns>
                </asp:GridView>
                <div id="pager" class="paging-bar">
                    <div class="l-btns">
                        <span>显示</span><asp:TextBox ID="txtPageNum" runat="server"
                            AutoPostBack="True"
                            CssClass="pagenum" onkeydown="return checkNumber(event);"
                            OnTextChanged="txtPageNum_TextChanged"></asp:TextBox><span>条/页</span>
                    </div>
                    <webdiyer:AspNetPager ID="AspNetPager1" runat="server" AlwaysShow="True" AlwaysShowFirstLastPageNumber="True" CssClass="paging-default" CurrentPageButtonClass="current" CurrentPageButtonPosition="Center" CustomInfoHTML="&lt;span&gt;共%RecordCount%条记录&lt;/span&gt;" CustomInfoSectionWidth="" CustomInfoStyle="" CustomInfoTextAlign="NotSet" FirstPageText="首页" LastPageText="尾页" NavigationToolTipTextFormatString="{0}" NextPageText="下一页" NumericButtonCount="7" OnPageChanged="AspNetPager1_PageChanged" PageIndexBoxType="TextBox" PageSize="10" PagingButtonSpacing="0px" PrevPageText="上一页" ShowCustomInfoSection="Left" ShowFirstLast="False" ShowNavigationToolTip="True" ShowPageIndexBox="Never" UrlPaging="false">
                    </webdiyer:AspNetPager>
                </div>
                <div style="display: block" id="div2">
                    <asp:Panel ID="pnlButtons" runat="server" CssClass="footer-bar">
                        <label for="chkAll" class="btnWhite light">
	                        <input id="chkAll" name="chkAll" type="checkbox" />全选
                        </label>
                         <input id="btnAdd" type="button" value="添加" class="btnGreen" onclick="HidePanel2();" />          
                        <asp:Button ID="btnImport" runat="server" Text="1.导入本年级教师" CssClass="btnWhite" OnClientClick=" HidePanel3()" OnClick="btnImport_Click" />
                                    
                        <asp:Panel ID="pnlImpotRooms" runat="server" Style="display: inline-block;">
                            <input id="btnFileImport" type="button" value="导入教师" class="btnWhite" />
                            <asp:FileUpload ID="fupScore" runat="server" onchange="FileImport();" Style="display: none;" />
                            <asp:Button ID="btnImpotTeachers" runat="server" Text="导入教师" CssClass="btnWhite" OnClick="btnImpotTeachers_Click" ValidationGroup="1" Style="display: none;" />
                            <asp:Button ID="btnDownloadTeachers" runat="server" CssClass="btnYellow" Text="下载模板" OnClick="btnDownloadTeachers_Click" />
                        </asp:Panel>
                        <asp:Button ID="btnPosition" runat="server" Text="2.批量设置为巡考员" CssClass="btnGreen" OnClick="btnPosition_Click" />
                         <asp:Button ID="btnInvigilator" runat="server" Text="批量设置为监考员" CssClass="btnGreen" OnClick="btnInvigilator_Click" />
                         <asp:Button ID="btnDel" runat="server" Text="全部删除" OnClientClick='return CheckDo("该操作将删除，不可恢复，是否删除?","btnDel")' CssClass="btnRed" OnClick="btnDeleteAll_Click" />
                        <asp:Button ID="btnBack" runat="server" Text="返回" CssClass="btnWhite" OnClick="btnBack_Click" />
                        <asp:Label ID="lblMessage" runat="server" ForeColor="Red"></asp:Label>
                        <br />
                        <asp:Button ID="btnOK" style="margin-top:5px;" runat="server" Text="3.设置每个考场监考人数" CssClass="btnGreen" OnClientClick="return HidePanel()" OnClick="btnOK_Click" />
                        为：<asp:TextBox ID="txtVNumber" runat="server" Width="28px"></asp:TextBox>
                        人         
                        &nbsp<div style="color: red; display: inline">
                            <asp:Label ID="lblShowMessage" runat="server" Text=""></asp:Label>
                        </div>
                        <br />
                        <asp:Button ID="btnJianKaoCount" runat="server" Text="4.批量设置监考场次数量" CssClass="btnGreen" OnClick="btnJianKaoCount_Click" />
                        为：<asp:TextBox ID="txtCount" runat="server" Width="28px"></asp:TextBox>
                        场&nbsp <span style="color: gray;">特殊情况下使用，为特定老师设置监考场次数量</span>
                        <br />
                       
                    </asp:Panel>
                </div>
            </div>
            <div style="display: none;" id="loading">
                处理中……
            </div>
            <div style="display: none" id="div3">
                <br />
                <div id="sidebar-tab">
                    <div id="tab-title">
                    </div>
                    <div id="tab-content">
                        <div id="list">
                            <table id="infoTable" cellspacing="1" cellpadding="3" border="0" align="left" style="width: 50%;" class="margin-bottom-bar">
                                <tbody>
                                    <tr>
                                        <td height="25" width="10%" align="right" class="left_title_1">
                                            <span class="red">*</span>监考教师：
                                        </td>
                                        <td width="15%">
                                            <asp:HiddenField ID="hfldTeacher" runat="server" />
                                            <asp:TextBox ID="txtTeachers" runat="server" Enabled="False" Width="100px"></asp:TextBox>
                                            <a id="aBtn" runat="server" onclick="SelectTeacher()" href="javascript:void(0)">选择</a>
                                        </td>

                                        <td height="25" width="10%" align="right" class="left_title_1">
                                            <span class="red">*</span>监考职位：
                                        </td>
                                        <td width="15%">
                                            <asp:DropDownList ID="ddlPosition" runat="server" Width="120px"></asp:DropDownList>
                                        </td>
                                        <%-- <td height="25" width="10%" align="right" class="left_title_1">
                                            <span class="red"></span>监考场次数量：
                                        </td>
                                       <td width="15%">
                                            <asp:TextBox ID="txtJianKaoCount" runat="server" Width="100px"></asp:TextBox>
                                        </td>--%>
                                    </tr>
                                </tbody>
                            </table>
                            <div style="display:inline-block;width:100%;text-align:center;">

                                <asp:Button ID="btnAddTeacher" runat="server" Text="提交" CssClass="btnGreen" OnClick="btnAddTeacher_Click" />
                                 <asp:Button ID="Button2" runat="server" Text="返回" CssClass="btnGray" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <%--     <script src="../js/pkc_testusers.js"></script>--%>    </script>
        <script type="text/javascript" charset="utf-8">
            //
            //选择教师
            function SelectTeacher() {
                layer.open({
                    type: 2,
                    title: '选择',
                    area: ['800px', '480px'],
                    content: '/Admin/jc_infos/JC_Teacher_view.aspx?NAME=txtTeachers&VALUE=hfldTeacher&ID=' + <%=modelAreaUser.ColumnID %>
                });
            }
        </script>
    <script type="text/javascript">
        //打开选择文件框 自动导入
        $("#btnImpotTeachers").hide();
        $("#btnFileImport").click(function () {
            $("#fupScore").click();
        });
        function FileImport() {
            $("#btnImpotTeachers").click();
        }
        ///////////end///////////

        function ShowEdit(e, id) {
            var url = '<%= "/admin/pkc_manage/pkc_testteachers_edit.aspx?t=" + YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"].ToString()) +"&testid="+Request.QueryString["id"]+ "&ctlId=hlEdit&id="%>' + id;
            layer.open({
                type: 2,
                title: '选择',
                area: ['500px', '230px'],
                content: url
                });
        };
        // colorbox添加
       <%-- $('#btnAdd').click(function () {
            var url = '<%= "/admin/pkc_manage/pkc_testteachers_edit.aspx?t=" + YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"].ToString()) + "&ctlId=btnAdd" +"&testid="+Request.QueryString["id"]%>';
            colorboxShow('btnAdd', url, 850, 550);
        });--%>
        function HidePanel() {
            layer.confirm('该操作将设置好每个老师的监考场次数，不可再添加和编辑，删除任何老师，是否执行此操作?', {
                icon: 3,
                title: '提示',
                btn: ['确认', '取消']
            }, function (index) {
                // 用户点击了确认按钮  
                layer.close(index);
                $("#div1").hide().addClass();
                $("#loading").show().addClass();
                return true;
            }, function (index) {
                // 用户点击了取消按钮  
                layer.close(index);
                return false;
            });
            return false;
        }
        function HidePanel2() {
            layer.open({
                type: 1,
                title: '选择',
                area: ['500px', '230px'],
                content: $("#div3")
            });
            return false;
        }
        function HidePanel3() {
            $("#div1").hide().addClass();
            $("#loading").show().addClass();
        }
    </script>
</body>
</html>
