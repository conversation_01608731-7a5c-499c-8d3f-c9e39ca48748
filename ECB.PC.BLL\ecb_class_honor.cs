﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    public partial class ecb_class_honor
    {
        public ecb_class_honor()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_class_honor");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_class_honor model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_class_honor(");
            strSql.Append("ID,ColumnID,ColumnPath,ClassId,AwardsName,TypeCode,LeveCode,RankCode,AwardsDate,AwardsDesc,CreatDate,LastEditDate,LastEditBy,is_show,is_top,IsPass,IsPassBy,IsPassDate)");
            strSql.Append(" values (");
            strSql.Append("@ID,@ColumnID,@ColumnPath,@ClassId,@AwardsName,@TypeCode,@LeveCode,@RankCode,@AwardsDate,@AwardsDesc,@CreatDate,@LastEditDate,@LastEditBy,@is_show,@is_top,@IsPass,@IsPassBy,@IsPassDate)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@AwardsName", SqlDbType.NVarChar,50),
                    new SqlParameter("@TypeCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@LeveCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@RankCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@AwardsDate", SqlDbType.DateTime),
                    new SqlParameter("@AwardsDesc", SqlDbType.NVarChar,255),
                    new SqlParameter("@CreatDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@is_show", SqlDbType.Int,4),
                    new SqlParameter("@is_top", SqlDbType.Int,4),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.ColumnID;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ClassId;
            parameters[4].Value = model.AwardsName;
            parameters[5].Value = model.TypeCode;
            parameters[6].Value = model.LeveCode;
            parameters[7].Value = model.RankCode;
            parameters[8].Value = model.AwardsDate;
            parameters[9].Value = model.AwardsDesc;
            parameters[10].Value = model.CreatDate;
            parameters[11].Value = model.LastEditDate;
            parameters[12].Value = model.LastEditBy;
            parameters[13].Value = model.is_show;
            parameters[14].Value = model.is_top;
            parameters[15].Value = model.IsPass;
            parameters[16].Value = model.IsPassBy;
            parameters[17].Value = model.IsPassDate;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_class_honor model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_class_honor set ");
            strSql.Append("ColumnID=@ColumnID,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("AwardsName=@AwardsName,");
            strSql.Append("TypeCode=@TypeCode,");
            strSql.Append("LeveCode=@LeveCode,");
            strSql.Append("RankCode=@RankCode,");
            strSql.Append("AwardsDate=@AwardsDate,");
            strSql.Append("AwardsDesc=@AwardsDesc,");
            strSql.Append("CreatDate=@CreatDate,");
            strSql.Append("LastEditDate=@LastEditDate,");
            strSql.Append("LastEditBy=@LastEditBy,");
            strSql.Append("is_show=@is_show,");
            strSql.Append("is_top=@is_top,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("IsPassDate=@IsPassDate");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnID", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@AwardsName", SqlDbType.NVarChar,50),
                    new SqlParameter("@TypeCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@LeveCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@RankCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@AwardsDate", SqlDbType.DateTime),
                    new SqlParameter("@AwardsDesc", SqlDbType.NVarChar,255),
                    new SqlParameter("@CreatDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditDate", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@is_show", SqlDbType.Int,4),
                    new SqlParameter("@is_top", SqlDbType.Int,4),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnID;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.ClassId;
            parameters[3].Value = model.AwardsName;
            parameters[4].Value = model.TypeCode;
            parameters[5].Value = model.LeveCode;
            parameters[6].Value = model.RankCode;
            parameters[7].Value = model.AwardsDate;
            parameters[8].Value = model.AwardsDesc;
            parameters[9].Value = model.CreatDate;
            parameters[10].Value = model.LastEditDate;
            parameters[11].Value = model.LastEditBy;
            parameters[12].Value = model.is_show;
            parameters[13].Value = model.is_top;
            parameters[14].Value = model.IsPass;
            parameters[15].Value = model.IsPassBy;
            parameters[16].Value = model.IsPassDate;
            parameters[17].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_class_honor ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_class_honor ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_class_honor GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,ColumnID,ColumnPath,ClassId,AwardsName,TypeCode,LeveCode,RankCode,AwardsDate,AwardsDesc,CreatDate,LastEditDate,LastEditBy,is_show,is_top,IsPass,IsPassBy,IsPassDate from ecb_class_honor ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            Model.ecb_class_honor model = new Model.ecb_class_honor();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_class_honor DataRowToModel(DataRow row)
        {
            Model.ecb_class_honor model = new Model.ecb_class_honor();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["ColumnID"] != null && row["ColumnID"].ToString() != "")
                {
                    model.ColumnID = int.Parse(row["ColumnID"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["AwardsName"] != null)
                {
                    model.AwardsName = row["AwardsName"].ToString();
                }
                if (row["TypeCode"] != null)
                {
                    model.TypeCode = row["TypeCode"].ToString();
                }
                if (row["LeveCode"] != null)
                {
                    model.LeveCode = row["LeveCode"].ToString();
                }
                if (row["RankCode"] != null)
                {
                    model.RankCode = row["RankCode"].ToString();
                }
                if (row["AwardsDate"] != null && row["AwardsDate"].ToString() != "")
                {
                    model.AwardsDate = DateTime.Parse(row["AwardsDate"].ToString());
                }
                if (row["AwardsDesc"] != null)
                {
                    model.AwardsDesc = row["AwardsDesc"].ToString();
                }
                if (row["CreatDate"] != null && row["CreatDate"].ToString() != "")
                {
                    model.CreatDate = DateTime.Parse(row["CreatDate"].ToString());
                }
                if (row["LastEditDate"] != null && row["LastEditDate"].ToString() != "")
                {
                    model.LastEditDate = DateTime.Parse(row["LastEditDate"].ToString());
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["is_show"] != null && row["is_show"].ToString() != "")
                {
                    model.is_show = int.Parse(row["is_show"].ToString());
                }
                if (row["is_top"] != null && row["is_top"].ToString() != "")
                {
                    model.is_top = int.Parse(row["is_top"].ToString());
                }
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    model.IsPass = int.Parse(row["IsPass"].ToString());
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,ColumnID,ColumnPath,ClassId,AwardsName,TypeCode,LeveCode,RankCode,AwardsDate,AwardsDesc,CreatDate,LastEditDate,LastEditBy,is_show,is_top,IsPass,IsPassBy,IsPassDate ");
            strSql.Append(" FROM ecb_class_honor ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,ColumnID,ColumnPath,ClassId,AwardsName,TypeCode,LeveCode,RankCode,AwardsDate,AwardsDesc,CreatDate,LastEditDate,LastEditBy,is_show,is_top,IsPass,IsPassBy,IsPassDate ");
            strSql.Append(" FROM ecb_class_honor ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_class_honor ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_class_honor T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "ecb_class_honor";
            parameters[1].Value = "ID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        public DataSet GetSchool(int ColumnID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM [BM_Areas] ");
            if (ColumnID > 0)
            {
                strSql.Append(" where ColumnID=" + ColumnID);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        #region  ExtendMethod
        /// <summary>
        /// 获得详情列表
        /// </summary>
        /// <returns></returns>
        public DataSet GetDetail(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.*,b.DictText AS TypeName,c.DictText AS LeveName,d.DictText AS RankName,e.ClassName ");
            strSql.Append(" FROM dbo.ecb_class_honor a LEFT JOIN Site_Dictionary b ON a.TypeCode=b.DictValue AND b.DictTypeId=20 LEFT JOIN Site_Dictionary c ON a.LeveCode=c.DictValue AND c.DictTypeId=22 LEFT JOIN Site_Dictionary d ON a.RankCode=d.DictValue AND d.DictTypeId=23 left join JC_ClassInfos e on a.ClassId=e.ID");
            if (ID != Guid.Empty)
            {
                strSql.Append(" where a.ID='" + ID + "'");
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 编辑展示年级、班级
        /// </summary>
        /// <param name="ColumnID"></param>
        /// <returns></returns>
        public DataSet showGC(Guid ClassId, string SchoolColumnPath)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM JC_ClassInfos ");
            if (ClassId != Guid.Empty)
            {
                strSql.Append(" where ID='" + ClassId + "'");
            }
            if (!string.IsNullOrEmpty(SchoolColumnPath))
            {
                strSql.Append(" and SchoolColumnPath='" + SchoolColumnPath + "'");
            }
            strSql.Append("and SchoolYear='" + getNowYear() + "'");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取当前学年
        /// </summary>
        /// <returns></returns>
        public static string getNowYear()
        {
            string strTime = "";
            string nowYear = System.DateTime.Now.ToString("yyyy");
            int toLastYear = int.Parse(nowYear) - 1;
            string lastYear = toLastYear.ToString();
            strTime = lastYear + "-" + nowYear;
            return strTime;
        }
        #endregion  ExtendMethod
    }
}
