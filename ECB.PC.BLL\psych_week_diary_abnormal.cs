﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:psych_week_diary_abnormal
    /// </summary>
    public partial class psych_week_diary_abnormal
    {
        public psych_week_diary_abnormal()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from psych_week_diary_abnormal");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.psych_week_diary_abnormal model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into psych_week_diary_abnormal(");
            strSql.Append("Id,ColumnId,ColumnPath,StudentId,SubmitTime,ClassName,ReviewerId,AbnormalKeywords,AbnormalEmotions,WeekDiaryPic,AbnormalType,AbnormalTypeExt,SeverityLevel,CLCS,CLCS_Ext,Mark,GJRecod,CreatorId,CreateTime,LastEditBy,LastEditTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@StudentId,@SubmitTime,@ClassName,@ReviewerId,@AbnormalKeywords,@AbnormalEmotions,@WeekDiaryPic,@AbnormalType,@AbnormalTypeExt,@SeverityLevel,@CLCS,@CLCS_Ext,@Mark,@GJRecod,@CreatorId,@CreateTime,@LastEditBy,@LastEditTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubmitTime", SqlDbType.DateTime),
                    new SqlParameter("@ClassName", SqlDbType.NVarChar,50),
                    new SqlParameter("@ReviewerId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@AbnormalKeywords", SqlDbType.NVarChar,-1),
                    new SqlParameter("@AbnormalEmotions", SqlDbType.NVarChar,-1),
                    new SqlParameter("@WeekDiaryPic", SqlDbType.NVarChar,-1),
                    new SqlParameter("@AbnormalType", SqlDbType.NVarChar,50),
                    new SqlParameter("@AbnormalTypeExt", SqlDbType.NVarChar,500),
                    new SqlParameter("@SeverityLevel", SqlDbType.NVarChar,4),
                    new SqlParameter("@CLCS", SqlDbType.NVarChar,50),
                    new SqlParameter("@CLCS_Ext", SqlDbType.NVarChar,500),
                    new SqlParameter("@Mark", SqlDbType.NVarChar,-1),
                    new SqlParameter("@GJRecod", SqlDbType.NVarChar,-1),
                    new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.StudentId;
            parameters[4].Value = model.SubmitTime;
            parameters[5].Value = model.ClassName;
            parameters[6].Value = model.ReviewerId;
            parameters[7].Value = model.AbnormalKeywords;
            parameters[8].Value = model.AbnormalEmotions;
            parameters[9].Value = model.WeekDiaryPic;
            parameters[10].Value = model.AbnormalType;
            parameters[11].Value = model.AbnormalTypeExt;
            parameters[12].Value = model.SeverityLevel;
            parameters[13].Value = model.CLCS;
            parameters[14].Value = model.CLCS_Ext;
            parameters[15].Value = model.Mark;
            parameters[16].Value = model.GJRecod;
            parameters[17].Value = model.CreatorId;
            parameters[18].Value = model.CreateTime;
            parameters[19].Value = model.LastEditBy;
            parameters[20].Value = model.LastEditTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.psych_week_diary_abnormal model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update psych_week_diary_abnormal set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("StudentId=@StudentId,");
            strSql.Append("SubmitTime=@SubmitTime,");
            strSql.Append("ClassName=@ClassName,");
            strSql.Append("ReviewerId=@ReviewerId,");
            strSql.Append("AbnormalKeywords=@AbnormalKeywords,");
            strSql.Append("AbnormalEmotions=@AbnormalEmotions,");
            strSql.Append("WeekDiaryPic=@WeekDiaryPic,");
            strSql.Append("AbnormalType=@AbnormalType,");
            strSql.Append("AbnormalTypeExt=@AbnormalTypeExt,");
            strSql.Append("SeverityLevel=@SeverityLevel,");
            strSql.Append("CLCS=@CLCS,");
            strSql.Append("CLCS_Ext=@CLCS_Ext,");
            strSql.Append("Mark=@Mark,");
            strSql.Append("GJRecod=@GJRecod,");
            strSql.Append("CreatorId=@CreatorId,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("LastEditBy=@LastEditBy,");
            strSql.Append("LastEditTime=@LastEditTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubmitTime", SqlDbType.DateTime),
                    new SqlParameter("@ClassName", SqlDbType.NVarChar,50),
                    new SqlParameter("@ReviewerId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@AbnormalKeywords", SqlDbType.NVarChar,-1),
                    new SqlParameter("@AbnormalEmotions", SqlDbType.NVarChar,-1),
                    new SqlParameter("@WeekDiaryPic", SqlDbType.NVarChar,-1),
                    new SqlParameter("@AbnormalType", SqlDbType.NVarChar,50),
                    new SqlParameter("@AbnormalTypeExt", SqlDbType.NVarChar,500),
                    new SqlParameter("@SeverityLevel", SqlDbType.NVarChar,4),
                    new SqlParameter("@CLCS", SqlDbType.NVarChar,50),
                    new SqlParameter("@CLCS_Ext", SqlDbType.NVarChar,500),
                    new SqlParameter("@Mark", SqlDbType.NVarChar,-1),
                    new SqlParameter("@GJRecod", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreatorId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.StudentId;
            parameters[3].Value = model.SubmitTime;
            parameters[4].Value = model.ClassName;
            parameters[5].Value = model.ReviewerId;
            parameters[6].Value = model.AbnormalKeywords;
            parameters[7].Value = model.AbnormalEmotions;
            parameters[8].Value = model.WeekDiaryPic;
            parameters[9].Value = model.AbnormalType;
            parameters[10].Value = model.AbnormalTypeExt;
            parameters[11].Value = model.SeverityLevel;
            parameters[12].Value = model.CLCS;
            parameters[13].Value = model.CLCS_Ext;
            parameters[14].Value = model.Mark;
            parameters[15].Value = model.GJRecod;
            parameters[16].Value = model.CreatorId;
            parameters[17].Value = model.CreateTime;
            parameters[18].Value = model.LastEditBy;
            parameters[19].Value = model.LastEditTime;
            parameters[20].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_week_diary_abnormal ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from psych_week_diary_abnormal ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_week_diary_abnormal GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,StudentId,SubmitTime,ClassName,ReviewerId,AbnormalKeywords,AbnormalEmotions,WeekDiaryPic,AbnormalType,AbnormalTypeExt,SeverityLevel,CLCS,CLCS_Ext,Mark,GJRecod,CreatorId,CreateTime,LastEditBy,LastEditTime from psych_week_diary_abnormal ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.psych_week_diary_abnormal model = new ECB.PC.Model.psych_week_diary_abnormal();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.psych_week_diary_abnormal DataRowToModel(DataRow row)
        {
            ECB.PC.Model.psych_week_diary_abnormal model = new ECB.PC.Model.psych_week_diary_abnormal();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["SubmitTime"] != null && row["SubmitTime"].ToString() != "")
                {
                    model.SubmitTime = DateTime.Parse(row["SubmitTime"].ToString());
                }
                if (row["ClassName"] != null)
                {
                    model.ClassName = row["ClassName"].ToString();
                }
                if (row["ReviewerId"] != null && row["ReviewerId"].ToString() != "")
                {
                    model.ReviewerId = new Guid(row["ReviewerId"].ToString());
                }
                if (row["AbnormalKeywords"] != null)
                {
                    model.AbnormalKeywords = row["AbnormalKeywords"].ToString();
                }
                if (row["AbnormalEmotions"] != null)
                {
                    model.AbnormalEmotions = row["AbnormalEmotions"].ToString();
                }
                if (row["WeekDiaryPic"] != null)
                {
                    model.WeekDiaryPic = row["WeekDiaryPic"].ToString();
                }
                if (row["AbnormalType"] != null)
                {
                    model.AbnormalType = row["AbnormalType"].ToString();
                }
                if (row["AbnormalTypeExt"] != null)
                {
                    model.AbnormalTypeExt = row["AbnormalTypeExt"].ToString();
                }
                if (row["SeverityLevel"] != null)
                {
                    model.SeverityLevel = row["SeverityLevel"].ToString();
                }
                if (row["CLCS"] != null)
                {
                    model.CLCS = row["CLCS"].ToString();
                }
                if (row["CLCS_Ext"] != null)
                {
                    model.CLCS_Ext = row["CLCS_Ext"].ToString();
                }
                if (row["Mark"] != null)
                {
                    model.Mark = row["Mark"].ToString();
                }
                if (row["GJRecod"] != null)
                {
                    model.GJRecod = row["GJRecod"].ToString();
                }
                if (row["CreatorId"] != null && row["CreatorId"].ToString() != "")
                {
                    model.CreatorId = new Guid(row["CreatorId"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,StudentId,SubmitTime,ClassName,ReviewerId,AbnormalKeywords,AbnormalEmotions,WeekDiaryPic,AbnormalType,AbnormalTypeExt,SeverityLevel,CLCS,CLCS_Ext,Mark,GJRecod,CreatorId,CreateTime,LastEditBy,LastEditTime ");
            strSql.Append(" FROM psych_week_diary_abnormal ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,StudentId,SubmitTime,ClassName,ReviewerId,AbnormalKeywords,AbnormalEmotions,WeekDiaryPic,AbnormalType,AbnormalTypeExt,SeverityLevel,CLCS,CLCS_Ext,Mark,GJRecod,CreatorId,CreateTime,LastEditBy,LastEditTime ");
            strSql.Append(" FROM psych_week_diary_abnormal ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM psych_week_diary_abnormal ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from psych_week_diary_abnormal T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_week_diary_abnormal";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获取周记反馈统计
        /// </summary>
        /// <param name="columnId"></param>
        /// <param name="gradeId"></param>
        /// <param name="classId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public DataSet GetWeekDiaryAbnormalSta(int? columnId = null, Guid? gradeId = null, Guid? classId = null, DateTime? startTime = null, DateTime? endTime = null)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"
         SELECT 
           COUNT(DISTINCT pw.ReviewerId) AS 参与教师,
           COUNT(DISTINCT pw.StudentId) AS 周记人数,
           COUNT(1) AS 周记人次
        FROM psych_week_diary_abnormal pw LEFT JOIN JC_StudentInfos stu ON stu.ID = pw.StudentId");

            sql.AppendLine(" WHERE 1=1");
            List<SqlParameter> parameters = new List<SqlParameter>();

            if (startTime.HasValue)
            {
                sql.AppendLine(" AND pw.SubmitTime >= @StartTime");
                parameters.Add(new SqlParameter("@StartTime", SqlDbType.DateTime) { Value = startTime.Value });
            }

            if (endTime.HasValue)
            {
                sql.AppendLine(" AND pw.SubmitTime <= @EndTime");
                parameters.Add(new SqlParameter("@EndTime", SqlDbType.DateTime) { Value = endTime.Value });
            }

            if (columnId.HasValue)
            {
                sql.AppendLine(" AND stu.ColumnId = @ColumnId");
                parameters.Add(new SqlParameter("@ColumnId", SqlDbType.Int) { Value = columnId.Value });
            }

            if (gradeId.HasValue)
            {
                sql.AppendLine(" AND stu.GradeID = @GradeId");
                parameters.Add(new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier) { Value = gradeId.Value });
            }

            if (classId.HasValue)
            {
                sql.AppendLine(" AND stu.ClassID = @ClassId");
                parameters.Add(new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier) { Value = classId.Value });
            }

            DataSet result = DbHelperSQL.Query(sql.ToString(), parameters.ToArray());
            return result;
        }
        #endregion  ExtensionMethod
    }
}