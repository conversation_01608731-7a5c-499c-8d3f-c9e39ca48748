﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 教师个人课程表
    /// </summary>
    [Serializable]
    public partial class JC_TimetableConfig
    {
        public JC_TimetableConfig()
        { }
        #region Model
        private Guid _id;
        private int _schoolcolumnid;
        private string _schoolcolumnpath;
        private Guid _termid;
        private string _weekday;
        private int _number;
        private DateTime _starttime;
        private DateTime _endtime;
        private int _lessontype;
        private Guid _placeid;
        private Guid _classid;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid ID
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 学校Id
        /// </summary>
        public int SchoolColumnID
        {
            set { _schoolcolumnid = value; }
            get { return _schoolcolumnid; }
        }
        /// <summary>
        /// 学校路径
        /// </summary>
        public string SchoolColumnPath
        {
            set { _schoolcolumnpath = value; }
            get { return _schoolcolumnpath; }
        }
        /// <summary>
        /// 学期id
        /// </summary>
        public Guid TermID
        {
            set { _termid = value; }
            get { return _termid; }
        }
        /// <summary>
        /// 星期
        /// </summary>
        public string Weekday
        {
            set { _weekday = value; }
            get { return _weekday; }
        }
        /// <summary>
        /// 第几节课
        /// </summary>
        public int Number
        {
            set { _number = value; }
            get { return _number; }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime
        {
            set { _starttime = value; }
            get { return _starttime; }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime
        {
            set { _endtime = value; }
            get { return _endtime; }
        }
        /// <summary>
        /// 0早读  1上午  2下午 3 晚自习
        /// </summary>
        public int LessonType
        {
            set { _lessontype = value; }
            get { return _lessontype; }
        }
        /// <summary>
        /// 场地Id
        /// </summary>
        public Guid PlaceId
        {
            set { _placeid = value; }
            get { return _placeid; }
        }
        /// <summary>
        /// 班级Id
        /// </summary>
        public Guid ClassId
        {
            set { _classid = value; }
            get { return _classid; }
        }
        #endregion Model

    }
}

