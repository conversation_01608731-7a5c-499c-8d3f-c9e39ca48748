﻿
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ai_prompts
	/// </summary>
	public partial class ai_prompts
	{
		public ai_prompts()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ai_prompts");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;
			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.ai_prompts model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ai_prompts(");
			strSql.Append("Id,ColumnId,ColumnPath,Model,PromptName,PromptValue,SceneType,IsSpeech,BrandType,OrderId)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@Model,@PromptName,@PromptValue,@SceneType,@IsSpeech,@BrandType,@OrderId)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,250),
					new SqlParameter("@Model", SqlDbType.NVarChar,100),
					new SqlParameter("@PromptName", SqlDbType.NVarChar,100),
					new SqlParameter("@PromptValue", SqlDbType.NVarChar,-1),
					new SqlParameter("@SceneType", SqlDbType.Int,4),
					new SqlParameter("@IsSpeech", SqlDbType.Int,4),
					new SqlParameter("@BrandType", SqlDbType.Int,4),
					new SqlParameter("@OrderId", SqlDbType.Int,4)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.Model;
			parameters[4].Value = model.PromptName;
			parameters[5].Value = model.PromptValue;
			parameters[6].Value = model.SceneType;
			parameters[7].Value = model.IsSpeech;
			parameters[8].Value = model.BrandType;
			parameters[9].Value = model.OrderId;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.ai_prompts model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ai_prompts set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("Model=@Model,");
			strSql.Append("PromptName=@PromptName,");
			strSql.Append("PromptValue=@PromptValue,");
			strSql.Append("SceneType=@SceneType,");
			strSql.Append("IsSpeech=@IsSpeech,");
			strSql.Append("BrandType=@BrandType,");
			strSql.Append("OrderId=@OrderId");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,250),
					new SqlParameter("@Model", SqlDbType.NVarChar,100),
					new SqlParameter("@PromptName", SqlDbType.NVarChar,100),
					new SqlParameter("@PromptValue", SqlDbType.NVarChar,-1),
					new SqlParameter("@SceneType", SqlDbType.Int,4),
					new SqlParameter("@IsSpeech", SqlDbType.Int,4),
					new SqlParameter("@BrandType", SqlDbType.Int,4),
					new SqlParameter("@OrderId", SqlDbType.Int,4),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.Model;
			parameters[3].Value = model.PromptName;
			parameters[4].Value = model.PromptValue;
			parameters[5].Value = model.SceneType;
			parameters[6].Value = model.IsSpeech;
			parameters[7].Value = model.BrandType;
			parameters[8].Value = model.OrderId;
			parameters[9].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ai_prompts ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ai_prompts ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.ai_prompts GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Model,PromptName,PromptValue,SceneType,IsSpeech,BrandType,OrderId from ai_prompts ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			Model.ai_prompts model=new Model.ai_prompts();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.ai_prompts DataRowToModel(DataRow row)
		{
			Model.ai_prompts model=new Model.ai_prompts();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["Model"]!=null)
				{
					model.Model=row["Model"].ToString();
				}
				if(row["PromptName"]!=null)
				{
					model.PromptName=row["PromptName"].ToString();
				}
				if(row["PromptValue"]!=null)
				{
					model.PromptValue=row["PromptValue"].ToString();
				}
				if(row["SceneType"]!=null && row["SceneType"].ToString()!="")
				{
					model.SceneType=int.Parse(row["SceneType"].ToString());
				}
				if(row["IsSpeech"]!=null && row["IsSpeech"].ToString()!="")
				{
					model.IsSpeech=int.Parse(row["IsSpeech"].ToString());
				}
				if(row["BrandType"]!=null && row["BrandType"].ToString()!="")
				{
					model.BrandType=int.Parse(row["BrandType"].ToString());
				}
				if(row["OrderId"]!=null && row["OrderId"].ToString()!="")
				{
					model.OrderId=int.Parse(row["OrderId"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,Model,PromptName,PromptValue,SceneType,IsSpeech,BrandType,OrderId ");
			strSql.Append(" FROM ai_prompts ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,Model,PromptName,PromptValue,SceneType,IsSpeech,BrandType,OrderId ");
			strSql.Append(" FROM ai_prompts ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ai_prompts ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ai_prompts T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ai_prompts";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod
        /// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ai_prompts GetModel(string Model, int SceneType)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,Model,PromptName,PromptValue,SceneType from ai_prompts ");
            strSql.Append(" where Model=@Model and SceneType=@SceneType");
            SqlParameter[] parameters = {
                    new SqlParameter("@Model", SqlDbType.NVarChar,254),
                    new SqlParameter("@SceneType", SqlDbType.Int,4)};
            parameters[0].Value = Model;
            parameters[1].Value = SceneType;
            ECB.PC.Model.ai_prompts model = new ECB.PC.Model.ai_prompts();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
		public bool ExecuteSqlTran(ArrayList list)
		{
			int rows = DbHelperSQL.ExecuteSqlTran(list);
			if (rows > 0)
			{
				return true;
			}
			return false;
		}
		#endregion  ExtensionMethod
	}
}

