﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理签到表
	/// </summary>
	[Serializable]
	public partial class ecb_XinLi_SignIn
	{
		public ecb_XinLi_SignIn()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _userid;
		private int _usertype;
		private DateTime _recorddate;
		private int _weekday;
		private int _number;
		private Guid _functionroomid;
		private DateTime _starttime;
		private DateTime _endtime;
		private DateTime _signtime;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 签到用户
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 用户类型 1教师2学生
		/// </summary>
		public int UserType
		{
			set{ _usertype=value;}
			get{return _usertype;}
		}
		/// <summary>
		/// 日期
		/// </summary>
		public DateTime RecordDate
		{
			set{ _recorddate=value;}
			get{return _recorddate;}
		}
		/// <summary>
		/// 周次
		/// </summary>
		public int Weekday
		{
			set{ _weekday=value;}
			get{return _weekday;}
		}
		/// <summary>
		/// 节次
		/// </summary>
		public int Number
		{
			set{ _number=value;}
			get{return _number;}
		}
		/// <summary>
		/// 功能室id
		/// </summary>
		public Guid FunctionRoomId
		{
			set{ _functionroomid=value;}
			get{return _functionroomid;}
		}
		/// <summary>
		/// 上课开始时间
		/// </summary>
		public DateTime StartTime
		{
			set{ _starttime=value;}
			get{return _starttime;}
		}
		/// <summary>
		/// 上课结束时间
		/// </summary>
		public DateTime EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 签到时间
		/// </summary>
		public DateTime SignTime
		{
			set{ _signtime=value;}
			get{return _signtime;}
		}
		#endregion Model

	}
}

