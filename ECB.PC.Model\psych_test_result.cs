﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理访谈信息
	/// </summary>
	[Serializable]
	public partial class psych_test_result
	{
		public psych_test_result()
		{}
		#region Model
		private Guid _id;
		private int? _columnid;
		private string _columnpath;
		private Guid _studentid;
		private Guid _classid;
		private string _contents;
		private decimal? _totalscore;
		private string _warningstatus;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// Json
		/// </summary>
		public string Contents
		{
			set{ _contents=value;}
			get{return _contents;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? TotalScore
		{
			set{ _totalscore=value;}
			get{return _totalscore;}
		}
		/// <summary>
		/// 预警状态
		/// </summary>
		public string WarningStatus
		{
			set{ _warningstatus=value;}
			get{return _warningstatus;}
		}
		#endregion Model

	}
}