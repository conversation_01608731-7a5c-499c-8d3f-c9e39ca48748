﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_abnormal_config
    /// </summary>
    public partial class ecb_abnormal_config
    {
        public ecb_abnormal_config()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_abnormal_config");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_abnormal_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_abnormal_config(");
            strSql.Append("Id,ColumnId,ColumnPath,Leave,TimeTable,Device,Attendance,LastEditTime,LastEditor)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@Leave,@TimeTable,@Device,@Attendance,@LastEditTime,@LastEditor)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.NVarChar,255),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@Leave", SqlDbType.Decimal,5),
                    new SqlParameter("@TimeTable", SqlDbType.Decimal,5),
                    new SqlParameter("@Device", SqlDbType.Decimal,5),
                    new SqlParameter("@Attendance", SqlDbType.Decimal,5),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.Leave;
            parameters[4].Value = model.TimeTable;
            parameters[5].Value = model.Device;
            parameters[6].Value = model.Attendance;
            parameters[7].Value = model.LastEditTime;
            parameters[8].Value = model.LastEditor;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_abnormal_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_abnormal_config set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Leave=@Leave,");
            strSql.Append("TimeTable=@TimeTable,");
            strSql.Append("Device=@Device,");
            strSql.Append("Attendance=@Attendance,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("LastEditor=@LastEditor");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.NVarChar,255),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@Leave", SqlDbType.Decimal,5),
                    new SqlParameter("@TimeTable", SqlDbType.Decimal,5),
                    new SqlParameter("@Device", SqlDbType.Decimal,5),
                    new SqlParameter("@Attendance", SqlDbType.Decimal,5),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.Leave;
            parameters[3].Value = model.TimeTable;
            parameters[4].Value = model.Device;
            parameters[5].Value = model.Attendance;
            parameters[6].Value = model.LastEditTime;
            parameters[7].Value = model.LastEditor;
            parameters[8].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_abnormal_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_abnormal_config ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_abnormal_config GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Leave,TimeTable,Device,Attendance,LastEditTime,LastEditor from ecb_abnormal_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.ecb_abnormal_config model = new Model.ecb_abnormal_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_abnormal_config DataRowToModel(DataRow row)
        {
            Model.ecb_abnormal_config model = new Model.ecb_abnormal_config();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null)
                {
                    model.ColumnId = row["ColumnId"].ToString();
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Leave"] != null && row["Leave"].ToString() != "")
                {
                    model.Leave = decimal.Parse(row["Leave"].ToString());
                }
                if (row["TimeTable"] != null && row["TimeTable"].ToString() != "")
                {
                    model.TimeTable = decimal.Parse(row["TimeTable"].ToString());
                }
                if (row["Device"] != null && row["Device"].ToString() != "")
                {
                    model.Device = decimal.Parse(row["Device"].ToString());
                }
                if (row["Attendance"] != null && row["Attendance"].ToString() != "")
                {
                    model.Attendance = decimal.Parse(row["Attendance"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
                if (row["LastEditor"] != null && row["LastEditor"].ToString() != "")
                {
                    model.LastEditor = new Guid(row["LastEditor"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,Leave,TimeTable,Device,Attendance,LastEditTime,LastEditor ");
            strSql.Append(" FROM ecb_abnormal_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,Leave,TimeTable,Device,Attendance,LastEditTime,LastEditor ");
            strSql.Append(" FROM ecb_abnormal_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_abnormal_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_abnormal_config T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_abnormal_config";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_abnormal_config GetModel(int ColumnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,Leave,TimeTable,Device,Attendance,LastEditTime,LastEditor from ecb_abnormal_config ");
            strSql.Append(" where ColumnId=@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = ColumnId;

            Model.ecb_abnormal_config model = new Model.ecb_abnormal_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        #endregion  ExtensionMethod
    }
}

