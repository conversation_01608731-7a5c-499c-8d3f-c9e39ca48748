﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_jishiyuan_timetable:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_jishiyuan_timetable
	{
		public ecb_jishiyuan_timetable()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private DateTime? _coursedate;
		private int _weeknum;
		private int _classnum;
		private DateTime _LesionBeginTime;
		private DateTime _LesionEndTime;
		private string _coursename;
		private string _teachername;
		private string _classname;
		private Guid _placeid;
		/// <summary>
		/// id
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 上课日期
		/// </summary>
		public DateTime? CourseDate
		{
			set{ _coursedate=value;}
			get{return _coursedate;}
		}
		/// <summary>
		/// 星期
		/// </summary>
		public int WeekNum
		{
			set{ _weeknum=value;}
			get{return _weeknum;}
		}
		/// <summary>
		/// 节次
		/// </summary>
		public int ClassNum
		{
			set{ _classnum=value;}
			get{return _classnum;}
		}
		/// <summary>
		/// 上课时间
		/// </summary>
		public DateTime LesionBeginTime
		{
			set{ _LesionBeginTime = value;}
			get{return _LesionBeginTime; }
		}
		/// 上课时间
		/// </summary>
		public DateTime LesionEndTime
		{
			set { _LesionEndTime = value; }
			get { return _LesionEndTime; }
		}
		/// <summary>
		/// 课程名称
		/// </summary>
		public string CourseName
		{
			set{ _coursename=value;}
			get{return _coursename;}
		}
		/// <summary>
		/// 任课教师名称
		/// </summary>
		public string TeacherName
		{
			set{ _teachername=value;}
			get{return _teachername;}
		}
		/// <summary>
		/// 上课班级名称
		/// </summary>
		public string ClassName
		{
			set{ _classname=value;}
			get{return _classname;}
		}
		/// <summary>
		/// 地点id
		/// </summary>
		public Guid PlaceId
		{
			set{ _placeid=value;}
			get{return _placeid;}
		}
		#endregion Model

	}
}

