﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// xy_app_msg_receive:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class xy_app_msg_receive
	{
		public xy_app_msg_receive()
		{}
		#region Model
		private Guid _id;
		private Guid _msgid;
		private string _cateid;
		private string _catepath;
		private Guid _userid;
		private int? _isread=0;
		private DateTime? _read_time;
		private string _msg_title;
		private string _msg_content;
		private DateTime? _send_time;
		private DateTime? _push_time;
		private string _gt_result;
		private string _gt_taskid;
		private string _gt_status;
		/// <summary>
		/// 
		/// </summary>
		public Guid id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 消息ID
		/// </summary>
		public Guid msgId
		{
			set{ _msgid=value;}
			get{return _msgid;}
		}
		/// <summary>
		/// 消息类别Id
		/// </summary>
		public string cateId
		{
			set{ _cateid=value;}
			get{return _cateid;}
		}
		/// <summary>
		/// 消息类别路径
		/// </summary>
		public string catePath
		{
			set{ _catepath=value;}
			get{return _catepath;}
		}
		/// <summary>
		/// 接收者
		/// </summary>
		public Guid userId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 是否查阅(0 未读 1 已读 2 已处理)
		/// </summary>
		public int? isRead
		{
			set{ _isread=value;}
			get{return _isread;}
		}
		/// <summary>
		/// 查阅时间
		/// </summary>
		public DateTime? read_time
		{
			set{ _read_time=value;}
			get{return _read_time;}
		}
		/// <summary>
		/// 标题
		/// </summary>
		public string msg_title
		{
			set{ _msg_title=value;}
			get{return _msg_title;}
		}
		/// <summary>
		/// 消息内容
		/// </summary>
		public string msg_content
		{
			set{ _msg_content=value;}
			get{return _msg_content;}
		}
		/// <summary>
		/// 发送时间
		/// </summary>
		public DateTime? send_time
		{
			set{ _send_time=value;}
			get{return _send_time;}
		}
		/// <summary>
		/// 推送时间
		/// </summary>
		public DateTime? push_time
		{
			set{ _push_time=value;}
			get{return _push_time;}
		}
		/// <summary>
		/// 个推结果
		/// </summary>
		public string gt_result
		{
			set{ _gt_result=value;}
			get{return _gt_result;}
		}
		/// <summary>
		/// 个推任务id，result=ok才会有值
		/// </summary>
		public string gt_taskId
		{
			set{ _gt_taskid=value;}
			get{return _gt_taskid;}
		}
		/// <summary>
		/// 个推状态
		/// </summary>
		public string gt_status
		{
			set{ _gt_status=value;}
			get{return _gt_status;}
		}
		#endregion Model

	}
}

