﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_kq_inout_student
    /// </summary>
    public partial class ecb_kq_inout_student
    {
        public ecb_kq_inout_student()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid UserId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_kq_inout_student");
            strSql.Append(" where UserId=@UserId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = UserId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_kq_inout_student model, DateTime date)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //如果请假时间大于等于当前时间 操作当日表
            if (Convert.ToDateTime(date.ToString("yyyy-MM-dd")) >= Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd")))
            {
                tabName = "ecb_kq_inout_student";
            }
            else
            {
                tabName = "ecb_kq_inout_student_history";
            }
            strSql.Append("insert into " + tabName + "(");
            strSql.Append("Id,ColumnId,ColumnPath,UserId,RecordDate,GradeId,ClassId,KQRecords)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@UserId,@RecordDate,@GradeId,@ClassId,@KQRecords)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@KQRecords", SqlDbType.NVarChar,-1)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.UserId;
            parameters[4].Value = model.RecordDate;
            parameters[5].Value = model.GradeId;
            parameters[6].Value = model.ClassId;
            parameters[7].Value = model.KQRecords;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_kq_inout_student model, DateTime date)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //如果请假时间大于等于当前时间 操作当日表
            if (Convert.ToDateTime(date.ToString("yyyy-MM-dd")) >= Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd")))
            {
                tabName = "ecb_kq_inout_student";
            }
            else
            {
                tabName = "ecb_kq_inout_student_history";
            }
            strSql.Append("update " + tabName + " set ");
            strSql.Append("Id=@Id,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("RecordDate=@RecordDate,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("KQRecords=@KQRecords");
            strSql.Append(" where UserId=@UserId and RecordDate=@RecordDate ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,50),
                    new SqlParameter("@RecordDate", SqlDbType.Date,3),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@KQRecords", SqlDbType.NVarChar,-1),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.RecordDate;
            parameters[4].Value = model.GradeId;
            parameters[5].Value = model.ClassId;
            parameters[6].Value = model.KQRecords;
            parameters[7].Value = model.UserId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid UserId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student ");
            strSql.Append(" where UserId=@UserId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = UserId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string UserIdlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student ");
            strSql.Append(" where UserId in (" + UserIdlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_inout_student GetModel(Guid UserId, DateTime RecordDate)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,UserId,RecordDate,GradeId,ClassId,KQRecords from ecb_kq_inout_student ");
            strSql.Append(" where UserId=@UserId AND RecordDate=@RecordDate");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16) ,
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = UserId;
            parameters[1].Value = RecordDate;

            ECB.PC.Model.ecb_kq_inout_student model = new ECB.PC.Model.ecb_kq_inout_student();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_kq_inout_student DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_kq_inout_student model = new ECB.PC.Model.ecb_kq_inout_student();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["RecordDate"] != null && row["RecordDate"].ToString() != "")
                {
                    model.RecordDate = DateTime.Parse(row["RecordDate"].ToString());
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["KQRecords"] != null)
                {
                    model.KQRecords = row["KQRecords"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,UserId,RecordDate,GradeId,ClassId,KQRecords ");
            strSql.Append(" FROM ecb_kq_inout_student ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,UserId,RecordDate,GradeId,ClassId,KQRecords ");
            strSql.Append(" FROM ecb_kq_inout_student ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }



        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_inout_student";
			parameters[1].Value = "UserId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetOpenJson(string FileName, string tabName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  " + FileName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string untabName, string tabName, string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select  COUNT(1)   FROM ( ");
            strSql.Append(untabName);
            strSql.Append(" ) a ");
            strSql.Append(tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string fileName, string tabName, string strWhere, string orderby, int startIndex, int PageSize = 10)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by " + orderby);
            }
            strSql.Append(")AS Row," + fileName + "  from ( " + tabName);
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) ss ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", (startIndex - 1) * PageSize + 1, startIndex * PageSize);
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select KQStage,KQType,SUM(CASE WHEN b.[Status] = 1 THEN 1 ELSE 0 END) '正常',SUM(CASE WHEN b.[Status] = 2 THEN 1 ELSE 0 END) '迟到',SUM(CASE WHEN b.[Status] = 3 THEN 1 ELSE 0 END) '缺卡',SUM(CASE WHEN b.[Status] = 4 THEN 1 ELSE 0 END) '请假',CAST(Round(convert(float,SUM(case when  b.[Status]=3 then 1 else 0 end))/convert(float,COUNT( b.[Status]))*100,2) as nvarchar)+'%' as '缺卡率'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_student union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_student_history ) a cross apply openjson(a.KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME) as b left join JC_StudentInfos c on a.UserId=c.ID where KQTime<=GETDATE() ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }

            strSql.Append(" group by KQStage,KQType order by KQStage,KQType");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendListInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ClassName '班级',StudentName '姓名',CONVERT(varchar(100),SignTime, 23) as '日期',case when KQStage=1 then '上午' when KQStage=2 then '下午' else '晚上' end '考勤时段',case when KQType=1 then '到校' else '离校' end '考勤类型',case when Status = 1 then '正常' when (Status = 2 and KQType = 1)  then '迟到' when (Status = 2 and KQType = 2) then '早退' when Status = 3 then '缺卡' when Status = 4 then '请假' else '' end '考勤状态',CASE WHEN Status=3 THEN '' ELSE CONVERT(varchar(100),SignTime, 120) END  AS '考勤时间'");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_student union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_student_history ) a cross apply openjson(KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME) as b LEFT JOIN JC_StudentInfos C ON UserId=C.ID LEFT JOIN JC_ClassInfos D ON c.ClassId=D.ID ");
            strSql.Append(" WHERE KQTime<=GETDATE() AND Status<>1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }

            strSql.Append(" ORDER BY RecordDate ,KQStage ,KQType ,SignTime ,StudentName");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得统计列表
        /// </summary>
        public DataSet GetStaAttendInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select UserId as ID,StudentName 'UserName',COUNT(1) total ");
            strSql.Append(" FROM (select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_student union all select ColumnId,KQRecords,RecordDate,UserId from ecb_kq_inout_student_history ) a cross apply openjson(a.KQRecords) with([Status] INT,KQStage INT,KQType INT,SignTime DATETIME,KQTime DATETIME) as b left join JC_StudentInfos c on a.UserId=c.ID   where KQTime<=GETDATE()");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" group by UserId,StudentName order by total desc");

            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        ///  修改考勤状态
        /// </summary>
        /// <param name="columnId">地区</param>
        /// <param name="day">日期</param>
        /// <param name="kqStage">要修改的类型</param>
        /// <param name="typeNum">类型数量</param>
        /// <param name="preStatus">改之前的状态</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <param name="gradeId">筛选的年级id</param>
        /// <param name="classId">筛选的班级id</param>
        /// <returns></returns>
        public bool ModifyStatus(int columnId, DateTime day, int kqType, int preStatus, int toStatus, Guid gradeId, Guid classId, string mark)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //修改时间
            if (day.Date == DateTime.Now.Date)
            {
                tabName = "ecb_kq_inout_student";
            }
            else
            {
                tabName = "ecb_kq_inout_student_history";
            }
            strSql.AppendLine("UPDATE " + tabName + " SET KQRecords = (");
            strSql.AppendLine("SELECT b.PlaceId,b.Photo,b.KQStage,b.KQType,b.KQTime,b.LeaveId,");
            strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN @ToStatus ELSE b.[Status] END [Status],");
            strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.[Status]=@PreStatus THEN @Mark ELSE b.Mark END Mark,");
            if (toStatus == 1)
            {
                // 改为正常
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.[Status]=@PreStatus THEN DATEADD(mi," + (kqType == 1 ? -1 : 1) + ",b.KQTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 2)
            {
                // 改为迟到
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.[Status]=@PreStatus THEN DATEADD(mi,2,b.KQTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.[Status]=@PreStatus THEN 120 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 3)
            {
                // 改为缺卡
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.[Status]=@PreStatus THEN '" + day.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.[Status]=@PreStatus THEN 0 ELSE b.LateTime END LateTime");
            }
            strSql.AppendLine("FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords)");
            strSql.AppendLine("WITH (PlaceId UNIQUEIDENTIFIER,[Status] INT,SignTime DATETIME,LateTime INT,Photo NVARCHAR(250),KQStage INT,KQType INT,KQTime DATETIME,LeaveId UNIQUEIDENTIFIER,Mark nvarchar(40)) b");
            strSql.AppendLine("WHERE a.ColumnId=@ColumnId and a.RecordDate=@Date AND a.UserId=" + tabName + ".UserId FOR json PATH)");
            strSql.AppendLine("WHERE ColumnId=@ColumnId and RecordDate=@Date");
            //修改班级考勤
            if (classId != Guid.Empty)
            {
                strSql.AppendLine("and ClassId='" + classId + "'");
            }
            //修改年级考勤
            else if (gradeId != Guid.Empty)
            {
                strSql.AppendLine("and GradeId='" + gradeId + "'");
            }

            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@KQType",SqlDbType.Int,4),
                new SqlParameter("@PreStatus",SqlDbType.Int,4),
                new SqlParameter("@ToStatus",SqlDbType.Int,4),
                new SqlParameter("@Mark",SqlDbType.NVarChar,40)
            };
            parameters[0].Value = columnId;
            parameters[1].Value = day;
            parameters[2].Value = kqType;
            parameters[3].Value = preStatus;
            parameters[4].Value = toStatus;
            parameters[5].Value = mark;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }
        /// <summary>
        ///  修改个人考勤状态
        /// </summary>
        /// <param name="UserId">用户Id</param>
        /// <param name="day">日期</param>
        /// <param name="kqStage">要修改的类型</param>
        /// <param name="toStatus">改之后的状态</param>
        /// <returns></returns>
        public bool ModifyInoutStatus(Guid UserId, DateTime day, int kqType, int KQStage, int toStatus, string mark)
        {
            StringBuilder strSql = new StringBuilder();
            string tabName = "";
            //修改时间
            if (day.Date == DateTime.Now.Date)
            {
                tabName = "ecb_kq_inout_student";
            }
            else
            {
                tabName = "ecb_kq_inout_student_history";
            }
            strSql.AppendLine("UPDATE " + tabName + " SET KQRecords = (");
            strSql.AppendLine("SELECT b.PlaceId,b.Photo,b.KQStage,b.KQType,b.KQTime,b.LeaveId,");
            strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.KQStage=@KQStage THEN @ToStatus ELSE b.[Status] END [Status],");
            strSql.AppendLine("CASE WHEN b.KQType=@KQType AND b.KQStage=@KQStage THEN @Mark ELSE b.Mark END Mark,");
            if (toStatus == 1)
            {
                // 改为正常
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.KQStage=@KQStage THEN DATEADD(mi," + (kqType == 1 ? -1 : 1) + ",b.KQTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.KQStage=@KQStage THEN 0 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 2)
            {
                // 改为迟到
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.KQStage=@KQStage THEN DATEADD(mi,2,b.KQTime) ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.KQStage=@KQStage THEN 120 ELSE b.LateTime END LateTime");
            }
            else if (toStatus == 3)
            {
                // 改为缺卡
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.KQStage=@KQStage THEN '" + day.ToString("yyyy-MM-dd 00:00:00") + "' ELSE b.SignTime END SignTime,");
                strSql.AppendLine("CASE WHEN  b.KQType=@KQType AND b.KQStage=@KQStage THEN 0 ELSE b.LateTime END LateTime");
            }
            strSql.AppendLine("FROM " + tabName + " a CROSS APPLY openjson(a.KQRecords)");
            strSql.AppendLine("WITH (PlaceId UNIQUEIDENTIFIER,[Status] INT,SignTime DATETIME,LateTime INT,Photo NVARCHAR(250),KQStage INT,KQType INT,KQTime DATETIME,LeaveId UNIQUEIDENTIFIER,Mark nvarchar(50)) b");
            strSql.AppendLine("WHERE a.UserId=@UserId and a.RecordDate=@Date  FOR json PATH)");
            strSql.AppendLine("WHERE UserId=@UserId and RecordDate=@Date");


            SqlParameter[] parameters = {
                new SqlParameter("@UserId",SqlDbType.UniqueIdentifier,16),
                new SqlParameter("@Date",SqlDbType.Date),
                new SqlParameter("@KQType",SqlDbType.Int,4),
                new SqlParameter("@KQStage",SqlDbType.Int,4),
                new SqlParameter("@ToStatus",SqlDbType.Int,4),
                new SqlParameter("@Mark",SqlDbType.NVarChar,40)
            };
            parameters[0].Value = UserId;
            parameters[1].Value = day;
            parameters[2].Value = kqType;
            parameters[3].Value = KQStage;
            parameters[4].Value = toStatus;
            parameters[5].Value = mark;
            int effectRows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            return effectRows > 0;
        }
        /// <summary>
        /// 删除指定日期之后的到离校考勤,不包括历史数据
        /// </summary>
        /// <param name="UserId">学生id</param>
        /// <param name="RecordDate">日期</param>
        /// <returns></returns>
        public bool Delete(Guid UserId, DateTime RecordDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_kq_inout_student");
            strSql.Append(" where UserId=@UserId AND RecordDate>@RecordDate");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16) ,
                    new SqlParameter("@RecordDate", SqlDbType.Date,3)};
            parameters[0].Value = UserId;
            parameters[1].Value = RecordDate;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 获取全部到离校考勤总数
        /// </summary>
        public int GetAttendCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"select count(1) FROM (SELECT ColumnId,RecordDate,KQRecords FROM ecb_kq_inout_student UNION ALL 

                        SELECT ColumnId, RecordDate, KQRecords FROM ecb_kq_inout_student_history UNION ALL

                        SELECT ColumnId, RecordDate, KQRecords FROM ecb_kq_inout_teacher UNION ALL

                        SELECT ColumnId, RecordDate, KQRecords FROM ecb_kq_inout_teacher_history
                        ) a CROSS APPLY openjson(a.KQRecords)
                           WITH(KQStage INT, KQType INT,[Status]INT) ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet Getinout_studentList(string strWhere)
        {
            //string tabel_name = "(SELECT * FROM ecb_kq_inout_student UNION ALL SELECT * FROM ecb_kq_inout_student_history)";
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(case when Status=1 then 1 else 0 end) as normal,SUM(case when Status=2 then 1 else 0 end) as late,SUM(case when Status=4 then 1 else 0 end) as leave");
            strSql.Append(string.Format(@" from {0} a
                   CROSS APPLY openjson(KQRecords) WITH(
              [Status] INT )inout", "ecb_kq_inout_student_history"));
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取考勤列表，每行数据为单个用户的所有日期的考勤
        /// 包含单个用户在所有日期内的状态统计
        /// </summary>
        /// <param name="dteBegin">开始时间</param>
        /// <param name="dteEnd">结束时间</param>
        /// <param name="kqStages">考勤阶段列表</param>
        /// <param name="kqTypes">考勤类型列表</param>
        /// <returns>考勤数据集</returns>
        public DataSet GetList(int columnId, DateTime dteBegin, DateTime dteEnd, List<string> kqStages, List<string> kqTypes)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("select c.ClassName '班级',b.CName '姓名',tmp.* from (");
            strSql.AppendLine("select  t.UserId,t.ClassId,");
            DateTime dteTemp = dteBegin;
            while (dteTemp <= dteEnd)
            {
                foreach (string kqStage in kqStages)
                {
                    string kqStageName = kqStage == "1" ? "上午" : kqStage == "2" ? "下午" : "晚上";
                    foreach (string kqType in kqTypes)
                    {
                        string kqTypeName = kqType == "1" ? "到校" : "离校";
                        strSql.AppendLine($"max(case when t.RecordDate='{dteTemp.Date:yyyy-MM-dd}' and t.KQStage={kqStage} and t.KQType={kqType} then (case when t.Status=1 then '正常'when t.Status=2 and t.KQType=1 then '迟到' when t.Status=2 and t.KQType=2 then '早退' when t.Status=3 then '缺卡' when Status = 4 then '请假' else '' end +'|'+t.Mark) else '' end) '{dteTemp.Date:yyyy-MM-dd}|{kqStageName}{kqTypeName}',");
                    }
                }
                dteTemp = dteTemp.AddDays(1);
            }
            strSql.AppendLine("sum(case when t.Status=1 then 1 else 0 end) '正常',");
            strSql.AppendLine("sum(case when t.Status=2 then 1 else 0 end) '迟到/早退',");
            strSql.AppendLine("sum(case when t.Status=3 then 1 else 0 end) '缺卡',");
            strSql.AppendLine("sum(case when t.Status=4 then 1 else 0 end) '请假' ");
            strSql.AppendLine("from (");
            strSql.AppendLine("select a.UserId,a.ClassId,a.RecordDate,b.KQStage,b.KQType,b.Status,isnull(b.Mark,'') Mark");
            strSql.AppendLine("from ecb_kq_inout_student_history a");
            strSql.AppendLine("cross apply openjson(KQRecords)with([Status] INT, KQStage INT, KQType INT, SignTime DATETIME, KQTime DATETIME,PlaceId UniqueIdentifier,Mark nvarchar(50)) b");
            strSql.AppendLine($"where a.ColumnId={columnId} and a.RecordDate>='{dteBegin:yyyy-MM-dd}' and a.RecordDate<='{dteEnd:yyyy-MM-dd}'");
            strSql.AppendLine("union");
            strSql.AppendLine("select a.UserId,a.ClassId,a.RecordDate,b.KQStage,b.KQType,b.Status,isnull(b.Mark,'') Mark");
            strSql.AppendLine("from ecb_kq_inout_student a");
            strSql.AppendLine("cross apply openjson(KQRecords)with([Status] INT, KQStage INT, KQType INT, SignTime DATETIME, KQTime DATETIME,PlaceId UniqueIdentifier,Mark nvarchar(50)) b");
            strSql.AppendLine($"where a.ColumnId={columnId} and a.RecordDate>='{dteBegin:yyyy-MM-dd}' and a.RecordDate<='{dteEnd:yyyy-MM-dd}'");
            strSql.AppendLine(") t group by t.UserId,t.ClassId");
            strSql.AppendLine(") tmp inner join aspnet_Membership b on b.UserId=tmp.UserId");
            strSql.AppendLine("left join JC_ClassInfos c on tmp.ClassId=c.ID");
            strSql.AppendLine("order by tmp.ClassId,b.CName");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}
