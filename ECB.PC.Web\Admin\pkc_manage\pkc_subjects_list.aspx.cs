﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.DBUtility;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_subjects_list : YunEdu.Authority.AdminCommonJC
    {
        string id;
        string tp;
        YunEdu.BLL.PKC_Subjects bllPKC_Subjects = new YunEdu.BLL.PKC_Subjects();
        YunEdu.Model.PKC_Subjects modelPKC_Subjects = new YunEdu.Model.PKC_Subjects();
        YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();
        YunEdu.BLL.PKC_TeacherSubjects bllteachersubjects = new YunEdu.BLL.PKC_TeacherSubjects();
        YunEdu.Model.PKC_TeacherSubjects modelteachersubjects = new YunEdu.Model.PKC_TeacherSubjects();

        YunEdu.BLL.JC_StudentInfos bllstudentinfos = new YunEdu.BLL.JC_StudentInfos();
        YunEdu.Model.JC_StudentInfos modelstudentinfos = new YunEdu.Model.JC_StudentInfos();
        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();

        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
            }
            if (Request.QueryString["tp"] != null)
            {
                tp = Request.QueryString["tp"].ToString();
            }
            if (!IsPostBack)
            {
                BindData();
                lblShowMessage.Text = "注：场次序号是指各科的考试顺序，同科科目场次序号必须唯一，文理科目场次序号要相对应，例：前3场为同科科目语、数、外则场次序号分别为1、2、3，第4场文科考政治理科考物理，则考试序号同为4。";
            }
        }
        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }

        private string GetWhere()
        {

            string userid = "";

            StringBuilder sbWhere = new StringBuilder();
            sbWhere.Append("TestId='" + id + "'");
            if (tp.Equals("search"))
            {
                if (RoleList.Contains("Parents"))
                {
                    foreach (DataRow item in bllstudentinfos.GetListByParentsUserID(base.UserId).Tables[0].Rows)
                    {
                        userid = item["ID"].ToString();
                    }
                }
                if (RoleList.Contains("Students"))
                {
                    userid = base.UserId;
                }
                string propertycode = YunEdu.Common.GetRecordByPageOrder.GetModelField("JC_ClassInfos a left join JC_StudentInfos b on a.ID=b.ClassID", "ClassPropertyCode", "b.ID='" + userid + "'");
                sbWhere.Append("and (PropertyCode='3' or PropertyCode='" + propertycode + "')");
            }

            return sbWhere.ToString();

        }
        private void BindData(bool isMain = false)
        {
            string strWhere = GetWhere();
            AspNetPager1.RecordCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", strWhere, isMain ? DbHelperSQL.ConnMain : null);
            AspNetPager1.PageSize = 15;
            DataSet dsData = YunEdu.Common.GetRecordByPageOrder.GetList("PKC_Subjects", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "*", strWhere, "PropertyCode desc", isMain ? DbHelperSQL.ConnMain : null);
            gvSubjectsList.DataSource = dsData;
            gvSubjectsList.DataBind();
            switch (tp)
            {
                case "search":
                    //for (int i = 0; i < gvSubjectsList.Rows.Count; i++)
                    //{
                    //    gvSubjectsList.Rows[i].Cells[0].Enabled = true;
                    //}
                    pnlBtn.Visible = false;
                    pnlMessage.Visible = false;
                    gvSubjectsList.Columns[0].Visible = false;
                    gvSubjectsList.Columns[4].Visible = false;
                    gvSubjectsList.Columns[5].Visible = false;
                    gvSubjectsList.Columns[6].Visible = false;
                    //gvSubjectsList.Columns[6].Visible = false;
                    break;
                case "set":
                    gvSubjectsList.Columns[3].Visible = false;
                    gvSubjectsList.Columns[8].Visible = false;
                    gvSubjectsList.Columns[7].Visible = false;
                    break;

            }
            modeltests = blltests.GetModel(new Guid(id));
            DateTime Teststarttime = (DateTime)modeltests.TestStartDate;
            DateTime Testendtime = (DateTime)modeltests.TestEndDate;
            hidStartTime.Value = Teststarttime.ToString();
            hidEndTime.Value = Testendtime.ToString();

        }
        protected void btnBack_Click(object sender, EventArgs e)
        {
            switch (tp)
            {
                case "search":
                    Response.Redirect("pkc_information_search.aspx?MenuId=" + Request.QueryString["MenuId"]);
                    break;
                case "set":
                    Response.Redirect("pkc_tests_list.aspx?MenuId=" + Request.QueryString["MenuId"]);
                    break;
            }
        }
        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            BindData();
        }

        protected void btnDelete_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvSubjectsList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        bllteachersubjects.DeleteBySubject(new Guid(id), new Guid(gvSubjectsList.DataKeys[myItem.RowIndex][0].ToString()));

                        iCount++;
                        if (!bllPKC_Subjects.Delete(new Guid(gvSubjectsList.DataKeys[myItem.RowIndex][0].ToString())))
                        {
                            iError++;
                        }


                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要删除的记录!');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('删除失败!');");
            }
            BindData(true);
        }


        /// <summary>
        /// 保存设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnSort_Click(object sender, EventArgs e)
        {
            if (gvSubjectsList.Rows.Count < 1)
            {
                return;
            }
            bool flag = false;
            int iError = 0;
            foreach (GridViewRow myItem in gvSubjectsList.Rows)
            {
                string subjectid = gvSubjectsList.DataKeys[myItem.RowIndex][0].ToString();
                modelPKC_Subjects = bllPKC_Subjects.GetModel(new Guid(subjectid));
                modeltests = blltests.GetModel(new Guid(id));
                int sortId;
                if (((TextBox)myItem.FindControl("txtSort")).Text.Trim() == string.Empty)
                {
                    sortId = 0;
                }
                else if (!int.TryParse(((TextBox)myItem.FindControl("txtSort")).Text.Trim(), out sortId))
                {
                    sortId = 99;
                }
                modelPKC_Subjects.OrderNum = sortId;

                if (((TextBox)myItem.FindControl("txtBeginDate")).Text.Trim() != string.Empty && ((TextBox)myItem.FindControl("txtEndDate")).Text.Trim() != string.Empty)
                {
                    DateTime EndDate = (DateTime)modeltests.TestEndDate;

                    if (modeltests.TestStartDate <= DateTime.Parse(((TextBox)myItem.FindControl("txtBeginDate")).Text) && DateTime.Parse(((TextBox)myItem.FindControl("txtBeginDate")).Text) <= EndDate.AddDays(1) && modeltests.TestStartDate <= DateTime.Parse(((TextBox)myItem.FindControl("txtEndDate")).Text) && DateTime.Parse(((TextBox)myItem.FindControl("txtEndDate")).Text) <= EndDate.AddDays(1) && DateTime.Parse(((TextBox)myItem.FindControl("txtBeginDate")).Text) <= DateTime.Parse(((TextBox)myItem.FindControl("txtEndDate")).Text))
                    {
                        modelPKC_Subjects.StartTime = DateTime.Parse(((TextBox)myItem.FindControl("txtBeginDate")).Text);
                        modelPKC_Subjects.EndTime = DateTime.Parse(((TextBox)myItem.FindControl("txtEndDate")).Text);
                    }
                    else
                    {
                        iError++;
                    }
                }
                flag = bllPKC_Subjects.Update(modelPKC_Subjects);
            }
            if (iError == 0)
            {
                if (flag)
                {
                    MessageBox.ResponseScript(this, "layer.msg('设置成功!所有科目设置完成后请进入第3步：考生管理!');");
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('保存设置失败!');");
                }
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('保存设置失败！存在科目时间不符合规范，结束时间必须晚与开始时间，且各科时间必须在本次考试时间段内!');");
            }

            BindData(true);
        }

        private void ExportSubjectToWordByXmls(string docPath)
        {
            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/subject.xml");
            rootWord.CreateXmlDom(xmlPath);
            myWordHelper.CreateXmlDom(xmlPath);
            ArrayList sectList = new ArrayList();

            #region 动态生成word文档

            int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 1);
            int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 2);
            if (Art != 0 && Math != 0)
            {
                for (int i = 1; i <= 2; i++)
                {
                    Dictionary<string, string> wordTexts = new Dictionary<string, string>();
                    Dictionary<string, YunEdu.Common.WordMLHelper.WordTable> wordTable = new Dictionary<string, YunEdu.Common.WordMLHelper.WordTable>();

                    myWordHelper.CreateXmlDom(xmlPath);

                    wordTexts.Clear();
                    if (i == 1)
                    {
                        wordTexts.Add("PropertyName", "文科科目表");
                    }
                    else
                    {
                        wordTexts.Add("PropertyName", "理科科目表");
                    }
                    myWordHelper.ReplaceNodeText(wordTexts);

                    DataTable dt = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "OrderNum 场次序号,SubjectName 考试科目,StartTime 开始时间,EndTime 结束时间", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=" + i + ")", "OrderNum").Tables[0];
                    //加入表格
                    wordTable.Clear();

                    Dictionary<string, string> bookmarks = new Dictionary<string, string>();
                    foreach (DataColumn column in dt.Columns)
                    {
                        bookmarks.Add(column.ColumnName, column.ColumnName);
                    }
                    myWordHelper.ReplaceNodeTable(dt, bookmarks);

                    sectList.Add(myWordHelper.GetDocSection());
                }
            }
            else
            {
                Dictionary<string, string> wordTexts = new Dictionary<string, string>();
                Dictionary<string, YunEdu.Common.WordMLHelper.WordTable> wordTable = new Dictionary<string, YunEdu.Common.WordMLHelper.WordTable>();

                myWordHelper.CreateXmlDom(xmlPath);

                wordTexts.Clear();
                wordTexts.Add("PropertyName", "科目表");
                myWordHelper.ReplaceNodeText(wordTexts);
                DataTable dt = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "OrderNum 场次序号,SubjectName 考试科目,StartTime 开始时间,EndTime 结束时间", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=2 or PropertyCode=1)", "OrderNum").Tables[0];
                //加入表格
                wordTable.Clear();

                Dictionary<string, string> bookmarks = new Dictionary<string, string>();
                foreach (DataColumn column in dt.Columns)
                {
                    bookmarks.Add(column.ColumnName, column.ColumnName);
                }
                myWordHelper.ReplaceNodeTable(dt, bookmarks);

                sectList.Add(myWordHelper.GetDocSection());
            }
            #endregion

            rootWord.MerginDoc(sectList);//合并word文档内容
            rootWord.Save(docPath);//保存文档
        }

        protected void btnExport_Click(object sender, EventArgs e)
        {
            YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();
            YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();

            modeltests = blltests.GetModel(new Guid(id));
            DataSet dsa = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "UserId", "TestId='" + modeltests.TestId + "'");
            if (dsa.Tables[0].Rows.Count > 0)
            {
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(dsa.Tables[0].Rows[0]["UserId"].ToString()));
                string foldName = Server.MapPath(CodeTable.GetDirectoryTemp(modelstudentinfos.ColumnPath, UserName, CodeTable.FileType.files));
                string tmpDesDir = foldName + modeltests.TestTitle;//Server.MapPath("/") 
                if (!Directory.Exists(tmpDesDir))
                    Directory.CreateDirectory(tmpDesDir);
                string docPath = tmpDesDir + "\\" + modeltests.TestTitle + "科目表" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".doc";
                ExportSubjectToWordByXmls(docPath);

                // 下载文件
                Response.Clear();
                Response.ContentType = "application/ms-word";
                Response.AddHeader("Content-Disposition", "attachment;filename=" + Server.UrlEncode(modeltests.TestTitle + "科目表.doc"));
                Response.TransmitFile(docPath);
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('导出失败，没有排考场用户!');");
            }
        }
    }

}