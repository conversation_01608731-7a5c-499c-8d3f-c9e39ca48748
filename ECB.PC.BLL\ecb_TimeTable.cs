﻿
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_TimeTable
    /// </summary>
    public partial class ecb_TimeTable
    {
        public ecb_TimeTable()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_TimeTable");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_TimeTable model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_TimeTable(");
            strSql.Append("Id,ColumnId,ColumnPath,TermID,GradeId,ClassId,LessonTime,Weekday,Number,SubjectCode,TeacherId,PlaceId,IsCover,WeekType,PlaceId1,SubjectCode1,TeacherId1)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@TermID,@GradeId,@ClassId,@LessonTime,@Weekday,@Number,@SubjectCode,@TeacherId,@PlaceId,@IsCover,@WeekType,@PlaceId1,@SubjectCode1,@TeacherId1)");

            strSql.Append("insert into ecb_TimeTable_stu");
            strSql.Append(" select NEWID(), @ColumnId, @ColumnPath, @TermId, ID, @PlaceId,@ClassId, '1',@SubjectCode, null, @TeacherId, @Weekday,@Number, GETDATE(),@PlaceId1,@SubjectCode1,@TeacherId1 from JC_StudentInfos ");
            strSql.Append(" where ClassID = @ClassId");

            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,200),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LessonTime", SqlDbType.NVarChar,50),
                    new SqlParameter("@Weekday", SqlDbType.Int,4),
                    new SqlParameter("@Number", SqlDbType.Int,4),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,50),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsCover", SqlDbType.Bit,1),
                    new SqlParameter("@WeekType", SqlDbType.NVarChar,50),
                    new SqlParameter("@PlaceId1", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode1", SqlDbType.NVarChar,50),
                    new SqlParameter("@TeacherId1", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.TermID;
            parameters[4].Value = model.GradeId;
            parameters[5].Value = model.ClassId;
            parameters[6].Value = model.LessonTime;
            parameters[7].Value = model.Weekday;
            parameters[8].Value = model.Number;
            parameters[9].Value = model.SubjectCode;
            parameters[10].Value = model.TeacherId;
            parameters[11].Value = model.PlaceId;
            parameters[12].Value = model.IsCover;
            parameters[13].Value = model.WeekType;
            parameters[14].Value = model.PlaceId1;
            parameters[15].Value = model.SubjectCode1;
            parameters[16].Value = model.TeacherId1;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_TimeTable model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_TimeTable set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("TermID=@TermID,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("LessonTime=@LessonTime,");
            strSql.Append("Weekday=@Weekday,");
            strSql.Append("Number=@Number,");
            strSql.Append("SubjectCode=@SubjectCode,");
            strSql.Append("TeacherId=@TeacherId,");
            strSql.Append("PlaceId=@PlaceId,");
            strSql.Append("IsCover=@IsCover,");
            strSql.Append("WeekType=@WeekType,");
            strSql.Append("PlaceId1=@PlaceId1,");
            strSql.Append("SubjectCode1=@SubjectCode1,");
            strSql.Append("TeacherId1=@TeacherId1");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,200),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LessonTime", SqlDbType.NVarChar,50),
                    new SqlParameter("@Weekday", SqlDbType.Int,4),
                    new SqlParameter("@Number", SqlDbType.Int,4),
                    new SqlParameter("@SubjectCode", SqlDbType.NVarChar,50),
                    new SqlParameter("@TeacherId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsCover", SqlDbType.Bit,1),
                    new SqlParameter("@WeekType", SqlDbType.NVarChar,50),
                    new SqlParameter("@PlaceId1", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SubjectCode1", SqlDbType.NVarChar,50),
                    new SqlParameter("@TeacherId1", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)
                    };
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.TermID;
            parameters[3].Value = model.GradeId;
            parameters[4].Value = model.ClassId;
            parameters[5].Value = model.LessonTime;
            parameters[6].Value = model.Weekday;
            parameters[7].Value = model.Number;
            parameters[8].Value = model.SubjectCode;
            parameters[9].Value = model.TeacherId;
            parameters[10].Value = model.PlaceId;
            parameters[11].Value = model.IsCover;
            parameters[12].Value = model.WeekType;
            parameters[13].Value = model.PlaceId1;
            parameters[14].Value = model.SubjectCode1;
            parameters[15].Value = model.TeacherId1;
            parameters[16].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_TimeTable GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 * from ecb_TimeTable ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_TimeTable model = new ECB.PC.Model.ecb_TimeTable();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_TimeTable DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_TimeTable model = new ECB.PC.Model.ecb_TimeTable();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["TermID"] != null && row["TermID"].ToString() != "")
                {
                    model.TermID = new Guid(row["TermID"].ToString());
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["LessonTime"] != null)
                {
                    model.LessonTime = row["LessonTime"].ToString();
                }
                if (row["Weekday"] != null && row["Weekday"].ToString() != "")
                {
                    model.Weekday = int.Parse(row["Weekday"].ToString());
                }
                if (row["Number"] != null && row["Number"].ToString() != "")
                {
                    model.Number = int.Parse(row["Number"].ToString());
                }
                if (row["SubjectCode"] != null)
                {
                    model.SubjectCode = row["SubjectCode"].ToString();
                }
                if (row["TeacherId"] != null && row["TeacherId"].ToString() != "")
                {
                    model.TeacherId = new Guid(row["TeacherId"].ToString());
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
                if (row["IsCover"] != null && row["IsCover"].ToString() != "")
                {
                    if ((row["IsCover"].ToString() == "1") || (row["IsCover"].ToString().ToLower() == "true"))
                    {
                        model.IsCover = true;
                    }
                    else
                    {
                        model.IsCover = false;
                    }
                }
                if (row["WeekType"] != null)
                {
                    model.WeekType = row["WeekType"].ToString();
                }
                if (row["PlaceId1"] != null && row["PlaceId1"].ToString() != "")
                {
                    model.PlaceId1 = new Guid(row["PlaceId1"].ToString());
                }
                if (row["SubjectCode1"] != null)
                {
                    model.SubjectCode1 = row["SubjectCode1"].ToString();
                }
                if (row["TeacherId1"] != null && row["TeacherId1"].ToString() != "")
                {
                    model.TeacherId1 = new Guid(row["TeacherId1"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM ecb_TimeTable ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" * ");
            strSql.Append(" FROM ecb_TimeTable ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_TimeTable a");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_TimeTable T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_TimeTable";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod


        /// <summary>
        /// 获取最大节数
        /// </summary>
        public int GetMaxNumber(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select MAX(a.Number) from ecb_TimeTable a LEFT JOIN JC_TimetableConfig e ON a.TermID=e.TermID and a.Weekday=e.Weekday and a.Number=e.Number and a.ClassId=e.ClassId  ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }



        /// <summary>
        /// 获取可代课教师
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetSubCourseTeacher(string userid, string termId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  a.TeacherId,b.TName FROM ecb_TimeTable a ");
            strSql.Append(" LEFT JOIN  dbo.UserInfos b ON a.TeacherId=b.UserId");
            strSql.Append(" WHERE a.TeacherId<>'" + userid + "' AND  TermID='" + termId + "' GROUP BY  a.TeacherId,b.TName");

            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取可代课教师课程 (待控制同科目，同年级，班级)
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetSubCourseTeacher(int weekday, int classnum, string userid, string termId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  a.TeacherId,b.TName FROM ecb_TimeTable a ");
            strSql.Append(" LEFT JOIN  dbo.UserInfos b ON a.TeacherId=b.UserId");
            strSql.Append(" WHERE EXISTS(SELECT TeacherId FROM dbo.ecb_TimeTable");
            strSql.Append(" WHERE Weekday=" + weekday + " AND Number=" + classnum + ")");
            strSql.Append(" and TeacherId<>'" + userid + "' AND  TermID='" + termId + "'");

            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetTerRecordCount(int weekday, int classnum, string strWhere, bool isSingle)
        {
            string Field = isSingle ? "" : "1";
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ( ");
            strSql.Append(" SELECT TeacherNo from ecb_TimeTable a  LEFT JOIN JC_Subjects b ON a.ClassId=b.ClassID and a.SubjectCode" + Field + "=b.SubjectCode ");
            strSql.Append("WHERE  TeacherNo NOT IN ( SELECT TeacherNo FROM ecb_TimeTable a  LEFT JOIN JC_Subjects b ON a.ClassId=b.ClassID and a.SubjectCode" + Field + "=b.SubjectCode WHERE Weekday=" + weekday + " AND Number=" + classnum + " and b.TeacherNo is not null  and b.StatusCode=1)");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append(" GROUP BY TeacherNo) tt ");
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 双重的 between
        /// 获取可以代课教师 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(int weekday, int classnum, string strWhere, int startIndex, int endIndex, bool isSingle)
        {
            string Field = isSingle ? "" : "1";
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select  * from ( select ROW_NUMBER() OVER (order by UserNo desc)AS Row, * FROM (  SELECT ");
            strSql.Append(" b.Telephone,UserNo,TName from (");
            strSql.Append(" SELECT TeacherNo,a.SubjectCode from ecb_TimeTable a  LEFT JOIN JC_Subjects b ON a.ClassId=b.ClassID and a.SubjectCode" + Field + "=b.SubjectCode WHERE ");
            strSql.Append("  TeacherNo NOT IN ( SELECT TeacherNo FROM ecb_TimeTable a  LEFT JOIN JC_Subjects b ON a.ClassId=b.ClassID and a.SubjectCode" + Field + "=b.SubjectCode and b.StatusCode=1 WHERE Weekday=" + weekday + " AND Number=" + classnum + " and b.TeacherNo is not null and b.StatusCode=1) ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and StatusCode=1 and " + strWhere);
            }
            strSql.Append(" GROUP BY TeacherNo,a.SubjectCode ) a LEFT JOIN dbo.UserInfos b ON  b.UserNo=a.TeacherNo");
            strSql.Append(" ) TT group by Telephone,UserNo,TName) AA");
            strSql.AppendFormat(" WHERE AA.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        /// <param name="tblName">表名</param> 
        /// <param name="PageSize">每页的大小</param>
        /// <param name="PageIndex">当前要显示的页码</param>
        /// <param name="FieldShow">以逗号分隔的要显示的字段列表,如果为空,则显示所有字段</param>
        /// <param name="strWhere">查询条件</param>
        /// <param name="orderConditon">以逗号分隔的排序字段列表</param>
        /// <returns>DataSet</returns>
        /// <summary> 
        /// 根据RowID分页
        /// </summary>
        public DataSet GetList(string tblName, int PageSize, int PageIndex, string ShowField, string strWhere, string OrderField, string[] ConnStr = null)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.NVarChar, -1),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@ShowField", SqlDbType.NVarChar,1000),
                    new SqlParameter("@OrderField", SqlDbType.NVarChar,1000),
                    new SqlParameter("@strWhere", SqlDbType.NVarChar,-1)
                    };
            parameters[0].Value = tblName;
            parameters[1].Value = PageSize;
            parameters[2].Value = PageIndex;
            parameters[3].Value = ShowField;
            parameters[4].Value = OrderField;
            parameters[5].Value = strWhere;
            return DbHelperSQL.RunProcedure("UP_GetListByPage_Distinct", parameters, tblName, 3600, ConnStr);
        }
        /// <summary>
        /// 删除一个班级数据
        /// 连带这个班的学生课表
        /// </summary>
        public bool Delete(Guid Id, Guid TermID, string weekdays = "")
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable ");
            strSql.Append(" where ClassId=@ClassId AND TermID=@TermID");
            if (!string.IsNullOrEmpty(weekdays))
            {
                strSql.Append(" AND Weekday in (" + weekdays + ")");
            }
            strSql.Append(" delete from ecb_TimeTable_stu ");
            strSql.Append(" where ClassId=@ClassId AND TermID=@TermID");
            if (!string.IsNullOrEmpty(weekdays))
            {
                strSql.Append(" AND Weekday in (" + weekdays + ")");
            }
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16) , new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16)         };
            parameters[0].Value = Id;
            parameters[1].Value = TermID;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 修改单节课程
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public bool UpdateCourse(Guid classId, int weekday, int Number, string SubjectCode, Guid teacherId, int weekType)
        {
            StringBuilder strSql = new StringBuilder();
            if (weekType == 1)
            {
                strSql.Append(" UPDATE dbo.ecb_TimeTable SET SubjectCode='" + SubjectCode + "',TeacherId='" + teacherId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

                strSql.Append(" UPDATE dbo.ecb_TimeTable_stu SET SubjectNo='" + SubjectCode + "',TeacherId='" + teacherId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

            }
            else
            {
                strSql.Append(" UPDATE dbo.ecb_TimeTable SET SubjectCode1='" + SubjectCode + "',TeacherId1='" + teacherId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

                strSql.Append(" UPDATE dbo.ecb_TimeTable_stu SET SubjectNo1='" + SubjectCode + "',TeacherId1='" + teacherId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

            }

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 修改场地 单节课程
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public bool UpdateTimeTablePlace(Guid classId, int weekday, int Number, Guid PlaceId, int weekType)
        {
            StringBuilder strSql = new StringBuilder();
            if (weekType == 1)
            {
                strSql.Append(" UPDATE dbo.ecb_TimeTable SET PlaceId='" + PlaceId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

                strSql.Append(" UPDATE dbo.ecb_TimeTable_stu SET PlaceId='" + PlaceId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

            }
            else
            {
                strSql.Append(" UPDATE dbo.ecb_TimeTable SET PlaceId1='" + PlaceId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

                strSql.Append(" UPDATE dbo.ecb_TimeTable_stu SET PlaceId1='" + PlaceId + "'");
                strSql.Append("  where ClassId = '" + classId + "' and Weekday = '" + weekday + "' and Number = '" + Number + "' ");

            }

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除数据
        /// </summary>
        public bool Delete(Guid classId, int weekday, int Number)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable ");
            strSql.Append(" where ClassId=@ClassId and Weekday = @weekday  and Number =@Number ");
            strSql.Append(" delete from ecb_TimeTable_stu ");
            strSql.Append(" where ClassId=@ClassId and Weekday = @weekday  and Number =@Number ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Weekday", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Number", SqlDbType.UniqueIdentifier,16) };
            parameters[0].Value = classId;
            parameters[1].Value = weekday;
            parameters[2].Value = Number;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 获得课表
        /// </summary>
        public DataSet GetCourseTimeTableList(string strWhere, string OrderId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.*,b.ClassName,b.ID 'ClassId',b.GradeId,(SELECT TName FROM dbo.UserInfos WHERE UserID=a.TeacherId) 'TName',c.DictText  ");
            strSql.Append(" FROM ecb_TimeTable a LEFT JOIN dbo.JC_ClassInfos b ON a.ClassID=b.ID ");
            strSql.Append(" left join Site_Dictionary c on DictTypeId=28 AND a.SubjectCode=c.DictValue and c.ColumnId in (0,a.ColumnId)");
            strSql.Append(" LEFT JOIN JC_TimetableConfig e ON a.TermID=e.TermID and a.Weekday=e.Weekday and a.Number=e.Number and a.ClassId=e.ClassId");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            if (OrderId.Trim() != "")
            {
                strSql.Append(" ORDER BY  " + OrderId);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得教师课表课表
        /// </summary>
        public DataSet GetTeacherTimeTableList(Guid TeacherId, Guid TermId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.ColumnId,a.Number Class,a.Weekday,isnull(b.DictText,'') SubjectName,isnull(b1.DictText,'') SubjectName1,c.ClassName,c.ID as classId,c.GradeId,e.StartTime UpTime,e.EndTime DnTime,f.Name,f1.Name as Name1 ,a.SubjectCode,a.PlaceId,a.SubjectCode1,a.PlaceId1 ,a.TeacherId,a.TeacherId1,case when (select  COUNT(*) from ecb_TimeTable TT where (TT.PlaceId=a.PlaceId OR TT.PlaceId1=a.PlaceId1) and TT.Weekday=a.Weekday and TT.Number=a.Number and TT.TermID=a.TermID)>1 then '(合)' else '' end as isHeBan ");
            strSql.Append("  from ecb_TimeTable a");
            strSql.Append(" left join Site_Dictionary b on b.DictTypeId=28 and a.SubjectCode=b.DictValue and b.ColumnId in (0,a.ColumnId) ");
            strSql.Append(" left join Site_Dictionary b1 on b1.DictTypeId=28 and a.SubjectCode1=b1.DictValue and b1.ColumnId in (0,a.ColumnId)");
            strSql.Append(" left join JC_ClassInfos c on a.ClassId=c.ID ");
            strSql.Append(" left join JC_Subjects d on a.ClassId=d.ClassID and a.SubjectCode=d.SubjectCode ");
            strSql.Append(" LEFT JOIN JC_TimetableConfig e ON a.TermID=e.TermID and a.Weekday=e.Weekday and a.Number=e.Number and a.ClassId=e.ClassId");
            strSql.Append(" left join ecb_Place f on a.PlaceId=f.ID ");
            strSql.Append(" left join ecb_Place f1 on a.PlaceId1=f1.ID ");
            strSql.Append(" where a.TermID='" + TermId + "' ");
            strSql.Append(" and a.weekday<=7 and (a.TeacherId='" + TeacherId + "' or TeacherId1='" + TeacherId + "') AND StartTime IS NOT NULL ORDER BY a.Weekday, a.Number");
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得教师课表课表  最大课节数
        /// </summary>
        public int GetTeacherMaxNum(string TeacherNo, Guid TermId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select TOP 1 Number  ");
            strSql.Append(" FROM dbo.ecb_TimeTable a  LEFT JOIN JC_Subjects b ON a.ClassId=b.ClassID and a.SubjectCode=b.SubjectCode AND b.StatusCode=1 LEFT JOIN dbo.UserInfos d ON d.UserNo=b.TeacherNo");
            strSql.Append(" where b.TeacherNo='" + TeacherNo + "' AND a.TermID='" + TermId + "'  ORDER  BY Number DESC");
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 获取 任教科目
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetTeachingSubject(string where)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select DictText FROM  ");
            strSql.Append(" dbo.JC_Subjects a left join Site_Dictionary b on DictTypeId=28 AND a.SubjectCode=b.DictValue and b.ColumnId in (0,a.ColumnId)");
            strSql.Append(" WHERE StatusCode=1 and " + where);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取有课教师总数
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetTeacherCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT TeacherNo FROM dbo.ecb_TimeTable a LEFT JOIN JC_Subjects b ON a.ClassId=b.ClassID");
            strSql.Append(" and (a.SubjectCode=b.SubjectCode or a.SubjectCode1=b.SubjectCode) and StatusCode=1");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append("GROUP BY  b.TeacherNo");
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得课表
        /// </summary>
        public DataSet GetTimeTableList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.GradeId,a.SubjectCode,a.ClassId,ClassName,a.Weekday,a.Number,d.TName,e.DictText,d.UserNo as TeacherNo");
            strSql.Append(" FROM dbo.ecb_TimeTable a  LEFT JOIN dbo.JC_ClassInfos c ON a.ClassId=c.ID  LEFT JOIN dbo.UserInfos d ON d.UserID=a.TeacherId left join Site_Dictionary e on DictTypeId=28 AND a.SubjectCode=e.DictValue and e.ColumnId in (0,a.ColumnId) LEFT JOIN JC_TimetableConfig f ON a.TermID=f.TermID and a.Weekday=f.Weekday and a.Number=f.Number   ");
            if (strWhere.Trim() != "")
            {
                // where and b.TeacherNo='" + TeacherNo + "' AND a.TermID='" + TermId + "' 
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" GROUP BY a.ClassId,ClassName,a.Weekday,a.Number,a.GradeId,e.DictText ,d.UserNo,a.SubjectCode,d.TName");
            return DbHelperSQL.Query(strSql.ToString());
        }


        /// <summary>
        /// 获取有课班级总数
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetClassCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT a.ClassId,b.ClassName FROM dbo.ecb_TimeTable a LEFT JOIN dbo.JC_ClassInfos b ON a.ClassId=b.ID");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append("GROUP BY  a.ClassId,b.ClassName");
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取有课教师总数
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetStudentCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT b.StudentName,b.StudentCode,b.ClassId FROM dbo.ecb_TimeTable a  LEFT JOIN dbo.JC_StudentInfos b ON b.ClassID=a.ClassId");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append("GROUP BY b.StudentName,b.StudentCode,b.ClassId");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取有课年级总数
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetGradeCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT b.ID,b.GradeName,b.OrderId FROM dbo.ecb_TimeTable a  LEFT JOIN dbo.JC_GradeInfos b ON a.GradeId=b.ID ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" GROUP BY b.ID,b.GradeName,b.OrderId ORDER BY b.OrderId");
            return DbHelperSQL.Query(strSql.ToString());
        }

        public DataSet GetList(string fileName, string strWhere, string tabName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  " + fileName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteByWhere(string where)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_TimeTable ");
            strSql.Append(" WHERE  " + where);
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 获取学生个人课表
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataTable getStudentTimeTable(Guid studentId, int ColumnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.ColumnId,a.Number Class,a.Weekday,isnull(b.DictText,'') SubjectName,isnull(b1.DictText,'') SubjectName1,c.ClassName,c.ID as classId,e.StartTime UpTime,e.EndTime DnTime,f.Name,f1.Name as Name1 ,g.CName,a.SubjectNo,a.PlaceId,a.SubjectNo1,a.PlaceId1 ,a.TeacherId,a.TeacherId1 ,g1.CName as CName1,case when (select  COUNT(*) from ecb_TimeTable TT where (TT.PlaceId=a.PlaceId OR TT.PlaceId1=a.PlaceId1) and TT.Weekday=a.Weekday and TT.Number=a.Number and TT.TermID=a.TermID)>1 then '(合)' else '' end as isHeBan ");
            strSql.Append("  from ecb_TimeTable_stu a");
            strSql.Append(" left join Site_Dictionary b on b.DictTypeId=28 and a.SubjectNo=b.DictValue and b.ColumnId in (0,a.ColumnId) ");
            strSql.Append(" left join Site_Dictionary b1 on b1.DictTypeId=28 and a.SubjectNo1=b1.DictValue and b1.ColumnId in (0,a.ColumnId)");
            strSql.Append(" left join JC_ClassInfos c on a.ClassId=c.ID ");
            strSql.Append(" LEFT JOIN JC_TimetableConfig e ON a.TermID=e.TermID and a.Weekday=e.Weekday and a.Number=e.Number and a.ClassId=e.ClassId");
            strSql.Append(" left join ecb_Place f on a.PlaceId=f.ID ");
            strSql.Append(" left join ecb_Place f1 on a.PlaceId1=f1.ID ");
            strSql.Append(" left join aspnet_Membership g on a.TeacherId=g.UserId ");
            strSql.Append(" left join aspnet_Membership g1 on a.TeacherId1=g1.UserId ");
            strSql.Append(" where a.TermID=(select ID from JC_TermInfos where SchoolColumnId=" + ColumnId + " and IsCurrentTerm=1) ");
            strSql.Append(" and a.weekday<=7 and a.StudentId='" + studentId + "' AND StartTime IS NOT NULL ORDER BY a.Weekday, a.Number");
            DataTable dtResult = DbHelperSQL.Query(strSql.ToString()).Tables[0];
            return dtResult;
        }

        /// <summary>
        /// 获取班级课表
        /// </summary>
        /// <param name="TermID">学期编号</param>
        /// <param name="ClassId">班级ID</param>
        /// <returns></returns>
        public DataTable GetClassTimetable(int ColumnId, Guid ClassId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.ColumnId,a.Number Class,a.Weekday,isnull(b.DictText,'') SubjectName,isnull(b1.DictText,'') SubjectName1,c.ClassName,c.ID as classId,e.StartTime UpTime,e.EndTime DnTime,f.Name,f1.Name as Name1 ,g.CName,a.SubjectCode,a.PlaceId,a.SubjectCode1,a.PlaceId1 ,a.TeacherId,a.TeacherId1 ,g1.CName as CName1,case when (select  COUNT(*) from ecb_TimeTable TT where (TT.PlaceId=a.PlaceId OR TT.PlaceId1=a.PlaceId1) and TT.Weekday=a.Weekday and TT.Number=a.Number and TT.TermID=a.TermID)>1 then '(合)' else '' end as isHeBan ");
            strSql.Append("  from ecb_TimeTable a");
            strSql.Append(" left join Site_Dictionary b on b.DictTypeId=28 and a.SubjectCode=b.DictValue and b.ColumnId in (0,a.ColumnId) ");
            strSql.Append(" left join Site_Dictionary b1 on b1.DictTypeId=28 and a.SubjectCode1=b1.DictValue and b1.ColumnId in (0,a.ColumnId)");
            strSql.Append(" left join JC_ClassInfos c on a.ClassId=c.ID ");
            strSql.Append(" LEFT JOIN JC_TimetableConfig e ON a.TermID=e.TermID and a.Weekday=e.Weekday and a.Number=e.Number and a.ClassId=e.ClassId");
            strSql.Append(" left join ecb_Place f on a.PlaceId=f.ID ");
            strSql.Append(" left join ecb_Place f1 on a.PlaceId1=f1.ID ");
            strSql.Append(" left join aspnet_Membership g on a.TeacherId=g.UserId ");
            strSql.Append(" left join aspnet_Membership g1 on a.TeacherId1=g1.UserId ");
            strSql.Append(" where a.TermID=(select ID from JC_TermInfos where SchoolColumnId=" + ColumnId + " and IsCurrentTerm=1) ");
            strSql.Append(" and a.weekday<=7 and a.ClassId='" + ClassId + "' AND StartTime IS NOT NULL ORDER BY a.Weekday, a.Number");
            DataTable dtResult = DbHelperSQL.Query(strSql.ToString()).Tables[0];
            return dtResult;
        }
        /// <summary>
        /// 获取场地课表
        /// </summary>
        /// <param name="TermID">学期编号</param>
        /// <param name="ClassId">班级ID</param>
        /// <returns></returns>
        public DataTable GetPlaceTimetable(int ColumnId, Guid PlaceId)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append("select a.ColumnId,a.Number Class,a.Weekday,isnull(b.DictText,'') SubjectName,isnull(b1.DictText,'') SubjectName1,c.ClassName,c.ID as classId,e.StartTime UpTime,e.EndTime DnTime,f.Name,f1.Name as Name1 ,g.CName,a.SubjectCode,a.PlaceId,a.SubjectCode1,a.PlaceId1 ,a.TeacherId,a.TeacherId1 ,g1.CName as CName1,case when (select  COUNT(*) from ecb_TimeTable TT where (TT.PlaceId=a.PlaceId OR TT.PlaceId1=a.PlaceId1) and TT.Weekday=a.Weekday and TT.Number=a.Number and TT.TermID=a.TermID)>1 then '(合)' else '' end as isHeBan ");
            strSql.Append("  from ecb_TimeTable a");
            strSql.Append(" left join Site_Dictionary b on b.DictTypeId=28 and a.SubjectCode=b.DictValue and b.ColumnId in (0,a.ColumnId)");
            strSql.Append(" left join Site_Dictionary b1 on b1.DictTypeId=28 and a.SubjectCode1=b1.DictValue and b1.ColumnId in (0,a.ColumnId)");
            strSql.Append(" left join JC_ClassInfos c on a.ClassId=c.ID ");
            strSql.Append(" LEFT JOIN JC_TimetableConfig e ON a.TermID=e.TermID and a.Weekday=e.Weekday and a.Number=e.Number and a.ClassId=e.ClassId");
            strSql.Append(" left join ecb_Place f on a.PlaceId=f.ID ");
            strSql.Append(" left join ecb_Place f1 on a.PlaceId1=f1.ID ");
            strSql.Append(" left join aspnet_Membership g on a.TeacherId=g.UserId ");
            strSql.Append(" left join aspnet_Membership g1 on a.TeacherId1=g1.UserId ");
            strSql.Append(" where a.TermID=(select ID from JC_TermInfos where SchoolColumnId=" + ColumnId + " and IsCurrentTerm=1) ");
            strSql.Append(" and a.weekday<=7 and ( a.PlaceId='" + PlaceId + "' or a.PlaceId1='" + PlaceId + "') AND StartTime IS NOT NULL ORDER BY a.Weekday, a.Number");
            DataTable dtResult = DbHelperSQL.Query(strSql.ToString()).Tables[0];
            return dtResult;
        }
        /// <summary>
        /// 更改班级绑定场地 修改课表 班级课表
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public bool UpdatePlaceByClassId(Guid classId,Guid oldPlaceId, Guid PlaceId, Guid TermID)
        {
            ArrayList list= new ArrayList();
			//更新班牌绑定表 根据班级id修改
			list.Add($" update ecb_ClassBrands set   PlaceId= '{PlaceId}' where  ClassId ='{classId}'");
			//更新班牌绑定表 根据班级id修改
			list.Add($" update JC_ClassInfos set   PlaceId= '{PlaceId }' where  Id ='{classId}'");

			//修改课表时间配置 根据班级id修改
			list.Add($" update JC_TimetableConfig set PlaceId='{ PlaceId}'  where ClassId = '{classId}' and TermID='{TermID}'");

			//修改课表 根据班级id修改
			list.Add($" UPDATE dbo.ecb_TimeTable SET PlaceId='{PlaceId}'  where ClassId = '{classId}' and TermID='{TermID}'");

			list.Add($" UPDATE dbo.ecb_TimeTable_stu SET PlaceId='{PlaceId}'  where ClassId = '{classId}' and TermID='{TermID}'");

			list.Add($" UPDATE dbo.ecb_TimeTable SET PlaceId1='{PlaceId}'  where ClassId = '{classId}' and TermID='{TermID}' and PlaceId1='{oldPlaceId}'");

			list.Add($" UPDATE dbo.ecb_TimeTable_stu SET PlaceId1='{PlaceId}'  where ClassId = '{classId}' and TermID='{TermID}' and PlaceId1='{oldPlaceId}'");

            int rows = DbHelperSQL.ExecuteSqlTran(list);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 获取多个班级相同的课程信息
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetCourseByClasses(string classIds, bool isSingle, int weekDay, Guid TermId, int ClassCount,string date)
        {
            string Field = isSingle ? "" : "1";
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select a.Number, CONVERT(nvarchar(5),b.StartTime,8) StartTime, CONVERT(nvarchar(5),b.EndTime,8) EndTime,SubjectCode" + Field + " SubjectCode,TeacherId" + Field + " TeacherId,isnull(c.DictText,'') SubjectName,d.CName");
            strSql.Append(" from ecb_TimeTable a left join JC_TimetableConfig b on a.ClassId = b.ClassId and a.Weekday = b.Weekday and a.Number = b.Number");
            strSql.Append(" left join Site_Dictionary c on a.SubjectCode" + Field + " = c.DictValue and DictTypeId = 28 left join aspnet_Membership d on a.TeacherId" + Field + " = d.UserId");
            strSql.Append(" where a.Weekday=" + weekDay + " and a.TermID ='" + TermId + "' and TeacherId!= '00000000-0000-0000-0000-000000000000' and a.ClassId in (" + classIds + ") and b.StartTime is not null and cast('"+date+ " '+CONVERT(nvarchar(5),b.StartTime, 8) as datetime)>GETDATE()");
            strSql.Append(" group by a.Number,CONVERT(nvarchar(5),b.StartTime, 8),CONVERT(nvarchar(5),b.EndTime, 8),SubjectCode" + Field + ",TeacherId" + Field + ",c.DictText,d.CName having COUNT(*) >=" + ClassCount);
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取课表
        /// </summary>
        /// <param name="ColumnId">学校id</param>
        /// <param name="GradeId">年级ID</param>
        /// <param name="ClassId">班级ID</param>
        /// <returns></returns>
        public DataTable GetTimetable(int ColumnId, Guid GradeId, Guid ClassId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select a.ColumnId,a.Number Class,a.Weekday,isnull(b.DictText,'') SubjectName,isnull(b1.DictText,'') SubjectName1,c.ClassName,c.ID as classId,e.StartTime UpTime,e.EndTime DnTime,f.Name,f1.Name as Name1 ,g.CName,a.SubjectCode,a.PlaceId,a.SubjectCode1,a.PlaceId1 ,a.TeacherId,a.TeacherId1 ,g1.CName as CName1 ");
            strSql.Append("  from ecb_TimeTable a");
            strSql.Append(" left join Site_Dictionary b on b.DictTypeId=28 and a.SubjectCode=b.DictValue and b.ColumnId in (0,a.ColumnId) ");
            strSql.Append(" left join Site_Dictionary b1 on b1.DictTypeId=28 and a.SubjectCode1=b1.DictValue and b1.ColumnId in (0,a.ColumnId)");
            strSql.Append(" left join JC_ClassInfos c on a.ClassId=c.ID ");
            strSql.Append(" LEFT JOIN JC_TimetableConfig e ON a.TermID=e.TermID and a.Weekday=e.Weekday and a.Number=e.Number and a.ClassId=e.ClassId");
            strSql.Append(" left join ecb_Place f on a.PlaceId=f.ID ");
            strSql.Append(" left join ecb_Place f1 on a.PlaceId1=f1.ID ");
            strSql.Append(" left join aspnet_Membership g on a.TeacherId=g.UserId ");
            strSql.Append(" left join aspnet_Membership g1 on a.TeacherId1=g1.UserId ");
            strSql.Append(" where a.TermID=(select ID from JC_TermInfos where SchoolColumnId=" + ColumnId + " and IsCurrentTerm=1) and a.weekday<=7 AND StartTime IS NOT NULL");
            //年级课表
            if (GradeId != Guid.Empty)
            {
                strSql.Append(" and a.GradeId='" + GradeId + "'  ");
            }
            //班级课表
            if (ClassId != Guid.Empty)
            {
                strSql.Append(" and a.ClassId='" + ClassId + "'  ");
            }
            strSql.Append(" ORDER BY a.Weekday, a.Number");
            DataTable dtResult = DbHelperSQL.Query(strSql.ToString()).Tables[0];
            return dtResult;
        }
    }
}

