﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:xy_Feedback
	/// </summary>
	public partial class xy_Feedback
	{
		public xy_Feedback()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from xy_Feedback");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.xy_Feedback model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into xy_Feedback(");
			strSql.Append("Id,deviceInfo,content,contact,CreatTime,Score,CreatUser)");
			strSql.Append(" values (");
			strSql.Append("@Id,@deviceInfo,@content,@contact,@CreatTime,@Score,@CreatUser)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@deviceInfo", SqlDbType.NVarChar,-1),
					new SqlParameter("@content", SqlDbType.NVarChar,-1),
					new SqlParameter("@contact", SqlDbType.NVarChar,50),
					new SqlParameter("@CreatTime", SqlDbType.DateTime),
					new SqlParameter("@Score", SqlDbType.Int,4),
					new SqlParameter("@CreatUser", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.deviceInfo;
			parameters[2].Value = model.content;
			parameters[3].Value = model.contact;
			parameters[4].Value = model.CreatTime;
			parameters[5].Value = model.Score;
			parameters[6].Value =model.CreatUser;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.xy_Feedback model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update xy_Feedback set ");
			strSql.Append("deviceInfo=@deviceInfo,");
			strSql.Append("content=@content,");
			strSql.Append("contact=@contact,");
			strSql.Append("CreatTime=@CreatTime,");
			strSql.Append("Score=@Score,");
			strSql.Append("CreatUser=@CreatUser");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@deviceInfo", SqlDbType.NVarChar,-1),
					new SqlParameter("@content", SqlDbType.NVarChar,-1),
					new SqlParameter("@contact", SqlDbType.NVarChar,50),
					new SqlParameter("@CreatTime", SqlDbType.DateTime),
					new SqlParameter("@Score", SqlDbType.Int,4),
					new SqlParameter("@CreatUser", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.deviceInfo;
			parameters[1].Value = model.content;
			parameters[2].Value = model.contact;
			parameters[3].Value = model.CreatTime;
			parameters[4].Value = model.Score;
			parameters[5].Value = model.CreatUser;
			parameters[6].Value = model.Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from xy_Feedback ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from xy_Feedback ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.xy_Feedback GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,deviceInfo,content,contact,CreatTime,Score,CreatUser from xy_Feedback ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.xy_Feedback model=new ECB.PC.Model.xy_Feedback();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.xy_Feedback DataRowToModel(DataRow row)
		{
			ECB.PC.Model.xy_Feedback model=new ECB.PC.Model.xy_Feedback();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["deviceInfo"]!=null)
				{
					model.deviceInfo=row["deviceInfo"].ToString();
				}
				if(row["content"]!=null)
				{
					model.content=row["content"].ToString();
				}
				if(row["contact"]!=null)
				{
					model.contact=row["contact"].ToString();
				}
				if(row["CreatTime"]!=null && row["CreatTime"].ToString()!="")
				{
					model.CreatTime=DateTime.Parse(row["CreatTime"].ToString());
				}
				if(row["Score"]!=null && row["Score"].ToString()!="")
				{
					model.Score=int.Parse(row["Score"].ToString());
				}
				if(row["CreatUser"]!=null && row["CreatUser"].ToString()!="")
				{
					model.CreatUser= new Guid(row["CreatUser"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,deviceInfo,content,contact,CreatTime,Score,CreatUser ");
			strSql.Append(" FROM xy_Feedback ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,deviceInfo,content,contact,CreatTime,Score,CreatUser ");
			strSql.Append(" FROM xy_Feedback ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM xy_Feedback ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from xy_Feedback T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "xy_Feedback";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Model.xy_Feedback model)
		{
			ArrayList array = new ArrayList();
			array.Add("delete from xy_Feedback where Id='" + model.Id.ToString() + "'");
			//附件信息
			array.Add("delete from xy_Attachments where DailyId='" + model.Id.ToString() + "'");
			int rows = DbHelperSQL.ExecuteSqlTran(array);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		#endregion  ExtensionMethod
	}
}

