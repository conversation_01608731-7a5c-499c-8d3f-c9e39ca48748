﻿using NPOI.HSSF.UserModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.Common.DEncrypt;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_testteachers_list : YunEdu.Authority.AdminCommonJC
    {
        string id;
        string strWhere;
        int maxd;
        YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();

        YunEdu.BLL.PKC_Rooms bllrooms = new YunEdu.BLL.PKC_Rooms();
        YunEdu.Model.PKC_Rooms modelrooms = new YunEdu.Model.PKC_Rooms();

        YunEdu.BLL.PKC_TestTeachers blltestteachers = new YunEdu.BLL.PKC_TestTeachers();
        YunEdu.Model.PKC_TestTeachers modeltestteachers = new YunEdu.Model.PKC_TestTeachers();

        YunEdu.BLL.UserInfos blluserinfos = new YunEdu.BLL.UserInfos();
        YunEdu.Model.UserInfos modeluserinfos = new YunEdu.Model.UserInfos();

        YunEdu.BLL.PKC_TeacherSubjects bllteachersubjects = new YunEdu.BLL.PKC_TeacherSubjects();
        YunEdu.Model.PKC_TeacherSubjects modelteachersubjects = new YunEdu.Model.PKC_TeacherSubjects();

        YunEdu.BLL.PKC_Subjects bllsubjects = new YunEdu.BLL.PKC_Subjects();
        YunEdu.Model.PKC_Subjects modelsubjects = new YunEdu.Model.PKC_Subjects();

        BLL.Site_Dictionary bllDictionary = new BLL.Site_Dictionary();


        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();

        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();


        protected void Page_Load(object sender, EventArgs e)
        {
            //GetArea();
            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
            }
            if (!IsPostBack)
            {
                InitControls();
                BindData();
                MessageBox.ResponseScript(this, "layer.msg('删除和添加教师必须在设置监考人数之前，当为每个老师分配了监考场次以后，删除和添加教师无法操作!');");
            }
        }

        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }
        /// <summary>
        /// 获取查询条件
        /// </summary>
        /// <returns></returns>
        private string GetWhere()
        {
            StringBuilder sbWhere = new StringBuilder();

            string strColumnId = string.Empty;
            string strColumnPath = string.Empty;
            //属于本次考试
            if (Request.QueryString["id"] != null)
            {
                sbWhere.Append("TestId='" + Request.QueryString["id"].ToString() + "'");
                // 第一次加载
                //if (!IsPostBack)
                //{
                //    strColumnId = modelAreaUser.ColumnID.ToString();
                //    strColumnPath = modelAreaUser.ColumnPath;
                //}
                //else
                //{
                // 是否选择附属学校
                //if (!string.IsNullOrEmpty(hidChildSchool.Value))
                //{
                //    modelArea = bllArea.GetModel(int.Parse(hidChildSchool.Value));
                //}
                //else if (!string.IsNullOrEmpty(hidSchoolId.Value))
                //{
                //    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                //}
                //if (modelArea != null && modelArea.ColumnID != 0)
                //{
                //    strColumnId = modelArea.ColumnID.ToString();
                //    strColumnPath = modelArea.ColumnPath;
                //    sbWhere.Append(" and (ColumnId=" + strColumnId + " or ColumnPath like '" + strColumnPath + "|%')");
                //}
                //}

                //if (!string.IsNullOrEmpty(hidTermId.Value))
                //{
                //    if (sbWhere.Length > 0)
                //    {
                //        sbWhere.Append(" and ");
                //    }
                //    sbWhere.Append("b.TermId='" + hidTermId.Value + "'");
                //}
                //if (!string.IsNullOrEmpty(hidGradeId.Value))
                //{
                //    if (sbWhere.Length > 0)
                //    {
                //        sbWhere.Append(" and ");
                //    }
                //    sbWhere.Append(" b.GradeId='" + hidGradeId.Value + "'");
                //}
                //if (!string.IsNullOrEmpty(hidClassId.Value))
                //{
                //    if (sbWhere.Length > 0)
                //    {
                //        sbWhere.Append(" and ");
                //    }
                //    sbWhere.Append(" b.ClassId='" + hidClassId.Value + "'");
                //}
                //if (!string.IsNullOrEmpty(this.ddlRoom.SelectedValue) && !this.ddlRoom.SelectedValue.ToString().Equals("0"))
                //{
                //    if (sbWhere.Length > 0)
                //    {
                //        sbWhere.Append(" and ");
                //    }
                //    sbWhere.Append(" RoomId='" + this.ddlRoom.SelectedValue.ToString() + "'");
                //}
                if (!string.IsNullOrEmpty(this.dorpShowSize.SelectedValue) && !this.dorpShowSize.SelectedValue.ToString().Equals("0"))
                {
                    AspNetPager1.PageSize = int.Parse(this.dorpShowSize.SelectedValue);
                }
                if (!string.IsNullOrEmpty(this.dropPosition.SelectedValue) && !this.dropPosition.SelectedValue.ToString().Equals("0"))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    sbWhere.Append(" Position='" + this.dropPosition.SelectedValue.ToString() + "'");
                }
                if (!string.IsNullOrEmpty(this.dropType.SelectedValue) && !this.dropType.SelectedValue.ToString().Equals("0"))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    string _text = YunEdu.Common.StringPlus.Filter(this.inputName.Text, "html");
                    switch (this.dropType.SelectedValue)
                    {
                        case "1":
                            sbWhere.Append(" TName like '%" + this.inputName.Text + "%'");
                            break;
                        case "2":
                            sbWhere.Append(" UserNo like '%" + this.inputName.Text + "%'");
                            break;
                        default:
                            break;
                    }
                }
            }
            else
            {
                sbWhere.Append("1<>1");
            }
            return sbWhere.ToString();
        }
        /// <summary>
        /// 获取数据并绑定
        /// </summary>
        private void BindData()
        {
            this.txtPageNum.Text = this.AspNetPager1.PageSize.ToString();
            strWhere = GetWhere();

            DataTable dt = new DataTable();
            AspNetPager1.RecordCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers a left join UserInfos b on a.UserId=b.UserID", strWhere);

            //int amount = 0;
            //int capacityamount = 0;
            //capacityamount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "'and IsUsing= 1");
            //modeltests = blltests.GetModel(new Guid(id));
            //amount = capacityamount * (int)modeltests.InvigilatorNum;

            //AspNetPager1.CustomInfoHTML = "共有<font color=red>" + AspNetPager1.RecordCount + "</font>条记录,需要监考老师<font color=red>" + amount + "</font>人，其中主监考<font color=red>" + capacityamount + "</font>人。当前第<font color=red>" + AspNetPager1.CurrentPageIndex + "/" + AspNetPager1.PageCount + "</font>页,每页<font color=red>" + AspNetPager1.PageSize + "</font>条记录";
            modeltests = blltests.GetModel(new Guid(id));
            dt.Columns.Add("TestTeacherId");
            dt.Columns.Add("TName");
            dt.Columns.Add("UserNo");
            dt.Columns.Add("TeachingSubject");
            dt.Columns.Add("Position");
            dt.Columns.Add("TestTitle");
            dt.Columns.Add("JianKaoCount");
            dt.Columns.Add("RoomLocation");
            dt.Columns.Add("IsEnabled");
            foreach (DataRow itemTestUsers in YunEdu.Common.GetRecordByPageOrder.GetList("PKC_TestTeachers a left join UserInfos b on a.UserId=b.UserID", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "a.*,b.UserNo,b.TName,b.TeachingSubject", strWhere, "Position desc,CreateTime asc ").Tables[0].Rows)
            {
                DataRow dr = dt.NewRow();
                dr["TestTeacherId"] = itemTestUsers["TestTeacherId"].ToString();
                dr["JianKaoCount"] = itemTestUsers["JianKaoCount"].ToString();
                dr["Position"] = itemTestUsers["Position"].ToString();
                if (itemTestUsers["IsEnabled"].ToString().Equals("False"))
                {
                    dr["IsEnabled"] = "否";
                }
                else
                {
                    dr["IsEnabled"] = "是";

                }
                dr["Position"] = YunEdu.Common.GetRecordByPageOrder.GetModelField("Site_Dictionary", "DictText", "DictTypeId=78 and DictValue='" + itemTestUsers["Position"].ToString() + "'");
                //switch (itemTestUsers["Position"].ToString())
                //{
                //    case "1":
                //        dr["Position"] = "监考员";
                //        break;
                //    case "2":
                //        dr["Position"] = "巡考员";
                //        break;
                //    //default:
                //    //    dr["Position"] = "";
                //    //    break;
                //}
                //modeluserinfos = blluserinfos.GetModel(new Guid(itemTestUsers["UserId"].ToString()));
                //foreach (DataRow itemUsers in YunEdu.Common.GetRecordByPageOrder.GetModel("UserInfos", "*", "UserId='" + itemTestUsers["UserId"].ToString() + "'").Tables[0].Rows)
                //{
                //    dr["TName"] = itemUsers["TName"].ToString();
                //    dr["UserNo"] = itemUsers["UserNo"].ToString();
                //    dr["TeachingSubject"] = itemUsers["TeachingSubject"].ToString();
                //}
                dr["TName"] = itemTestUsers["TName"].ToString();
                dr["UserNo"] = itemTestUsers["UserNo"].ToString();
                dr["TeachingSubject"] = itemTestUsers["TeachingSubject"].ToString();
                //modelrooms = bllrooms.GetModel(new Guid(itemTestUsers["RoomId"].ToString()));
                //if (modelrooms != null)
                //{
                //    dr["RoomNum"] = modelrooms.RoomNum;
                //    dr["RoomLocation"] = modelrooms.RoomLocation;
                //}
                dr["TestTitle"] = modeltests.TestTitle;
                dt.Rows.Add(dr);
            }
            gvTestTeachersList.DataSource = dt;
            gvTestTeachersList.DataBind();
            for (int i = 0; i <= gvTestTeachersList.Rows.Count - 1; i++)
            {
                DataRow mydr = dt.Rows[i];
                string position = Convert.ToString(mydr["Position"]);
                if (position.Equals("巡考员"))
                {
                    gvTestTeachersList.Rows[i].Cells[4].ForeColor = System.Drawing.Color.Red;
                }

            }



            int a = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");
            int b = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "' and IsUsing=1");
            if (a != 0 && b != 0)
            {
                int c = a / b;
                maxd = c;
                lblShowMessage.Text = @"本次考试使用考场" + b + "个，现有监考员" + a + "名，每个考场最多可安排" + c + "人监考";
            }
            //int d = 0;
            //if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") > 0)
            //{
            //    foreach (DataRow row in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "JianKaoCount", "TestId='" + id + "' and Position=1", "").Tables[0].Rows)
            //    {
            //        d += (int)row["JianKaoCount"];
            //    }
            //}
            //Label1.Text = "" + d;
        }

        private void InitControls()
        {
            //YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Rooms", "RoomId,RoomNum", "RoomNum", "RoomId", "TestId='" + id + "'and IsUsing= 1", "RoomNum asc", ddlRoom, "0", true, "请选择");
            DataTable data = null;
            data = bllDictionary.GetList("DictTypeId=78").Tables[0];
            GetRecordByPageOrder.BindDropDownList(ddlPosition, data, 78);
        }
        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            BindData();
        }
        //设置分页显示数量
        protected void txtPageNum_TextChanged(object sender, EventArgs e)
        {
            int _pagesize;
            if (int.TryParse(txtPageNum.Text.Trim(), out _pagesize))
            {
                this.AspNetPager1.PageSize = _pagesize;
                this.AspNetPager1.CurrentPageIndex = 1;
                BindData();
            }
        }
        protected void btnBack_Click(object sender, EventArgs e)
        {
            Response.Redirect("pkc_tests_list.aspx?MenuId=" + Request.QueryString["MenuId"]);
        }
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            BindData();
        }
        protected void btnDeleteAll_Click(object sender, EventArgs e)
        {

            if (blltestteachers.DeleteAll(new Guid(id)) || bllteachersubjects.DeleteAll(new Guid(id)))
            {
                MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('删除失败!');");
            }
            BindData();
        }
        protected void btnDelete_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，无法删除!');");
                return;
            }
            foreach (GridViewRow myItem in gvTestTeachersList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        iCount++;
                        if (!blltestteachers.Delete(new Guid(gvTestTeachersList.DataKeys[myItem.RowIndex][0].ToString())))
                        {
                            iError++;
                        }
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要删除的记录!');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('删除失败!');");
            }
            BindData();
        }

        protected void btnAddTeacher_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，无法添加!');");
                return;
            }
            string userid = YunEdu.Common.GetRecordByPageOrder.GetModelField("UserInfos", "UserID", "UserNo='" + this.hfldTeacher.Value + "'");
            int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and " + "UserId='" + userid + "'");
            if (count == 0)
            {
                modeltestteachers = new YunEdu.Model.PKC_TestTeachers();
                if (this.ddlPosition.SelectedValue.Trim() == string.Empty)
                {
                    this.ddlPosition.SelectedValue = "1";
                }
                string _Position = this.ddlPosition.SelectedValue;
                //string _roomid = ddlRoomNum.SelectedValue.ToString();
                //if (!string.IsNullOrEmpty(_roomid))
                //{
                //    modeltestteachers.RoomId = new Guid(_roomid);
                //}
                modeltestteachers.Position = int.Parse(_Position);
                modeltestteachers.CreateTime = DateTime.Now;
                modeltestteachers.ColumnId = modelAreaUser.ColumnID;
                modeltestteachers.ColumnPath = modelAreaUser.ColumnPath;
                //modeltestteachers.JianKaoCount = int.Parse(txtJianKaoCount.Text);
                modeltestteachers.TestId = new Guid(id);
                modeltestteachers.UserId = new Guid(userid);

                modeltests = blltests.GetModel(new Guid(id));
                modeltestteachers.SchoolYear = modeltests.SchoolYear;
                if (blltestteachers.Add(modeltestteachers))
                {
                    MessageBox.ResponseScript(this, "layer.msg('添加成功!');");
                    BindData();
                }
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('教师已存在，不能重复添加!');");
                return;
            }
        }

        #region 导入监考教师
        /// <summary>
        /// 导入本次考试考生信息  查询条件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private string GetWhereTeachers()
        {
            StringBuilder sbWhere = new StringBuilder();

            modeltests = blltests.GetModel(new Guid(id));
            string columnid = modeltests.ColumnId.ToString();
            string columnpath = modeltests.ColumnPath;
            string termid = modeltests.TermId.ToString();
            string gradeid = modeltests.GradeId.ToString();
            string classid = modeltests.ClassId.ToString();

            //string strColumnId = string.Empty;
            //string strColumnPath = string.Empty;

            //// 是否选择附属学校
            //if (!string.IsNullOrEmpty(hidChildSchool.Value))
            //{
            //    modelArea = bllArea.GetModel(int.Parse(hidChildSchool.Value));
            //}
            //else if (!string.IsNullOrEmpty(hidSchoolId.Value))
            //{
            //    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
            //}
            //if (modelArea != null && modelArea.ColumnID != 0)
            //{
            //    strColumnId = modelArea.ColumnID.ToString();
            //    strColumnPath = modelArea.ColumnPath;
            //}

            sbWhere.Append("(ColumnId=" + columnid + " or ColumnPath like '" + columnpath + "|%')");
            if (!termid.Equals("00000000-0000-0000-0000-000000000000"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append("TermId='" + termid + "'");
            }
            if (!gradeid.Equals("00000000-0000-0000-0000-000000000000"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append("GradeId='" + gradeid + "'");
            }
            if (!classid.Equals("00000000-0000-0000-0000-000000000000"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append("ClassId='" + classid + "'");
            }

            //学校版，省市县区版需要更改
            if (string.IsNullOrEmpty(sbWhere.ToString()))
            {
                sbWhere.Append("1<>1");
            }
            return sbWhere.ToString();
        }

        /// <summary>
        /// 导入本次教师信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnImport_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，不能再导入本年级教师!');");
                return;
            }
            string strWhereTeachers = GetWhereTeachers();
            foreach (DataRow itemSubjects in YunEdu.Common.GetRecordByPageOrder.GetListByVW("JC_Subjects", 0, "TeacherNo", strWhereTeachers, "CreateDate desc").Tables[0].Rows)
            {
                try
                {
                    string userid = YunEdu.Common.GetRecordByPageOrder.GetModelField("UserInfos", "UserID", "UserNo='" + itemSubjects["TeacherNo"].ToString() + "'");
                    //modelstudentinfos = bllstudentinfos.GetModel(new Guid(item["ID"].ToString()));
                    //if (modelstudentinfos != null)
                    //{
                    int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and " + "UserId='" + userid + "'");
                    if (count == 0)
                    {
                        modeltestteachers = new YunEdu.Model.PKC_TestTeachers();
                        modeltestteachers.TestId = new Guid(id);
                        modeltestteachers.UserId = new Guid(userid);
                        modeltestteachers.CreateTime = DateTime.Now;
                        modeltestteachers.IsEnabled = true;
                        modeltestteachers.Position = 1;
                        modeltestteachers.ColumnId = modelAreaUser.ColumnID;
                        modeltestteachers.ColumnPath = modelAreaUser.ColumnPath;
                        modeltests = blltests.GetModel(new Guid(id));
                        modeltestteachers.SchoolYear = modeltests.SchoolYear;
                        blltestteachers.Add(modeltestteachers);
                    }
                    //}
                }
                catch (Exception)
                {
                    continue;
                }
            }


            BindData();
            MessageBox.ResponseScript(this, "layer.msg('导入完老师后，请确保不会再添加和删除，修改老师，并且巡考人员已设定好，然后再设置每个考场的监考人数!');");

        }

        #endregion

        /// <summary>
        /// 批量设为巡考员
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnPosition_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，无法修改!');");
                return;
            }
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvTestTeachersList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        iCount++;
                        modeltestteachers = blltestteachers.GetModel(new Guid(gvTestTeachersList.DataKeys[myItem.RowIndex][0].ToString()));
                        modeltestteachers.Position = 2;
                        if (!blltestteachers.Update(modeltestteachers))
                        {
                            iError++;
                        }
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要设置的记录!');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('设置成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('设置失败!');");
            }
            BindData();
        }
        /// <summary>
        /// 批量设置为监考员
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnInvigilator_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，无法修改!');");
                return;
            }
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvTestTeachersList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        iCount++;
                        modeltestteachers = blltestteachers.GetModel(new Guid(gvTestTeachersList.DataKeys[myItem.RowIndex][0].ToString()));
                        modeltestteachers.Position = 1;
                        if (!blltestteachers.Update(modeltestteachers))
                        {
                            iError++;
                        }
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要设置的记录!');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('设置成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('设置失败!');");
            }
            BindData();
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {

            int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 1);
            int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 2);
            int VCount;//每个考场的监考人数


            if (txtVNumber.Text.Trim() != null && txtVNumber.Text.Trim() != "")
            {
                if (int.TryParse(txtVNumber.Text.ToString(), out VCount))
                {
                    if (VCount > 5)
                    {
                        MessageBox.ResponseScript(this, "layer.msg('每个考场安排超过5人，人数是否过多？请重新输入!');");
                        return;
                    }
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('请确保输入的为数字!');");
                    return;
                }
            }
            else
            {
                int a = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");
                int b = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "' and IsUsing=1");
                int c = a / b;
                MessageBox.ResponseScript(this, "layer.msg('输入框不能为空,请输入不大于" + c + "的数字！!');");
                return;
            }

            //获取已排考场数
            int roomCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "' and IsUsing=1");
            if (roomCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('未排考场，请先进入“排考场及数据导出”完成排考场再排监考!');");
                return;//没有排考场就退出
            }
            //找出监考老师表里面的监考老师
            int teacherCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");

            int a1 = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");
            int b1 = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + id + "' and IsUsing=1");
            if (a1 != 0 && b1 != 0)
            {
                int c1 = a1 / b1;
                maxd = c1;
            }
            //if (teacherCount < VCount * roomCount)
            //{
            if (maxd < VCount)
            {
                //监考人数少于所需人数
                int needCount = VCount * roomCount - (int)teacherCount;
                MessageBox.ResponseScript(this, "layer.msg('输入的数据大于每个考场最多可安排监考人数，若需要设置每个考场" + VCount + "人监考,则至少还需添加监考员" + needCount + "名!');");
                //lblShowMessage.Text = "此操作需要" + VCount * roomCount + "名监考员, 现有监考员" + teacherCount + "名,至少还需添加监考员" + needCount + "名";
            }
            else
            {
                lblShowMessage.Text = "至少需要监考员" + VCount * roomCount + "名, 现有监考员" + teacherCount + "名";
                modeltests = blltests.GetModel(new Guid(id));

                #region   插入教师监考数据
                if (Art != 0 && Math != 0)//分文理考试,说明考生性质有1也有2
                {
                    //先判断是否可以分配
                    //监考总数,以文理考的数目相同而论
                    int sum = roomCount * YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=1)") * VCount;
                    int t = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");
                    int a = sum / t;//平均分配几场
                    int b = sum % t;
                    int c = sum / YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=1)") + 1;
                    int d = c - t;
                    if (b == 0)
                    {
                        if (a > YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=1)"))
                        {
                            MessageBox.ResponseScript(this, "layer.msg('监考老师太少，导致每个老师的监考场数大于最大监考场数，请至少再导入" + d + "名监考老师!');");
                            return;
                        }
                    }
                    else
                    {
                        if (a + 1 > YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=1)"))
                        {
                            MessageBox.ResponseScript(this, "layer.msg('监考老师太少，导致每个老师的监考场数大于最大监考场数，请至少再导入" + d + "名监考老师!');");
                            return;
                        }
                    }
                    //如果监考表有数据直接删除再添加
                    if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "'") != 0)
                    {
                        bllteachersubjects.DeleteAll(new Guid(id));
                    }
                    for (int ArtOrMath = 1; ArtOrMath <= 2; ArtOrMath++)
                    {
                        foreach (DataRow dsSubject in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "ID", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=" + ArtOrMath + ")", "").Tables[0].Rows)
                        {
                            foreach (DataRow dsRoom in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, " distinct RoomId", "TestId='" + id + "' and PropertyCode=" + ArtOrMath, "").Tables[0].Rows)
                            {
                                for (int i = 1; i <= VCount; i++)
                                {
                                    modelteachersubjects = new YunEdu.Model.PKC_TeacherSubjects();
                                    modelteachersubjects.SubjectId = new Guid(dsSubject["ID"].ToString());
                                    modelteachersubjects.RoomId = new Guid(dsRoom["RoomId"].ToString());
                                    modelteachersubjects.TestId = new Guid(id);
                                    modelteachersubjects.CreateTime = DateTime.Now;
                                    modelteachersubjects.SameNum = i;
                                    modelteachersubjects.SchoolYear = modeltests.SchoolYear;

                                    bllteachersubjects.Add(modelteachersubjects);
                                }
                            }
                        }
                    }
                    //插入监考人数
                    modeltests.InvigilatorNum = VCount;
                    blltests.Update(modeltests);
                }
                else//不分文理
                {
                    //如果监考表有数据直接删除再添加
                    if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "'") != 0)
                    {
                        bllteachersubjects.DeleteAll(new Guid(id));
                    }

                    foreach (DataRow dsSubject in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "ID", "TestId='" + id + "' ", "").Tables[0].Rows)
                    {
                        foreach (DataRow dsRoom in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "distinct RoomId", "TestId='" + id + "' and IsUsing=1", "").Tables[0].Rows)
                        {

                            for (int i = 1; i <= VCount; i++)
                            {
                                modelteachersubjects = new YunEdu.Model.PKC_TeacherSubjects();
                                modelteachersubjects.SubjectId = new Guid(dsSubject["ID"].ToString());
                                modelteachersubjects.RoomId = new Guid(dsRoom["RoomId"].ToString());
                                modelteachersubjects.TestId = new Guid(id);
                                modelteachersubjects.CreateTime = DateTime.Now;
                                modelteachersubjects.SameNum = i;
                                modelteachersubjects.SchoolYear = modeltests.SchoolYear;

                                bllteachersubjects.Add(modelteachersubjects);
                            }
                        }
                    }
                    //插入监考人数
                    modeltests.InvigilatorNum = VCount;
                    blltests.Update(modeltests);
                }
                #endregion

                #region 插入每个老师的监考场次
                //计算已经安排的场次
                int TSCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "'");
                int teacCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");
                if (teacCount == 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('未设置监考老师，请设置!');");
                    return;
                }
                int JCount = TSCount / teacCount;
                int remianCount = TSCount % teacCount;
                int cCount = 0;//为平均分配监考场次计数

                //为空的和监考场数不是特殊的老师平均分配监考场数
                foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "TestTeacherId", "TestId='" + id + "' and Position=1", "newid()").Tables[0].Rows)
                {
                    if (cCount < remianCount)
                    {
                        modeltestteachers = blltestteachers.GetModel((Guid)item["TestTeacherId"]);
                        modeltestteachers.JianKaoCount = JCount + 1;
                        blltestteachers.Update(modeltestteachers);
                    }
                    else
                    {
                        modeltestteachers = blltestteachers.GetModel((Guid)item["TestTeacherId"]);
                        modeltestteachers.JianKaoCount = JCount;
                        blltestteachers.Update(modeltestteachers);
                    }
                    cCount++;
                }

                #endregion

                BindData();
                MessageBox.ResponseScript(this, "layer.msg('设置成功！请进入第七步：监考安排及数据导出!');");
            }

        }

        protected void btnJianKaoCount_Click(object sender, EventArgs e)
        {
            int VCount;
            if (txtCount.Text.Trim() != null && txtCount.Text.Trim() != "")
            {
                if (int.TryParse(txtCount.Text.ToString(), out VCount))
                {
                    int subjectCount1 = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=1)");
                    int subjectCount2 = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=1)");
                    if (subjectCount1 != 0)
                    {
                        if (VCount > subjectCount1 || VCount > subjectCount2)
                        {
                            MessageBox.ResponseScript(this, "layer.msg('输入监考数大于总科目数，请输入小于总科目数的数字!');");
                            return;
                        }
                    }
                    else
                    {
                        MessageBox.ResponseScript(this, "layer.msg('请到科目管理先添加科目!');");
                        return;
                    }

                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('请确保输入的为数字!');");
                    return;
                }
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('输入框不能为空,请输入小于总科目数的数字!');");
                return;
            }

            modeltests = blltests.GetModel(new Guid(id));
            if (modeltests.InvigilatorNum == null)
            {
                MessageBox.ResponseScript(this, "layer.msg('请先设置每个考场要安排的监考人数!');");
                return;
            }
            int teacCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");
            if (teacCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('未导入老师，请导入老师!');");
                return;
            }
            int iError = 0;
            int iCount = 0;
            int a = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1");
            string[,] s = new string[a, 2];
            string strTeacher = string.Empty;
            int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 1);
            int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and PropertyCode=" + 2);
            int TSCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + id + "'");
            int n = TSCount / teacCount;
            if (n >= int.Parse(txtCount.Text.ToString()))
            {
                foreach (GridViewRow myItem in gvTestTeachersList.Rows)
                {
                    CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                    if (chkSel != null)
                    {
                        if (chkSel.Checked)
                        {
                            modeltestteachers = blltestteachers.GetModel(new Guid(gvTestTeachersList.DataKeys[myItem.RowIndex][0].ToString()));
                            if (modeltestteachers.Position == 1)
                            {
                                s[iCount, 0] = gvTestTeachersList.DataKeys[myItem.RowIndex][0].ToString();
                                s[iCount, 1] = modeltestteachers.JianKaoCount.ToString();//保留原来正确的

                                if (iCount == 0)
                                {
                                    strTeacher = "'" + modeltestteachers.UserId + "'";
                                }
                                else
                                {
                                    strTeacher += ",'" + modeltestteachers.UserId + "'";
                                }
                                modeltestteachers.JianKaoCount = int.Parse(txtCount.Text);
                                blltestteachers.Update(modeltestteachers);

                                iCount++;
                            }
                        }
                    }
                }
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('请输入小于平均场次的数字!');");
                return;
            }
            if (iCount != 0)
            {
                #region 重新安排场次

                //重新插入监考场次，但特殊的不会被改变
                //计算已经安排的场次
                int k = TSCount % teacCount;
                int cCount = 0;//为平均分配监考场次计数
                int teacherSum = 0;
                int remianteacher;
                if (n > int.Parse(txtCount.Text.ToString()))
                {
                    foreach (DataRow teacher in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "JianKaoCount", "TestId='" + id + "' and Position=1 and JianKaoCount <" + n, "").Tables[0].Rows)//读取每个老师
                    {
                        teacherSum += (int)teacher["JianKaoCount"];
                    }
                }
                else
                {
                    //小于平均数以下的才是特殊情况
                    foreach (DataRow teacher in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "JianKaoCount", "TestId='" + id + "' and JianKaoCount <" + n + "and UserId not in(" + strTeacher + ")  and Position=1", "").Tables[0].Rows)//读取每个老师
                    {
                        teacherSum += (int)teacher["JianKaoCount"];
                    }
                }
                if (n > int.Parse(txtCount.Text.ToString()))
                {
                    remianteacher = teacCount - YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount <" + n);
                }
                else
                {
                    remianteacher = teacCount - YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount <" + n + "and UserId not in(" + strTeacher + ")");
                }

                if (remianteacher == 0)//如果全部小于所需监考场次平均数
                {
                    MessageBox.ResponseScript(this, "layer.msg('除零操作!');");
                    return;
                }
                else
                {
                    //还剩场次
                    int remianCount = (TSCount - teacherSum) % remianteacher;
                    int JCount = (TSCount - teacherSum) / remianteacher;//每个老师监考几场
                    if (Art != 0 && Math != 0)//分文理考试
                    {
                        if (JCount + 1 > YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=1)") || JCount + 1 > YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=2)"))
                        {
                            for (int i = 0; i < s.Length; i++)
                            {
                                if (s[i, 0] != null)
                                {
                                    modeltestteachers = blltestteachers.GetModel(new Guid(s[i, 0]));

                                    modeltestteachers.JianKaoCount = int.Parse(s[i, 1]);
                                    blltestteachers.Update(modeltestteachers);
                                }
                                else
                                {
                                    break;
                                }
                            }
                            MessageBox.ResponseScript(this, "layer.msg('这样设置会导致老师的监考场数大于最大监考场数，请重新填写数字!');");
                            return;
                        }
                    }
                    else
                    {
                        if (JCount + 1 > YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + id + "'"))
                        {
                            for (int i = 0; i < s.Length; i++)
                            {
                                if (s[i, 0] != null)
                                {
                                    modeltestteachers = blltestteachers.GetModel(new Guid(s[i, 0]));

                                    modeltestteachers.JianKaoCount = int.Parse(s[i, 1]);
                                    blltestteachers.Update(modeltestteachers);
                                }
                                else
                                {
                                    break;
                                }
                            }
                            MessageBox.ResponseScript(this, "layer.msg('这样设置会导致老师的监考场数大于最大监考场数，请重新填写数字!');");
                            return;
                        }
                    }
                    if (n > int.Parse(txtCount.Text.ToString()))
                    {
                        //为空的和监考场数不是特殊的老师平均分配监考场数
                        foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "TestTeacherId", "TestId='" + id + "'  and Position=1 and (JianKaoCount is null or JianKaoCount>=" + n + ")", "newid()").Tables[0].Rows)
                        {
                            if (cCount < remianCount)
                            {
                                modeltestteachers = blltestteachers.GetModel((Guid)item["TestTeacherId"]);
                                modeltestteachers.JianKaoCount = JCount + 1;
                                blltestteachers.Update(modeltestteachers);
                            }
                            else
                            {
                                modeltestteachers = blltestteachers.GetModel((Guid)item["TestTeacherId"]);
                                modeltestteachers.JianKaoCount = JCount;
                                blltestteachers.Update(modeltestteachers);
                            }
                            cCount++;
                        }
                    }
                    else
                    {
                        //为空的和监考场数不是特殊的老师平均分配监考场数
                        foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestTeachers", 0, "TestTeacherId", "TestId='" + id + "'  and Position=1 and (JianKaoCount is null or JianKaoCount>=" + n + ") and UserId not in(" + strTeacher + ")", "newid()").Tables[0].Rows)
                        {
                            if (cCount < remianCount)
                            {
                                modeltestteachers = blltestteachers.GetModel((Guid)item["TestTeacherId"]);
                                modeltestteachers.JianKaoCount = JCount + 1;
                                blltestteachers.Update(modeltestteachers);
                            }
                            else
                            {
                                modeltestteachers = blltestteachers.GetModel((Guid)item["TestTeacherId"]);
                                modeltestteachers.JianKaoCount = JCount;
                                blltestteachers.Update(modeltestteachers);
                            }
                            cCount++;
                        }
                    }
                }
                #endregion
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要设置监考老师的记录!');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('设置成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('设置失败!');");
            }
            BindData();
        }

        #region 导入教师
        protected void btnImpotTeachers_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('由于已经为每位老师安排了监考场数，不能再导入新的老师!');");
                return;
            }
            try
            {
                if (this.fupScore.FileName.Length == 0)
                {
                    lblMessage.Text = "你没有选择导入的Excel文件！";
                    return;
                }
                else if (System.IO.Path.GetExtension(this.fupScore.FileName) != ".xls" && System.IO.Path.GetExtension(this.fupScore.FileName) != ".xlsx")
                {
                    lblMessage.Text = "请选择Excel格式“*.xls,*.xlsx”的文件！";
                    return;
                }
                else
                {
                    UpLoad _upload = new UpLoad();
                    UpLoad.UploadFileInfo _fileInfo = _upload.UpLoadFileTemp(fupScore, modelAreaUser.ColumnPath, UserName, CodeTable.FileType.files);
                    DataTable dt = GetExcelData(_fileInfo.RelativePath);//获取Excel表数据 文件名+*.格式
                    ImportData(dt);//导入
                }
            }
            catch (Exception exception)
            {
                MessageBox.ResponseScript(this, "layer.msg('出现错误：'" + exception.Message + "');");
            }
            BindData();
            MessageBox.ResponseScript(this, "layer.msg('导入完老师后，请确保不会再添加和删除，修改老师，并且巡考人员已设定好，然后再设置每个考场的监考人数!');");
        }
        /// <summary>
        /// 返回 导入数据
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private DataTable GetExcelData(string fileName)
        {
            string path = Server.MapPath(fileName);
            HSSFWorkbook hssfworkbook;
            using (FileStream file = new FileStream(path, FileMode.Open, FileAccess.Read))
                hssfworkbook = new HSSFWorkbook(file);

            HSSFSheet sheet = (HSSFSheet)hssfworkbook.GetSheetAt(0);
            System.Collections.IEnumerator rows = sheet.GetRowEnumerator();
            DataTable dt = new DataTable();
            //移动的标题行，若不存在提示用户
            if (rows.MoveNext())
            {
                HSSFRow rowTitle = (HSSFRow)rows.Current;
                for (int i = 0; i < rowTitle.LastCellNum; i++)
                {
                    HSSFCell cell = (HSSFCell)rowTitle.GetCell(i);
                    if (cell != null)
                        dt.Columns.Add(cell.ToString().Trim());
                }
                while (rows.MoveNext())
                {

                    HSSFRow row = (HSSFRow)rows.Current;
                    DataRow dr = dt.NewRow();
                    for (int i = 0; i < row.LastCellNum; i++)
                    {
                        HSSFCell cell = (HSSFCell)row.GetCell(i);
                        if (cell != null) dr[i] = cell.ToString().Trim();
                        else dr[i] = null;
                    }
                    dt.Rows.Add(dr);
                }
            }
            else throw new Exception("无效格式");
            return dt;
        }
        /// <summary>
        /// 导入数据库
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        private void ImportData(DataTable dt)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                if (!CheckExcel(dt))
                {
                    MessageBox.ResponseScript(this, "layer.msg('导入失败!');");
                    return;
                }
                else
                {
                    bool isend = false;
                    int count = 0;

                    if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and Position=1 and JianKaoCount is not null") == 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (!string.IsNullOrEmpty(dt.Rows[i]["教师姓名"].ToString()))
                            {
                                string userid;
                                foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetModel("UserInfos", "UserID,ColumnPath,ColumnId", "UserNo='" + dt.Rows[i]["教师编号"].ToString() + "'").Tables[0].Rows)
                                {
                                    if (modelAreaUser.ColumnID.ToString() == item["ColumnId"].ToString() && modelAreaUser.ColumnPath == item["ColumnPath"].ToString())
                                    {
                                        userid = item["UserID"].ToString();
                                        int countteacher = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + id + "' and " + "UserId='" + userid + "'");
                                        if (countteacher == 0)
                                        {
                                            modeltestteachers = new YunEdu.Model.PKC_TestTeachers();
                                            modeltestteachers.IsEnabled = true;
                                            modeltestteachers.Position = 1;
                                            modeltestteachers.CreateTime = DateTime.Now;
                                            modeltestteachers.ColumnId = modelAreaUser.ColumnID;
                                            modeltestteachers.ColumnPath = modelAreaUser.ColumnPath;
                                            modeltestteachers.TestId = new Guid(id);
                                            modeltestteachers.UserId = new Guid(userid);
                                            modeltests = blltests.GetModel(new Guid(id));
                                            modeltestteachers.SchoolYear = modeltests.SchoolYear;

                                            if (blltestteachers.Add(modeltestteachers))
                                            {
                                                count++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        isend = true;
                    }

                    if (isend)
                    {
                        sb.Append("<li style=\"color: Black;display:inline;\">由于已经为每位老师安排了监考场数，无法导入！</li>");
                    }
                    else
                    {
                        sb.Append("<li style=\"color: Black;display:inline;\">成功导入 " + count + "个 教师！</li>");
                    }
                    this.lblMessage.Text += sb.ToString();
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        /// 验证Excel格式
        /// </summary>
        /// <returns></returns>
        private bool CheckExcel(DataTable dt)
        {
            bool result = false;
            #region 标题
            bool UserNo_Check = false, TName_Check = false;
            //bool RoomTitle_Check = false, RoomNum_Check = false, RoomLocation_Check = false, RoomType_Check = false, IsEnabled_Check = false, RoomLayout_Check = false, Rank_Check = false;
            string str = string.Empty;

            if (dt.Columns.Contains("教师姓名")) { UserNo_Check = true; }
            if (dt.Columns.Contains("教师编号")) { TName_Check = true; }


            if (!UserNo_Check) str += "<li>“教师姓名”标题未找到</li>";
            if (!TName_Check) str += "<li>“教师编号”标题未找到</li>";


            if (UserNo_Check && TName_Check) result = true;
            else str += "<li>导入文件的标题内容格式不正确！</li>";
            this.lblMessage.Text = str;
            #endregion

            //if (result)
            //{
            //    dt.Columns.Add("TypeCode", typeof(object));
            //    dt.Columns.Add("LeveCode", typeof(object));
            //    dt.Columns.Add("RankCode", typeof(object));

            //    DataSet dsDictionary = new YunEdu.BLL.Site_Dictionary().GetList(" DictTypeId in(20,22,23) ");
            //    string strResult = string.Empty;//返回错误信息
            //    for (int i = 0; i < dt.Rows.Count; i++)
            //    {
            //        dsDictionary.Tables[0].DefaultView.RowFilter = "DictTypeId=20 and DictText='" + dt.Rows[i]["获奖类别"] + "'";
            //        if (dsDictionary.Tables[0].DefaultView.ToTable().Rows.Count == 0)
            //        {
            //            strResult += "<li>导入数据，Excel第 " + (i + 2) + " 行的获奖类别：“" + dt.Rows[i]["获奖类别"] + "”不存在！</li>";
            //        }
            //        else
            //        {
            //            dt.Rows[i]["TypeCode"] = dsDictionary.Tables[0].DefaultView.ToTable().Rows[0]["DictValue"];
            //        }
            //        dsDictionary.Tables[0].DefaultView.RowFilter = "DictTypeId=22 and DictText='" + dt.Rows[i]["获奖级别"] + "'";
            //        if (dsDictionary.Tables[0].DefaultView.ToTable().Rows.Count == 0)
            //        {
            //            strResult += "<li>导入数据，Excel第 " + (i + 2) + " 行的获奖级别：“" + dt.Rows[i]["获奖级别"] + "”不存在！</li>";
            //        }
            //        else
            //        {
            //            dt.Rows[i]["LeveCode"] = dsDictionary.Tables[0].DefaultView.ToTable().Rows[0]["DictValue"];
            //        }
            //        //dsDictionary.Tables[0].DefaultView.RowFilter = "DictTypeId=23 and DictText='" + dt.Rows[i]["获奖等级"] + "'";
            //        //if (dsDictionary.Tables[0].DefaultView.ToTable().Rows.Count == 0)
            //        //{
            //        //    strResult += "<li>导入数据，Excel第 " + (i + 2) + " 行的获奖等级：“" + dt.Rows[i]["获奖等级"] + "”不存在！</li>";
            //        //}
            //        //else
            //        //{
            //        //    dt.Rows[i]["RankCode"] = dsDictionary.Tables[0].DefaultView.ToTable().Rows[0]["DictValue"];
            //        //}

            //    }
            //    if (strResult != string.Empty)
            //    { this.lblMessage.Text = strResult; }
            //}
            return result;
        }

        protected void btnDownloadTeachers_Click(object sender, EventArgs e)
        {
            Response.Clear();
            Response.ContentType = "application/x-zip-compressed";
            Response.AddHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode("监考教师模板.xls", System.Text.Encoding.UTF8));
            string filename = Server.MapPath("/Admin/pkc_manage/userfiles/监考教师模板.xls");
            Response.TransmitFile(filename);
        }

        #endregion

        protected void gvTestTeachersList_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "Del")
            {
                int index = Convert.ToInt32(e.CommandArgument);
                Guid Id = new Guid(gvTestTeachersList.DataKeys[index][0].ToString());
                blltestteachers.Delete(Id);
                MessageBox.ResponseScript(this, "layer.msg('删除成功！')");
                BindData();
            }
        }

        protected void gvTestTeachersList_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                Button btnDelete = e.Row.FindControl("btnDelete") as Button;
                btnDelete.CommandArgument = e.Row.RowIndex.ToString();
            }
        }
    }
}