﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:api_kq_inout
    /// </summary>
    public partial class api_kq_inout
    {
        public api_kq_inout()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from api_kq_inout");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.api_kq_inout model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into api_kq_inout(");
            strSql.Append("Id,CardNo,UserId,Name,IdCardNo,AttendanceType,DeviceNo,Place,PlaceType,ReadDateTime,SendDateTime,ColumnId,IsPush,Company,ColumnPath,TeacherNo,ClassId,ClassName,GradeId,SendResault,AttendanceStatus,AttendStage)");
            strSql.Append(" values (");
            strSql.Append("@Id,@CardNo,@UserId,@Name,@IdCardNo,@AttendanceType,@DeviceNo,@Place,@PlaceType,@ReadDateTime,@SendDateTime,@ColumnId,@IsPush,@Company,@ColumnPath,@TeacherNo,@ClassId,@ClassName,@GradeId,@SendResault,@AttendanceStatus,@AttendStage)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,255),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,25),
                    new SqlParameter("@IdCardNo", SqlDbType.NVarChar,50),
                    new SqlParameter("@AttendanceType", SqlDbType.NVarChar,20),
                    new SqlParameter("@DeviceNo", SqlDbType.NVarChar,50),
                    new SqlParameter("@Place", SqlDbType.NVarChar,255),
                    new SqlParameter("@PlaceType", SqlDbType.Int,4),
                    new SqlParameter("@ReadDateTime", SqlDbType.DateTime),
                    new SqlParameter("@SendDateTime", SqlDbType.DateTime),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@IsPush", SqlDbType.Int,4),
                    new SqlParameter("@Company", SqlDbType.NVarChar,50),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,250),
                    new SqlParameter("@TeacherNo", SqlDbType.NVarChar,250),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassName", SqlDbType.NVarChar,250),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SendResault", SqlDbType.NVarChar,250),
                    new SqlParameter("@AttendanceStatus", SqlDbType.Int,4),
                    new SqlParameter("@AttendStage", SqlDbType.Int,4)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.CardNo;
            parameters[2].Value = model.UserId;
            parameters[3].Value = model.Name;
            parameters[4].Value = model.IdCardNo;
            parameters[5].Value = model.AttendanceType;
            parameters[6].Value = model.DeviceNo;
            parameters[7].Value = model.Place;
            parameters[8].Value = model.PlaceType;
            parameters[9].Value = model.ReadDateTime;
            parameters[10].Value = model.SendDateTime;
            parameters[11].Value = model.ColumnId;
            parameters[12].Value = model.IsPush;
            parameters[13].Value = model.Company;
            parameters[14].Value = model.ColumnPath;
            parameters[15].Value = model.TeacherNo;
            parameters[16].Value = model.ClassId;
            parameters[17].Value = model.ClassName;
            parameters[18].Value = model.GradeId;
            parameters[19].Value = model.SendResault;
            parameters[20].Value = model.AttendanceStatus;
            parameters[21].Value = model.AttendStage;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.api_kq_inout model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update api_kq_inout set ");
            strSql.Append("CardNo=@CardNo,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("Name=@Name,");
            strSql.Append("IdCardNo=@IdCardNo,");
            strSql.Append("AttendanceType=@AttendanceType,");
            strSql.Append("DeviceNo=@DeviceNo,");
            strSql.Append("Place=@Place,");
            strSql.Append("PlaceType=@PlaceType,");
            strSql.Append("ReadDateTime=@ReadDateTime,");
            strSql.Append("SendDateTime=@SendDateTime,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("IsPush=@IsPush,");
            strSql.Append("Company=@Company,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("TeacherNo=@TeacherNo,");
            strSql.Append("ClassId=@ClassId,");
            strSql.Append("ClassName=@ClassName,");
            strSql.Append("GradeId=@GradeId,");
            strSql.Append("SendResault=@SendResault,");
            strSql.Append("AttendanceStatus=@AttendanceStatus,");
            strSql.Append("AttendStage=@AttendStage");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,255),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,25),
                    new SqlParameter("@IdCardNo", SqlDbType.NVarChar,50),
                    new SqlParameter("@AttendanceType", SqlDbType.NVarChar,20),
                    new SqlParameter("@DeviceNo", SqlDbType.NVarChar,50),
                    new SqlParameter("@Place", SqlDbType.NVarChar,255),
                    new SqlParameter("@PlaceType", SqlDbType.Int,4),
                    new SqlParameter("@ReadDateTime", SqlDbType.DateTime),
                    new SqlParameter("@SendDateTime", SqlDbType.DateTime),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@IsPush", SqlDbType.Int,4),
                    new SqlParameter("@Company", SqlDbType.NVarChar,50),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,250),
                    new SqlParameter("@TeacherNo", SqlDbType.NVarChar,250),
                    new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassName", SqlDbType.NVarChar,250),
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SendResault", SqlDbType.NVarChar,250),
                    new SqlParameter("@AttendanceStatus", SqlDbType.Int,4),
                    new SqlParameter("@AttendStage", SqlDbType.Int,4)};
            parameters[0].Value = model.CardNo;
            parameters[1].Value = model.UserId;
            parameters[2].Value = model.Name;
            parameters[3].Value = model.IdCardNo;
            parameters[4].Value = model.AttendanceType;
            parameters[5].Value = model.DeviceNo;
            parameters[6].Value = model.Place;
            parameters[7].Value = model.PlaceType;
            parameters[8].Value = model.ReadDateTime;
            parameters[9].Value = model.SendDateTime;
            parameters[10].Value = model.ColumnId;
            parameters[11].Value = model.IsPush;
            parameters[12].Value = model.Company;
            parameters[13].Value = model.Id;
            parameters[14].Value = model.ColumnPath;
            parameters[15].Value = model.TeacherNo;
            parameters[16].Value = model.ClassId;
            parameters[17].Value = model.ClassName;
            parameters[18].Value = model.GradeId;
            parameters[19].Value = model.SendResault;
            parameters[20].Value = model.AttendanceStatus;
            parameters[21].Value = model.AttendStage;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from api_kq_inout ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from api_kq_inout ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.api_kq_inout GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,CardNo,UserId,Name,IdCardNo,AttendanceType,DeviceNo,Place,PlaceType,ReadDateTime,SendDateTime,ColumnId,IsPush,Company,ColumnPath,TeacherNo,ClassId,ClassName,GradeId,SendResault,AttendanceStatus,AttendStage from api_kq_inout ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.api_kq_inout model = new ECB.PC.Model.api_kq_inout();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.api_kq_inout DataRowToModel(DataRow row)
        {
            ECB.PC.Model.api_kq_inout model = new ECB.PC.Model.api_kq_inout();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["CardNo"] != null)
                {
                    model.CardNo = row["CardNo"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["IdCardNo"] != null)
                {
                    model.IdCardNo = row["IdCardNo"].ToString();
                }
                if (row["AttendanceType"] != null)
                {
                    model.AttendanceType = row["AttendanceType"].ToString();
                }
                if (row["DeviceNo"] != null)
                {
                    model.DeviceNo = row["DeviceNo"].ToString();
                }
                if (row["Place"] != null)
                {
                    model.Place = row["Place"].ToString();
                }
                if (row["PlaceType"] != null && row["PlaceType"].ToString() != "")
                {
                    model.PlaceType = int.Parse(row["PlaceType"].ToString());
                }
                if (row["ReadDateTime"] != null && row["ReadDateTime"].ToString() != "")
                {
                    model.ReadDateTime = DateTime.Parse(row["ReadDateTime"].ToString());
                }
                if (row["SendDateTime"] != null && row["SendDateTime"].ToString() != "")
                {
                    model.SendDateTime = DateTime.Parse(row["SendDateTime"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["IsPush"] != null && row["IsPush"].ToString() != "")
                {
                    model.IsPush = int.Parse(row["IsPush"].ToString());
                }
                if (row["Company"] != null)
                {
                    model.Company = row["Company"].ToString();
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["TeacherNo"] != null)
                {
                    model.TeacherNo = row["TeacherNo"].ToString();
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["ClassName"] != null)
                {
                    model.ClassName = row["ClassName"].ToString();
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["SendResault"] != null)
                {
                    model.SendResault = row["SendResault"].ToString();
                }
                if (row["AttendanceStatus"] != null && row["AttendanceStatus"].ToString() != "")
                {
                    model.AttendanceStatus = int.Parse(row["AttendanceStatus"].ToString());
                }
                if (row["AttendStage"] != null && row["AttendStage"].ToString() != "")
                {
                    model.AttendStage = int.Parse(row["AttendStage"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,CardNo,UserId,Name,IdCardNo,AttendanceType,DeviceNo,Place,PlaceType,ReadDateTime,SendDateTime,ColumnId,IsPush,Company,ColumnPath,TeacherNo,ClassId,ClassName,GradeId,SendResault,AttendanceStatus,AttendStage ");
            strSql.Append(" FROM api_kq_inout ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,CardNo,UserId,Name,IdCardNo,AttendanceType,DeviceNo,Place,PlaceType,ReadDateTime,SendDateTime,ColumnId,IsPush,Company,ColumnPath,TeacherNo,ClassId,ClassName,GradeId,SendResault,AttendanceStatus,AttendStage ");
            strSql.Append(" FROM api_kq_inout ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM api_kq_inout ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from api_kq_inout T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "api_kq_inout";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        public int GetAttendanceStatus(Guid GradeId, DateTime ReadTime, string AttendanceType, out int AttendStage)
        {
            //舍弃秒数
            ReadTime = Convert.ToDateTime(ReadTime.ToString("yyyy-MM-dd HH:mm"));
            int rowsAffected;
            SqlParameter[] parameters = {
                    new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ReadTime", SqlDbType.DateTime),
                    new SqlParameter("@AttendanceType",SqlDbType.NVarChar,50),
                    new SqlParameter("@AttendStage",SqlDbType.Int)};
            parameters[0].Value = GradeId;
            parameters[1].Value = ReadTime;
            parameters[2].Value = AttendanceType;
            parameters[3].Direction = ParameterDirection.Output;

            int status = DbHelperSQL.RunProcedure("UP_GetAttendanceStatus", parameters, out rowsAffected);
            AttendStage = (int)(parameters[3].Value.ToString() == "" ? 0 : parameters[3].Value);
            return status;
        }
        /// <summary>
        /// 修改学生课堂考勤记录
        /// </summary>
        /// <param name="columnId">学校id</param>
        /// <param name="day">要修改记录的日期</param>
        /// <param name="prestatus">需要修改的状态记录</param>
        /// <param name="status">要修改为的状态</param>
        public void UpdateStudentAttendClassRecord(int columnId, DateTime day, int prestatus, int status)
        {
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@date",SqlDbType.Date),
                new SqlParameter("@prestatus",SqlDbType.Int),
                new SqlParameter("@status",SqlDbType.Int),
            };
            parameters[0].Value = columnId;
            parameters[1].Value = day;
            parameters[2].Value = prestatus;
            parameters[3].Value = status;
            DbHelperSQL.RunProcedureReturnMoreDateTable("UP_UpdateStudentAttendClass", parameters);
        }
        /// <summary>
        /// 修改教师课堂考勤记录
        /// </summary>
        /// <param name="columnId">学校id</param>
        /// <param name="day">要修改记录的日期</param>
        /// <param name="prestatus">需要修改的状态记录</param>
        /// <param name="status">要修改为的状态</param>
        public void UpdateTeacherAttendClassRecord(int columnId, DateTime day, int prestatus, int status)
        {
            SqlParameter[] parameters = {
                new SqlParameter("@ColumnId",SqlDbType.Int),
                new SqlParameter("@date",SqlDbType.Date),
                new SqlParameter("@prestatus",SqlDbType.Int),
                new SqlParameter("@status",SqlDbType.Int),
            };
            parameters[0].Value = columnId;
            parameters[1].Value = day;
            parameters[2].Value = prestatus;
            parameters[3].Value = status;
            DbHelperSQL.RunProcedureReturnMoreDateTable("UP_UpdateTeacherAttendClass", parameters);
        }
        #endregion  ExtensionMethod
    }
}

