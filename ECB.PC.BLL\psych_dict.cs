﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;
using System.Linq;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:psych_dict
	/// </summary>
	public partial class psych_dict
	{
		public psych_dict()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from psych_dict");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(), parameters);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.psych_dict model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into psych_dict(");
			strSql.Append("Id,ColumnId,ColumnPath,DictTypeId,DictText,DictValue,Memo,IsWarning,WarningLevel,IsNeedInput,SortId,LastEditBy,LastEditTime)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@DictTypeId,@DictText,@DictValue,@Memo,@IsWarning,@WarningLevel,@IsNeedInput,@SortId,@LastEditBy,@LastEditTime)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@DictTypeId", SqlDbType.Int,4),
					new SqlParameter("@DictText", SqlDbType.NVarChar,50),
					new SqlParameter("@DictValue", SqlDbType.NVarChar,4),
					new SqlParameter("@Memo", SqlDbType.NVarChar,150),
					new SqlParameter("@IsWarning", SqlDbType.Int,4),
					new SqlParameter("@WarningLevel", SqlDbType.NVarChar,2),
					new SqlParameter("@IsNeedInput", SqlDbType.Int,4),
					new SqlParameter("@SortId", SqlDbType.Int,4),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
			parameters[0].Value = model.Id = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.DictTypeId;
			parameters[4].Value = model.DictText;
			parameters[5].Value = model.DictValue;
			parameters[6].Value = model.Memo;
			parameters[7].Value = model.IsWarning;
			parameters[8].Value = model.WarningLevel;
			parameters[9].Value = model.IsNeedInput;
			parameters[10].Value = model.SortId;
			parameters[11].Value = model.LastEditBy;
			parameters[12].Value = model.LastEditTime;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.psych_dict model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update psych_dict set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("DictTypeId=@DictTypeId,");
			strSql.Append("DictText=@DictText,");
			strSql.Append("DictValue=@DictValue,");
			strSql.Append("Memo=@Memo,");
			strSql.Append("IsWarning=@IsWarning,");
			strSql.Append("WarningLevel=@WarningLevel,");
			strSql.Append("IsNeedInput=@IsNeedInput,");
			strSql.Append("SortId=@SortId,");
			strSql.Append("LastEditBy=@LastEditBy,");
			strSql.Append("LastEditTime=@LastEditTime");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@DictTypeId", SqlDbType.Int,4),
					new SqlParameter("@DictText", SqlDbType.NVarChar,50),
					new SqlParameter("@DictValue", SqlDbType.NVarChar,4),
					new SqlParameter("@Memo", SqlDbType.NVarChar,150),
					new SqlParameter("@IsWarning", SqlDbType.Int,4),
					new SqlParameter("@WarningLevel", SqlDbType.NVarChar,2),
					new SqlParameter("@IsNeedInput", SqlDbType.Int,4),
					new SqlParameter("@SortId", SqlDbType.Int,4),
					new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@LastEditTime", SqlDbType.DateTime),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.DictTypeId;
			parameters[3].Value = model.DictText;
			parameters[4].Value = model.DictValue;
			parameters[5].Value = model.Memo;
			parameters[6].Value = model.IsWarning;
			parameters[7].Value = model.WarningLevel;
			parameters[8].Value = model.IsNeedInput;
			parameters[9].Value = model.SortId;
			parameters[10].Value = model.LastEditBy;
			parameters[11].Value = model.LastEditTime;
			parameters[12].Value = model.Id;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from psych_dict ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = Id;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from psych_dict ");
			strSql.Append(" where Id in (" + Idlist + ")  ");
			int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.psych_dict GetModel(Guid Id)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,DictTypeId,DictText,DictValue,Memo,IsWarning,WarningLevel,IsNeedInput,SortId,LastEditBy,LastEditTime from psych_dict ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
			parameters[0].Value = Id;

			ECB.PC.Model.psych_dict model = new ECB.PC.Model.psych_dict();
			DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.psych_dict DataRowToModel(DataRow row)
		{
			ECB.PC.Model.psych_dict model = new ECB.PC.Model.psych_dict();
			if (row != null)
			{
				if (row["Id"] != null && row["Id"].ToString() != "")
				{
					model.Id = new Guid(row["Id"].ToString());
				}
				if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
				{
					model.ColumnId = int.Parse(row["ColumnId"].ToString());
				}
				if (row["ColumnPath"] != null)
				{
					model.ColumnPath = row["ColumnPath"].ToString();
				}
				if (row["DictTypeId"] != null && row["DictTypeId"].ToString() != "")
				{
					model.DictTypeId = int.Parse(row["DictTypeId"].ToString());
				}
				if (row["DictText"] != null)
				{
					model.DictText = row["DictText"].ToString();
				}
				if (row["DictValue"] != null)
				{
					model.DictValue = row["DictValue"].ToString();
				}
				if (row["Memo"] != null)
				{
					model.Memo = row["Memo"].ToString();
				}
				if (row["IsWarning"] != null && row["IsWarning"].ToString() != "")
				{
					model.IsWarning = int.Parse(row["IsWarning"].ToString());
				}
				if (row["WarningLevel"] != null)
				{
					model.WarningLevel = row["WarningLevel"].ToString();
				}
				if (row["IsNeedInput"] != null && row["IsNeedInput"].ToString() != "")
				{
					model.IsNeedInput = int.Parse(row["IsNeedInput"].ToString());
				}
				if (row["SortId"] != null && row["SortId"].ToString() != "")
				{
					model.SortId = int.Parse(row["SortId"].ToString());
				}
				if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
				{
					model.LastEditBy = new Guid(row["LastEditBy"].ToString());
				}
				if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
				{
					model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,DictTypeId,DictText,DictValue,Memo,IsWarning,WarningLevel,IsNeedInput,SortId,LastEditBy,LastEditTime ");
			strSql.Append(" FROM psych_dict ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,DictTypeId,DictText,DictValue,Memo,IsWarning,WarningLevel,IsNeedInput,SortId,LastEditBy,LastEditTime ");
			strSql.Append(" FROM psych_dict ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM psych_dict ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from psych_dict T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}



		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "psych_dict";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod
		public void AddDefaultData()
		{
			ArrayList list = new ArrayList();

		}

		public string GetModel(int dictTypeId, string dictValue)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select DictText FROM psych_dict ");
			strSql.Append($" where DictTypeId={dictTypeId} and DictValue='{dictValue}'");
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return "";
			}
			else
			{
				return obj.ToString();
			}
		}

		/// <summary>
		/// 根据ColumnPath向上查找数据
		/// </summary>
		/// <param name="columnPath"></param>
		/// <returns>包含查询结果的DataSet</returns>
		public DataTable GetList(string columnPath, string where)
		{

			// 分割路径，从右到左解析每个部分
			string[] pathParts = columnPath.Split('|');

			// 从最后一个节点开始查询
			for (int i = pathParts.Length - 1; i >= 0; i--)
			{
				if (!int.TryParse(pathParts[i], out int columnId))
				{
					continue;
				}

				// 查询指定columnId的数据
				string strWhere = $"ColumnId={columnId}";
				if (where != "")
				{
					strWhere += " and " + where;
				}
				DataSet ds = GetList(0, strWhere, "DictTypeId,sortid");

				// 如果有数据，则返回
				if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
				{
					return ds.Tables[0];
				}
			}

			// 如果所有节点都没有找到数据，则返回空结果
			return new DataTable();
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetListData(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select DictTypeId,DictText,DictValue,case when LEN(Memo)=0 then DictText else DictText+'('+Memo+')' end as name,DictValue id,Memo,IsWarning,WarningLevel,IsNeedInput ");
			strSql.Append(" FROM psych_dict ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by SortId");
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取字典对象
		/// </summary>
		/// <param name="DictTypeId">字典类型</param>
		/// <param name="ColumnPath">地区路径</param>
		/// <param name="DictValue">字典值</param>
		/// <returns></returns>
		public Model.psych_dict GetModel(int DictTypeId, string ColumnPath, string DictValue)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,DictTypeId,DictText,DictValue,Memo,IsWarning,WarningLevel,IsNeedInput,SortId,LastEditBy,LastEditTime from psych_dict ");
			strSql.Append(" where DictTypeId=@DictTypeId and @ColumnPath like ColumnPath+'|%' and DictValue=@DictValue ");
			SqlParameter[] parameters = {
					new SqlParameter("@DictTypeId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
					new SqlParameter("@DictValue", SqlDbType.NVarChar,4)
			};
			parameters[0].Value = DictTypeId;
			parameters[1].Value = ColumnPath;
			parameters[2].Value = DictValue;

			DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		/// 获得数据列表，如果dictTypeIds为空则查询所有类型
		/// </summary>
		/// <param name="columnId">地区Id</param>
		/// <param name="columnPath">地区Id路径</param>
		/// <param name="dictTypeIds">类型Id，为空时查询所有类型</param>
		/// <returns></returns>
		public DataTable GetDictWarningList(string columnPath, string dictTypeIds)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select a.Id,a.ColumnId,a.ColumnPath,a.DictTypeId,a.DictText,a.DictValue,a.Memo,a.IsWarning,a.WarningLevel,a.IsNeedInput,a.SortId,a.LastEditBy,a.LastEditTime,b.WarningName,b.Color ");
			strSql.Append(" FROM psych_dict a left join psych_warning_config b on b.ColumnPath=@ColumnPath and b.WarningCode=a.WarningLevel");
			strSql.Append($" where{(!string.IsNullOrEmpty(dictTypeIds) ? $" a.DictTypeId in ({dictTypeIds}) and " : "")} @ColumnPath+'|' like a.ColumnPath+'|%' order by a.ColumnPath desc");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,250)
			};
			parameters[0].Value = columnPath;
			DataTable dtPsychDict = DbHelperSQL.Query(strSql.ToString(), parameters).Tables[0];

			// 复制字典table结构
			DataTable dtTemp = dtPsychDict.Clone();
			// 从查询结果中获取所有不重复的DictTypeId
			string[] dictTypeIdList = dtPsychDict.AsEnumerable()
				.Select(row => row["DictTypeId"].ToString())
				.Distinct()
				.ToArray();

			// 循环字典类型
			foreach (string dictTypeId in dictTypeIdList)
			{
				// 筛选该类型字典
				DataRow[] rows = dtPsychDict.Select($"DictTypeId={dictTypeId}");
				// 判断是否有值
				if (rows.Length > 1)
				{
					// 拿到第一行的ColumnId
					int dictColumnId = Convert.ToInt32(rows[0]["ColumnId"]);
					// 找到对应ColumnId和字典类型下的所有字典
					DataRow[] rows2 = dtPsychDict.Select($"ColumnId={dictColumnId} AND DictTypeId={dictTypeId}");
					// 添加到复制的table中
					foreach (DataRow item in rows2)
					{
						dtTemp.ImportRow(item);
					}
				}
			}
			return dtTemp;
		}

		#endregion  ExtensionMethod
	}
}