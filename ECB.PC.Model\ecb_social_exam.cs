﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 社会化考试表
	/// </summary>
	[Serializable]
	public partial class ecb_social_exam
	{
		public ecb_social_exam()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _examname;
		private string _subtitle;
		private DateTime _examsta;
		private DateTime _examend;
		private DateTime _showsta;
		private DateTime _showend;
		private string _mark;
        private string _modeltype;
        private Guid _ruleid;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnID
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 考试名称
		/// </summary>
		public string ExamName
		{
			set{ _examname=value;}
			get{return _examname;}
		}
		/// <summary>
		/// 副标题
		/// </summary>
		public string Subtitle
		{
			set{ _subtitle=value;}
			get{return _subtitle;}
		}
		/// <summary>
		/// 考试开始时间
		/// </summary>
		public DateTime ExamSta
		{
			set{ _examsta=value;}
			get{return _examsta;}
		}
		/// <summary>
		/// 考试结束时间
		/// </summary>
		public DateTime ExamEnd
		{
			set{ _examend=value;}
			get{return _examend;}
		}
		/// <summary>
		/// 展示开始时间
		/// </summary>
		public DateTime ShowSta
		{
			set{ _showsta=value;}
			get{return _showsta;}
		}
		/// <summary>
		/// 展示结束时间
		/// </summary>
		public DateTime ShowEnd
		{
			set{ _showend=value;}
			get{return _showend;}
		}
		/// <summary>
		/// 考试说明
		/// </summary>
		public string Mark
		{
			set{ _mark=value;}
			get{return _mark;}
		}
        /// <summary>
		/// 模式类型
		/// </summary>
		public string ModelType
        {
            set { _modeltype = value; }
            get { return _modeltype; }
        }
        /// <summary>
		/// 临时开关机表主键ID
		/// </summary>
		public Guid RuleId
        {
            set { _ruleid = value; }
            get { return _ruleid; }
        }
        #endregion Model

    }
}

