﻿
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections;

namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:custom_Attendance_record
	/// </summary>
	public partial class custom_Attendance_record
	{
		public custom_Attendance_record()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from custom_Attendance_record");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.custom_Attendance_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into custom_Attendance_record(");
			strSql.Append("ID,UserId,AttendanceType,AttendanceTime,Reason,AttendanceId)");
			strSql.Append(" values (");
			strSql.Append("@ID,@UserId,@AttendanceType,@AttendanceTime,@Reason,@AttendanceId)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@AttendanceType", SqlDbType.NVarChar,255),
					new SqlParameter("@AttendanceTime", SqlDbType.DateTime),
					new SqlParameter("@Reason", SqlDbType.NVarChar,255),
					new SqlParameter("@AttendanceId", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.UserId;
			parameters[2].Value = model.AttendanceType;
			parameters[3].Value = model.AttendanceTime;
			parameters[4].Value = model.Reason;
			parameters[5].Value = model.AttendanceId;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.custom_Attendance_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update custom_Attendance_record set ");
			strSql.Append("UserId=@UserId,");
			strSql.Append("AttendanceType=@AttendanceType,");
			strSql.Append("AttendanceTime=@AttendanceTime,");
			strSql.Append("Reason=@Reason,");
			strSql.Append("AttendanceId=@AttendanceId");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@AttendanceType", SqlDbType.NVarChar,255),
					new SqlParameter("@AttendanceTime", SqlDbType.DateTime),
					new SqlParameter("@Reason", SqlDbType.NVarChar,255),
					new SqlParameter("@AttendanceId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.UserId;
			parameters[1].Value = model.AttendanceType;
			parameters[2].Value = model.AttendanceTime;
			parameters[3].Value = model.Reason;
			parameters[4].Value = model.AttendanceId;
			parameters[5].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Attendance_record ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from custom_Attendance_record ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Attendance_record GetModel(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,UserId,AttendanceType,AttendanceTime,Reason,AttendanceId from custom_Attendance_record ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.custom_Attendance_record model=new ECB.PC.Model.custom_Attendance_record();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.custom_Attendance_record DataRowToModel(DataRow row)
		{
			ECB.PC.Model.custom_Attendance_record model=new ECB.PC.Model.custom_Attendance_record();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["UserId"]!=null && row["UserId"].ToString()!="")
				{
					model.UserId= new Guid(row["UserId"].ToString());
				}
				if(row["AttendanceType"]!=null)
				{
					model.AttendanceType=row["AttendanceType"].ToString();
				}
				if(row["AttendanceTime"]!=null && row["AttendanceTime"].ToString()!="")
				{
					model.AttendanceTime=DateTime.Parse(row["AttendanceTime"].ToString());
				}
				if(row["Reason"]!=null)
				{
					model.Reason=row["Reason"].ToString();
				}
				if(row["AttendanceId"]!=null && row["AttendanceId"].ToString()!="")
				{
					model.AttendanceId= new Guid(row["AttendanceId"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,UserId,AttendanceType,AttendanceTime,Reason,AttendanceId ");
			strSql.Append(" FROM custom_Attendance_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,UserId,AttendanceType,AttendanceTime,Reason,AttendanceId ");
			strSql.Append(" FROM custom_Attendance_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM custom_Attendance_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from custom_Attendance_record T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "custom_Attendance_record";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 添加考勤记录到事务列表
		/// </summary>
		/// <param name="model">考勤记录模型</param>
		/// <param name="sqlList">SQL事务列表</param>
		public void Add(ECB.PC.Model.custom_Attendance_record model, ArrayList sqlList)
		{
			string sql = $@"INSERT INTO custom_Attendance_record(ID,UserId,AttendanceType,AttendanceTime,Reason,AttendanceId)
                           VALUES('{model.ID}','{model.UserId}','{model.AttendanceType}','{model.AttendanceTime:yyyy-MM-dd HH:mm:ss}','{model.Reason}','{model.AttendanceId}')";
			sqlList.Add(sql);
		}

		/// <summary>
		/// 根据考勤主记录ID删除所有相关记录
		/// </summary>
		/// <param name="attendanceId">考勤主记录ID</param>
		/// <returns>删除结果</returns>
		public bool DeleteByAttendanceId(Guid attendanceId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("DELETE FROM custom_Attendance_record ");
			strSql.Append(" WHERE AttendanceId=@AttendanceId ");
			SqlParameter[] parameters = {
				new SqlParameter("@AttendanceId", SqlDbType.UniqueIdentifier, 16)
			};
			parameters[0].Value = attendanceId;

			int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
			return rows >= 0;
		}

		/// <summary>
		/// 获取考勤记录详细信息（包含教师信息）
		/// </summary>
		/// <param name="attendanceId">考勤主记录ID</param>
		/// <returns>考勤记录详细数据</returns>
		public DataSet GetRecordDetails(Guid attendanceId)
		{
			string sql = @"SELECT r.*, m.CName as TeacherName, m.IDCardNo, u.TName
                          FROM custom_Attendance_record r
                          LEFT JOIN aspnet_Membership m ON r.UserId = m.UserId
                          LEFT JOIN UserInfos u ON r.UserId = u.UserID
                          WHERE r.AttendanceId = @AttendanceId
                          ORDER BY r.AttendanceTime DESC, m.CName";

			SqlParameter[] parameters = {
				new SqlParameter("@AttendanceId", SqlDbType.UniqueIdentifier, 16)
			};
			parameters[0].Value = attendanceId;

			return DbHelperSQL.Query(sql, parameters);
		}

		#endregion  ExtensionMethod
	}
}

