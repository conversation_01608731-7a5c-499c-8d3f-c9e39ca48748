﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:JC_Leaves
    /// </summary>
    public partial class JC_Leaves
    {
        public JC_Leaves()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from JC_Leaves");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.JC_Leaves model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into JC_Leaves(");
            strSql.Append("ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,UserID,UserTypeCode,LeaveTypeCode,LeaveReason,BeginTime,EndTime,IsPass,IsPassBy,IsPassDate,IsSmsNote,IsInnerNote,SchoolYear)");
            strSql.Append(" values (");
            strSql.Append("@ID,@SchoolColumnID,@SchoolColumnPath,@GradeID,@ClassID,@TermID,@UserID,@UserTypeCode,@LeaveTypeCode,@LeaveReason,@BeginTime,@EndTime,@IsPass,@IsPassBy,@IsPassDate,@IsSmsNote,@IsInnerNote,@SchoolYear)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@GradeID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UserID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UserTypeCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@LeaveTypeCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@LeaveReason", SqlDbType.NVarChar,255),
                    new SqlParameter("@BeginTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@IsPass", SqlDbType.Bit,1),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@IsSmsNote", SqlDbType.Bit,1),
                    new SqlParameter("@IsInnerNote", SqlDbType.Bit,1),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.SchoolColumnID;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.GradeID;
            parameters[4].Value = model.ClassID;
            parameters[5].Value = model.TermID;
            parameters[6].Value = model.UserID;
            parameters[7].Value = model.UserTypeCode;
            parameters[8].Value = model.LeaveTypeCode;
            parameters[9].Value = model.LeaveReason;
            parameters[10].Value = model.BeginTime;
            parameters[11].Value = model.EndTime;
            parameters[12].Value = model.IsPass;
            parameters[13].Value = model.IsPassBy;
            parameters[14].Value = model.IsPassDate;
            parameters[15].Value = model.IsSmsNote;
            parameters[16].Value = model.IsInnerNote;
            parameters[17].Value = model.SchoolYear;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.JC_Leaves model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update JC_Leaves set ");
            strSql.Append("SchoolColumnID=@SchoolColumnID,");
            strSql.Append("SchoolColumnPath=@SchoolColumnPath,");
            strSql.Append("GradeID=@GradeID,");
            strSql.Append("ClassID=@ClassID,");
            strSql.Append("TermID=@TermID,");
            strSql.Append("UserID=@UserID,");
            strSql.Append("UserTypeCode=@UserTypeCode,");
            strSql.Append("LeaveTypeCode=@LeaveTypeCode,");
            strSql.Append("LeaveReason=@LeaveReason,");
            strSql.Append("BeginTime=@BeginTime,");
            strSql.Append("EndTime=@EndTime,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("IsPassDate=@IsPassDate,");
            strSql.Append("IsSmsNote=@IsSmsNote,");
            strSql.Append("IsInnerNote=@IsInnerNote,");
            strSql.Append("SchoolYear=@SchoolYear");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
                    new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@GradeID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UserID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@UserTypeCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@LeaveTypeCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@LeaveReason", SqlDbType.NVarChar,255),
                    new SqlParameter("@BeginTime", SqlDbType.DateTime),
                    new SqlParameter("@EndTime", SqlDbType.DateTime),
                    new SqlParameter("@IsPass", SqlDbType.Bit,1),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@IsSmsNote", SqlDbType.Bit,1),
                    new SqlParameter("@IsInnerNote", SqlDbType.Bit,1),
                    new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.SchoolColumnID;
            parameters[1].Value = model.SchoolColumnPath;
            parameters[2].Value = model.GradeID;
            parameters[3].Value = model.ClassID;
            parameters[4].Value = model.TermID;
            parameters[5].Value = model.UserID;
            parameters[6].Value = model.UserTypeCode;
            parameters[7].Value = model.LeaveTypeCode;
            parameters[8].Value = model.LeaveReason;
            parameters[9].Value = model.BeginTime;
            parameters[10].Value = model.EndTime;
            parameters[11].Value = model.IsPass;
            parameters[12].Value = model.IsPassBy;
            parameters[13].Value = model.IsPassDate;
            parameters[14].Value = model.IsSmsNote;
            parameters[15].Value = model.IsInnerNote;
            parameters[16].Value = model.SchoolYear;
            parameters[17].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Leaves ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Leaves ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Leaves GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,UserID,UserTypeCode,LeaveTypeCode,LeaveReason,BeginTime,EndTime,IsPass,IsPassBy,IsPassDate,IsSmsNote,IsInnerNote,SchoolYear from JC_Leaves ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.JC_Leaves model = new ECB.PC.Model.JC_Leaves();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Leaves DataRowToModel(DataRow row)
        {
            ECB.PC.Model.JC_Leaves model = new ECB.PC.Model.JC_Leaves();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["SchoolColumnID"] != null && row["SchoolColumnID"].ToString() != "")
                {
                    model.SchoolColumnID = int.Parse(row["SchoolColumnID"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["GradeID"] != null && row["GradeID"].ToString() != "")
                {
                    model.GradeID = new Guid(row["GradeID"].ToString());
                }
                if (row["ClassID"] != null && row["ClassID"].ToString() != "")
                {
                    model.ClassID = new Guid(row["ClassID"].ToString());
                }
                if (row["TermID"] != null && row["TermID"].ToString() != "")
                {
                    model.TermID = new Guid(row["TermID"].ToString());
                }
                if (row["UserID"] != null && row["UserID"].ToString() != "")
                {
                    model.UserID = new Guid(row["UserID"].ToString());
                }
                if (row["UserTypeCode"] != null)
                {
                    model.UserTypeCode = row["UserTypeCode"].ToString();
                }
                if (row["LeaveTypeCode"] != null)
                {
                    model.LeaveTypeCode = row["LeaveTypeCode"].ToString();
                }
                if (row["LeaveReason"] != null)
                {
                    model.LeaveReason = row["LeaveReason"].ToString();
                }
                if (row["BeginTime"] != null && row["BeginTime"].ToString() != "")
                {
                    model.BeginTime = DateTime.Parse(row["BeginTime"].ToString());
                }
                if (row["EndTime"] != null && row["EndTime"].ToString() != "")
                {
                    model.EndTime = DateTime.Parse(row["EndTime"].ToString());
                }
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    if ((row["IsPass"].ToString() == "1") || (row["IsPass"].ToString().ToLower() == "true"))
                    {
                        model.IsPass = true;
                    }
                    else
                    {
                        model.IsPass = false;
                    }
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
                if (row["IsSmsNote"] != null && row["IsSmsNote"].ToString() != "")
                {
                    if ((row["IsSmsNote"].ToString() == "1") || (row["IsSmsNote"].ToString().ToLower() == "true"))
                    {
                        model.IsSmsNote = true;
                    }
                    else
                    {
                        model.IsSmsNote = false;
                    }
                }
                if (row["IsInnerNote"] != null && row["IsInnerNote"].ToString() != "")
                {
                    if ((row["IsInnerNote"].ToString() == "1") || (row["IsInnerNote"].ToString().ToLower() == "true"))
                    {
                        model.IsInnerNote = true;
                    }
                    else
                    {
                        model.IsInnerNote = false;
                    }
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,UserID,UserTypeCode,LeaveTypeCode,LeaveReason,BeginTime,EndTime,IsPass,IsPassBy,IsPassDate,IsSmsNote,IsInnerNote,SchoolYear ");
            strSql.Append(" FROM JC_Leaves ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,UserID,UserTypeCode,LeaveTypeCode,LeaveReason,BeginTime,EndTime,IsPass,IsPassBy,IsPassDate,IsSmsNote,IsInnerNote,SchoolYear ");
            strSql.Append(" FROM JC_Leaves ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_Leaves ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_Leaves T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "JC_Leaves";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 教师请假 考勤打卡数据修改
        /// </summary>
        /// <param name="LeaveId">请假di</param>
        /// <returns></returns>
        public DataSet Teacher_Leave_Attendce(Guid LeaveId)
        {
            SqlParameter[] parameters = {
                new SqlParameter("@LeaveId",SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = LeaveId;
            try
            {
                return DbHelperSQL.RunProcedureReturnMoreDateTable("UP_Teacher_Leave_Attendce", parameters);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        /// <summary>
        /// 获得数据
        /// </summary>
        public DataSet GetListCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select GradeName,ClassName,CName,UserName,COUNT(1) as _count,a.UserID");
            strSql.Append(" FROM  JC_Leaves a left join aspnet_Membership b on a.UserID=b.UserId left join JC_GradeInfos c on a.GradeID=c.ID left join JC_ClassInfos d on a.ClassID=d.ID");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append("group by  GradeName,ClassName,CName,UserName,a.UserID,c.OrderId,d.OrderId");
            strSql.Append(" order by c.OrderId,d.OrderId,CName");
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得数据
        /// </summary>
        public DataSet GetListByUserId(Guid userId, DateTime staDate, DateTime endDate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  a.*,AreaName,C.UserName,C.CName,DictText,M.CName AS PName ");
            strSql.Append(" FROM  JC_Leaves a left join BM_Areas b on a.SchoolColumnID=b.ColumnID left join aspnet_Membership c on a.UserId=c.UserID  left join  Site_Dictionary s on a.LeaveTypeCode=s.DictValue and s.DictTypeId=64  left join aspnet_Membership m on a.IsPassBy=m.UserId");
            strSql.Append(" where a.UserId='" + userId + "' AND BeginTime>='" + staDate.ToString("yyyy-MM-dd") + "' AND EndTime<'" + endDate.AddDays(1).ToString("yyyy-MM-dd") + "' AND IsPass=1  ");
            strSql.Append(" order by begintime desc");
            return DbHelperSQL.Query(strSql.ToString());
        }
        #endregion  ExtensionMethod
    }
}

