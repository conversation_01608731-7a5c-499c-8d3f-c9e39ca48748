﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 留言表
	/// </summary>
	[Serializable]
	public partial class ecb_GuestBook
	{
		public ecb_GuestBook()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _senduserid;
		private Guid _receiveuserid;
		private string _content;
		private DateTime _sendtime;
        private int _msgtype;
        private int _timespan;
        /// <summary>
        /// 
        /// </summary>
        public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 发送人员
		/// </summary>
		public Guid SendUserId
		{
			set{ _senduserid=value;}
			get{return _senduserid;}
		}
		/// <summary>
		/// 接收人员
		/// </summary>
		public Guid ReceiveUserId
		{
			set{ _receiveuserid=value;}
			get{return _receiveuserid;}
		}
		/// <summary>
		/// 内容
		/// </summary>
		public string Content
		{
			set{ _content=value;}
			get{return _content;}
		}
		/// <summary>
		/// 发送时间
		/// </summary>
		public DateTime SendTime
		{
			set{ _sendtime=value;}
			get{return _sendtime;}
		}
        /// <summary>
        /// 留言类型 0 语音  1文字 
        /// </summary>
        public int MsgType
        {
            set { _msgtype = value; }
            get { return _msgtype; }
        }
        /// <summary>
        /// 语音时长 单位 s 秒
        /// </summary>
        public int TimeSpan
        {
            set { _timespan = value; }
            get { return _timespan; }
        }
        #endregion Model

    }
}

