﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    public partial class ecb_class_honor_attachement
    {
        public ecb_class_honor_attachement()
        { }
        #region  Method

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from [ecb_class_honor_attachement]");
            strSql.Append(" where ID=@ID ");

            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_class_honor_attachement model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into [ecb_class_honor_attachement] (");
            strSql.Append("ID,HonorCode,FileName,FilePath,UploadDate,SchoolColumnID,SchoolColumnPath)");
            strSql.Append(" values (");
            strSql.Append("@ID,@HonorCode,@FileName,@FilePath,@UploadDate,@SchoolColumnID,@SchoolColumnPath)");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@HonorCode", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@FileName", SqlDbType.NVarChar,50),
					new SqlParameter("@FilePath", SqlDbType.NVarChar,255),
					new SqlParameter("@UploadDate", SqlDbType.DateTime),
					new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,50)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.HonorCode;
            parameters[2].Value = model.FileName;
            parameters[3].Value = model.FilePath;
            parameters[4].Value = model.UploadDate;
            parameters[5].Value = model.SchoolColumnID;
            parameters[6].Value = model.SchoolColumnPath;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_class_honor_attachement model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update [ecb_class_honor_attachement] set ");
            strSql.Append("HonorCode=@HonorCode,");
            strSql.Append("FileName=@FileName,");
            strSql.Append("FilePath=@FilePath,");
            strSql.Append("UploadDate=@UploadDate,");
            strSql.Append("SchoolColumnID=@SchoolColumnID,");
            strSql.Append("SchoolColumnPath=@SchoolColumnPath");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@HonorCode", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@FileName", SqlDbType.NVarChar,50),
					new SqlParameter("@FilePath", SqlDbType.NVarChar,255),
					new SqlParameter("@UploadDate", SqlDbType.DateTime),
					new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,50),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.HonorCode;
            parameters[1].Value = model.FileName;
            parameters[2].Value = model.FilePath;
            parameters[3].Value = model.UploadDate;
            parameters[4].Value = model.SchoolColumnID;
            parameters[5].Value = model.SchoolColumnPath;
            parameters[6].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid HonorCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from [ecb_class_honor_attachement] ");
            strSql.Append(" where HonorCode=@HonorCode ");
            SqlParameter[] parameters = {
					new SqlParameter("@HonorCode", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = HonorCode;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_class_honor_attachement GetModel(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,HonorCode,FileName,FilePath,UploadDate,SchoolColumnID,SchoolColumnPath ");
            strSql.Append(" FROM [ecb_class_honor_attachement] ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = ID;

            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        public Model.ecb_class_honor_attachement DataRowToModel(DataRow row)
        {
            Model.ecb_class_honor_attachement model = new Model.ecb_class_honor_attachement();
            if (row != null)
            {
                if (row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["HonorCode"] != null && row["HonorCode"].ToString() != "")
                {
                    model.HonorCode = new Guid(row["HonorCode"].ToString());
                }
                if (row["FileName"] != null)
                {
                    model.FileName = row["FileName"].ToString();
                }
                if (row["FilePath"] != null)
                {
                    model.FilePath = row["FilePath"].ToString();
                }
                if (row["UploadDate"] != null && row["UploadDate"].ToString() != "")
                {
                    model.UploadDate = DateTime.Parse(row["UploadDate"].ToString());
                }
                if (row["SchoolColumnID"] != null && row["SchoolColumnID"].ToString() != "")
                {
                    model.SchoolColumnID = int.Parse(row["SchoolColumnID"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM [ecb_class_honor_attachement] ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        #endregion  Method
    }
}
