﻿using ICSharpCode.SharpZipLib.Zip;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.DBUtility;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_testusers_list : YunEdu.Authority.AdminCommonJC
    {

        string id;
        string tp;
        string strWhere;
        YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();

        YunEdu.BLL.PKC_Rooms bllrooms = new YunEdu.BLL.PKC_Rooms();
        YunEdu.Model.PKC_Rooms modelrooms = new YunEdu.Model.PKC_Rooms();

        YunEdu.BLL.PKC_TestUsers blltestusers = new YunEdu.BLL.PKC_TestUsers();
        YunEdu.Model.PKC_TestUsers modeltestusers = new YunEdu.Model.PKC_TestUsers();

        YunEdu.BLL.JC_StudentInfos bllstudentinfos = new YunEdu.BLL.JC_StudentInfos();
        YunEdu.Model.JC_StudentInfos modelstudentinfos = new YunEdu.Model.JC_StudentInfos();

        YunEdu.BLL.JC_SchoolInfos bllJC_SchoolInfos = new YunEdu.BLL.JC_SchoolInfos();
        YunEdu.Model.JC_SchoolInfos modelJC_SchoolInfos = new YunEdu.Model.JC_SchoolInfos();

        YunEdu.BLL.JC_GradeInfos bllJC_GradeInfos = new YunEdu.BLL.JC_GradeInfos();
        YunEdu.Model.JC_GradeInfos modelJC_GradeInfos = new YunEdu.Model.JC_GradeInfos();

        YunEdu.BLL.JC_ClassInfos bllJC_ClassInfos = new YunEdu.BLL.JC_ClassInfos();
        YunEdu.Model.JC_ClassInfos modelJC_ClassInfos = new YunEdu.Model.JC_ClassInfos();

        BLL.Site_Dictionary bllDictionary = new BLL.Site_Dictionary();

        YunEdu.BLL.JC_Message bllmessage = new YunEdu.BLL.JC_Message();
        YunEdu.BLL.JC_MessageRead bllmessageread = new YunEdu.BLL.JC_MessageRead();


        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();

        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();

        protected void Page_Load(object sender, EventArgs e)
        {
            GetArea();
            if (Request.QueryString["id"] != null)
            {
                id = Request.QueryString["id"].ToString();
                tp = Request.QueryString["tp"].ToString();
            }
            if (!IsPostBack)
            {
                InitControls();
                BindData();
                int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 1);
                int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is null and PropertyCode=" + 2);
                //if (Art != 0 && Math != 0)
                //{
                //    MessageBox.Show(this, "友情提示：检测到你是分文理排考场的，建议你先试排一次考场，如果文科的最后只有一小部分学生在一个考场，可以平均增加前面考场布局容量来排这些多出来的考生，同理，理科也是一样，确保一个考场不会空出太多的位子");
                //}
            }
        }
        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }
        //返回
        protected void btnBack_Click(object sender, EventArgs e)
        {
            switch (tp)
            {
                case "users":
                    Response.Redirect("pkc_tests_list.aspx?MenuId=" + Request.QueryString["MenuId"]);
                    break;
                case "pkc":
                    Response.Redirect("pkc_tests_list.aspx?MenuId=" + Request.QueryString["MenuId"]);
                    break;
                case "headmaster":
                    Response.Redirect("pkc_headmaster_search.aspx?MenuId=" + Request.QueryString["MenuId"]);
                    break;
            }

        }
        /// <summary>
        /// 获取查询条件
        /// </summary>
        /// <returns></returns>
        private string GetWhere()
        {
            StringBuilder sbWhere = new StringBuilder();



            string strColumnId = string.Empty;
            string strColumnPath = string.Empty;
            //属于本次考试
            if (Request.QueryString["id"] != null)
            {
                sbWhere.Append("TestId='" + Request.QueryString["id"].ToString() + "'");
                // 第一次加载
                //if (!IsPostBack)
                //{
                //    strColumnId = modelAreaUser.ColumnID.ToString();
                //    strColumnPath = modelAreaUser.ColumnPath;
                //}
                //else
                //{
                // 是否选择附属学校
                if (!string.IsNullOrEmpty(hidChildSchool.Value))
                {
                    modelArea = bllArea.GetModel(int.Parse(hidChildSchool.Value));
                }
                else if (!string.IsNullOrEmpty(hidSchoolId.Value))
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                }
                if (modelArea != null && modelArea.ColumnID != 0)
                {
                    strColumnId = modelArea.ColumnID.ToString();
                    strColumnPath = modelArea.ColumnPath;
                    sbWhere.Append(" and (b.ColumnId=" + strColumnId + " or b.ColumnPath like '" + strColumnPath + "|%')");
                }
                //}

                //if (!string.IsNullOrEmpty(hidTermId.Value))
                //{
                //    if (sbWhere.Length > 0)
                //    {
                //        sbWhere.Append(" and ");
                //    }
                //    sbWhere.Append("b.TermId='" + hidTermId.Value + "'");
                //}
                if (!string.IsNullOrEmpty(hidGradeId.Value))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    sbWhere.Append(" b.GradeId='" + hidGradeId.Value + "'");
                }

                if (tp == "headmaster")
                {
                    string classid = YunEdu.Common.GetRecordByPageOrder.GetModelField("JC_ClassInfos", "ID", "SchoolId='" + modelAreaUser.ColumnID + "'and TeacherNo='" + UserName + "'");
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    sbWhere.Append(" b.ClassId='" + classid + "'");
                }

                if (!string.IsNullOrEmpty(hidClassId.Value))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    sbWhere.Append(" b.ClassId='" + hidClassId.Value + "'");
                }
                if (!string.IsNullOrEmpty(this.ddlRoom.SelectedValue) && !this.ddlRoom.SelectedValue.ToString().Equals("0"))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    sbWhere.Append(" a.RoomId='" + this.ddlRoom.SelectedValue.ToString() + "'");
                }
                if (!string.IsNullOrEmpty(this.dorpShowSize.SelectedValue) && !this.dorpShowSize.SelectedValue.ToString().Equals("0"))
                {
                    AspNetPager1.PageSize = int.Parse(this.dorpShowSize.SelectedValue);
                }
                if (!string.IsNullOrEmpty(this.ddlPropertyCode.SelectedValue) && !this.ddlPropertyCode.SelectedValue.ToString().Equals("0"))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    sbWhere.Append(" a.PropertyCode='" + this.ddlPropertyCode.SelectedValue.ToString() + "'");
                }

                if (!string.IsNullOrEmpty(this.dropType.SelectedValue) && !this.dropType.SelectedValue.ToString().Equals("0"))
                {
                    if (sbWhere.Length > 0)
                    {
                        sbWhere.Append(" and ");
                    }
                    string _text = YunEdu.Common.StringPlus.Filter(this.inputName.Text, "html");
                    switch (this.dropType.SelectedValue)
                    {
                        case "1":
                            sbWhere.Append(" b.StudentName like '%" + this.inputName.Text + "%'");
                            break;
                        case "2":
                            sbWhere.Append(" b.StudentCode like '%" + this.inputName.Text + "%'");
                            break;
                        default:
                            break;
                    }
                }
            }
            else
            {
                sbWhere.Append("1<>1");
            }
            return sbWhere.ToString();
        }
        /// <summary>
        /// 获取数据并绑定
        /// </summary>
        private void BindData(bool isMain = false)
        {
            this.txtPageNum.Text = this.AspNetPager1.PageSize.ToString();
            strWhere = GetWhere();

            DataTable dt = new DataTable();
            AspNetPager1.RecordCount = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers a left join JC_StudentInfos b on a.UserId=b.ID", strWhere, isMain ? DbHelperSQL.ConnMain : null);
            modeltests = blltests.GetModel(new Guid(id));
            dt.Columns.Add("TestUserId");
            dt.Columns.Add("StudentName");
            dt.Columns.Add("StudentCode");
            //dt.Columns.Add("SchoolName");
            dt.Columns.Add("PropertyName");
            dt.Columns.Add("ClassName");
            dt.Columns.Add("TestTitle");
            dt.Columns.Add("TicketNumber");
            dt.Columns.Add("RoomNum");
            dt.Columns.Add("SeatNumber");
            dt.Columns.Add("RoomLocation");
            dt.Columns.Add("AbilityValue");
            foreach (DataRow itemTestUsers in YunEdu.Common.GetRecordByPageOrder.GetList("PKC_TestUsers a left join JC_StudentInfos b on a.UserId=b.ID", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "a.*,b.ColumnId,b.ColumnPath,b.GradeID,b.ClassID", strWhere, " TicketNumber asc,AbilityValue desc ", isMain ? DbHelperSQL.ConnMain : null).Tables[0].Rows)
            {
                DataRow dr = dt.NewRow();
                dr["TestTitle"] = modeltests.TestTitle;
                dr["TicketNumber"] = itemTestUsers["TicketNumber"].ToString();
                dr["TestUserId"] = itemTestUsers["TestUserId"].ToString();
                dr["SeatNumber"] = itemTestUsers["SeatNumber"].ToString();
                dr["AbilityValue"] = itemTestUsers["AbilityValue"].ToString();
                if (!string.IsNullOrEmpty(itemTestUsers["PropertyCode"].ToString()))
                {
                    dr["PropertyName"] = YunEdu.Common.GetRecordByPageOrder.GetModelField("Site_Dictionary", "DictText", "DictTypeId=48 and DictValue=" + itemTestUsers["PropertyCode"].ToString());
                }
                modelrooms = bllrooms.GetModel(new Guid(itemTestUsers["RoomId"].ToString()));
                if (modelrooms != null)
                {
                    dr["RoomNum"] = modelrooms.RoomNum;
                    dr["RoomLocation"] = modelrooms.RoomLocation;
                }
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(itemTestUsers["UserId"].ToString()));
                dr["StudentName"] = modelstudentinfos.StudentName;
                dr["StudentCode"] = modelstudentinfos.StudentCode;
                //modelJC_SchoolInfos = bllJC_SchoolInfos.GetModel(int.Parse(modelstudentinfos.ColumnId.ToString()));
                //dr["SchoolName"] = modelJC_SchoolInfos.SchoolName;
                modelJC_GradeInfos = bllJC_GradeInfos.GetModel(modelstudentinfos.GradeID);
                //dr["GradeName"] = modelJC_GradeInfos.GradeName;
                modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);
                dr["ClassName"] = modelJC_ClassInfos.ClassName;
                dt.Rows.Add(dr);
            }
            switch (tp)
            {
                case "users":
                    pnlroom.Visible = false;
                    pnlAbility.Visible = false;
                    btnPKC.Visible = false;
                    pnlExpor.Visible = false;
                    gvTestUsersList.Columns[9].Visible = false;
                    gvTestUsersList.Columns[5].Visible = false;
                    gvTestUsersList.Columns[6].Visible = false;
                    gvTestUsersList.Columns[7].Visible = false;
                    gvTestUsersList.Columns[8].Visible = false;
                    break;
                case "pkc":
                    spnMessage.Visible = true;
                    btnImport.Visible = false;
                    btnDel.Visible = false;
                    gvTestUsersList.Columns[10].Visible = false;
                    break;
                case "headmaster":
                    pnlclass.Visible = false;
                    pnlButtons.Visible = false;
                    pnlHeadmaster.Visible = true;
                    gvTestUsersList.Columns[10].Visible = false;
                    break;


            }
            gvTestUsersList.DataSource = dt;
            gvTestUsersList.DataBind();

        }


        /// <summary>
        /// 绑定年级信息
        /// </summary>
        /// <param name="schoolId">学校ID</param>
        /// <param name="selectedValue"></param>
        private void BindGradeInfos(string schoolid, string selectedValue = "")
        {
            YunEdu.BLL.JC_GradeInfos bllGrade = new YunEdu.BLL.JC_GradeInfos();
            DataSet dsGradeInfos = new DataSet();
            // 管理员
            if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()))//全部显示
            {
                dsGradeInfos = new YunEdu.BLL.JC_GradeInfos().GetGradeBySchoolYear(schoolid, ModelTermInfos(schoolid).SchoolYear);
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsGradeInfos = new YunEdu.BLL.JC_Subjects().GetTeachGrade(Int32.Parse(schoolid), UserName);
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModel(new Guid(UserId));
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsGradeInfos = new YunEdu.BLL.JC_GradeInfos().GetGradeBySchoolYear(schoolid, ModelTermInfos(schoolid).SchoolYear);
            }
            // 绑定年级信息列表
            if (dsGradeInfos != null && dsGradeInfos.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlGrade, dsGradeInfos.Tables[0], "", "GradeName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    ddlGrade.SelectedValue = selectedValue;
                    hidGradeId.Value = selectedValue;
                    BindClassInfos(new Guid(selectedValue), hidClassId.Value);
                }
            }
        }

        /// <summary>
        /// 绑定班级信息
        /// </summary>
        /// <param name="gradeId"></param>
        /// <param name="selectedValue"></param>
        private void BindClassInfos(Guid gradeId, string selectedValue = "")
        {
            YunEdu.BLL.JC_ClassInfos bllClass = new YunEdu.BLL.JC_ClassInfos();
            DataSet dsClass = new DataSet();
            if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", gradeId.ToString()), "OrderId");
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsClass = new YunEdu.BLL.JC_Subjects().GetTeachClass(modelAreaUser.ColumnID, UserName, gradeId.ToString());
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }

            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(UserName);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", gradeId.ToString()), "OrderId");
            }
            if (dsClass != null && dsClass.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlClass, dsClass.Tables[0], "", "ClassName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    hidClassId.Value = selectedValue;
                    ddlClass.SelectedValue = selectedValue;
                }
            }
        }

        #region 加载区域信息

        private void GetArea()
        {
            //设区市，绑定，同时，根据用户所处级别，选定当前选择项
            //任何级别的用户都要显示市级
            //hidSchoolId.value默认显示用户所在县区学校
            string strColumnPath = "";

            if (!IsPostBack)
            {
                strColumnPath = modelAreaUser.ColumnPath;
                hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
            }
            else
            {
                if (string.IsNullOrEmpty(hidSchoolId.Value))
                {
                    strColumnPath = modelAreaUser.ColumnPath;
                    hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
                }
                else
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                    if (modelArea == null)
                    {
                        return;
                    }
                    else
                    {
                        strColumnPath = modelArea.ColumnPath;
                    }
                }
            }
            if (string.IsNullOrEmpty(strColumnPath))
            {
                return;
            }
            string[] place = strColumnPath.Split('|');
            bllArea.BindAreaDropDown(S1, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S2, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S3, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(ddlChildSchool, "", IsPostBack, "", "请选择市");
            GetRecordByPageOrder.BindDropDownList(ddlGrade, null, "", "GradeName", "ID", "请选择年级");
            GetRecordByPageOrder.BindDropDownList(ddlClass, null, "", "ClassName", "ID", "请选择班级");
            switch (place.Length)
            {
                case 1:
                    // 通过省查处市级
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, "", "请选择市");
                    break;
                case 2:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, "", "请选择县区");
                    break;
                case 3:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, "", "请选择学校");
                    break;
                case 4:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, "", "请选择学校");
                    if (ddlChildSchool.Items.Count > 1)
                    {
                        ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                        if (!string.IsNullOrEmpty(hidChildSchool.Value))
                        {
                            ddlChildSchool.SelectedValue = hidChildSchool.Value;
                        }
                    }
                    BindGradeInfos(place[3], hidGradeId.Value);
                    break;
                case 5:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    pnlSchool.Visible = false;
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, place[4], "请选择附属学校");
                    ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                    BindGradeInfos(place[4], hidGradeId.Value);
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    break;
                default:
                    break;
            }
        }
        #endregion
        //绑定
        private void InitControls()
        {
            YunEdu.Common.GetRecordByPageOrder.BindColumnList("PKC_Rooms", "RoomId,RoomNum", "RoomNum", "RoomId", "TestId='" + id + "'", "RoomNum asc", ddlRoom, "0", true, "请选择");
            modeltests = blltests.GetModel(new Guid(id));
            YunEdu.Common.GetRecordByPageOrder.BindColumnList("JC_Tests", "DISTINCT TOP 10  ID,TestName,CreateDate", "TestName", "ID", "GradeID='" + modeltests.GradeId.ToString() + "'", "CreateDate desc", ddlTests, "0", true, "选择考试");

            DataTable data = null;
            data = bllDictionary.GetList("DictTypeId=48").Tables[0];
            // 文理
            GetRecordByPageOrder.BindDropDownList(ddlPropertyCode, data, 48);

        }

        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            BindData();
        }
        //设置分页显示数量
        protected void txtPageNum_TextChanged(object sender, EventArgs e)
        {
            int _pagesize;
            if (int.TryParse(txtPageNum.Text.Trim(), out _pagesize))
            {
                this.AspNetPager1.PageSize = _pagesize;
                this.AspNetPager1.CurrentPageIndex = 1;
                BindData();
            }
        }
        //下拉框数据拉取
        //protected void ddlRoom_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    BindData();
        //}
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            BindData(!string.IsNullOrEmpty(Request["__EVENTARGUMENT"]));
		}
        protected void btnDelete_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvTestUsersList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        iCount++;
                        if (!blltestusers.Delete(new Guid(gvTestUsersList.DataKeys[myItem.RowIndex][0].ToString())))
                        {
                            iError++;
                        }
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择要删除的记录!');");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('删除失败!');");
            }
            BindData(true);
        }
        protected void btnDelAll_Click(object sender, EventArgs e)
        {

            if (blltestusers.DeleteByTestId(new Guid(id)))
            {
                MessageBox.ResponseScript(this, "layer.msg('删除成功!');");
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('删除失败!');");
            }
            BindData(true);
        }

        #region 导入考生
        /// <summary>
        /// 导入本次考试考生信息  查询条件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private string GetWhereStudent()
        {
            StringBuilder sbWhere = new StringBuilder();

            modeltests = blltests.GetModel(new Guid(id));
            string columnid = modeltests.ColumnId.ToString();
            string columnpath = modeltests.ColumnPath;
            string termid = modeltests.TermId.ToString();
            string gradeid = modeltests.GradeId.ToString();
            string classid = modeltests.ClassId.ToString();
            string classropertyCode = modeltests.PropertyCode.ToString();

            //string strColumnId = string.Empty;
            //string strColumnPath = string.Empty;

            //// 是否选择附属学校
            //if (!string.IsNullOrEmpty(hidChildSchool.Value))
            //{
            //    modelArea = bllArea.GetModel(int.Parse(hidChildSchool.Value));
            //}
            //else if (!string.IsNullOrEmpty(hidSchoolId.Value))
            //{
            //    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
            //}
            //if (modelArea != null && modelArea.ColumnID != 0)
            //{
            //    strColumnId = modelArea.ColumnID.ToString();
            //    strColumnPath = modelArea.ColumnPath;
            //}

            //sbWhere.Append("(ColumnId=" + columnid + " or ColumnPath like '" + columnpath + "|%')");
            //if (!termid.Equals("00000000-0000-0000-0000-000000000000"))
            //{
            //    if (sbWhere.Length > 0)
            //    {
            //        sbWhere.Append(" and ");
            //    }
            //    sbWhere.Append("TermId='" + termid + "'");
            //}
            if (!gradeid.Equals("00000000-0000-0000-0000-000000000000"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append("GradeId='" + gradeid + "'");
            }
            if (classropertyCode == "1" || classropertyCode == "2")
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append("ClassPropertyCode='" + classropertyCode + "'");
            }

            //学校版，省市县区版需要更改
            if (string.IsNullOrEmpty(sbWhere.ToString()))
            {
                sbWhere.Append("1<>1");
            }
            return sbWhere.ToString();
        }

        /// <summary>
        /// 导入本次考试考生信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnImport_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            string strWhereStudent = GetWhereStudent();
            foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("JC_ClassInfos", 0, "*", strWhereStudent, "ClassPropertyCode asc").Tables[0].Rows)
            {
                try
                {
                    foreach (DataRow itemuser in YunEdu.Common.GetRecordByPageOrder.GetListByVW("JC_StudentInfos", 0, "*", "ClassID='" + item["ID"].ToString() + "'", "CreateDate desc").Tables[0].Rows)
                    {
                        modelstudentinfos = bllstudentinfos.GetModel(new Guid(itemuser["ID"].ToString()));
                        if (modelstudentinfos != null)
                        {
                            int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and " + "UserId='" + itemuser["ID"].ToString() + "'");
                            if (count == 0)
                            {
                                modeltestusers = new YunEdu.Model.PKC_TestUsers();
                                modeltestusers.TestId = modeltests.TestId;
                                modeltestusers.UserId = modelstudentinfos.ID;
                                modeltestusers.MyClassId = modelstudentinfos.ClassID;
                                modeltestusers.PropertyCode = item["ClassPropertyCode"].ToString();
                                int abilityvalue = 0;
                                modeltestusers.AbilityValue = abilityvalue;
                                if (!blltestusers.Add(modeltestusers))
                                {
                                    iError++;
                                }
                                else
                                {
                                    iCount++;
                                }
                            }
                        }

                    }
                }
                catch (Exception)
                {
                    continue;
                }
            }
            //没有导入学生 并且考生表没有学生
            if (iCount == 0 && YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' ") == 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('未导入考生，请前往学生管理查看当前考试的年级是否存在学生!');");
                return;
            }
            else if (iCount == 0 && YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' ") > 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('导入成功！请进入第4步：考场管理!');");
                return;
            }
            else if (iError == 0)
            {
                BindData(true);
                MessageBox.ResponseScript(this, "layer.msg('导入成功！请进入第4步：考场管理!');");
                return;
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('导入失败!');");
                return;
            }
        }

        #endregion

        #region 排考场算法
        protected void btnPKC_Click(object sender, EventArgs e)
        {

            int allnumber = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "'");
            int amount = 0;
            foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "RoomLayout", "TestId='" + id + "'and IsEnabled = 1", "RoomNum asc").Tables[0].Rows)
            {
                try
                {
                    string layout = item["RoomLayout"].ToString();
                    string[] _tempLayouts = layout.Split('|');
                    int[] a = new int[_tempLayouts.Length];
                    for (int i = 0; i < _tempLayouts.Length; i++)
                    {
                        a[i] = Convert.ToInt32(_tempLayouts[i]);
                    }
                    for (int i = 0; i < a.Length; i++)
                    {
                        amount += a[i];
                    }
                }
                catch (Exception)
                {
                    continue;
                }
            }
            if (allnumber <= amount)
            {
                if (id != null)
                {
                    PaiKaoChang(id);
                    BindData(true);
                }
            }
            else
            {
                int lacknum = allnumber - amount;
                MessageBox.ResponseScript(this, "layer.msg('考场座位总数小于考试人数，缺少" + lacknum + "个座位!');");
            }
        }
        public int ticketNum = 1;
        private void PaiKaoChang(string _id)
        {
            modeltests = blltests.GetModel(new Guid(_id));
            string testtitle = modeltests.TestTitle;
            string gradeid = modeltests.GradeId.ToString();
            DateTime createTime = DateTime.Now;
            bool RoomFull = false;//判断考场是否过多

            //1 获取考场布局
            //2 利用数据库排序
            //3 将条件排序完的第一条数据读取存放
            //4 重复2,3

            //DataSet dsRooms = YunEdu.Common.GetRecordByPageOrder.GetList("PKC_Rooms", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "", "RoomId,RoomType,RoomLayout", "TestId='" + _id + "'", "RoomNum desc");
            DataSet dsRooms = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "RoomId,RoomType,RoomLayout", "TestId='" + _id + "' and IsEnabled = 1", " RoomNum");
            //找出最大列
            foreach (DataRow item1 in dsRooms.Tables[0].Rows)
            {
                if (RoomFull) return;
                string layout = item1["RoomLayout"].ToString();
                string s = "s";

                if ((int)item1["RoomType"] == 1)
                {
                    s = "s";
                }
                if ((int)item1["RoomType"] == 2)
                {
                    s = "c";
                }
                //找出最大列
                string[] _tempLayouts = layout.Split('|');
                int[] a = new int[_tempLayouts.Length];
                int MaxRow = 0;
                for (int i = 0; i < _tempLayouts.Length; i++)
                {
                    a[i] = Convert.ToInt32(_tempLayouts[i]);
                }
                for (int i = 0; i < _tempLayouts.Length; i++)
                {
                    if (MaxRow < a[i])
                    {
                        MaxRow = a[i];
                    }
                }

                //根据考场序号，从数据库读出一个的类型
                //对这个考场进行排布，循环多次就完成多个考场的排布
                string roomid = item1["RoomId"].ToString();
                string ExistId = "'" + new Guid().ToString() + "'";
                //修改考场已被使用
                modelrooms = bllrooms.GetModel(new Guid(roomid));
                modelrooms.IsUsing = true;
                bllrooms.Update(modelrooms);

                DataSet dsExistId = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "UserId", "TestId='" + _id + "'and isnull(TicketNumber,'')<>''");
                //if (dsExistId.Tables[0].Rows.Count != 0)
                //{
                //    foreach (DataRow item2 in dsExistId.Tables[0].Rows)
                //    {
                //        ExistId += ",'" + item2["UserId"] + "'";
                //    }
                //}
                //按上次分数编排
                if (true)
                {
                    if (s == "s")
                        RoomFull = PaiKaoChangS(_tempLayouts.Length, MaxRow, roomid, createTime, RoomFull);
                    else
                        RoomFull = PaiKaoChangC(_tempLayouts.Length, MaxRow, a, roomid, createTime, RoomFull);
                }
                else
                {

                }
            }


        }

        /* 
         * 列，行，布局数组，考场id，创建时间，是否排完
         * */

        public bool PaiKaoChangC(int MaxColum, int MaxRow, int[] a, string roomid, DateTime createTime, bool IsOver)
        {
            int SeatNum = 1;
            bool IsNull = false;//判断考生是否已全部排完
            string ExistId = string.Empty;
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];
            for (int i = 0; i < MaxColum; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一列
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = SeatNum.ToString();
                        SeatNum += 1;
                    }
                    else return IsOver = true;//所有学生都已经排完了

                    ExistId += ",'" + sum[0, 1] + "'";

                    for (int j = 1; j < MaxRow; j++)
                    {
                        if (j < a[i])//小于实际列长度，插入学生
                        {
                            DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            //如果只剩下一个班
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else//只剩下一个班
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else
                    if (i % 2 != 0)//判断偶数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow >= MaxRow - a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")//第二列第一个左侧无人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            }
                            else
                                if (sum[j - 1, 0] == "0")//上一个没有人
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            }
                            else
                            {
                                if (sum[j - count, 0] == "0")//左侧没有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                               GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                                }
                                else//左侧和上一个位置都有人
                                {
                                    dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                                }
                            }
                            if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                SeatNum += 1;
                                ExistId += ",'" + sum[j, 1] + "'";
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                                if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
                else//第一列以外的奇数列
                {
                    for (int j = i * MaxRow, count = 1; j < (i + 1) * MaxRow; j++, count += 2)
                    {
                        if (j - i * MaxRow < a[i])
                        {
                            DataSet dsa;
                            if (sum[j - 1, 0] == "0" && sum[j - count, 0] == "0")
                            {
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            }
                            else
                                if (sum[j - 1, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - count, 0] + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            else
                                    if (sum[j - count, 0] == "0")
                                dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId <>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            else dsa = YunEdu.Common.GetRecordByPageOrder.
                                                GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            if (dsa.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                        GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                                if (dsb.Tables[0].Rows.Count != 0)
                                {
                                    sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                    sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                    sum[j, 2] = SeatNum.ToString();
                                    ExistId += ",'" + sum[j, 1] + "'";
                                    SeatNum += 1;
                                }
                                else
                                {
                                    IsNull = true;
                                    IsOver = true;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            sum[j, 0] = 0 + "";
                            sum[j, 1] = 0 + "";
                            sum[j, 2] = 0 + "";
                        }

                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        //这场考试的学生id
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;
                        blltestusers.Update(modeltestusers);
                    }
                }
                else return IsOver = true;
            }
            return IsOver;
        }

        /* 
         * 列，行，考场id，创建时间，是否排完
         * */
        public bool PaiKaoChangS(int MaxColum, int MaxRow, string roomid, DateTime createTime, bool IsOver)
        {
            int SeatNum = 1;//每个考场的座位号
            bool IsNull = false;
            string ExistId = string.Empty;
            //确定存放已排好考生的数组大小
            string[,] sum = new string[MaxRow * MaxColum, 3];

            for (int i = 0; i < MaxRow; i++)
            {
                if (IsNull) break;
                if (i == 0)//判断第一行
                {
                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        sum[0, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                        sum[0, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                        sum[0, 2] = "" + SeatNum;
                        ExistId += ",'" + sum[0, 1] + "'";
                        SeatNum += 1;
                    }
                    else return IsOver = true;
                    for (int j = 1; j < MaxColum; j++)
                    {
                        DataSet dsa = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId<>'" + sum[j - 1, 0] + "' and UserId not in(" + ExistId + ")  and isnull(TicketNumber,'')=''", "AbilityValue desc");
                        if (dsa != null && dsa.Tables.Count != 0 && dsa.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = dsa.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = dsa.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum;
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
                else
                    if (i % 2 != 0)//判断偶数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                        if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            if (dsb != null && dsb.Tables.Count != 0 && dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
                else//第一行以外的奇数行
                {
                    for (int j = i * MaxColum, count = 1; j < (i + 1) * MaxColum; count += 2, j++)
                    {
                        DataSet ds = YunEdu.Common.GetRecordByPageOrder.
                                            GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and MyClassId not in('" + sum[j - 1, 0] + "','" + sum[j - count, 0] + "') and UserId not in(" +
                                            ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                        if (ds.Tables[0].Rows.Count != 0)
                        {
                            sum[j, 0] = ds.Tables[0].Rows[0]["MyClassId"].ToString();
                            sum[j, 1] = ds.Tables[0].Rows[0]["UserId"].ToString();
                            sum[j, 2] = "" + SeatNum.ToString();
                            ExistId += ",'" + sum[j, 1] + "'";
                            SeatNum += 1;
                        }
                        else
                        {
                            DataSet dsb = YunEdu.Common.GetRecordByPageOrder.
                                    GetListByVW("PKC_TestUsers", 1, "MyClassId,UserId", "TestId='" + id + "' and UserId not in(" + ExistId + ") and isnull(TicketNumber,'')=''", "AbilityValue desc");
                            if (dsb.Tables[0].Rows.Count != 0)
                            {
                                sum[j, 0] = dsb.Tables[0].Rows[0]["MyClassId"].ToString();
                                sum[j, 1] = dsb.Tables[0].Rows[0]["UserId"].ToString();
                                sum[j, 2] = SeatNum.ToString();
                                ExistId += ",'" + sum[j, 1] + "'";
                                SeatNum += 1;
                            }
                            else
                            {
                                IsNull = true;
                                IsOver = true;
                                break;
                            }
                        }
                    }
                }
            }
            for (int i = 0; i < MaxColum * MaxRow; i++)
            {
                if (sum[i, 0] != null)
                {
                    if (sum[i, 1].ToString() != "0")
                    {
                        string testuserid = YunEdu.Common.GetRecordByPageOrder.GetModelField("PKC_TestUsers", "TestUserId", "TestId=" + "'" + id + "'" + "and UserId='" + sum[i, 1] + "'").ToString();

                        string n;
                        if (ticketNum > 10000) n = ticketNum.ToString();
                        else n = ticketNum.ToString().PadLeft(5, '0');

                        modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                        modeltestusers.SeatNumber = int.Parse(sum[i, 2]);
                        modeltestusers.RoomId = new Guid(roomid);
                        modeltestusers.CreateTime = createTime;
                        modeltestusers.TicketNumber = DateTime.Parse(modeltests.TestStartDate.ToString()).ToString("yyyyMMdd") + n;
                        ticketNum += 1;

                        blltestusers.Update(modeltestusers);
                    }
                }
                else return IsOver = true;
            }
            return IsOver;
        }

        #endregion

        #region 方法

        //下载文件
        private void DownLoadFile(string customerFileName, string filePath, string fileType)
        {
            // 下载文件
            //Response.Clear();
            //Response.ContentType = fileType;
            //Response.AddHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(customerFileName, System.Text.Encoding.UTF8));
            //Response.TransmitFile(filePath);
            FileInfo fileInfo = new FileInfo(filePath);
            Response.Clear();
            Response.ClearContent();
            Response.ClearHeaders();
            Response.AddHeader("Content-Disposition", "attachment;filename=" + customerFileName);
            Response.AddHeader("Content-Length", fileInfo.Length.ToString());
            Response.AddHeader("Content-Transfer-Encoding", "binary");
            Response.ContentType = "application/octet-stream";
            Response.ContentEncoding = System.Text.Encoding.GetEncoding("gb2312");
            Response.WriteFile(fileInfo.FullName);
            Response.Flush();
            Response.End();
        }

        //导出每个考场的考生信息
        private void ExportToWordByXml(System.Data.DataTable dt, DataRow dw, string strFileName, string testtitle)
        {

            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/roominfos.xml");

            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            rootWord.CreateXmlDom(xmlPath);//加载xml

            ArrayList sectList = new ArrayList();
            Dictionary<string, string> bookmarks = new Dictionary<string, string>();
            bookmarks.Add("座位号", "SeatNumber");
            bookmarks.Add("姓名", "UserName");
            bookmarks.Add("准考证号", "TicketNumber");
            bookmarks.Add("班级", "ClassName");
            bookmarks.Add("备注", "BeiZhu");

            #region 动态生成word文档
            Dictionary<string, string> wordTexts = new Dictionary<string, string>();
            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            myWordHelper.CreateXmlDom(xmlPath);

            //替换标签
            wordTexts.Clear();
            wordTexts.Add("TestTitle", testtitle);
            wordTexts.Add("RoomNum", dw["RoomNum"].ToString());
            wordTexts.Add("StuCount", dw["UserId"].ToString());
            wordTexts.Add("RoomLayout", dw["RoomLayout"].ToString());
            wordTexts.Add("RoomName", dw["RoomTitle"].ToString());
            wordTexts.Add("RoomLocation", dw["RoomLocation"].ToString());

            myWordHelper.ReplaceNodeText(wordTexts);

            //获取数据源

            DataColumn countColumn = new DataColumn();
            countColumn.DataType = System.Type.GetType("System.String");
            countColumn.ColumnName = "ClassName";
            dt.Columns.Add(countColumn);

            DataColumn countColumn2 = new DataColumn();
            countColumn2.DataType = System.Type.GetType("System.String");
            countColumn2.ColumnName = "BeiZhu";
            dt.Columns.Add(countColumn2);


            YunEdu.BLL.JC_ClassInfos bllclassinfos = new YunEdu.BLL.JC_ClassInfos();
            YunEdu.Model.JC_ClassInfos modelclassinfos = new YunEdu.Model.JC_ClassInfos();
            foreach (DataRow row in dt.Rows)
            {
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(row["UserId"].ToString()));
                modelclassinfos = bllclassinfos.GetModel(modelstudentinfos.ClassID);

                row["ClassName"] = modelclassinfos.ClassName.ToString();
                row["BeiZhu"] = "";
            }
            myWordHelper.ReplaceNodeTable(dt, bookmarks);

            sectList.Add(myWordHelper.GetDocSection());
            #endregion

            rootWord.MerginDoc(sectList);//合并word文档内容
            rootWord.Save(strFileName);//保存文档
        }

        //布局
        private void ExportLayoutToWordByXmls(string strFileName)
        {

            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/layouts.xml");
            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            rootWord.CreateXmlDom(xmlPath);//加载xml


            ArrayList sectList = new ArrayList();

            int Art = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is not null and PropertyCode=" + 1);
            int Math = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and SeatNumber is not null and PropertyCode=" + 2);
            string roomname;
            modeltests = blltests.GetModel(new Guid(id));
            if (Art != 0 && Math != 0)
            {
                for (int i = 1; i <= 2; i++)
                {
                    foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "distinct RoomId", "TestId='" + id + "' and PropertyCode=" + i, "").Tables[0].Rows)
                    {
                        myWordHelper.CreateXmlDom(xmlPath);//加载xml
                        modelrooms = bllrooms.GetModel(new Guid(item["RoomId"].ToString()));

                        if (i == 1)
                        {
                            roomname = "第" + modelrooms.RoomNum + "考场(文科)";
                        }
                        else
                        {
                            roomname = "第" + modelrooms.RoomNum + "考场(理科)";
                        }

                        DataTable dt = ShowRoomLayout(modelrooms);
                        Dictionary<string, string> wordTexts = new Dictionary<string, string>();
                        wordTexts.Clear();
                        wordTexts.Add("TestTitle", modeltests.TestTitle);
                        wordTexts.Add("RoomName", roomname);
                        wordTexts.Add("RoomLocation", modelrooms.RoomLocation);
                        myWordHelper.ReplaceNodeText(wordTexts);

                        Dictionary<string, DataTable> wordTable = new Dictionary<string, DataTable>();
                        wordTable.Clear();
                        wordTable.Add("pkc_layout", dt);
                        myWordHelper.SetNodeTable(wordTable);
                        sectList.Add(myWordHelper.GetDocSection());
                    }
                }
            }
            else
            {
                foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "*", "TestId='" + id + "' and IsUsing=1", "RoomNum").Tables[0].Rows)
                {
                    myWordHelper.CreateXmlDom(xmlPath);//加载xml
                    modelrooms = bllrooms.GetModel(new Guid(item["RoomId"].ToString()));

                    roomname = "第" + modelrooms.RoomNum + "考场";
                    DataTable dt = ShowRoomLayout(modelrooms);

                    Dictionary<string, string> wordTexts = new Dictionary<string, string>();
                    wordTexts.Clear();
                    wordTexts.Add("TestTitle", modeltests.TestTitle);
                    wordTexts.Add("RoomName", roomname);
                    wordTexts.Add("RoomLocation", modelrooms.RoomLocation);
                    myWordHelper.ReplaceNodeText(wordTexts);

                    Dictionary<string, DataTable> wordTable = new Dictionary<string, DataTable>();
                    wordTable.Clear();
                    wordTable.Add("pkc_layout", dt);
                    myWordHelper.SetNodeTable(wordTable);
                    sectList.Add(myWordHelper.GetDocSection());
                }
            }



            rootWord.MerginDoc(sectList);//合并word文档内容
            rootWord.Save(strFileName);//保存文档
        }
        //导出每个班的学生分布信息
        private void ExportStuAssginToWordByXml(string docPath, YunEdu.Model.JC_ClassInfos modelclass)
        {
            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/classtestinfos.xml");

            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            rootWord.CreateXmlDom(xmlPath);//加载xml

            ArrayList sectList = new ArrayList();

            #region 动态生成word文档

            Dictionary<string, string> wordTexts = new Dictionary<string, string>();
            Dictionary<string, DataTable> wordTable = new Dictionary<string, DataTable>();

            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            myWordHelper.CreateXmlDom(xmlPath);

            DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetModel("JC_StudentInfos", "ID", "ClassID='" + modelclass.ID + "'");

            modeltests = blltests.GetModel(new Guid(id));

            //替换标签
            wordTexts.Clear();
            wordTexts.Add("ClassName", modelclass.ClassName);
            wordTexts.Add("TestTitle", modeltests.TestTitle);
            wordTexts.Add("StuCount", ds.Tables[0].Rows.Count.ToString());
            wordTexts.Add("StartDate", modeltests.TestStartDate.ToString());
            wordTexts.Add("EndDate", modeltests.TestEndDate.ToString());

            myWordHelper.ReplaceNodeText(wordTexts);


            //获取数据源
            System.Data.DataTable dt = MakeNamesTable(ds);

            DataView dv = dt.DefaultView;
            dv.Sort = "准考证号 asc";
            dt = dv.ToTable();

            Dictionary<string, string> bookmarks = new Dictionary<string, string>();
            foreach (DataColumn column in dt.Columns)
            {
                bookmarks.Add(column.ColumnName, column.ColumnName);
            }
            myWordHelper.ReplaceNodeTable(dt, bookmarks);

            DataTable dts = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "OrderNum 场次序号,SubjectName 考试科目,StartTime 开始时间,EndTime 结束时间", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=" + modelclass.ClassPropertyCode + ")", "OrderNum").Tables[0];
            //加入表格
            wordTable.Clear();

            wordTable.Clear();
            wordTable.Add("subjects", dts);
            myWordHelper.SetNodeTable(wordTable);

            sectList.Add(myWordHelper.GetDocSection());

            #endregion

            rootWord.MerginDoc(sectList);//合并word文档内容
            rootWord.Save(docPath);//保存文档
        }

        //导出每个考场的考生信息
        private void ExportRoomAssginToWordByXmls(DataTable dtuser, string docPath, string testid, YunEdu.Model.PKC_Rooms modelrooms, int count)
        {
            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/roominfos.xml");

            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            rootWord.CreateXmlDom(xmlPath);//加载xml

            ArrayList sectList = new ArrayList();

            #region 动态生成word文档

            Dictionary<string, string> wordTexts = new Dictionary<string, string>();
            Dictionary<string, YunEdu.Common.WordMLHelper.WordTable> wordTable = new Dictionary<string, YunEdu.Common.WordMLHelper.WordTable>();

            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            myWordHelper.CreateXmlDom(xmlPath);

            //替换标签
            modeltests = blltests.GetModel(new Guid(testid));
            wordTexts.Clear();
            wordTexts.Add("TestTitle", modeltests.TestTitle);
            wordTexts.Add("RoomNum", modelrooms.RoomNum.ToString());
            wordTexts.Add("StuCount", count.ToString());
            wordTexts.Add("StartDate", modeltests.TestStartDate.ToString());
            wordTexts.Add("EndDate", modeltests.TestEndDate.ToString());
            wordTexts.Add("RoomLayout", modelrooms.RoomLayout.ToString());
            wordTexts.Add("RoomName", modelrooms.RoomTitle.ToString());
            wordTexts.Add("RoomLocation", modelrooms.RoomLocation.ToString());
            wordTexts.Add("RoomStruction", modelrooms.RoomInstruction.ToString());
            myWordHelper.ReplaceNodeText(wordTexts);
            //加入表格
            wordTable.Clear();
            //获取数据源
            System.Data.DataTable tbl = new System.Data.DataTable("tbStudents");

            DataColumn idColumn = new DataColumn();
            idColumn.DataType = System.Type.GetType("System.Int32");
            idColumn.ColumnName = "座位号";
            tbl.Columns.Add(idColumn);

            DataColumn cnColumn = new DataColumn();
            cnColumn.DataType = System.Type.GetType("System.String");
            cnColumn.ColumnName = "姓名";
            tbl.Columns.Add(cnColumn);

            DataColumn codeIdColumn = new DataColumn();
            codeIdColumn.DataType = System.Type.GetType("System.String");
            codeIdColumn.ColumnName = "学号";
            tbl.Columns.Add(codeIdColumn);

            DataColumn entranceIdColumn = new DataColumn();
            entranceIdColumn.DataType = System.Type.GetType("System.String");
            entranceIdColumn.ColumnName = "准考证号";
            tbl.Columns.Add(entranceIdColumn);

            DataColumn pwdColumn = new DataColumn();
            pwdColumn.DataType = System.Type.GetType("System.String");
            pwdColumn.ColumnName = "班级";
            tbl.Columns.Add(pwdColumn);

            DataColumn BeiZhuColumn = new DataColumn();
            BeiZhuColumn.DataType = System.Type.GetType("System.String");
            BeiZhuColumn.ColumnName = "备注";
            tbl.Columns.Add(BeiZhuColumn);

            //DataColumn[] keys = new DataColumn[1];
            //keys[0] = entranceIdColumn;
            //tbl.PrimaryKey = keys;


            foreach (DataRow myRow in dtuser.Rows)
            {
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(myRow["UserId"].ToString()));
                modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);
                DataRow newRow = tbl.NewRow();
                newRow[0] = myRow["SeatNumber"];
                newRow[1] = modelstudentinfos.StudentName;
                if (dropShowDao.Value == "2")
                {
                    newRow[2] = modelstudentinfos.StuInClassNo;
                }
                else
                {
                    newRow[2] = modelstudentinfos.StudentCode;
                }

                newRow[3] = myRow["TicketNumber"];
                newRow[4] = modelJC_ClassInfos.ClassName;
                newRow[5] = "";
                tbl.Rows.Add(newRow);
            }
            //System.Data.DataTable dt = YunEdu.Common.GetRecordByPageOrder.GetListByVW(dtMk, 0, "*","","TicketNumber desc").Tables[0];
            Dictionary<string, string> bookmarks = new Dictionary<string, string>();
            foreach (DataColumn column in tbl.Columns)
            {
                bookmarks.Add(column.ColumnName, column.ColumnName);
            }
            myWordHelper.ReplaceNodeTable(tbl, bookmarks);

            sectList.Add(myWordHelper.GetDocSection());
            //}
            #endregion

            rootWord.MerginDoc(sectList);//合并word文档内容
            rootWord.Save(docPath);//保存文档
        }

        //导出所有学生到一张表
        private void ExportAllStudentsToWordByXmls(string docPath)
        {
            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/allkaochang.xml");

            ArrayList sectList = new ArrayList();

            #region 动态生成word文档

            Dictionary<string, string> wordTexts = new Dictionary<string, string>();
            Dictionary<string, YunEdu.Common.WordMLHelper.WordTable> wordTable = new Dictionary<string, YunEdu.Common.WordMLHelper.WordTable>();

            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            myWordHelper.CreateXmlDom(xmlPath);

            //替换标签
            wordTexts.Clear();
            modeltests = blltests.GetModel(new Guid(id));
            int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "'");
            wordTexts.Add("TestTitle", modeltests.TestTitle);
            wordTexts.Add("StuCount", count.ToString());
            wordTexts.Add("StartDate", modeltests.TestStartDate.ToString());
            wordTexts.Add("EndDate", modeltests.TestEndDate.ToString());

            myWordHelper.ReplaceNodeText(wordTexts);
            //加入表格
            wordTable.Clear();
            //获取数据源
            System.Data.DataTable tbl = new System.Data.DataTable("tbStudents");

            DataColumn idColumn = new DataColumn();
            idColumn.DataType = System.Type.GetType("System.String");
            idColumn.ColumnName = "准考证号";
            tbl.Columns.Add(idColumn);

            DataColumn cnColumn = new DataColumn();
            cnColumn.DataType = System.Type.GetType("System.String");
            cnColumn.ColumnName = "姓名";
            tbl.Columns.Add(cnColumn);

            DataColumn BeiZhuColumn = new DataColumn();
            BeiZhuColumn.DataType = System.Type.GetType("System.String");
            BeiZhuColumn.ColumnName = "考场";
            tbl.Columns.Add(BeiZhuColumn);

            DataColumn entranceIdColumn = new DataColumn();
            entranceIdColumn.DataType = System.Type.GetType("System.Int32");
            entranceIdColumn.ColumnName = "座位号";
            tbl.Columns.Add(entranceIdColumn);

            DataColumn pwdColumn = new DataColumn();
            pwdColumn.DataType = System.Type.GetType("System.String");
            pwdColumn.ColumnName = "班级";
            tbl.Columns.Add(pwdColumn);

            //DataColumn[] keys = new DataColumn[1];
            //keys[0] = entranceIdColumn;
            //tbl.PrimaryKey = keys;

            foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("JC_ClassInfos", 0, "ID", "GradeId='" + modeltests.GradeId + "'", "ClassName").Tables[0].Rows)
            {
                foreach (DataRow myRow in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "UserId,TicketNumber,SeatNumber,RoomId", "TestId='" + id + "' and MyClassId='" + item["ID"] + "'", "TicketNumber").Tables[0].Rows)
                {
                    modelstudentinfos = bllstudentinfos.GetModel(new Guid(myRow["UserId"].ToString()));
                    modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);
                    DataSet dsr = blltestusers.GetList("TestId='" + modeltests.TestId + "' and UserId='" + myRow["UserId"] + "'");
                    modelrooms = bllrooms.GetModel(new Guid(dsr.Tables[0].Rows[0]["RoomId"].ToString()));
                    DataRow newRow = tbl.NewRow();
                    newRow[0] = myRow["TicketNumber"];
                    newRow[1] = modelstudentinfos.StudentName;
                    newRow[2] = "第" + modelrooms.RoomNum + "考场";
                    newRow[3] = myRow["SeatNumber"];
                    newRow[4] = modelJC_ClassInfos.ClassName;
                    tbl.Rows.Add(newRow);
                }
            }
            Dictionary<string, string> bookmarks = new Dictionary<string, string>();
            foreach (DataColumn column in tbl.Columns)
            {
                bookmarks.Add(column.ColumnName, column.ColumnName);
            }
            myWordHelper.ReplaceNodeTable(tbl, bookmarks);

            //}
            #endregion
            myWordHelper.Save(docPath);//保存文档
        }

        private List<YunEdu.Common.WordMLHelper.PKC_UserPicInfos> GetUserPicList(string strWhere)
        {
            System.Data.DataTable dt = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "TestId,UserId,RoomId,TicketNumber,SeatNumber", strWhere, "SeatNumber asc").Tables[0];
            List<YunEdu.Common.WordMLHelper.PKC_UserPicInfos> modelList = new List<YunEdu.Common.WordMLHelper.PKC_UserPicInfos>();
            int rowsCount = dt.Rows.Count;
            if (rowsCount > 0)
            {
                YunEdu.Common.WordMLHelper.PKC_UserPicInfos model;
                for (int n = 0; n < rowsCount; n++)
                {
                    modelstudentinfos = bllstudentinfos.GetModel(new Guid(dt.Rows[n]["UserId"].ToString()));
                    modelrooms = bllrooms.GetModel(new Guid(dt.Rows[n]["RoomId"].ToString()));
                    modeltests = blltests.GetModel(new Guid(dt.Rows[n]["TestId"].ToString()));

                    model = new YunEdu.Common.WordMLHelper.PKC_UserPicInfos();
                    //model.TestName = modeltests.TestTitle;
                    //model.PhotoPath = Server.MapPath(@"~\UserFiles\" + dt.Rows[n]["PhotoPath"].ToString().ToLower().Replace("/userfiles", ""));
                    model.StudentName = modelstudentinfos.StudentName;
                    //model.RoomTitle = modelrooms.RoomTitle;
                    model.TicketNumber = dt.Rows[n]["TicketNumber"].ToString();
                    model.SeatNumber = int.Parse(dt.Rows[n]["SeatNumber"].ToString());

                    modelList.Add(model);
                }
            }
            return modelList;
        }

        //导出签到表
        private void ExportStudentPicInfoToWordByXml(string docPath, YunEdu.Model.PKC_Rooms modelrooms, List<YunEdu.Common.WordMLHelper.PKC_UserPicInfos> userpicinfoList, int count)
        {
            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/studentpicinfotemplate.xml");
            string rootXmlrootPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/studentpicinfo.xml");
            rootWord.CreateXmlDom(rootXmlrootPath);//加载xml
            myWordHelper.CreateXmlDom(xmlPath);

            Dictionary<string, string> wordTexts = new Dictionary<string, string>();
            Dictionary<string, YunEdu.Common.WordMLHelper.WordTable> wordTable = new Dictionary<string, YunEdu.Common.WordMLHelper.WordTable>();
            //替换标签
            modeltests = blltests.GetModel(new Guid(id));
            wordTexts.Clear();
            wordTexts.Add("TestNum", modeltests.TestTitle);
            wordTexts.Add("RoomNum", count.ToString());//该考场的人数
            wordTexts.Add("RoomName", modelrooms.RoomTitle);
            wordTexts.Add("RoomLocation", modelrooms.RoomLocation);


            rootWord.ReplaceNodeText(wordTexts);
            //创建单元格
            rootWord.AddCell(count);

            //插入图片信息
            try
            {
                rootWord.InsertStudentInfos(userpicinfoList, myWordHelper.GetDocSection());
            }
            catch (Exception)
            {
                //Response.Write("<script>alert('"+ex.Message+"');</script>");
                //throw;
                //lblMessage.Text = lblMessage.Text + ex.Message + "<br/>";
            }
            rootWord.Save(docPath);//保存文档
        }
        private System.Data.DataTable MakeNamesTable(DataSet ds)
        {
            System.Data.DataTable tbl = new System.Data.DataTable("tbStudents");

            DataColumn idColumn = new DataColumn();
            idColumn.DataType = System.Type.GetType("System.String");
            idColumn.ColumnName = "准考证号";
            tbl.Columns.Add(idColumn);

            DataColumn cnColumn = new DataColumn();
            cnColumn.DataType = System.Type.GetType("System.String");
            cnColumn.ColumnName = "姓名";
            tbl.Columns.Add(cnColumn);

            DataColumn codeIdColumn = new DataColumn();
            codeIdColumn.DataType = System.Type.GetType("System.String");
            codeIdColumn.ColumnName = "学号";
            tbl.Columns.Add(codeIdColumn);

            DataColumn entranceIdColumn = new DataColumn();
            entranceIdColumn.DataType = System.Type.GetType("System.String");
            entranceIdColumn.ColumnName = "考场号";
            tbl.Columns.Add(entranceIdColumn);

            DataColumn pwdColumn = new DataColumn();
            pwdColumn.DataType = System.Type.GetType("System.Int32");
            pwdColumn.ColumnName = "座位号";
            tbl.Columns.Add(pwdColumn);

            DataColumn otherColumn = new DataColumn();
            otherColumn.DataType = System.Type.GetType("System.String");
            otherColumn.ColumnName = "考场所在位置";
            tbl.Columns.Add(otherColumn);

            //DataColumn[] keys = new DataColumn[1];
            //keys[0] = entranceIdColumn;
            //tbl.PrimaryKey = keys;


            foreach (DataRow myRow in ds.Tables[0].Rows)
            {
                DataTable dtTestUser = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "TestUserId", "TestId='" + id + "' and UserId='" + myRow["ID"] + "' and isnull(TicketNumber,'')<>''").Tables[0];
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(myRow["ID"].ToString()));

                if (dtTestUser != null && dtTestUser.Rows.Count != 0)
                {
                    string testuserid = dtTestUser.Rows[0]["TestUserId"].ToString();
                    modeltestusers = blltestusers.GetModel(new Guid(testuserid));
                    modelrooms = bllrooms.GetModel(modeltestusers.RoomId);//获取已排考生的考场

                    DataRow newRow = tbl.NewRow();
                    newRow[0] = modeltestusers.TicketNumber;
                    newRow[1] = modelstudentinfos.StudentName;
                    if (dropShowDao.Value == "2")
                    {
                        newRow[2] = modelstudentinfos.StuInClassNo;
                    }
                    else
                    {
                        newRow[2] = modelstudentinfos.StudentCode;
                    }
                    newRow[3] = modelrooms.RoomNum;
                    newRow[4] = modeltestusers.SeatNumber;
                    newRow[5] = modelrooms.RoomLocation;

                    tbl.Rows.Add(newRow);
                }
                else
                {
                    DataRow newRow = tbl.NewRow();
                    newRow[0] = 0;
                    newRow[1] = modelstudentinfos.StudentName;
                    if (dropShowDao.Value == "2")
                    {
                        newRow[2] = modelstudentinfos.StuInClassNo;
                    }
                    else
                    {
                        newRow[2] = modelstudentinfos.StudentCode;
                    }
                    newRow[3] = "";
                    newRow[4] = 0;
                    newRow[5] = "";

                    tbl.Rows.Add(newRow);
                }
            }
            return tbl;
        }

        //压缩文件
        public void PackFiles(string filename, string directory)
        {
            try
            {
                FastZip fz = new FastZip();
                fz.CreateEmptyDirectories = true;
                fz.CreateZip(filename, directory, true, "");
                fz = null;
            }
            catch (Exception)
            {
                throw;
            }
        }

        //显示布局算法
        private System.Data.DataTable ShowRoomLayout(YunEdu.Model.PKC_Rooms modelroom)
        {
            #region 显示布局算法
            modeltests = blltests.GetModel(new Guid(id));
            int _type = (int)modelroom.RoomType;
            string _layout = modelroom.RoomLayout;

            string[] _tempLayouts = _layout.Split('|');
            int[] a = new int[_tempLayouts.Length];
            int MaxRow = 0;
            for (int i = 0; i < _tempLayouts.Length; i++)
            {
                a[i] = int.Parse(_tempLayouts[i]);
            }
            for (int i = 0; i < _tempLayouts.Length; i++)
            {
                if (MaxRow < a[i])
                {
                    MaxRow = a[i];
                }
            }

            DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "TestUserId", "TestId='" + id + "' and RoomId='" + modelroom.RoomId + "'", "SeatNumber");
            DataTable dt = new DataTable();
            if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
            {
                string colum = modeltests.TestTitle;
                for (int i = 1; i < _tempLayouts.Length + 1; i++)
                {
                    dt.Columns.Add("第" + i + "列");
                }
                if (_type == 1)//s
                {
                    for (int i = 0; i < MaxRow; i++)
                    {
                        int n = i * _tempLayouts.Length; ; //控制table的取值
                        int k = (i + 1) * _tempLayouts.Length - 1;
                        DataRow dr = dt.NewRow();
                        for (int j = 0; j < _tempLayouts.Length; j++)
                        {
                            // 奇数行
                            if (i % 2 == 0)
                            {
                                if (n < ds.Tables[0].Rows.Count)//判断取值是否超过了表长
                                {
                                    modeltestusers = blltestusers.GetModel(new Guid(ds.Tables[0].Rows[n]["TestUserId"].ToString()));
                                    modelstudentinfos = bllstudentinfos.GetModel(modeltestusers.UserId);
                                    modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);
                                    if (dropShowDao.Value == "2")
                                    {
                                        dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + Convert.ToString((char)(10)) + "\n" + modelstudentinfos.StuInClassNo + "\n" + modelJC_ClassInfos.ClassName;
                                    }
                                    else
                                    {
                                        dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + Convert.ToString((char)(10)) + "\n" + modelstudentinfos.StudentCode + "\n" + modelJC_ClassInfos.ClassName;
                                    }
                                }
                                else
                                {
                                    dr[j] = " ";
                                }
                                n++;
                            }
                            else//偶数行
                            {
                                if (k < ds.Tables[0].Rows.Count)
                                {
                                    modeltestusers = blltestusers.GetModel(new Guid(ds.Tables[0].Rows[k]["TestUserId"].ToString()));
                                    modelstudentinfos = bllstudentinfos.GetModel(modeltestusers.UserId);
                                    modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);
                                    if (dropShowDao.Value == "2")
                                    {
                                        dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + "\n" + modelstudentinfos.StuInClassNo + "\n" + modelJC_ClassInfos.ClassName;
                                    }
                                    else
                                    {
                                        dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + "\n" + modelstudentinfos.StudentCode + "\n" + modelJC_ClassInfos.ClassName;
                                    }

                                }
                                else
                                {
                                    dr[j] = " ";
                                }
                                k--;
                            }

                        }
                        dt.Rows.Add(dr);
                    }
                }
                if (_type == 2)
                {

                    for (int i = 0; i < MaxRow; i++)
                    {
                        int n = i; //控制table的取值
                        DataRow dr = dt.NewRow();
                        for (int j = 0; j < _tempLayouts.Length; j++)
                        {
                            // 奇数列
                            if (j % 2 == 0)
                            {
                                //如果i小于这列的大小，那就去数据库取值，否则，赋值为空字符串
                                if (i < a[j])
                                {
                                    if (n < ds.Tables[0].Rows.Count)
                                    {
                                        modeltestusers = blltestusers.GetModel(new Guid(ds.Tables[0].Rows[n]["TestUserId"].ToString()));
                                        if (modeltestusers != null)
                                        {
                                            modeltestusers = blltestusers.GetModel(new Guid(ds.Tables[0].Rows[n]["TestUserId"].ToString()));
                                            modelstudentinfos = bllstudentinfos.GetModel(modeltestusers.UserId);
                                            if (modelstudentinfos != null)
                                            {
                                                modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);
                                                if (dropShowDao.Value == "2")
                                                {
                                                    dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + "\n" + modelstudentinfos.StuInClassNo + "\n" + modelJC_ClassInfos.ClassName;
                                                }
                                                else
                                                {
                                                    dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + "\n" + modelstudentinfos.StudentCode + "\n" + modelJC_ClassInfos.ClassName;
                                                }
                                            }

                                        }
                                    }
                                    else
                                    {
                                        dr[j] = " ";

                                    }
                                }
                                else
                                {
                                    dr[j] = " ";

                                }
                                if (j + 1 < a.Length)
                                {
                                    n = n + a[j] + a[j + 1] - 1 - 2 * i; //控制每列的坐标差
                                }
                                else//走到一行的尾部了，
                                {

                                }

                            }
                            else
                            {
                                if (i < a[j])
                                {
                                    if (n < ds.Tables[0].Rows.Count)
                                    {
                                        modeltestusers = blltestusers.GetModel(new Guid(ds.Tables[0].Rows[n]["TestUserId"].ToString()));
                                        modelstudentinfos = bllstudentinfos.GetModel(modeltestusers.UserId);
                                        modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);
                                        if (dropShowDao.Value == "2")
                                        {
                                            dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + "\n" + modelstudentinfos.StuInClassNo + "\n" + modelJC_ClassInfos.ClassName;
                                        }
                                        else
                                        {
                                            dr[j] = modeltestusers.SeatNumber + " " + modelstudentinfos.StudentName + "\n" + modelstudentinfos.StudentCode + "\n" + modelJC_ClassInfos.ClassName;
                                        }
                                    }
                                    else
                                    {
                                        dr[j] = " ";
                                    }
                                }
                                else
                                {
                                    dr[j] = " ";
                                }
                                n = n + 1 + 2 * i;
                            }

                        }
                        dt.Rows.Add(dr);
                    }
                }

            }
            return dt;
            #endregion
        }

        private List<YunEdu.Common.WordMLHelper.PKC_UserTicket> GetUserTicketList(string strWhere)
        {
            System.Data.DataTable dt = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "TestId,UserId,RoomId,TicketNumber,SeatNumber", strWhere, "SeatNumber asc").Tables[0];
            List<YunEdu.Common.WordMLHelper.PKC_UserTicket> modelList = new List<YunEdu.Common.WordMLHelper.PKC_UserTicket>();
            int rowsCount = dt.Rows.Count;
            if (rowsCount > 0)
            {
                YunEdu.Common.WordMLHelper.PKC_UserTicket model;
                for (int n = 0; n < rowsCount; n++)
                {
                    modelstudentinfos = bllstudentinfos.GetModel(new Guid(dt.Rows[n]["UserId"].ToString()));
                    modelrooms = bllrooms.GetModel(new Guid(dt.Rows[n]["RoomId"].ToString()));
                    modeltests = blltests.GetModel(new Guid(dt.Rows[n]["TestId"].ToString()));

                    model = new YunEdu.Common.WordMLHelper.PKC_UserTicket();

                    model.StudentName = modelstudentinfos.StudentName;
                    model.TicketNumber = dt.Rows[n]["TicketNumber"].ToString();
                    model.SeatNumber = int.Parse(dt.Rows[n]["SeatNumber"].ToString());
                    model.RoomTitle = modelrooms.RoomTitle;
                    model.RoomLoaction = modelrooms.RoomLocation;
                    modelList.Add(model);
                }
            }
            return modelList;
        }

        //导出准考证
        private void ExportTicketToWordByXmls(string strClass, string docPath)
        {
            YunEdu.Common.WordMLHelper rootWord = new YunEdu.Common.WordMLHelper();
            YunEdu.Common.WordMLHelper myWordHelper = new YunEdu.Common.WordMLHelper();
            string xmlPath = Server.MapPath(@"~/Admin/pkc_manage/userfiles/stuexamcard.xml");
            myWordHelper.CreateXmlDom(xmlPath);
            rootWord.CreateXmlDom(xmlPath);
            ArrayList sectList = new ArrayList();

            #region 动态生成word文档
            DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "*", strClass, "TicketNumber");
            foreach (DataRow student in ds.Tables[0].Rows)
            {
                Dictionary<string, string> wordTexts = new Dictionary<string, string>();
                Dictionary<string, YunEdu.Common.WordMLHelper.WordTable> wordTable = new Dictionary<string, YunEdu.Common.WordMLHelper.WordTable>();

                myWordHelper.CreateXmlDom(xmlPath);

                //替换标签
                wordTexts.Clear();
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(student["UserId"].ToString()));
                modelrooms = bllrooms.GetModel((Guid)student["RoomId"]);
                wordTexts.Add("cnname", modelstudentinfos.StudentName);
                wordTexts.Add("roomname", modelrooms.RoomNum.ToString());
                wordTexts.Add("seatnumber", student["SeatNumber"].ToString());
                wordTexts.Add("ticketnumber", student["TicketNumber"].ToString());
                if (dropShowDao.Value == "2")
                {
                    wordTexts.Add("studentcode", modelstudentinfos.StuInClassNo);
                }
                else
                {
                    wordTexts.Add("studentcode", modelstudentinfos.StudentCode);
                }

                modelJC_ClassInfos = bllJC_ClassInfos.GetModel((Guid)student["MyClassId"]);
                wordTexts.Add("classname", "[" + modelJC_ClassInfos.ClassName + "]");
                wordTexts.Add("roomaddress", modelrooms.RoomLocation);



                myWordHelper.ReplaceNodeText(wordTexts);

                DataTable dt = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Subjects", 0, "SubjectName,StartTime,EndTime", "TestId='" + id + "' and (PropertyCode=3 or PropertyCode=" + student["PropertyCode"] + ")", "OrderNum").Tables[0];
                //加入表格
                wordTable.Clear();

                //获取数据源
                System.Data.DataTable tbl = new System.Data.DataTable("tbTeachers");

                DataColumn idColumn = new DataColumn();
                idColumn.DataType = System.Type.GetType("System.String");
                idColumn.ColumnName = "考试科目";
                tbl.Columns.Add(idColumn);

                DataColumn sColumn = new DataColumn();
                sColumn.DataType = System.Type.GetType("System.String");
                sColumn.ColumnName = "开始时间";
                tbl.Columns.Add(sColumn);

                DataColumn cnColumn = new DataColumn();
                cnColumn.DataType = System.Type.GetType("System.String");
                cnColumn.ColumnName = "结束时间";
                tbl.Columns.Add(cnColumn);

                foreach (DataRow subject in dt.Rows)
                {
                    DataRow newRow = tbl.NewRow();

                    newRow[0] = subject["SubjectName"];
                    DateTime dtime;
                    if (!string.IsNullOrEmpty(subject["StartTime"].ToString()))
                    {
                        dtime = (DateTime)subject["StartTime"];
                        newRow[1] = dtime.ToString("MM/dd hh:mm");
                    }
                    else
                    {
                        newRow[1] = "";
                    }
                    if (!string.IsNullOrEmpty(subject["EndTime"].ToString()))
                    {
                        dtime = (DateTime)subject["EndTime"];
                        newRow[2] = dtime.ToString("MM/dd hh:mm");
                    }
                    else
                    {
                        newRow[2] = "";
                    }

                    tbl.Rows.Add(newRow);
                }

                Dictionary<string, string> bookmarks = new Dictionary<string, string>();
                foreach (DataColumn column in tbl.Columns)
                {
                    bookmarks.Add(column.ColumnName, column.ColumnName);
                }
                myWordHelper.ReplaceNodeTable(tbl, bookmarks);

                sectList.Add(myWordHelper.GetDocSection());
            }
            #endregion

            rootWord.MerginDoc(sectList);//合并word文档内容
            rootWord.Save(docPath);//保存文档
        }
        #endregion

        //按考场导出
        protected void btnExportByRoom_Click(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>''") <= 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先导入学生，然后排考场，再可执行此操作!');");
                    return;
                }
                modeltests = blltests.GetModel(new Guid(id));
                DataSet dsa = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "UserId", "TestId='" + modeltests.TestId + "'");
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(dsa.Tables[0].Rows[0]["UserId"].ToString()));
                string foldName = Server.MapPath(CodeTable.GetDirectoryTemp(modelstudentinfos.ColumnPath, UserName, CodeTable.FileType.files));
                DataTable dtRooms = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "*", "TestId='" + id + "'and IsUsing=1", "RoomNum").Tables[0];
                if (dtRooms.Rows.Count > 0)
                {
                    foreach (DataRow itemRoom in dtRooms.Rows)
                    {
                        string tmpDesDir = foldName + modeltests.TestTitle + "\\考场信息";//Server.MapPath("/") 
                        if (!Directory.Exists(tmpDesDir))
                            Directory.CreateDirectory(tmpDesDir);
                        string docPath = tmpDesDir + "\\第" + itemRoom["RoomNum"] + "考场考生基本信息" + ".doc";
                        int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and RoomId='" + itemRoom["RoomId"] + "'");
                        DataSet dsTestusers = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "SeatNumber,UserId,TicketNumber", "TestId='" + id + "' and RoomId='" + itemRoom["RoomId"] + "'", "SeatNumber");
                        modelrooms = bllrooms.GetModel(new Guid(itemRoom["RoomId"].ToString()));
                        ExportRoomAssginToWordByXmls(dsTestusers.Tables[0], docPath, id, modelrooms, count);

                        //压缩文件夹下载
                    }
                    PackFiles(foldName + modeltests.TestTitle + "\\全部考场信息.zip", foldName + modeltests.TestTitle + "\\" + "考场信息");
                    DownLoadFile(modeltests.TestTitle + "全部考场信息.zip", foldName + modeltests.TestTitle + "\\全部考场信息.zip", "application/x-zip-compressed");
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先设置排考场，再可执行此操作!');");
                }
            }
        }

        //导出考场布局
        protected void btnExportByLayout_Click(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>''") <= 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先导入学生，然后排考场，再可执行此操作!');");
                    return;
                }
                modeltests = blltests.GetModel(new Guid(id));
                DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "UserId", "TestId='" + modeltests.TestId + "'");
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(ds.Tables[0].Rows[0]["UserId"].ToString()));
                string foldName = Server.MapPath(CodeTable.GetDirectoryTemp(modelstudentinfos.ColumnPath, UserName, CodeTable.FileType.files));
                string tmpDesDir = foldName + modeltests.TestTitle + "\\布局";//Server.MapPath("/") 
                if (!Directory.Exists(tmpDesDir))
                {
                    Directory.CreateDirectory(tmpDesDir);
                }
                string docPath = tmpDesDir + "\\全部考场布局图" + ".doc";
                ExportLayoutToWordByXmls(docPath);
                DownLoadFile("考场布局图.doc", docPath, "application/octet-stream");
            }
        }

        //导出考场签到表
        protected void btnExportByCheck_Click(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>''") <= 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先导入学生，然后排考场，再可执行此操作!');");
                    return;
                }
                //全部考场考生信息核对表
                modeltests = blltests.GetModel(new Guid(id));
                DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetModel("PKC_TestUsers", "UserId", "TestId='" + modeltests.TestId + "'");
                modelstudentinfos = bllstudentinfos.GetModel(new Guid(ds.Tables[0].Rows[0]["UserId"].ToString()));
                string foldName = Server.MapPath(CodeTable.GetDirectoryTemp(modelstudentinfos.ColumnPath, UserName, CodeTable.FileType.files));
                DataTable dtRooms = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_Rooms", 0, "*", "TestId='" + id + "'and IsUsing=1", "RoomNum").Tables[0];
                if (dtRooms.Rows.Count > 0)
                {
                    foreach (DataRow itemRoom in dtRooms.Rows)
                    {
                        modelrooms = bllrooms.GetModel(new Guid(itemRoom["RoomId"].ToString()));

                        string tmpDesDir = foldName + "\\" + modeltests.TestTitle + "\\" + "考场考生信息核对";//Server.MapPath("/")
                        if (!Directory.Exists(tmpDesDir))
                        {
                            Directory.CreateDirectory(tmpDesDir);
                        }

                        string docPath = tmpDesDir + "\\第" + itemRoom["RoomNum"] + "考场签到表.doc";//保存路径
                        DataSet dsRooms = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "SeatNumber,UserId,TicketNumber", "RoomId='" + itemRoom["RoomId"] + "'", "SeatNumber");

                        int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and RoomId='" + itemRoom["RoomId"] + "'");

                        strWhere = "TestId='" + id + "' and RoomId='" + itemRoom["RoomId"] + "'";
                        ExportStudentPicInfoToWordByXml(docPath, modelrooms, GetUserPicList(strWhere), count);
                    }
                    PackFiles(foldName + "\\" + modeltests.TestTitle + "\\全部考场考生信息核对.zip", foldName + "\\" + modeltests.TestTitle + "\\考场考生信息核对");
                    DownLoadFile("全部考场考生信息核对.zip", foldName + "\\" + modeltests.TestTitle + "\\全部考场考生信息核对.zip", "application/x-zip-compressed");
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先设置排考场，再可执行此操作!');");
                }
            }
        }
        //按班级导出
        protected void btnExportByClass_Click(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>''") <= 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先导入学生，然后排考场，再可执行此操作!');");
                    return;
                }

                //if (!string.IsNullOrEmpty(hidGradeId.Value))
                //{
                //    modeltests = blltests.GetModel(new Guid(id));
                //    modelJC_GradeInfos = bllJC_GradeInfos.GetModel(new Guid(hidGradeId.Value));

                //    string tmpDesDir = @"D:/" + modeltests.TestTitle + "/" + modelJC_GradeInfos.GradeName;//Server.MapPath("/") 
                //    if (!Directory.Exists(tmpDesDir))
                //        Directory.CreateDirectory(tmpDesDir);
                //    string docPath;
                //    if (!string.IsNullOrEmpty(hidClassId.Value))
                //    {
                //        if (!Directory.Exists(tmpDesDir))
                //            Directory.CreateDirectory(tmpDesDir);
                //        modelJC_ClassInfos = bllJC_ClassInfos.GetModel(new Guid(hidClassId.Value));
                //        docPath = tmpDesDir + "/" + modelJC_ClassInfos.ClassName + ".doc";//保存路径

                //        DataSet dsAllUser = YunEdu.Common.GetRecordByPageOrder.GetModel("JC_StudentInfos", "ID", "ClassID='" + hidClassId.Value + "'");
                //        ExportStuAssginToWordByXml(dsAllUser, docPath, modelJC_ClassInfos.ClassName);
                //        DownLoadFile(modelJC_ClassInfos.ClassName + "安排信息表.doc", docPath, "application/msword");
                //    }
                //    else
                //    {
                //导出所有考生
                //modeltests = blltests.GetModel(new Guid(id));
                //docPath = tmpDesDir + "/" + modeltests.TestTitle + "_所有考生信息表总表.doc";
                //DataSet dstestuser = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "*", "TestId='" + id + "' and TicketNumber is not null", "TicketNumber asc");
                //int count = YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and TicketNumber is not null");

                //DownLoadFile(modeltests.TestTitle + "_所有考生信息表.doc", docPath, "application/msword");
                //MessageBox.Show(this, "导出成功");
                modeltests = blltests.GetModel(new Guid(id));
                string tmpDesDir = "";//Server.MapPath("/") 
                string docPath;
                string foldName = "";

                modelJC_GradeInfos = bllJC_GradeInfos.GetModel(modeltests.GradeId);
                foreach (DataRow classid in YunEdu.Common.GetRecordByPageOrder.GetListByVW("JC_ClassInfos", 0, "ID", "GradeId='" + modeltests.GradeId + "'", "ClassName").Tables[0].Rows)
                {
                    modelJC_ClassInfos = bllJC_ClassInfos.GetModel((Guid)classid["ID"]);

                    DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetModel("JC_StudentInfos", "ColumnPath", "ClassID='" + modelJC_ClassInfos.ID + "'");
                    if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                    {
                        foldName = Server.MapPath(CodeTable.GetDirectoryTemp(ds.Tables[0].Rows[0]["ColumnPath"].ToString(), UserName, CodeTable.FileType.files));
                        tmpDesDir = foldName + "\\" + modeltests.TestTitle + "\\" + modelJC_GradeInfos.GradeName;
                        if (!Directory.Exists(tmpDesDir))
                            Directory.CreateDirectory(tmpDesDir);
                        docPath = tmpDesDir + "\\" + modelJC_ClassInfos.ClassName + ".doc";//保存路径
                        //找出这个班的所有人

                        ExportStuAssginToWordByXml(docPath, modelJC_ClassInfos);
                    }

                }
                PackFiles(foldName + "\\" + modeltests.TestTitle + "\\全部班级.zip", tmpDesDir);
                DownLoadFile(modeltests.TestTitle + "全部班级.zip", foldName + "\\" + modeltests.TestTitle + "\\全部班级.zip", "application/x-zip-compressed");
                //}

                //}
                //else
                //{

                //    //压缩文件夹
                //    //PackFiles(@"D:/" + modeltests.TestTitle + "/",)
                //    YunEdu.Common.MessageBox.Show(this, "请选择导出的年级");
                //}             
            }
            //DataBind();
        }

        //准考证导出
        protected void btnExportByTicket_Click(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                string strClass;
                //没有准考证的考生数
                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>''") <= 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('请先导入学生，然后排考场，再可执行此操作!');");
                    return;
                }

                //if (!string.IsNullOrEmpty(hidGradeId.Value))
                //{
                //    modeltests = blltests.GetModel(new Guid(id));
                //    modelJC_GradeInfos = bllJC_GradeInfos.GetModel(new Guid(hidGradeId.Value));

                //    
                //    if (!string.IsNullOrEmpty(hidClassId.Value))
                //    {
                //        if (!Directory.Exists(tmpDesDir))
                //            Directory.CreateDirectory(tmpDesDir);
                //        modelJC_ClassInfos = bllJC_ClassInfos.GetModel(new Guid(hidClassId.Value));
                //        docPath = tmpDesDir + "/" + modelJC_ClassInfos.ClassName + ".doc";//保存路径

                //        strClass = "TestId='" + id + "' and MyClassId='" + hidClassId.Value + "'";
                //        ExportTicketToWordByXmls(strClass, docPath);
                //        DownLoadFile(modelJC_ClassInfos.ClassName + "准考证.doc", docPath, "application/msword");
                //    }
                //    else
                //    {

                modeltests = blltests.GetModel(new Guid(id));
                string tmpDesDir = "";
                string foldName = "";
                string ExistClass = "'" + new Guid().ToString() + "'";//找出已存在的班级 
                bool IsOver = false;//是否全部找到所有班级

                while (!IsOver)
                {
                    DataSet dsUserId = YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "UserId", "TestId='" + id + "' and MyClassId not in(" + ExistClass + ") and isnull(TicketNumber,'')<>''", " UserId");
                    if (dsUserId.Tables[0].Rows.Count != 0)
                    {
                        modelstudentinfos = bllstudentinfos.GetModel(new Guid(dsUserId.Tables[0].Rows[0]["UserId"].ToString()));//根据UserId找ClassID
                        ExistClass += ",'" + modelstudentinfos.ClassID + "'";
                        modelJC_ClassInfos = bllJC_ClassInfos.GetModel(modelstudentinfos.ClassID);//根据CLassId找班级名字
                        modelJC_GradeInfos = bllJC_GradeInfos.GetModel(modelJC_ClassInfos.GradeId);//找年级

                        foldName = Server.MapPath(CodeTable.GetDirectoryTemp(modelstudentinfos.ColumnPath, UserName, CodeTable.FileType.files));
                        tmpDesDir = foldName + "\\" + modeltests.TestTitle + "\\" + modelJC_GradeInfos.GradeName + "准考证";
                        if (!Directory.Exists(tmpDesDir))
                            Directory.CreateDirectory(tmpDesDir);
                        string docPath = tmpDesDir + "\\" + modelJC_ClassInfos.ClassName + "学生准考证.doc";//保存路径
                        strClass = "TestId='" + id + "' and MyClassId='" + modelstudentinfos.ClassID + "'";
                        ExportTicketToWordByXmls(strClass, docPath);
                    }
                    else IsOver = true;
                }
                PackFiles(foldName + "\\" + modeltests.TestTitle + "\\准考证.zip", tmpDesDir);
                DownLoadFile(modeltests.TestTitle + "全部班级学生准考证.zip", foldName + "\\" + modeltests.TestTitle + "\\准考证.zip", "application/x-zip-compressed");
                //}

                //}
                //else
                //{

                //    //压缩文件夹
                //    //PackFiles(@"D:/" + modeltests.TestTitle + "/",)
                //    YunEdu.Common.MessageBox.Show(this, "请选择导出的年级");
                //}

            }
        }

        //学生总表
        protected void btnExportAllStudents_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>''") <= 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请先导入学生，然后排考场，再可执行此操作!');");
            }
            else
            {
                modeltests = blltests.GetModel(new Guid(id));
                modelJC_GradeInfos = bllJC_GradeInfos.GetModel(modeltests.GradeId);
                DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetModel("JC_StudentInfos", "ColumnPath", "GradeID='" + modelJC_GradeInfos.ID + "'");
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    string foldName = Server.MapPath(CodeTable.GetDirectoryTemp(ds.Tables[0].Rows[0]["ColumnPath"].ToString(), UserName, CodeTable.FileType.files));
                    string tmpDesDir = foldName + "\\" + modeltests.TestTitle + "\\";
                    if (!Directory.Exists(tmpDesDir))
                        Directory.CreateDirectory(tmpDesDir);
                    string docPath = tmpDesDir + modelJC_GradeInfos.GradeName + "全年级学生总表.doc";
                    ExportAllStudentsToWordByXmls(docPath);
                    DownLoadFile(modeltests.TestTitle + "学生总表.doc", docPath, "application/ms-word");
                }

            }
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            Response.Redirect("pkc_layout_show.aspx");
        }

        protected void btnAbilityValue_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(this.ddlTests.SelectedValue) && !this.ddlTests.SelectedValue.ToString().Equals("0"))
            {
                foreach (DataRow item in YunEdu.Common.GetRecordByPageOrder.GetListByVW("PKC_TestUsers", 0, "*", "TestId='" + id + "'", "AbilityValue asc").Tables[0].Rows)
                {
                    modeltestusers = blltestusers.GetModel(new Guid(item["TestUserId"].ToString()));

                    decimal abilityvalue = 0;
                    foreach (DataRow itemscore in YunEdu.Common.GetRecordByPageOrder.GetListByVW("JC_TestScores", 0, "ScoreTotal", "UserID='" + item["UserId"].ToString() + "'and TestID='" + this.ddlTests.SelectedValue + "'", "SubjectCode asc").Tables[0].Rows)
                    {
                        abilityvalue += decimal.Parse(itemscore["ScoreTotal"].ToString());
                    }
                    modeltestusers.AbilityValue = abilityvalue;
                    blltestusers.Update(modeltestusers);
                }
                BindData(true);
            }
            else
            {
                MessageBox.ResponseScript(this, "layer.msg('请选择一次考试!');");
                return;
            }

        }

        protected void btnExportAllStudentsToExcel_Click(object sender, EventArgs e)
        {
            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + id + "' and isnull(TicketNumber,'')<>''") <= 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('请先导入学生，然后排考场，再可执行此操作!');");
            }
            else
            {
                modeltests = blltests.GetModel(new Guid(id));
                modelJC_GradeInfos = bllJC_GradeInfos.GetModel(modeltests.GradeId);
                DataSet ds = YunEdu.Common.GetRecordByPageOrder.GetModel("JC_StudentInfos", "ColumnPath", "GradeID='" + modelJC_GradeInfos.ID + "'");
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    string foldName = CodeTable.GetDirectoryTemp(ds.Tables[0].Rows[0]["ColumnPath"].ToString(), UserName, CodeTable.FileType.files);
                    string docPath = foldName + "全年级学生总表.xls";
                    //所有该年级考生信息
                    DataTable _dt = blltestusers.getAllStudent(new Guid(id));
                    YunEdu.Common.DataToExcel.ExportEasy(_dt, "", docPath, Server.MapPath("/"));
                    Response.ContentType = "application/x-zip-compressed";
                    Response.AddHeader("Content-Disposition", "attachment;filename=" + Server.UrlEncode("全年级学生总表.xls"));
                    Response.TransmitFile(Server.MapPath(docPath));
                }

            }
        }

    }
}