﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:Ecb_PraiseType
    /// </summary>
    public partial class Ecb_PraiseType
    {
        public Ecb_PraiseType()
        {
        }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Ecb_PraiseType");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.Ecb_PraiseType model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Ecb_PraiseType(");
            strSql.Append("ID,ColumnId,ColumnPath,ParentId,Name,ImgPath,OrderId)");
            strSql.Append(" values (");
            strSql.Append("@ID,@ColumnId,@ColumnPath,@ParentId,@Name,@ImgPath,@OrderId)");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ParentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,50),
                    new SqlParameter("@ImgPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@OrderId", SqlDbType.Int,4)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ParentId;
            parameters[4].Value = model.Name;
            parameters[5].Value = model.ImgPath;
            parameters[6].Value = model.OrderId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool UP_Add(ECB.PC.Model.Ecb_PraiseType model)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Name", SqlDbType.NVarChar,50),
                    new SqlParameter("@ParentId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ImgPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@row",SqlDbType.Int,4)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.Name;
            parameters[2].Value = model.ParentId;
            parameters[3].Value = model.ColumnId;
            parameters[4].Value = model.ColumnPath;
            parameters[5].Value = model.ImgPath;
            parameters[6].Value = ParameterDirection.Output;

            object obj = DbHelperSQL.RunProcedure("UP_Ecb_PraiseType_ADD", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.Ecb_PraiseType model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Ecb_PraiseType set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ParentId=@ParentId,");
            strSql.Append("Name=@Name,");
            strSql.Append("ImgPath=@ImgPath,");
            strSql.Append("OrderId=@OrderId");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@ParentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Name", SqlDbType.NVarChar,50),
					new SqlParameter("@ImgPath", SqlDbType.NVarChar,500),
					new SqlParameter("@OrderId", SqlDbType.Int,4),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.ParentId;
            parameters[3].Value = model.Name;
            parameters[4].Value = model.ImgPath;
            parameters[5].Value = model.OrderId;
            parameters[6].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Ecb_PraiseType ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Ecb_PraiseType ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.Ecb_PraiseType GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,ColumnId,ColumnPath,ParentId,Name,ImgPath,OrderId from Ecb_PraiseType ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            ECB.PC.Model.Ecb_PraiseType model = new ECB.PC.Model.Ecb_PraiseType();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.Ecb_PraiseType DataRowToModel(DataRow row)
        {
            ECB.PC.Model.Ecb_PraiseType model = new ECB.PC.Model.Ecb_PraiseType();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ParentId"] != null && row["ParentId"].ToString() != "")
                {
                    model.ParentId = new Guid(row["ParentId"].ToString());
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["ImgPath"] != null)
                {
                    model.ImgPath = row["ImgPath"].ToString();
                }
                if (row["OrderId"] != null && row["OrderId"].ToString() != "")
                {
                    model.OrderId = int.Parse(row["OrderId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,ColumnId,ColumnPath,ParentId,Name,ImgPath,OrderId ");
            strSql.Append(" FROM Ecb_PraiseType ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,ColumnId,ColumnPath,ParentId,Name,ImgPath,OrderId ");
            strSql.Append(" FROM Ecb_PraiseType ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Ecb_PraiseType ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from Ecb_PraiseType T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Ecb_PraiseType";
            parameters[1].Value = "ID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.Ecb_PraiseType GetParentModel(Guid ParentId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,ColumnId,ColumnPath,ParentId,Name,ImgPath,OrderId from Ecb_PraiseType ");
            strSql.Append(" where ParentId=@ParentId ");
            SqlParameter[] parameters = {
					new SqlParameter("@ParentId", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ParentId;

            ECB.PC.Model.Ecb_PraiseType model = new ECB.PC.Model.Ecb_PraiseType();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsbyBname(int columnId, string Name, Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Ecb_PraiseType");
            strSql.Append(" where ColumnId=@ColumnId and Name=@Name and Id!=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@Name",SqlDbType.NVarChar,150),
                    new SqlParameter("@Id",SqlDbType.UniqueIdentifier)
			};
            parameters[0].Value = columnId;
            parameters[1].Value = Name;
            parameters[2].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters,DbHelperSQL.ConnMain);
        }

        //上移
        public object MoveUp(Guid Id)
        {
            SqlParameter[] parameter = { new SqlParameter("@Id", Id) };
            return DbHelperSQL.RunProcedure("UP_Ecb_PraiseType_moveup", parameter);
        }
        //下移
        public object MoveDown(Guid Id)
        {
            SqlParameter[] parameter = { new SqlParameter("@Id", Id) };
            return DbHelperSQL.RunProcedure("UP_Ecb_PraiseType_movedown", parameter);
        }
    }
}

