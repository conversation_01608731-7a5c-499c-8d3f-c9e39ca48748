﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.Common.DEncrypt;
using System.Collections;
using System.Collections.ObjectModel;
using YunEdu.DBUtility;

namespace ECB.PC.Web.Admin.pkc_manage
{
    public partial class pkc_tests_list : YunEdu.Authority.AdminCommonJC
    {
        YunEdu.BLL.PKC_Tests blltests = new YunEdu.BLL.PKC_Tests();
        YunEdu.Model.PKC_Tests modeltests = new YunEdu.Model.PKC_Tests();
        YunEdu.BLL.PKC_TestUsers blltestusers = new YunEdu.BLL.PKC_TestUsers();
        YunEdu.BLL.JC_ClassInfos bllClassInfos = new YunEdu.BLL.JC_ClassInfos();
        YunEdu.BLL.PKC_TestTeachers blltestteachers = new YunEdu.BLL.PKC_TestTeachers();
        YunEdu.BLL.JC_Tests bllTest = new YunEdu.BLL.JC_Tests();
        YunEdu.Model.JC_Tests modelTests = new YunEdu.Model.JC_Tests();
        YunEdu.BLL.JC_Message bllmessage = new YunEdu.BLL.JC_Message();
        YunEdu.Model.JC_Message modelmessage = new YunEdu.Model.JC_Message();
        /// <summary>
        /// 区域信息BLL
        /// </summary>
        YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();
        /// <summary>
        /// 区域信息model
        /// </summary>
        YunEdu.Model.BM_Areas modelArea = new YunEdu.Model.BM_Areas();

        protected void Page_Load(object sender, EventArgs e)
        {
            GetArea();
            if (!IsPostBack)
            {
                if (this.modelAreaUser.ColumnDepth > 3)
                {
                    DataTable dt = new YunEdu.BLL.JC_GradeInfos().GetGradeBySchoolYear(modelAreaUser.ColumnID.ToString(), ModelTermInfos(modelAreaUser.ColumnID.ToString()).SchoolYear).Tables[0];
                    YunEdu.Common.GetRecordByPageOrder.BindDropDownList(ddlGrade, dt, "", "GradeName", "ID", "请选择年级", "");
                }
                BindData();
            }
        }

        protected override void OnLoadComplete(EventArgs e)
        {
            InitControlAuthority(this);
        }

        private string GetWhere()
        {
            StringBuilder sbWhere = new StringBuilder();

            string strColumnId = string.Empty;
            string strColumnPath = string.Empty;
            // 第一次加载
            if (!IsPostBack)
            {
                strColumnId = modelAreaUser.ColumnID.ToString();
                strColumnPath = modelAreaUser.ColumnPath;
            }
            else
            {
                // 是否选择附属学校
                if (!string.IsNullOrEmpty(hidChildSchool.Value))
                {
                    modelArea = bllArea.GetModel(int.Parse(hidChildSchool.Value));
                }
                else if (!string.IsNullOrEmpty(hidSchoolId.Value))
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                }
                if (modelArea != null && modelArea.ColumnID != 0)
                {
                    strColumnId = modelArea.ColumnID.ToString();
                    strColumnPath = modelArea.ColumnPath;
                }
            }
            sbWhere.Append("(ColumnId=" + strColumnId + " or ColumnPath like '" + strColumnPath + "|%')");
            if (!string.IsNullOrEmpty(hidGradeId.Value))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append($"GradeId='{new Guid(hidGradeId.Value)}'");
            }
            if (!string.IsNullOrEmpty(hidClassId.Value))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                sbWhere.Append($" charindex('{new Guid(hidClassId.Value)}',ClassId)>0");
            }
            if (!string.IsNullOrEmpty(this.dorpShowSize.SelectedValue) && !this.dorpShowSize.SelectedValue.ToString().Equals("0"))
            {
                AspNetPager1.PageSize = int.Parse(this.dorpShowSize.SelectedValue);
            }
            if (!string.IsNullOrEmpty(this.dropType.SelectedValue) && !this.dropType.SelectedValue.ToString().Equals("0"))
            {
                if (sbWhere.Length > 0)
                {
                    sbWhere.Append(" and ");
                }
                string _text = YunEdu.Common.StringPlus.Filter(this.inputName.Text, "html");
                switch (this.dropType.SelectedValue)
                {
                    case "1":
                        sbWhere.Append("TestTitle like '%" + this.inputName.Text + "%'");
                        break;

                    default:
                        break;
                }
            }
            return sbWhere.ToString();
        }

        /// <summary>
        /// 获取数据并绑定
        /// </summary>
        private void BindData(bool isMain = false)
        {
            this.txtPageNum.Text = this.AspNetPager1.PageSize.ToString();
            string strWhere = GetWhere();
            AspNetPager1.RecordCount = GetRecordByPageOrder.GetCount("PKC_Tests", strWhere, isMain ? DbHelperSQL.ConnMain : null);
            DataTable dt = GetRecordByPageOrder.GetList("PKC_Tests", AspNetPager1.PageSize, AspNetPager1.CurrentPageIndex, "*", strWhere, " TestAddDate desc ", isMain ? DbHelperSQL.ConnMain : null).Tables[0];
            gvTestList.DataSource = dt;
            gvTestList.DataBind();
        }

        #region 加载区域信息

        /// <summary>
        /// 绑定年级信息
        /// </summary>
        /// <param name="schoolId">学校ID</param>
        /// <param name="selectedValue"></param>
        private void BindGradeInfos(string schoolid, string selectedValue = "")
        {
            YunEdu.BLL.JC_GradeInfos bllGrade = new YunEdu.BLL.JC_GradeInfos();
            DataSet dsGradeInfos = new DataSet();
            // 管理员
            if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()))//全部显示
            {
                dsGradeInfos = new YunEdu.BLL.JC_GradeInfos().GetGradeBySchoolYear(schoolid, ModelTermInfos(schoolid).SchoolYear);
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsGradeInfos = new YunEdu.BLL.JC_Subjects().GetTeachGrade(Int32.Parse(schoolid), UserName);
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModel(new Guid(UserId));
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.GradeID.ToString();
                    dsGradeInfos = bllGrade.GetList("ID='" + modelStudent.GradeID.ToString() + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsGradeInfos = new YunEdu.BLL.JC_GradeInfos().GetGradeBySchoolYear(schoolid, ModelTermInfos(schoolid).SchoolYear);
            }
            // 绑定年级信息列表
            if (dsGradeInfos != null && dsGradeInfos.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlGrade, dsGradeInfos.Tables[0], "", "GradeName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    ddlGrade.SelectedValue = selectedValue;
                    hidGradeId.Value = selectedValue;
                    BindClassInfos(new Guid(selectedValue), hidClassId.Value);
                }
            }
        }

        /// <summary>
        /// 绑定班级信息
        /// </summary>
        /// <param name="gradeId"></param>
        /// <param name="selectedValue"></param>
        private void BindClassInfos(Guid gradeId, string selectedValue = "")
        {
            YunEdu.BLL.JC_ClassInfos bllClass = new YunEdu.BLL.JC_ClassInfos();
            DataSet dsClass = new DataSet();
            if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.ProvinceAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CityAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.CountyAdmin).ToString()) ||
                RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", gradeId.ToString()), "OrderId");
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Teacher).ToString())) // 如果是教师
            {
                dsClass = new YunEdu.BLL.JC_Subjects().GetTeachClass(modelAreaUser.ColumnID, UserName, gradeId.ToString());
            }
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Parents).ToString())) // 如果是家长
            {
                // 家长的用户名为学籍号+1或者2
                string strStudentCode = UserName;
                strStudentCode = strStudentCode.Substring(0, strStudentCode.Length - 1);
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(strStudentCode);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }

            }
            // 判断是否为学生
            else if (RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.Students).ToString()))
            {
                YunEdu.Model.JC_StudentInfos modelStudent = new YunEdu.BLL.JC_StudentInfos().GetModelByUserName(UserName);
                if (modelStudent != null)
                {
                    selectedValue = modelStudent.ClassID.ToString();
                    dsClass = bllClass.GetList("ID='" + modelStudent.ClassID + "'");
                }
            }
            else //其他角色默认全部显示
            {
                dsClass = bllClass.GetList(0, string.Format("GradeId='{0}'", gradeId.ToString()), "OrderId");
            }
            if (dsClass != null && dsClass.Tables.Count > 0)
            {
                GetRecordByPageOrder.BindDropDownList(ddlClass, dsClass.Tables[0], "", "ClassName", "ID");
                if (!string.IsNullOrEmpty(selectedValue))
                {
                    hidClassId.Value = selectedValue;
                    ddlClass.SelectedValue = selectedValue;
                }
            }
        }

        private void GetArea()
        {
            //设区市，绑定，同时，根据用户所处级别，选定当前选择项
            //任何级别的用户都要显示市级
            //hidSchoolId.value默认显示用户所在县区学校
            string strColumnPath = "";

            if (!IsPostBack)
            {
                strColumnPath = modelAreaUser.ColumnPath;
                hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
            }
            else
            {
                if (string.IsNullOrEmpty(hidSchoolId.Value))
                {
                    strColumnPath = modelAreaUser.ColumnPath;
                    hidSchoolId.Value = modelAreaUser.ColumnID.ToString();
                }
                else
                {
                    modelArea = bllArea.GetModel(int.Parse(hidSchoolId.Value));
                    if (modelArea == null)
                    {
                        return;
                    }
                    else
                    {
                        strColumnPath = modelArea.ColumnPath;
                    }
                }
            }
            if (string.IsNullOrEmpty(strColumnPath))
            {
                return;
            }
            string[] place = strColumnPath.Split('|');
            bllArea.BindAreaDropDown(S1, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S2, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(S3, "", IsPostBack, "", "请选择市");
            bllArea.BindAreaDropDown(ddlChildSchool, "", IsPostBack, "", "请选择市");
            GetRecordByPageOrder.BindDropDownList(ddlGrade, null, "", "GradeName", "ID", "请选择年级");
            GetRecordByPageOrder.BindDropDownList(ddlClass, null, "", "ClassName", "ID", "请选择班级");
            switch (place.Length)
            {
                case 1:
                    // 通过省查处市级
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, "", "请选择市");
                    break;
                case 2:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, "", "请选择县区");
                    break;
                case 3:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, "", "请选择学校");
                    break;
                case 4:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, "", "请选择学校");
                    if (ddlChildSchool.Items.Count > 1)
                    {
                        ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                        if (!string.IsNullOrEmpty(hidChildSchool.Value))
                        {
                            ddlChildSchool.SelectedValue = hidChildSchool.Value;
                        }
                    }
                    BindGradeInfos(place[3], hidGradeId.Value);
                    break;
                case 5:
                    // 查询出市级并赋值
                    bllArea.BindAreaDropDown(S1, place[0], IsPostBack, place[1], "请选择市");
                    // 查询出县区
                    bllArea.BindAreaDropDown(S2, place[1], IsPostBack, place[2], "请选择县区");
                    // 查询学校
                    bllArea.BindAreaDropDown(S3, place[2], IsPostBack, place[3], "请选择学校");
                    pnlSchool.Visible = false;
                    // 查询附属学校
                    bllArea.BindAreaDropDown(ddlChildSchool, place[3], IsPostBack, place[4], "请选择附属学校");
                    ddlChildSchool.Items.Insert(1, new ListItem(S3.SelectedItem.Text, place[3]));
                    BindGradeInfos(place[4], hidGradeId.Value);
                    if (!IsPostBack)
                    {
                        pnlSchool.Visible = false;
                    }
                    break;
                default:
                    break;
            }
        }

        #endregion

        protected void btnDelete_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvTestList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        int count4 = GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + gvTestList.DataKeys[myItem.RowIndex][0].ToString() + "'");
                        if (count4 == 0)
                        {
                            int count3 = GetRecordByPageOrder.GetCount("PKC_TestTeachers", "TestId='" + gvTestList.DataKeys[myItem.RowIndex][0].ToString() + "'");
                            if (count3 == 0)
                            {
                                int count2 = GetRecordByPageOrder.GetCount("PKC_Rooms", "TestId='" + gvTestList.DataKeys[myItem.RowIndex][0].ToString() + "'");
                                if (count2 == 0)
                                {
                                    int count1 = GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + gvTestList.DataKeys[myItem.RowIndex][0].ToString() + "'");
                                    if (count1 == 0)
                                    {


                                        int count = GetRecordByPageOrder.GetCount("PKC_Subjects", "TestId='" + gvTestList.DataKeys[myItem.RowIndex][0].ToString() + "'");
                                        if (count == 0)
                                        {

                                            iCount++;
                                            if (!blltests.Delete(new Guid(gvTestList.DataKeys[myItem.RowIndex][0].ToString())))
                                            {
                                                iError++;
                                            }
                                            if (iError == 0)
                                            {
                                                //删除消息
                                                bllmessage.Delete(CodeTable.MessageType.kao_shi, gvTestList.DataKeys[myItem.RowIndex][0].ToString());
                                            }
                                        }
                                        else
                                        {
                                            MessageBox.ResponseScript(this, "showMessage('false','请先进入“科目管理”，删除本考试下的所有科目！',0)");
                                            return;
                                        }
                                    }
                                    else
                                    {
                                        MessageBox.ResponseScript(this, "showMessage('false','请先进入“考生管理”，删除本考试下的所有考生！',0)");
                                        return;
                                    }
                                }
                                else
                                {

                                    MessageBox.ResponseScript(this, "showMessage('false','请先进入“考场管理”，删除本考试下的所有考场！',0)");
                                    return;
                                }
                            }
                            else
                            {
                                MessageBox.ResponseScript(this, "showMessage('false','请先进入“教师管理”，删除本考试下的所有教师！',0)");
                                return;
                            }
                        }
                        else
                        {
                            MessageBox.ResponseScript(this, "showMessage('false','请先进入“监考安排及数据导出”，删除本考试下的所有监考安排！',0)");
                            return;
                        }
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "showMessage('false','请选择要删除的记录！',0)");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "showMessage('true','删除成功！',1)");
            }
            else
            {
                MessageBox.ResponseScript(this, "showMessage('false','删除失败！',0)");
            }
            BindData(true);
        }

        /// <summary>
        /// 同步考试到考试管理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnSyncTest_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvTestList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        iCount++;


                        modeltests = blltests.GetModel(new Guid(gvTestList.DataKeys[myItem.RowIndex][0].ToString()));


                        Guid _testid = Guid.NewGuid();
                        Guid classId = Guid.Empty;

                        modelTests = new YunEdu.Model.JC_Tests();

                        modelTests.PkcTestId = new Guid(gvTestList.DataKeys[myItem.RowIndex][0].ToString());
                        modelTests.SchoolColumnId = modeltests.ColumnId;
                        modelTests.SchoolColumnPath = modeltests.ColumnPath;
                        modelTests.GradeID = modeltests.GradeId;
                        if (!string.IsNullOrEmpty(modeltests.TermId.ToString()))
                        {
                            modelTests.TermID = modeltests.TermId;
                        }
                        modelTests.PropertyCode = modeltests.PropertyCode;
                        modelTests.TestName = modeltests.TestTitle;
                        modelTests.BeginDate = modeltests.TestStartDate;
                        modelTests.EndDate = modeltests.TestEndDate;
                        modelTests.Remark = modeltests.TestInstruction;
                        modelTests.TestSubjectCode = modeltests.TestSubjectCode;
                        modelTests.SchoolYear = ModelTermInfos(modeltests.ColumnId.ToString()).SchoolYear;
                        int errorCount = 0;


                        modelTests.ID = _testid;

                        // 判断是否有班级
                        if (!string.IsNullOrEmpty(hidClassId.Value))
                        {
                            classId = new Guid(hidClassId.Value);
                            modelTests.ClassID = classId;

                            int TestCount = new YunEdu.BLL.JC_Tests().GetRecordCount("(TestName='" + modelTests.TestName + "' AND ClassID='" + modelTests.ClassID + "') or (PkcTestId='" + gvTestList.DataKeys[myItem.RowIndex][0].ToString() + "' and " + "ClassID='" + modelTests.ClassID + "')");
                            if (TestCount == 0)
                            {
                                if (!bllTest.Add(modelTests))
                                {
                                    errorCount++;
                                }
                            }

                        }
                        else
                        {
                            DataSet dsClass = bllClassInfos.GetList("SchoolId=" + modelTests.SchoolColumnId + " and GradeId='" + modeltests.GradeId.ToString() + "'");
                            if (dsClass != null && dsClass.Tables.Count > 0 && dsClass.Tables[0].Rows.Count > 0)
                            {
                                // 循环给每个班级添加当前考试
                                foreach (DataRow item in dsClass.Tables[0].Rows)
                                {
                                    classId = new Guid(item["ID"].ToString());
                                    modelTests.ClassID = classId;

                                    int TestCount = new YunEdu.BLL.JC_Tests().GetRecordCount("(TestName='" + modelTests.TestName + "' AND ClassID='" + modelTests.ClassID + "') or (PkcTestId='" + gvTestList.DataKeys[myItem.RowIndex][0].ToString() + "' and " + "ClassID='" + modelTests.ClassID + "')");
                                    if (TestCount == 0)
                                    {
                                        if (!bllTest.Add(modelTests))
                                        {
                                            errorCount++;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                MessageBox.ResponseScript(this, "showMessage('false','保存失败,当前年级没有班级！',0)");
                                return;
                            }
                        }
                        //if (!blltests.Delete(new Guid(gvTestList.DataKeys[myItem.RowIndex][0].ToString())))
                        //{
                        //    iError++;
                        //}
                    }

                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "showMessage('false','请选择要同步的记录！',0)");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "showMessage('true','同步成功！',1)");
            }
            else
            {
                MessageBox.ResponseScript(this, "showMessage('false','同步失败！',0)");
            }
            BindData(true);
        }

        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            BindData();
        }
        //设置分页显示数量
        protected void txtPageNum_TextChanged(object sender, EventArgs e)
        {
            int _pagesize;
            if (int.TryParse(txtPageNum.Text.Trim(), out _pagesize))
            {
                this.AspNetPager1.PageSize = _pagesize;
                this.AspNetPager1.CurrentPageIndex = 1;
                BindData();
            }
        }
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            BindData(!string.IsNullOrEmpty(Request["__EVENTARGUMENT"]));
        }

        protected void btnAdd_Click(object sender, EventArgs e)
        {
            Response.Redirect("pkc_tests_edit.aspx?t=" + YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"]) + "&ctlId=" + this.btnAdd.ClientID + "&MenuId=" + Request.QueryString["MenuId"]);
        }

        protected void btnIsSearch_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvTestList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {

                        iCount++;
                        string testid = gvTestList.DataKeys[myItem.RowIndex][0].ToString();
                        modeltests = blltests.GetModel(new Guid(testid));
                        if (!modeltests.IsEnabled)
                        {
                            modeltests.IsEnabled = true;
                            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + testid + "' and TicketNumber is not null") != 0)
                            {
                                List<string> userlist = new List<string>();
                                foreach (DataRow item in blltestusers.GetList("TestId='" + testid + "'").Tables[0].Rows)
                                {
                                    userlist.Add(item["UserId"].ToString());
                                }
                                bllmessage.AddMessage(userlist, CodeTable.MessageType.kao_shi, testid);
                            }
                        }
                        if (!blltests.Update(modeltests))
                        {
                            iError++;
                        }
                    }
                }
            }
            if (iCount == 0)
            {
                MessageBox.ResponseScript(this, "showMessage('false','请选择要开放查询的记录！',0)");
                return;
            }
            if (iError == 0)
            {
                MessageBox.ResponseScript(this, "showMessage('true','开放查询成功！',1)");
            }
            else
            {
                MessageBox.ResponseScript(this, "showMessage('false','开放查询失败！',0)");

            }
            BindData(true);
        }

        protected void btnPushTest_Click(object sender, EventArgs e)
        {
            int iError = 0;
            int iCount = 0;
            foreach (GridViewRow myItem in gvTestList.Rows)
            {
                CheckBox chkSel = (CheckBox)myItem.FindControl("chkItem1");
                if (chkSel != null)
                {
                    if (chkSel.Checked)
                    {
                        string testid = gvTestList.DataKeys[myItem.RowIndex][0].ToString();
                        modelmessage = bllmessage.GetModel(CodeTable.MessageType.kao_shi, testid);
                        if (modelmessage == null)
                        {
                            List<string> userlist = new List<string>();
                            if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TestUsers", "TestId='" + testid + "' and TicketNumber is not null") != 0)
                            {

                                foreach (DataRow item in blltestusers.GetList("TestId='" + testid + "'").Tables[0].Rows)
                                {
                                    userlist.Add(item["UserId"].ToString());
                                }
                            }
                            else
                            {
                                iError++;
                            }
                            if (iError == 0)
                            {
                                if (YunEdu.Common.GetRecordByPageOrder.GetCount("PKC_TeacherSubjects", "TestId='" + testid + "' and TeacherId<>'00000000-0000-0000-0000-000000000000'") != 0)
                                {
                                    foreach (DataRow item in blltestteachers.GetList("TestId='" + testid + "' and JianKaoCount>0").Tables[0].Rows)
                                    {
                                        userlist.Add(item["UserId"].ToString());
                                    }
                                }
                                bllmessage.AddMessage(userlist, CodeTable.MessageType.kao_shi, testid);

                                iCount++;
                            }
                        }
                        else
                        {
                            iCount++;
                        }
                    }
                }
            }
            if (iCount != 0)
            {
                MessageBox.ResponseScript(this, "layer.msg('消息推送成功');");
            }
            else
            {
                if (iError != 0)
                {
                    MessageBox.ResponseScript(this, "layer.msg('排考场未完成，暂无消息可推送');");
                }
                else
                {
                    MessageBox.ResponseScript(this, "layer.msg('推送失败');");
                }
            }
        }

        protected void btnImpotTest_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.fupScore.FileName.Length == 0)
                {
                    lblMessage.Text = "你没有选择导入的Excel文件！";
                    return;
                }
                else if (System.IO.Path.GetExtension(this.fupScore.FileName) != ".xls" && System.IO.Path.GetExtension(this.fupScore.FileName) != ".xlsx")
                {
                    lblMessage.Text = "请选择Excel格式“*.xls,*.xlsx”的文件！";
                    return;
                }
                else
                {
                    UpLoad _upload = new UpLoad();
                    UpLoad.UploadFileInfo _fileInfo = _upload.UpLoadFileTemp(fupScore, modelAreaUser.ColumnPath, UserName, CodeTable.FileType.files);
                    DataTable dt = YunEdu.Common.DataToExcel.GetExcelData(_fileInfo.AbsolutePath);//获取Excel表数据 文件名+*.格式
                    ImportData(dt);//导入
                }
            }
            catch (Exception exception)
            {
                lblMessage.Text = "出现错误：" + exception.Message;
            }
            //BindData();
        }

        public void ImportData(DataTable dt)
        {
            StringBuilder sb = new System.Text.StringBuilder();
            BLL.JC_ClassInfos bllClass = new BLL.JC_ClassInfos();
            YunEdu.BLL.JC_GradeInfos bllGrade = new YunEdu.BLL.JC_GradeInfos();
            YunEdu.BLL.JC_GradeSubjectConfig bllSubjectConfig = new YunEdu.BLL.JC_GradeSubjectConfig();
            int index = 1;
            DataTable dtGrade = bllGrade.GetGradeBySchoolId(modelAreaUser.ColumnID.ToString()).Tables[0];

            foreach (DataRow dr in dt.Rows)
            {
                index++;
                if (string.IsNullOrEmpty(dr["考试名称"].ToString()) || string.IsNullOrEmpty(dr["考试年级"].ToString()) || string.IsNullOrEmpty(dr["考试类型"].ToString()) || string.IsNullOrEmpty(dr["科目"].ToString()) || string.IsNullOrEmpty(dr["考试开始时间"].ToString()) || string.IsNullOrEmpty(dr["考试结束时间"].ToString()))
                {
                    continue;
                }
                DataRow[] drGrades = dtGrade.Select("GradeName='" + dr["考试年级"].ToString() + "'");

                if (drGrades.Length > 0)
                {
                    DataTable dtSubject = bllSubjectConfig.GetSubjectByGradeID(drGrades[0]["ID"].ToString(), "DictValue,DictText,PropertyCode", true).Tables[0];
                    DateTime dteStart, dteEnd;
                    if (!DateTime.TryParse(dr["考试开始时间"].ToString(), out dteStart))
                    {
                        sb.AppendLine("行号:" + index + "考试开始时间:[" + dr["考试开始时间"].ToString() + "]不符合要求"); continue;
                    }
                    if (!DateTime.TryParse(dr["考试开始时间"].ToString(), out dteEnd))
                    {
                        sb.AppendLine("行号:" + index + "考试开始时间:[" + dr["考试开始时间"].ToString() + "]不符合要求"); continue;
                    }
                    string PropertyCode = "3";
                    switch (dr["考试类型"].ToString())
                    {
                        case "文科":
                            PropertyCode = "1";
                            break;
                        case "理科":
                            PropertyCode = "2";
                            break;
                        case "全年级":
                            PropertyCode = "3";
                            break;
                        default:
                            sb.AppendLine("行号:" + index + "考试类型:应选择[文科][理科][全年级]"); continue;
                    }
                    Dictionary<string, string> dicSubject = new Dictionary<string, string>();
                    ArrayList lstSQL = new ArrayList();
                    Guid TestId = Guid.NewGuid();
                    string SchoolYear = ModelTermInfos(modelAreaUser.ColumnID.ToString()).SchoolYear;
                    Guid TermId = ModelTermInfos(modelAreaUser.ColumnID.ToString()).ID;
                    string strSub = "";
                    foreach (string sub in dr["科目"].ToString().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries))
                    {
                        DataRow[] drsSub = dtSubject.Select("DictText='" + sub.Trim() + "'");
                        if (drsSub.Length > 0)
                        {
                            if (!dicSubject.ContainsKey(sub.Trim()))
                            {
                                //添加考试科目
                                dicSubject.Add(drsSub[0]["DictValue"].ToString(), drsSub[0]["DictText"].ToString());
                                lstSQL.Add(string.Format(@"INSERT INTO [dbo].[PKC_Subjects]
           ([ID],[SubjectName],[TestId],[SubjectCode],[PropertyCode],[CreateTime],[SchoolYear]) VALUES ('{0}','{1}','{2}','{3}','{4}','{5}','{6}')", Guid.NewGuid(), drsSub[0]["DictText"].ToString(), TestId, drsSub[0]["DictValue"].ToString(), drsSub[0]["DictText"].ToString(), DateTime.Now, SchoolYear));
                                strSub += drsSub[0]["DictValue"].ToString() + ",";
                            }
                            else
                            {
                                sb.AppendLine("行号:" + index + "科目:" + sub + "不存在年级科目配置中"); continue;
                            }
                        }
                    }
                    //添加考试记录
                    lstSQL.Add(string.Format("INSERT INTO [dbo].[PKC_Tests]([TestId],[TestTitle],[TestAddDate],[IsEnabled],[IsStart],[UserId],[TestStartDate],[TestEndDate],[TestInstruction],[IsOlnyIp],[IsShowFen],[IsJiShi],[IsCrack],[ColumnId],[ColumnPath],[GradeId],[TermId],[PropertyCode],[TestSubjectCode],[IsJoinEnrollment],[SchoolYear],[ClassId])VALUES ('{0}','{1}','{2}',1,0,'{3}','{4}','{5}','{6}',0,0,0,0,'{7}','{8}','{9}','{10}','{11}','{12}',0,'{13}','{14}')", TestId, dr["考试名称"], DateTime.Now, UserId, dteStart, dteEnd, dr["考试说明"], modelAreaUser.ColumnID, modelAreaUser.ColumnPath, drGrades[0]["ID"], TermId, PropertyCode, strSub, SchoolYear, Guid.Empty));
                    //同步到考试管理
                    DataSet dsClass = bllClass.GetList("SchoolId=" + modelAreaUser.ColumnID + " and GradeId='" + drGrades[0]["ID"].ToString() + "'");
                    if (dsClass != null && dsClass.Tables.Count > 0 && dsClass.Tables[0].Rows.Count > 0)
                    {
                        // 循环给每个班级添加当前考试
                        foreach (DataRow item in dsClass.Tables[0].Rows)
                        {

                            lstSQL.Add(string.Format(@"INSERT INTO [dbo].[JC_Tests]
           ([ID],[SchoolColumnId],[SchoolColumnPath],[GradeID],[TermID],[PropertyCode],[TestName],[BeginDate],[EndDate],[Remark],[CreateDate],[TestSubjectCode],[PkcTestId],[SchoolYear],[ClassId])VALUES('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}')", Guid.NewGuid(), modelAreaUser.ColumnID, modelAreaUser.ColumnPath, drGrades[0]["ID"], TermId, PropertyCode, dr["考试名称"], dteStart, dteEnd, dr["考试说明"], DateTime.Now, strSub, TestId, SchoolYear, item["ID"]));
                        }
                    }


                    int count = YunEdu.DBUtility.DbHelperSQL.ExecuteSqlTran(lstSQL);
                    if (count > 0)
                    {
                        sb.AppendLine("行号:" + index + "考试添加成功！");
                    }
                }
                else
                {
                    sb.AppendLine("行号:" + index + "年级:[" + dr["考试年级"].ToString() + "]不存在");
                }
            }
            lblMessage.Text = sb.ToString();
            BindData(true);
        }

        protected void btnDownloadTest_Click(object sender, EventArgs e)
        {
            Response.Clear();
            Response.ContentType = "application/x-zip-compressed";
            Response.AddHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode("考试模板.xls", System.Text.Encoding.UTF8));
            string filename = Server.MapPath("/Admin/pkc_manage/userfiles/考试模板.xls");
            Response.TransmitFile(filename);
        }
    }
}