﻿<%@ Page Language="C#" AutoEventWireup="true" StylesheetTheme="Admin_Default" EnableEventValidation="false" CodeBehind="pkc_tests_list.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_tests_list" %>

<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>考试列表</title>
    <script src="../../js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <link href="../../js/layer/skin/default/layer.css" rel="stylesheet" />
    <script src="../../js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script type="text/javascript">
        function showMessage(success, content, ico) {
            var _index = parent.layer.alert(content, {
                icon: ico,
                shade: ['0.1', '#000'],
                time: success == "true" ? 1000 : 0,
                end: function () {
                    if (success == "true") {
                    }
                    else {
                        parent.layer.close(_index);
                    }
                }
            });
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <asp:Panel ID="pnlButtons" runat="server" CssClass="search-bar">
                                    <asp:Panel ID="pnlSchool" runat="server">
                        辖区选项：<asp:DropDownList ID="S1" runat="server" EnableViewState="True">
                            <asp:ListItem Value="" Selected="True">==请选择地市==</asp:ListItem>
                        </asp:DropDownList>
                        <asp:DropDownList ID="S2" runat="server" EnableViewState="True">
                            <asp:ListItem Value="" Selected="True">==请选择县区==</asp:ListItem>
                        </asp:DropDownList>
                        <asp:DropDownList ID="S3" runat="server" EnableViewState="True" ValidationGroup="1">
                            <asp:ListItem Value="" Selected="True">==请选择学校==</asp:ListItem>
                        </asp:DropDownList>
                        <asp:DropDownList ID="ddlChildSchool" runat="server" EnableViewState="True" Style="display: none;">
                            <asp:ListItem Value="" Selected="True">==请选附属学校==</asp:ListItem>
                        </asp:DropDownList>
                    </asp:Panel>
                    年级：<asp:DropDownList ID="ddlGrade" runat="server" ></asp:DropDownList>
                    班级：<asp:DropDownList ID="ddlClass" runat="server"></asp:DropDownList>
                    显示：<asp:DropDownList ID="dorpShowSize" runat="server">
                        <asp:ListItem Value="10">10</asp:ListItem>
                        <asp:ListItem Value="15">15</asp:ListItem>
                        <asp:ListItem Value="20">20</asp:ListItem>
                        <asp:ListItem Value="50">50</asp:ListItem>
                        <asp:ListItem Value="80">80</asp:ListItem>
                        <asp:ListItem Value="100">100</asp:ListItem>
                    </asp:DropDownList>
                    查询：<asp:DropDownList ID="dropType" runat="server">
                        <asp:ListItem Value="0">=请选择=</asp:ListItem>
                        <asp:ListItem Value="1" Selected="True">考试标题</asp:ListItem>
                    </asp:DropDownList>
                    <asp:TextBox ID="inputName" runat="server"></asp:TextBox>
                    <asp:Button ID="btnSearch" runat="server" Text="查 找" CssClass="btn" OnClick="btnSearch_Click" ValidationGroup="1" />
            </asp:Panel>
            <asp:GridView ID="gvTestList" runat="server" Width="100%" CellPadding="4" SkinID="gridviewSkin1" EmptyDataText="未找到信息"
                BorderWidth="1px" DataKeyNames="TestId"
                AutoGenerateColumns="False" RowStyle-HorizontalAlign="Center">
                <Columns>
                    <asp:TemplateField HeaderText="选择">
                        <ItemTemplate>
                            <label class="chkItem">
                            <asp:CheckBox ID="chkItem1" runat="server" />
                            <asp:HiddenField ID="hidID" runat="server" Value='<%# Eval("TestId") %>' />
                                </label>
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Center" Width="30" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="TestTitle" HeaderText="考试名称" SortExpression="TestTitle">
                        <ItemStyle HorizontalAlign="Left"></ItemStyle>
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="是否可查">
                        <ItemTemplate>
                            <asp:Label ID="lblIsEnable" runat="server" Text='<%#Eval("IsEnabled").ToString()=="True"?"是":"否" %>'></asp:Label>
                        </ItemTemplate>
                        <HeaderStyle HorizontalAlign="Center" Width="6%" />
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="操作">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlSubject" runat="server" CssClass="btnWhite light" NavigateUrl='<%# "pkc_subjects_list.aspx?t="+YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"])+"&CtlId=hlSubject&tp=set&id="+Eval("TestId")+ "&MenuId=" + Request.QueryString["MenuId"] %>'>2.科目管理</asp:HyperLink>
                            <asp:HyperLink ID="hlExaminee" runat="server" CssClass="btnWhite light" NavigateUrl='<%# "pkc_testusers_list.aspx?t="+YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"])+"&CtlId=hlExaminee&tp=users&id="+Eval("TestId") + "&MenuId=" + Request.QueryString["MenuId"] %>'>3.考生管理</asp:HyperLink>
                            <asp:HyperLink ID="hlAddRoom" runat="server" CssClass="btnWhite light" NavigateUrl='<%# "pkc_rooms_list.aspx?t="+YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"])+"&CtlId=hlAddRoom&tp=test&id="+Eval("TestId")+ "&MenuId=" + Request.QueryString["MenuId"] %>'>4.考场管理</asp:HyperLink>
                            <asp:HyperLink ID="hlPKC" runat="server" CssClass="btnWhite light" NavigateUrl='<%# "pkc_testusers_list.aspx?t="+YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"])+"&CtlId=hlPKC&tp=pkc&id="+Eval("TestId") + "&MenuId=" + Request.QueryString["MenuId"] %>'>5.排考场及数据导出</asp:HyperLink>
                            <asp:HyperLink ID="hlTeachers" runat="server" CssClass="btnWhite light" NavigateUrl='<%# "pkc_testteachers_list.aspx?t="+YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"])+"&CtlId=hlTeachers&id="+Eval("TestId") + "&MenuId=" + Request.QueryString["MenuId"] %>'>6.教师管理及监考设置</asp:HyperLink>
                            <asp:HyperLink ID="hlInvigilation" runat="server" CssClass="btnWhite light" NavigateUrl='<%# "pkc_subjectteacher_list.aspx?t="+YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"])+"&CtlId=hlInvigilation&id="+Eval("TestId") + "&MenuId=" + Request.QueryString["MenuId"] %>'>7.监考安排及数据导出</asp:HyperLink>
                            <%-- <asp:HyperLink ID="HyperLink2" runat="server" CssClass="btnGray" NavigateUrl='<%# "pkc_testteachers_list.aspx?t="+YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"])+"&CtlId=hlInvigilator&id="+Eval("TestId") + "&MenuId=" + Request.QueryString["MenuId"] %>'>8.打印安排表</asp:HyperLink>--%>
                             <asp:HyperLink CssClass="btn light" ID="hlEdit" runat="server" NavigateUrl="javascript:void(0);" onclick='<%#"ShowEdit(this,\""+Eval("TestId")+"\")"%>'>编辑</asp:HyperLink>
                            <asp:Button ID="btnIsSearch" runat="server" Text="开放查询" CssClass="btnWhite light" OnClick="btnIsSearch_Click" Visible="false" />
                        </ItemTemplate>
                        <HeaderStyle HorizontalAlign="Center" Width="75%" />
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:TemplateField>
                </Columns>
            </asp:GridView>
            <div id="pager" class="paging-bar">
                    <div class="l-btns">
                        <span>显示</span><asp:TextBox ID="txtPageNum" runat="server"
                            AutoPostBack="True"
                            CssClass="pagenum" onkeydown="return checkNumber(event);"
                            OnTextChanged="txtPageNum_TextChanged"></asp:TextBox><span>条/页</span>
                    </div>
                    <webdiyer:AspNetPager ID="AspNetPager1" runat="server" AlwaysShow="True" AlwaysShowFirstLastPageNumber="True" CssClass="paging-default" CurrentPageButtonClass="current" CurrentPageButtonPosition="Center" CustomInfoHTML="&lt;span&gt;共%RecordCount%条记录&lt;/span&gt;" CustomInfoSectionWidth="" CustomInfoStyle="" CustomInfoTextAlign="NotSet" FirstPageText="首页" LastPageText="尾页" NavigationToolTipTextFormatString="{0}" NextPageText="下一页" NumericButtonCount="7" OnPageChanged="AspNetPager1_PageChanged" PageIndexBoxType="TextBox" PageSize="10" PagingButtonSpacing="0px" PrevPageText="上一页" ShowCustomInfoSection="Left" ShowFirstLast="False" ShowNavigationToolTip="True" ShowPageIndexBox="Never" UrlPaging="false">
                    </webdiyer:AspNetPager>
                </div>
            <div class="footer-bar">
                <label for="chkAll" class="btnWhite light">
	                <input id="chkAll" name="chkAll" type="checkbox" />全选
                </label>
                   <asp:Button ID="btnAdd" runat="server" Text="1.添加考试" CssClass="btnGreen" />
                <asp:Panel ID="pnlImpotTest" runat="server" Style="display: inline-block;">
                    <asp:Button ID="btnDownloadTest" runat="server" CssClass="btnYellow" Text="下载模板" OnClick="btnDownloadTest_Click" />
                    <input id="btnFileImport" type="button" value="导入考试" class="btn" />
                    <asp:FileUpload ID="fupScore" runat="server" onchange="FileImport();" Style="display: none;" />
                    <asp:Button ID="btnImpotTest" runat="server" Text="导入考试" CssClass="btn" OnClick="btnImpotTest_Click" ValidationGroup="1" Style="display: none;" />
                </asp:Panel>
                <asp:Button ID="btnSyncTest" runat="server" Text="同步到考试管理" CssClass="btnGreen" OnClientClick='return CheckDo("该操作将同步所选考试到考试管理中，是否同步?","btnSyncTest)' OnClick="btnSyncTest_Click" />
                <asp:Button ID="btnPush" runat="server" Text="推送考试信息" CssClass="btnGreen" OnClientClick='return CheckDo("该操作将推送考试信息到相关的学生和教师,请确保排考场和排监考已全部完成，确定推送?","btnPush")' OnClick="btnPushTest_Click" />
                <asp:Button ID="btnDel" runat="server" Text="删除" OnClientClick='return CheckDoAll("该操作将删除所有所选考试，不可恢复，是否删除?","btnDel")' CssClass="btnRed" OnClick="btnDelete_Click" />
            </div>
            <asp:Label ID="lblMessage" runat="server" ForeColor="Red"></asp:Label>
        </div>
        <asp:HiddenField ID="hidSchoolId" runat="server" Value="" />
        <asp:HiddenField ID="hidChildSchool" runat="server" Value="" />
        <asp:HiddenField ID="hidGradeId" runat="server" />
        <asp:HiddenField ID="hidClassId" runat="server" />
        <asp:HiddenField ID="hidContentCode" runat="server" />
    </form>
    <script type="text/javascript">
        //打开选择文件框 自动导入
        $("#btnImpotTest").hide();
        $("#btnFileImport").click(function () {
            $("#fupScore").click();
        });
        function FileImport() {
            $("#btnImpotTest").click();
        }
        //以下是实现级联菜单
        // 市
        function select1() {
            $.ajax(
                {
                    type: "post",
                    url: "/admin/ajax/getSchool.ashx",
                    data: { "type": "province" },
                    success: function (msg) {
                        for (var i = 0; i < msg.length; i++) {
                            $("#S1").append("<option value=" + msg[i].ProvinceID + ">" + msg[i].ProvinceName + "</option>");
                        }
                        select2();
                    }
                })
        };
        // 县区
        function select2() {
            $("#S2").html("");
            $("#S2").append("<option value=\"\" selected=\"selected\">==请选择县区==</option>");
            $.ajax(
                {
                    type: "post",
                    url: "/admin/ajax/getSchool.ashx",
                    data: { "type": "city", "provinceID": $('#S1').val() },
                    success: function (msg) {
                        for (var i = 0; i < msg.length; i++) {
                            $("#S2").append("<option value=" + msg[i].CityID + ">" + msg[i].CityName + "</option>");
                        }
                        select3();
                    }
                })
        };
        // 学校
        function select3() {
            $("#S3").html("");
            $("#S3").append("<option value=\"\" selected=\"selected\">==请选择学校==</option>");
            $.ajax(
                {
                    type: "post",
                    url: "/admin/ajax/getSchool.ashx",
                    data: { "type": "district", "cityID": $('#S2').val() },
                    success: function (msg) {
                        for (var i = 0; i < msg.length; i++) {
                            $("#S3").append("<option value=" + msg[i].DistrictID + ">" + msg[i].DistrictName + "</option>");
                        }
                        selectChildSchool();
                    }
                })
        };
        // 附属学校
        function selectChildSchool() {
            $("#ddlChildSchool").html("");
            $("#ddlChildSchool").append("<option value=\"\" selected=\"selected\">==请选择附属学校==</option>");
            $.ajax(
                {
                    type: "post",
                    url: "/admin/ajax/getSchool.ashx",
                    data: { "type": "childSchool", "schoolId": $('#S3').val() },
                    success: function (msg) {
                        for (var i = 0; i < msg.length; i++) {
                            $("#ddlChildSchool").append("<option value=" + msg[i].SchoolId + ">" + msg[i].SchoolName + "</option>");
                        }
                        if (msg.length > 0) {
                            $('#ddlChildSchool').css("display", "inline-block");
                        }
                        else {
                            $('#ddlChildSchool').css("display", "none");
                        }
                    }
                });
            selectGrade();
        };

        function selectGrade() {
            $('#ddlGrade').html("");
            $('#ddlGrade').append("<option value=\"\" selected=\"selected\">==请选择年级==</option>");
            $.ajax({
                type: "post",
                dataType: "json",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "grade", "schoolId": $('#hidSchoolId').val() },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#ddlGrade").append("<option value=" + msg[i].ID + ">" + msg[i].GradeName + "</option>");
                    }
                    selectClass();
                }
            });
        };
        function selectClass() {
            $('#ddlClass').html("");
            $('#ddlClass').append("<option value=\"\" selected=\"selected\">==请选择班级==</option>");
            $.ajax({
                type: "post",
                dataType: "json",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "class", "gradeId": $('#ddlGrade').val() },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#ddlClass").append("<option value=" + msg[i].ID + ">" + msg[i].ClassName + "</option>");
                    }
                }
            });
        };

        function ShowEdit(e, id) {
            var url = '<%= "/admin/pkc_manage/pkc_tests_edit.aspx?t=" + YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"].ToString()) + "&ctlId=hlEdit&id="%>' + id;
            layer.open({
                type: 2,
                title: '编辑',
                area: ['780px', '500px'],
                content: url
            });
        };

        $(document).ready(function () {
            //级联菜单
            $('#S1').bind("change", select2);
            $('#S2').bind("change", select3);
            $('#S3').bind("change", selectChildSchool);
            $('#ddlChildSchool').bind("change", selectGrade);
            $('#ddlGrade').bind("change", selectClass);
            // 年级
            $('#ddlGrade').change(function () {
                $('#hidClassId').val("");
                $('#hidGradeId').val($('#ddlGrade').val());
            });
            // 班级
            $('#ddlClass').change(function () {
                $('#hidClassId').val($('#ddlClass').val());
            });

            //  第一次加载判断是否有附属学校，有则显示
            if ($("#ddlChildSchool").children().length > 1) {
                $('#ddlChildSchool').css("display", "inline-block");
            }
            // 每次变动都给隐藏域赋值
            $('#S1').change(function () {
                $('#hidChildSchool').val("");
                $('#hidSchoolId').val($('#S1').val());
            });

            $('#S2').change(function () {
                $('#hidChildSchool').val("");
                if ($('#S2').val() != "") {
                    $('#hidSchoolId').val($('#S2').val());
                }
                else {
                    if ($('#S1').length > 0 && $('#S1').val() != "") {
                        $('#hidSchoolId').val($('#S1').val());
                    }
                    else {
                        $('#hidSchoolId').val("");
                    }
                }
            });

            $('#S3').change(function () {
                $('#hidChildSchool').val("");
                if ($('#S3').val() != "") {
                    $('#hidSchoolId').val($('#S3').val());
                }
                else {
                    if ($('#S2').length > 0 && $('#S2').val() != "") {
                        $('#hidSchoolId').val($('#S2').val());
                    }
                    else if ($('#S1').length > 0 && $('#S1').val() != "") {
                        $('#hidSchoolId').val($('#S1').val());
                    }
                    else {
                        $('#hidSchoolId').val("");
                    }
                }
            });
            $('#ddlChildSchool').change(function () {
                $('#hidChildSchool').val("");
                if ($('#ddlChildSchool').length > 0 && $('#ddlChildSchool').val() != "") {
                    // 给学校隐藏域赋值
                    if ($('#hidSchoolId').length > 0) {
                        $('#hidSchoolId').val($('#S3').val());
                    }
                    // 给附属学校隐藏域赋值
                    $('#hidChildSchool').val($('#ddlChildSchool').val());
                }
                else if ($('#S3').length > 0 && $('#S3').val() != "") {
                    $('#hidSchoolId').val($('#S3').val());
                }
                else if ($('#S2').length > 0 && $('#S2').val() != "") {
                    $('#hidSchoolId').val($('#S2').val());
                }
                else if ($('#S1').length > 0 && $('#S1').val() != "") {
                    $('#hidSchoolId').val($('#S1').val());
                }
                else {
                    $('#hidSchoolId').val("");
                }
            });
            //结束级联菜单
            // colorbox添加
            $('#btnAdd').click(function () {
                var url = '<%= "/admin/pkc_manage/pkc_tests_edit.aspx?t=" + YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"].ToString()) + "&ctlId=btnAdd"%>';
                layer.open({
                    type: 2,
                    title: '添加考试',
                    area: ['780px', '600px'],
                    content: url
                });
                return false;
            });
        });
    </script>
</body>
</html>
