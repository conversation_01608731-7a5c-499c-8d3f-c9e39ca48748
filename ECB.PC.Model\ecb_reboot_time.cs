﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_reboot_time:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_reboot_time
    {
        public ecb_reboot_time()
        { }
        #region Model
        private Guid _id;
        private Guid _ruleid;
        private int? _weekid;
        private DateTime? _boottime;
        private DateTime? _offtime;
        public bool IsUpdate;//自定义字段 用于事务判断
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 配置Id
        /// </summary>
        public Guid RuleId
        {
            set { _ruleid = value; }
            get { return _ruleid; }
        }
        /// <summary>
        /// 周次
        /// </summary>
        public int? WeekId
        {
            set { _weekid = value; }
            get { return _weekid; }
        }
        /// <summary>
        /// 开机时间
        /// </summary>
        public DateTime? BootTime
        {
            set { _boottime = value; }
            get { return _boottime; }
        }
        /// <summary>
        /// 关机时间
        /// </summary>
        public DateTime? OffTime
        {
            set { _offtime = value; }
            get { return _offtime; }
        }
        #endregion Model

    }
}

