﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 审批流程列表
    /// </summary>
    [Serializable]
    public partial class wpsl_flows
    {
        public wpsl_flows()
        { }
        #region Model
        private Guid _id;
        private int _columnId;
        private string _columnpath;
        private string _name;
        private int? _typecode;
        private bool _isenable;
        private DateTime? _createtime;
        private Guid _lasteditor;
        /// <summary>
        /// 编号
        /// </summary>
        public Guid Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 所属地区
        /// </summary>
        public int ColumnId
        {
            set { _columnId = value; }
            get { return _columnId; }
        }
        /// <summary>
        /// 所属地区路径
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name
        {
            set { _name = value; }
            get { return _name; }
        }
        /// <summary>
        /// 类型
        /// </summary>
        public int? TypeCode
        {
            set { _typecode = value; }
            get { return _typecode; }
        }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable
        {
            set { _isenable = value; }
            get { return _isenable; }
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime
        {
            set { _createtime = value; }
            get { return _createtime; }
        }
        /// <summary>
        /// 修改人
        /// </summary>
        public Guid LastEditor
        {
            set { _lasteditor = value; }
            get { return _lasteditor; }
        }
        #endregion Model

    }
}

