﻿using System;
namespace ECB.PC.Model
{
    /// <summary>
    /// 功能模块配置
    /// </summary>
    [Serializable]
    public partial class aspnet_Welcome
    {
        public aspnet_Welcome()
        { }
        #region Model
        private Guid _appid;
        private string _moduleid;
        private string _modulename;
        private string _moduleurl;
        private string _parentmoduleid;
        private string _modulepath;
        private int? _moduledepth;
        private bool _isbutton;
        private string _buttonid;
        private int? _sortid;
        private string _moduleimg;
        private DateTime? _lasteditdate;
        private Guid _lasteditby;
        private int _moduletype;
        private string _modulecolor;
        private string _moduledescripion;
        private bool _isshow;
        private string _helpurl;
        private string _helpimg;
        private string _helpcontent;
        private int _columnid;
        private string _columnpath;
        private bool _ismessage;
        private string _messagetype;
        private string _oldmoduleid;
        /// <summary>
        /// 应用程序ID
        /// </summary>
        public Guid AppId
        {
            set { _appid = value; }
            get { return _appid; }
        }
        /// <summary>
        /// 模块编号
        /// </summary>
        public string ModuleId
        {
            set { _moduleid = value; }
            get { return _moduleid; }
        }
        /// <summary>
        /// 功能模块名称
        /// </summary>
        public string ModuleName
        {
            set { _modulename = value; }
            get { return _modulename; }
        }
        /// <summary>
        /// 功能模块地址
        /// </summary>
        public string ModuleUrl
        {
            set { _moduleurl = value; }
            get { return _moduleurl; }
        }
        /// <summary>
        /// 上级模块
        /// </summary>
        public string ParentModuleId
        {
            set { _parentmoduleid = value; }
            get { return _parentmoduleid; }
        }
        /// <summary>
        /// 模块路径
        /// </summary>
        public string ModulePath
        {
            set { _modulepath = value; }
            get { return _modulepath; }
        }
        /// <summary>
        /// 模块深度
        /// </summary>
        public int? ModuleDepth
        {
            set { _moduledepth = value; }
            get { return _moduledepth; }
        }
        /// <summary>
        /// 
        /// </summary>
        public bool IsButton
        {
            set { _isbutton = value; }
            get { return _isbutton; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ButtonID
        {
            set { _buttonid = value; }
            get { return _buttonid; }
        }
        /// <summary>
        /// 排序
        /// </summary>
        public int? SortId
        {
            set { _sortid = value; }
            get { return _sortid; }
        }
        /// <summary>
        /// 图标
        /// </summary>
        public string ModuleImg
        {
            set { _moduleimg = value; }
            get { return _moduleimg; }
        }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? LastEditDate
        {
            set { _lasteditdate = value; }
            get { return _lasteditdate; }
        }
        /// <summary>
        /// 最后更新人
        /// </summary>
        public Guid LastEditBy
        {
            set { _lasteditby = value; }
            get { return _lasteditby; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int ModuleType
        {
            set { _moduletype = value; }
            get { return _moduletype; }
        }
        /// <summary>
        /// 颜色
        /// </summary>
        public string ModuleColor
        {
            set { _modulecolor = value; }
            get { return _modulecolor; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ModuleDescripion
        {
            set { _moduledescripion = value; }
            get { return _moduledescripion; }
        }
        /// <summary>
        /// 
        /// </summary>
        public bool IsShow
        {
            set { _isshow = value; }
            get { return _isshow; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string HelpUrl
        {
            set { _helpurl = value; }
            get { return _helpurl; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string HelpImg
        {
            set { _helpimg = value; }
            get { return _helpimg; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string HelpContent
        {
            set { _helpcontent = value; }
            get { return _helpcontent; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 
        /// </summary>
        public bool IsMessage
        {
            set { _ismessage = value; }
            get { return _ismessage; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string MessageType
        {
            set { _messagetype = value; }
            get { return _messagetype; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string oldModuleId
        {
            set { _oldmoduleid = value; }
            get { return _oldmoduleid; }
        }
        #endregion Model

    }
}

