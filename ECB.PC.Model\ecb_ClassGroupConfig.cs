﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 班级组员配置表
	/// </summary>
	[Serializable]
	public partial class ecb_ClassGroupConfig
	{
		public ecb_ClassGroupConfig()
		{}
		#region Model
		private Guid _id;
		private Guid _groupid;
		private Guid _classid;
		private Guid _studentid;
		private int _type;
		private DateTime _createdate;
		private Guid _createuserid;
		private int _columnid;
		private string _columnpath;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 分组ID
		/// </summary>
		public Guid GroupId
		{
			set{ _groupid=value;}
			get{return _groupid;}
		}
		/// <summary>
		/// 所属班级
		/// </summary>
		public Guid ClassId
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学生id
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 是否组长 0不是 1是组长 2副组长
		/// </summary>
		public int Type
		{
            set { _type = value; }
            get { return _type; }
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateDate
		{
			set{ _createdate=value;}
			get{return _createdate;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		#endregion Model

	}
}

