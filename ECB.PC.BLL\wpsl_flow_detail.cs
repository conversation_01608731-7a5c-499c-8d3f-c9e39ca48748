﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:wpsl_flow_detail
    /// </summary>
    public partial class wpsl_flow_detail
    {
        public wpsl_flow_detail()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from wpsl_flow_detail");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.wpsl_flow_detail model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into wpsl_flow_detail(");
            strSql.Append("Id,ColumnId,ColumnPath,FlowId,FlowName,FlowCode,PriceLine,LastEditor,LastEditTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@FlowId,@FlowName,@FlowCode,@PriceLine,@LastEditor,@LastEditTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowName", SqlDbType.NVarChar,100),
                    new SqlParameter("@FlowCode", SqlDbType.NVarChar,3),
                    new SqlParameter("@PriceLine", SqlDbType.Decimal,9),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.FlowId;
            parameters[4].Value = model.FlowName;
            parameters[5].Value = model.FlowCode;
            parameters[6].Value = model.PriceLine;
            parameters[7].Value = model.LastEditor;
            parameters[8].Value = model.LastEditTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.wpsl_flow_detail model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update wpsl_flow_detail set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("FlowId=@FlowId,");
            strSql.Append("FlowName=@FlowName,");
            strSql.Append("FlowCode=@FlowCode,");
            strSql.Append("PriceLine=@PriceLine,");
            strSql.Append("LastEditor=@LastEditor,");
            strSql.Append("LastEditTime=@LastEditTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowName", SqlDbType.NVarChar,100),
                    new SqlParameter("@FlowCode", SqlDbType.NVarChar,3),
                    new SqlParameter("@PriceLine", SqlDbType.Decimal,9),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.FlowId;
            parameters[3].Value = model.FlowName;
            parameters[4].Value = model.FlowCode;
            parameters[5].Value = model.PriceLine;
            parameters[6].Value = model.LastEditor;
            parameters[7].Value = model.LastEditTime;
            parameters[8].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from wpsl_flow_detail ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from wpsl_flow_detail ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.wpsl_flow_detail GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,FlowId,FlowName,FlowCode,PriceLine,LastEditor,LastEditTime from wpsl_flow_detail ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            Model.wpsl_flow_detail model = new Model.wpsl_flow_detail();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.wpsl_flow_detail DataRowToModel(DataRow row)
        {
            Model.wpsl_flow_detail model = new Model.wpsl_flow_detail();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["FlowId"] != null && row["FlowId"].ToString() != "")
                {
                    model.FlowId = new Guid(row["FlowId"].ToString());
                }
                if (row["FlowName"] != null)
                {
                    model.FlowName = row["FlowName"].ToString();
                }
                if (row["FlowCode"] != null)
                {
                    model.FlowCode = row["FlowCode"].ToString();
                }
                if (row["PriceLine"] != null && row["PriceLine"].ToString() != "")
                {
                    model.PriceLine = decimal.Parse(row["PriceLine"].ToString());
                }
                if (row["LastEditor"] != null && row["LastEditor"].ToString() != "")
                {
                    model.LastEditor = new Guid(row["LastEditor"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,FlowId,FlowName,FlowCode,PriceLine,LastEditor,LastEditTime ");
            strSql.Append(" FROM wpsl_flow_detail ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,FlowId,FlowName,FlowCode,PriceLine,LastEditor,LastEditTime ");
            strSql.Append(" FROM wpsl_flow_detail ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM wpsl_flow_detail ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from wpsl_flow_detail T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "wpsl_flow_detail";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获取最大审核流程编码
        /// </summary>
        /// <returns></returns>
        public int GetMaxFlowCode(Guid FlowId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ISNULL(MAX(FlowCode),200) ");
            strSql.Append(" FROM wpsl_flow_detail ");
            strSql.Append(" where FlowId='" + FlowId + "'AND SUBSTRING(FlowCode,1,1)=2");
            DataSet ds = DbHelperSQL.Query(strSql.ToString());
            if (ds.Tables[0].Rows.Count > 0)
            {
                return int.Parse(ds.Tables[0].Rows[0][0].ToString());
            }
            else return 200;
        }

        /// <summary>
        /// 获取初始流程信息
        /// </summary>
        /// <param name="columnId">地区</param>
        /// <param name="typeCode">流程类型</param>
        /// <returns></returns>
        public Model.wpsl_flow_detail GetFlow(int columnId, string typeCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT top 1 b.* FROM dbo.wpsl_flows a RIGHT JOIN dbo.wpsl_flow_detail b ON a.ColumnId=b.ColumnId AND a.Id=b.FlowId ");
            strSql.Append(" where a.ColumnId=@ColumnId and a.TypeCode=@TypeCode and a.IsEnable=1 order by b.FlowCode");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@TypeCode", SqlDbType.NVarChar,20)};
            parameters[0].Value = columnId;
            parameters[1].Value = typeCode;
            Model.wpsl_flow_detail model = new Model.wpsl_flow_detail();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 根据条件获取到初始的流程节点
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <param name="typeCode">流程类型</param>
        /// <param name="priceTotal">金额（要额度大于申请的金额）</param>
        /// <returns></returns>
        public Model.wpsl_flow_detail GetFlow(int columnId, string typeCode, decimal priceTotal=0)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT top 1 b.* FROM dbo.wpsl_flows a RIGHT JOIN dbo.wpsl_flow_detail b ON a.ColumnId=b.ColumnId AND a.Id=b.FlowId ");
            strSql.Append(" where a.ColumnId=@ColumnId and a.TypeCode=@TypeCode and b.PriceLine>@PriceTotal and a.IsEnable=1 order by b.FlowCode");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@TypeCode", SqlDbType.NVarChar,1),
                    new SqlParameter("@PriceTotal", SqlDbType.Decimal,9)};
            parameters[0].Value = columnId;
            parameters[1].Value = typeCode;
            parameters[2].Value = priceTotal;
            Model.wpsl_flow_detail model = new Model.wpsl_flow_detail();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        #endregion  ExtensionMethod
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsbyFlowIdCode(Guid FlowId, string FlowCode)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from wpsl_flow_detail");
            strSql.Append(" where FlowId=@FlowId and FlowCode=@FlowCode ");
            SqlParameter[] parameters = {
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode",SqlDbType.NVarChar,10)
                                        };
            parameters[0].Value = FlowId;
            parameters[1].Value = FlowCode;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.wpsl_flow_detail GetModelByflowIdCode(Guid FlowId, string FlowCode)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,FlowId,FlowName,FlowCode,PriceLine,LastEditor,LastEditTime from wpsl_flow_detail ");
            strSql.Append(" where FlowId=@FlowId and FlowCode=@FlowCode ");
            SqlParameter[] parameters = {
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode",SqlDbType.NVarChar,20)
                                        };
            parameters[0].Value = FlowId;
            parameters[1].Value = FlowCode;

            Model.wpsl_flow_detail model = new Model.wpsl_flow_detail();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
    }
}

