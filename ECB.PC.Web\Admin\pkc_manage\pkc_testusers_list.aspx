﻿<%@ Page Language="C#" AutoEventWireup="true" StylesheetTheme="Admin_Default" EnableEventValidation="false" CodeBehind="pkc_testusers_list.aspx.cs" Inherits="ECB.PC.Web.Admin.pkc_manage.pkc_testusers_list" %>

<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>考生考试考场信息管理</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/js/layer/layer.js"></script>
    <script src="/admin/js/Common.js"></script>
</head>
<body>
    <form id="form1" runat="server">

        <div id="container">
            <div style="display: block" id="div1">
                <asp:Panel ID="pnlAdminTop" CssClass="search-bar" runat="server">
                    <asp:Panel ID="pnlArea" runat="server">
                        <asp:Panel ID="pnlSchool" runat="server">
                            辖区选项：<asp:DropDownList ID="S1" runat="server" EnableViewState="True">
                                <asp:ListItem Value="" Selected="True">==请选择地市==</asp:ListItem>
                            </asp:DropDownList>
                            <asp:DropDownList ID="S2" runat="server" EnableViewState="True">
                                <asp:ListItem Value="" Selected="True">==请选择县区==</asp:ListItem>
                            </asp:DropDownList>
                            <asp:DropDownList ID="S3" runat="server" EnableViewState="True" ValidationGroup="1">
                                <asp:ListItem Value="" Selected="True">==请选择学校==</asp:ListItem>
                            </asp:DropDownList>
                            <asp:DropDownList ID="ddlChildSchool" runat="server" EnableViewState="True" Style="display: none;">
                                <asp:ListItem Value="" Selected="True">==请选附属学校==</asp:ListItem>
                            </asp:DropDownList>
                        </asp:Panel>
                        <asp:Panel ID="pnlclass" runat="server">
                            年级：<asp:DropDownList ID="ddlGrade" runat="server" ></asp:DropDownList>
                            班级：<asp:DropDownList ID="ddlClass" runat="server" ></asp:DropDownList>
                            <br />
                        </asp:Panel>
                        <asp:Panel ID="pnlroom" runat="server" Style="display: inline">
                            考场：<asp:DropDownList ID="ddlRoom" runat="server" ></asp:DropDownList>
                        </asp:Panel>
                        显示：<asp:DropDownList ID="dorpShowSize" runat="server">
                            <asp:ListItem Value="10">10</asp:ListItem>
                            <asp:ListItem Value="15">15</asp:ListItem>
                            <asp:ListItem Value="20">20</asp:ListItem>
                            <asp:ListItem Value="50">50</asp:ListItem>
                            <asp:ListItem Value="80">80</asp:ListItem>
                            <asp:ListItem Value="100">100</asp:ListItem>
                        </asp:DropDownList>
                        文理：<asp:DropDownList ID="ddlPropertyCode" runat="server" Width="100px"></asp:DropDownList>
                        查询：<asp:DropDownList ID="dropType" runat="server">
                            <asp:ListItem Value="0">=请选择=</asp:ListItem>
                            <asp:ListItem Value="1" Selected="True">考生姓名</asp:ListItem>
                            <asp:ListItem Value="2">学籍号</asp:ListItem>
                        </asp:DropDownList>
                        <asp:TextBox ID="inputName" runat="server"></asp:TextBox>
                        <asp:Button ID="btnSearch" runat="server" Text="查 找" CssClass="btn" OnClick="btnSearch_Click" ValidationGroup="1" />
                    </asp:Panel>
                </asp:Panel>

                <%--     <asp:ScriptManager ID="ScriptManager1" runat="server" EnablePartialRendering="true"></asp:ScriptManager>
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>--%>
                <asp:GridView ID="gvTestUsersList" runat="server" Width="100%" CellPadding="4" SkinID="gridviewSkin1" EmptyDataText="未找到信息"
                    BorderWidth="1px" DataKeyNames="TestUserId"
                    AutoGenerateColumns="False" RowStyle-HorizontalAlign="Center">
                    <Columns>
                        <asp:TemplateField HeaderText="选择">
                            <ItemTemplate>
                                <label class="chkItem">
                                <asp:CheckBox ID="chkItem1" runat="server" />
                                <asp:HiddenField ID="hidID" runat="server" Value='<%# Eval("TestUserId") %>' />
                                    </label>
                            </ItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" Width="40px" />
                            <ItemStyle HorizontalAlign="Center" />
                        </asp:TemplateField>
                        <asp:BoundField DataField="StudentName" HeaderText="考生姓名" SortExpression="StudentName">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="StudentCode" HeaderText="学籍号" SortExpression="StudentCode">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="ClassName" HeaderText="所属班级" SortExpression="ClassName">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="PropertyName" HeaderText="文理" SortExpression="PropertyName">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="AbilityValue" HeaderText="参考分数" SortExpression="AbilityValue">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="TicketNumber" HeaderText="准考证号" SortExpression="TicketNumber">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="RoomNum" HeaderText="考场号" SortExpression="RoomNum">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="SeatNumber" HeaderText="座位号" SortExpression="SeatNumber">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:BoundField DataField="RoomLocation" HeaderText="考场所在位置" SortExpression="RoomLocation">
                            <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        </asp:BoundField>
                        <asp:TemplateField HeaderText="操作">
                            <ItemTemplate>
                                <asp:Button ID="btnDelete" runat="server" Text="删除" CssClass="btnRed light" OnClick="btnDelete_Click" />
                            </ItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" />
                        </asp:TemplateField>
                    </Columns>
                </asp:GridView>
                <div id="pager" class="paging-bar">
                    <div class="l-btns">
                        <span>显示</span><asp:TextBox ID="txtPageNum" runat="server"
                            AutoPostBack="True"
                            CssClass="pagenum" onkeydown="return checkNumber(event);"
                            OnTextChanged="txtPageNum_TextChanged"></asp:TextBox><span>条/页</span>
                    </div>
                    <webdiyer:AspNetPager ID="AspNetPager1" runat="server" AlwaysShow="True" AlwaysShowFirstLastPageNumber="True" CssClass="paging-default" CurrentPageButtonClass="current" CurrentPageButtonPosition="Center" CustomInfoHTML="&lt;span&gt;共%RecordCount%条记录&lt;/span&gt;" CustomInfoSectionWidth="" CustomInfoStyle="" CustomInfoTextAlign="NotSet" FirstPageText="首页" LastPageText="尾页" NavigationToolTipTextFormatString="{0}" NextPageText="下一页" NumericButtonCount="7" OnPageChanged="AspNetPager1_PageChanged" PageIndexBoxType="TextBox" PageSize="10" PagingButtonSpacing="0px" PrevPageText="上一页" ShowCustomInfoSection="Left" ShowFirstLast="False" ShowNavigationToolTip="True" ShowPageIndexBox="Never" UrlPaging="false">
                    </webdiyer:AspNetPager>
                </div>

                <asp:Panel ID="pnlButtons" runat="server" CssClass="footer-bar">
                <label for="chkAll" class="btnWhite light">
	            <input id="chkAll" name="chkAll" type="checkbox" />全选
            </label>
                    <asp:Button ID="btnImport" runat="server" Text="导入考生" CssClass="btnGreen" OnClientClick="HidePanel()" OnClick="btnImport_Click" />
                    <asp:Panel ID="pnlAbility" runat="server" Style="display: inline;">
                        <span style="color: red; font-size: medium"><b>选择一次考试成绩作为参考分数</b></span>
                        <asp:DropDownList ID="ddlTests" runat="server" ></asp:DropDownList>
                        <asp:Button ID="btnAbilityValue" runat="server" Text="1.导入分数" CssClass="btnWhite" OnClientClick="HidePanel()" OnClick="btnAbilityValue_Click" />
                    </asp:Panel>
                    <input id="btnPKC" type="button"  runat="server" value="2.排考场" class="btnGreen" onclick="ShowPKC(this)" />
                    <asp:Button ID="btnDel" runat="server" Text="批量删除" OnClientClick='return CheckDoAll("该操作将删除，不可恢复，是否删除?","btnDel")' CssClass="btnRed" OnClick="btnDelete_Click" />
                    <asp:Button ID="btnDelAll" runat="server" Text="全部删除" OnClientClick='return CheckDo("该操作将删除本次考试所有考生数据，不可恢复，是否删除?","btnDelAll")' CssClass="btnRed" OnClick="btnDelAll_Click" />
                    <asp:Button ID="btnBack" runat="server" Text="返回" CssClass="btnWhite" OnClick="btnBack_Click" />
                    <asp:Panel ID="pnlExpor" runat="server" Style="display: inline">
                        <br />
            
                        <select id="dropShowDao" name="dropShowDao" class="select-control-wh120" runat="server">
                            <option value="1">按学籍号导出（默认）</option>
                            <option value="2">按学号导出</option>

                        </select>
                        
                        <asp:Button ID="btnExportByClass" runat="server" Text="按班级导出" CssClass="btnWhite" OnClick="btnExportByClass_Click" />
                        <asp:Button ID="btnExportByRoom" runat="server" Text="按考场导出" CssClass="btnWhite" OnClick="btnExportByRoom_Click" />
                        <asp:Button ID="btnExportByCheck" runat="server" Text="导出考场考生信息核对表" CssClass="btnWhite" OnClick="btnExportByCheck_Click" />
                        <asp:Button ID="btnExportByLayout" runat="server" Text="按布局导出" CssClass="btnWhite" OnClick="btnExportByLayout_Click" />
                        <asp:Button ID="btnExportByTicket" runat="server" Text="导出准考证" CssClass="btnWhite" OnClick="btnExportByTicket_Click" />
                        <asp:Button ID="btnExportAllStudents" runat="server" Text="导出考生信息" CssClass="btnWhite" OnClick="btnExportAllStudents_Click" />
                        <asp:Button ID="btnExportAllStudentsToExcel" runat="server" Text="导出考生总表" CssClass="btnWhite" OnClick="btnExportAllStudentsToExcel_Click"  />
                    </asp:Panel>
                </asp:Panel>
                <asp:Panel ID="pnlHeadmaster" runat="server" Visible="false">
                    <asp:CheckBox ID="CheckBox1" runat="server" Text="全选" />
                    <asp:Button ID="Button1" runat="server" Text="返回" CssClass="btnWhite" OnClick="btnBack_Click" />
                </asp:Panel>
                &nbsp<span runat="server" style="color: red; font-size: medium" visible="false" id="spnMessage"><b>注意：导出数据表格需要一定时间，请耐心等待！</b></span>

                <%--               </ContentTemplate>
                </asp:UpdatePanel>--%>
            </div>

            <div style="display: none;" id="loading">
                处理中……
            </div>
        </div>
        <asp:HiddenField ID="hidSchoolId" runat="server" Value="" />
        <asp:HiddenField ID="hidChildSchool" runat="server" Value="" />
        <asp:HiddenField ID="hidGradeId" runat="server" />
        <asp:HiddenField ID="hidClassId" runat="server" />
        <asp:HiddenField ID="hidContentCode" runat="server" />
    </form>
    <%--     <script src="../js/pkc_testusers.js"></script>--%>
    <script type="text/javascript">
        //以下是实现级联菜单
        // 市
        function select1() {
            $.ajax(
            {
                type: "post",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "province" },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#S1").append("<option value=" + msg[i].ProvinceID + ">" + msg[i].ProvinceName + "</option>");
                    }
                    select2();
                }
            })
        };
        // 县区
        function select2() {
            $("#S2").html("");
            $("#S2").append("<option value=\"\" selected=\"selected\">==请选择县区==</option>");
            $.ajax(
            {
                type: "post",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "city", "provinceID": $('#S1').val() },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#S2").append("<option value=" + msg[i].CityID + ">" + msg[i].CityName + "</option>");
                    }
                    select3();
                }
            })
        };
        // 学校
        function select3() {
            $("#S3").html("");
            $("#S3").append("<option value=\"\" selected=\"selected\">==请选择学校==</option>");
            $.ajax(
            {
                type: "post",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "district", "cityID": $('#S2').val() },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#S3").append("<option value=" + msg[i].DistrictID + ">" + msg[i].DistrictName + "</option>");
                    }
                    selectChildSchool();
                }
            })
        };
        // 附属学校
        function selectChildSchool() {
            $("#ddlChildSchool").html("");
            $("#ddlChildSchool").append("<option value=\"\" selected=\"selected\">==请选择附属学校==</option>");
            $.ajax(
            {
                type: "post",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "childSchool", "schoolId": $('#S3').val() },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#ddlChildSchool").append("<option value=" + msg[i].SchoolId + ">" + msg[i].SchoolName + "</option>");
                    }
                    if (msg.length > 0) {
                        $('#ddlChildSchool').css("display", "inline-block");
                    }
                    else {
                        $('#ddlChildSchool').css("display", "none");
                    }
                }
            });
            selectGrade();
        };
        function selectGrade() {
            $('#ddlGrade').html("");
            $('#ddlGrade').append("<option value=\"\" selected=\"selected\">==请选择年级==</option>");
            $.ajax({
                type: "post",
                dataType: "json",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "grade", "schoolId": $('#hidSchoolId').val() },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#ddlGrade").append("<option value=" + msg[i].ID + ">" + msg[i].GradeName + "</option>");
                    }
                    selectClass();
                }
            });
        };
        function selectClass() {
            $('#ddlClass').html("");
            $('#ddlClass').append("<option value=\"\" selected=\"selected\">==请选择班级==</option>");
            $.ajax({
                type: "post",
                dataType: "json",
                url: "/admin/ajax/getSchool.ashx",
                data: { "type": "class", "gradeId": $('#ddlGrade').val() },
                success: function (msg) {
                    for (var i = 0; i < msg.length; i++) {
                        $("#ddlClass").append("<option value=" + msg[i].ID + ">" + msg[i].ClassName + "</option>");
                    }
                }
            });
        };

        $(document).ready(function () {
            //级联菜单
            $('#S1').bind("change", select2);
            $('#S2').bind("change", select3);
            $('#S3').bind("change", selectChildSchool);
            $('#ddlChildSchool').bind("change", selectGrade);
            $('#ddlGrade').bind("change", selectClass);
            // 年级
            $('#ddlGrade').change(function () {
                $('#hidClassId').val("");
                $('#hidGradeId').val($('#ddlGrade').val());
            });
            // 班级
            $('#ddlClass').change(function () {
                $('#hidClassId').val($('#ddlClass').val());
            });

            //  第一次加载判断是否有附属学校，有则显示
            if ($("#ddlChildSchool").children().length > 1) {
                $('#ddlChildSchool').css("display", "inline-block");
            }
            // 每次变动都给隐藏域赋值
            $('#S1').change(function () {
                $('#hidChildSchool').val("");
                $('#hidSchoolId').val($('#S1').val());
            });

            $('#S2').change(function () {
                $('#hidChildSchool').val("");
                if ($('#S2').val() != "") {
                    $('#hidSchoolId').val($('#S2').val());
                }
                else {
                    if ($('#S1').length > 0 && $('#S1').val() != "") {
                        $('#hidSchoolId').val($('#S1').val());
                    }
                    else {
                        $('#hidSchoolId').val("");
                    }
                }
            });

            $('#S3').change(function () {
                $('#hidChildSchool').val("");
                if ($('#S3').val() != "") {
                    $('#hidSchoolId').val($('#S3').val());
                }
                else {
                    if ($('#S2').length > 0 && $('#S2').val() != "") {
                        $('#hidSchoolId').val($('#S2').val());
                    }
                    else if ($('#S1').length > 0 && $('#S1').val() != "") {
                        $('#hidSchoolId').val($('#S1').val());
                    }
                    else {
                        $('#hidSchoolId').val("");
                    }
                }
            });
            $('#ddlChildSchool').change(function () {
                $('#hidChildSchool').val("");
                if ($('#ddlChildSchool').length > 0 && $('#ddlChildSchool').val() != "") {
                    // 给学校隐藏域赋值
                    if ($('#hidSchoolId').length > 0) {
                        $('#hidSchoolId').val($('#S3').val());
                    }
                    // 给附属学校隐藏域赋值
                    $('#hidChildSchool').val($('#ddlChildSchool').val());
                }
                else if ($('#S3').length > 0 && $('#S3').val() != "") {
                    $('#hidSchoolId').val($('#S3').val());
                }
                else if ($('#S2').length > 0 && $('#S2').val() != "") {
                    $('#hidSchoolId').val($('#S2').val());
                }
                else if ($('#S1').length > 0 && $('#S1').val() != "") {
                    $('#hidSchoolId').val($('#S1').val());
                }
                else {
                    $('#hidSchoolId').val("");
                }
            });
            //结束级联菜单
        });

        // colorbox添加
        function ShowPKC(e) {
            var url = '<%= "/admin/pkc_manage/pkc_setting.aspx?t=" + YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(Request.QueryString["MenuId"].ToString()) + "&ctlId=btnPKC&id="+Request.QueryString["id"]%>';
            layer.open({
                type: 2,
                title: '设置',
                area: ['600px', '250px'],
                content: url
            });
            return false;
        };
        function HidePanel() {
            $("#div1").hide().addClass();
            $("#loading").show().addClass();
        }
    </script>
</body>
</html>
