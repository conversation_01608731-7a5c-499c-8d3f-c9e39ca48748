﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
	/// <summary>
	/// 年级职务信息表
	/// </summary>
	[Serializable]
	public partial class JC_GradeLeader
	{
		public JC_GradeLeader()
		{ }
		#region Model
		private Guid _id;
		private Guid _gradeid;
		private Guid _gradeleaderid;
		private string _dutytypecode;
		private string _statuscode;
		private int? _schoolColumnID;
		private string _schoolColumnPath;
		public string SchoolYear;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid ID
		{
			set { _id = value; }
			get { return _id; }
		}
		/// <summary>
		/// 所属年级
		/// </summary>
		public Guid GradeID
		{
			set { _gradeid = value; }
			get { return _gradeid; }
		}
		/// <summary>
		/// 年级组长
		/// </summary>
		public Guid GradeLeaderId
		{
			set { _gradeleaderid = value; }
			get { return _gradeleaderid; }
		}
		/// <summary>
		/// 职务类型
		/// </summary>
		public string DutyTypeCode
		{
			set { _dutytypecode = value; }
			get { return _dutytypecode; }
		}
		/// <summary>
		/// 状态
		/// </summary>
		public string StatusCode
		{
			set { _statuscode = value; }
			get { return _statuscode; }
		}
		/// <summary>
		/// 学校ColumnID
		/// </summary>
		public int? SchoolColumnID
		{
			get { return _schoolColumnID; }
			set { _schoolColumnID = value; }
		}
		/// <summary>
		/// 学校ColumnPath
		/// </summary>
		public string SchoolColumnPath
		{
			get { return _schoolColumnPath; }
			set { _schoolColumnPath = value; }
		}
		#endregion Model

	}
}
