﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_SchoolStars
	/// </summary>
	public partial class ecb_SchoolStars
	{
		public ecb_SchoolStars()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_SchoolStars");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_SchoolStars model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_SchoolStars(");
			strSql.Append("Id,ColumnId,ColumnPath,TypeId,StudentId,CreateUserId,CreateTime,PraiseDesc,ClassId,GradeId,TermId,IsPass,IsPassBy,IsPassDate)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@TypeId,@StudentId,@CreateUserId,@CreateTime,@PraiseDesc,@ClassId,@GradeId,@TermId,@IsPass,@IsPassBy,@IsPassDate)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
					new SqlParameter("@TypeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@PraiseDesc", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime)};
			parameters[0].Value = model.Id;
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.TypeId;
			parameters[4].Value = model.StudentId;
			parameters[5].Value = model.CreateUserId;
			parameters[6].Value = model.CreateTime;
			parameters[7].Value = model.PraiseDesc;
			parameters[8].Value = model.ClassId;
			parameters[9].Value = model.GradeId;
			parameters[10].Value = model.TermId;
            parameters[11].Value = model.IsPass;
            parameters[12].Value = model.IsPassBy;
            parameters[13].Value = model.IsPassDate;

            int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_SchoolStars model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_SchoolStars set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("TypeId=@TypeId,");
			strSql.Append("StudentId=@StudentId,");
			strSql.Append("CreateUserId=@CreateUserId,");
			strSql.Append("CreateTime=@CreateTime,");
			strSql.Append("PraiseDesc=@PraiseDesc,");
			strSql.Append("ClassId=@ClassId,");
			strSql.Append("GradeId=@GradeId,");
			strSql.Append("TermId=@TermId,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("IsPassDate=@IsPassDate");
            strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
					new SqlParameter("@TypeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@PraiseDesc", SqlDbType.NVarChar,-1),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.TypeId;
			parameters[3].Value = model.StudentId;
			parameters[4].Value = model.CreateUserId;
			parameters[5].Value = model.CreateTime;
			parameters[6].Value = model.PraiseDesc;
			parameters[7].Value = model.ClassId;
			parameters[8].Value = model.GradeId;
			parameters[9].Value = model.TermId;
            parameters[10].Value = model.IsPass;
            parameters[11].Value = model.IsPassBy;
            parameters[12].Value = model.IsPassDate;
            parameters[13].Value = model.Id;

            int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_SchoolStars ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_SchoolStars ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_SchoolStars GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,TypeId,StudentId,CreateUserId,CreateTime,PraiseDesc,ClassId,GradeId,TermId,IsPass,IsPassBy,IsPassDate  from ecb_SchoolStars ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_SchoolStars model=new ECB.PC.Model.ecb_SchoolStars();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_SchoolStars DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_SchoolStars model=new ECB.PC.Model.ecb_SchoolStars();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["TypeId"]!=null && row["TypeId"].ToString()!="")
				{
					model.TypeId= new Guid(row["TypeId"].ToString());
				}
				if(row["StudentId"]!=null && row["StudentId"].ToString()!="")
				{
					model.StudentId= new Guid(row["StudentId"].ToString());
				}
				if(row["CreateUserId"]!=null && row["CreateUserId"].ToString()!="")
				{
					model.CreateUserId= new Guid(row["CreateUserId"].ToString());
				}
				if(row["CreateTime"]!=null && row["CreateTime"].ToString()!="")
				{
					model.CreateTime=DateTime.Parse(row["CreateTime"].ToString());
				}
				if(row["PraiseDesc"]!=null)
				{
					model.PraiseDesc=row["PraiseDesc"].ToString();
				}
				if(row["ClassId"]!=null && row["ClassId"].ToString()!="")
				{
					model.ClassId= new Guid(row["ClassId"].ToString());
				}
				if(row["GradeId"]!=null && row["GradeId"].ToString()!="")
				{
					model.GradeId= new Guid(row["GradeId"].ToString());
				}
				if(row["TermId"]!=null && row["TermId"].ToString()!="")
				{
					model.TermId= new Guid(row["TermId"].ToString());
				}
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    model.IsPass = int.Parse(row["IsPass"].ToString());
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
            }
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,TypeId,StudentId,CreateUserId,CreateTime,PraiseDesc,ClassId,GradeId,TermId,IsPass,IsPassBy,IsPassDate  ");
			strSql.Append(" FROM ecb_SchoolStars ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,TypeId,StudentId,CreateUserId,CreateTime,PraiseDesc,ClassId,GradeId,TermId,IsPass,IsPassBy,IsPassDate  ");
			strSql.Append(" FROM ecb_SchoolStars ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_SchoolStars ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_SchoolStars T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_SchoolStars";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsbyType(int columnId, Guid TermId, Guid TypeId, Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_SchoolStars");
            strSql.Append(" where ColumnId=@ColumnId and TermId=@TermId and TypeId=@TypeId and Id!=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@TermId",SqlDbType.UniqueIdentifier),
                    new SqlParameter("@TypeId",SqlDbType.UniqueIdentifier),
                    new SqlParameter("@Id",SqlDbType.UniqueIdentifier)
			};
            parameters[0].Value = columnId;
            parameters[1].Value = TermId;
            parameters[2].Value = TypeId;
            parameters[3].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters,DbHelperSQL.ConnMain);
        }
	}
}

