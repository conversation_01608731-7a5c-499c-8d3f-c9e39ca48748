﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 教师到离校考勤记录表
	/// </summary>
	[Serializable]
	public partial class ecb_kq_inout_teacher
	{
		public ecb_kq_inout_teacher()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _userid;
		private int _kqstage;
		private int _kqtype;
		private DateTime _kqtime;
		private DateTime? _signtime;
		private int _kqstatus;
		/// <summary>
		/// 考勤记录id
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 考勤人id
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 考勤阶段 1上午 2下午 3晚上
		/// </summary>
		public int KQStage
		{
			set{ _kqstage=value;}
			get{return _kqstage;}
		}
		/// <summary>
		/// 打卡类型 1到校 2离校
		/// </summary>
		public int KQType
		{
			set{ _kqtype=value;}
			get{return _kqtype;}
		}
		/// <summary>
		/// 考勤时间点
		/// </summary>
		public DateTime KQTime
		{
			set{ _kqtime=value;}
			get{return _kqtime;}
		}
		/// <summary>
		/// 打卡时间
		/// </summary>
		public DateTime? SignTime
		{
			set{ _signtime=value;}
			get{return _signtime;}
		}
        /// <summary>
        /// 考勤状态  1:正常，2:迟到，3:缺卡，4:请假
        /// </summary>
        public int KQStatus
		{
			set{ _kqstatus=value;}
			get{return _kqstatus;}
		}
		#endregion Model

	}
}

