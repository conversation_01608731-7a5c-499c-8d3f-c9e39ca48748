﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:DA_XS_StudentWorks
    /// </summary>
    public partial class DA_XS_StudentWorks
    {
        public DA_XS_StudentWorks()
        { }
        #region  Method

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            int result = DbHelperSQL.RunProcedure("UP_DA_XS_StudentWorks_Exists", parameters, out rowsAffected);
            if (result == 1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.DA_XS_StudentWorks model)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentName", SqlDbType.NVarChar,50),
					new SqlParameter("@WorkName", SqlDbType.NVarChar,100),
					new SqlParameter("@Description", SqlDbType.NVarChar,-1),
					new SqlParameter("@TeacherComment", SqlDbType.NVarChar,-1),
					new SqlParameter("@IsShowInClass", SqlDbType.Bit,1),
					new SqlParameter("@IsShowInSchool", SqlDbType.Bit,1),
					new SqlParameter("@TeacherCheck", SqlDbType.Bit,1),
					new SqlParameter("@TeacherCheckUser", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TeacherCheckDate", SqlDbType.DateTime),
					new SqlParameter("@SchoolCheck", SqlDbType.Bit,1),
					new SqlParameter("@SchoolCheckUser", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolCheckDate", SqlDbType.DateTime),
					new SqlParameter("@AgreeCount", SqlDbType.Int,4),
					new SqlParameter("@DisagreeCount", SqlDbType.Int,4),
					new SqlParameter("@CreateTime", SqlDbType.DateTime)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.SchoolColumnId;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.SchoolYear;
            parameters[4].Value = model.TermId;
            parameters[5].Value = model.GradeId;
            parameters[6].Value = model.ClassId;
            parameters[7].Value = model.StudentId;
            parameters[8].Value = model.StudentName;
            parameters[9].Value = model.WorkName;
            parameters[10].Value = model.Description;
            parameters[11].Value = model.TeacherComment;
            parameters[12].Value = model.IsShowInClass;
            parameters[13].Value = model.IsShowInSchool;
            parameters[14].Value = model.TeacherCheck;
            parameters[15].Value = model.TeacherCheckUser;
            parameters[16].Value = model.TeacherCheckDate;
            parameters[17].Value = model.SchoolCheck;
            parameters[18].Value = model.SchoolCheckUser;
            parameters[19].Value = model.SchoolCheckDate;
            parameters[20].Value = model.AgreeCount;
            parameters[21].Value = model.DisagreeCount;
            parameters[22].Value = model.CreateTime;

            DbHelperSQL.RunProcedure("UP_DA_XS_StudentWorks_ADD", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.DA_XS_StudentWorks model)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolColumnId", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@TermId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@GradeId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@StudentName", SqlDbType.NVarChar,50),
					new SqlParameter("@WorkName", SqlDbType.NVarChar,100),
					new SqlParameter("@Description", SqlDbType.NVarChar,-1),
					new SqlParameter("@TeacherComment", SqlDbType.NVarChar,-1),
					new SqlParameter("@IsShowInClass", SqlDbType.Bit,1),
					new SqlParameter("@IsShowInSchool", SqlDbType.Bit,1),
					new SqlParameter("@TeacherCheck", SqlDbType.Bit,1),
					new SqlParameter("@TeacherCheckUser", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TeacherCheckDate", SqlDbType.DateTime),
					new SqlParameter("@SchoolCheck", SqlDbType.Bit,1),
					new SqlParameter("@SchoolCheckUser", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolCheckDate", SqlDbType.DateTime),
					new SqlParameter("@AgreeCount", SqlDbType.Int,4),
					new SqlParameter("@DisagreeCount", SqlDbType.Int,4),
					new SqlParameter("@CreateTime", SqlDbType.DateTime)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.SchoolColumnId;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.SchoolYear;
            parameters[4].Value = model.TermId;
            parameters[5].Value = model.GradeId;
            parameters[6].Value = model.ClassId;
            parameters[7].Value = model.StudentId;
            parameters[8].Value = model.StudentName;
            parameters[9].Value = model.WorkName;
            parameters[10].Value = model.Description;
            parameters[11].Value = model.TeacherComment;
            parameters[12].Value = model.IsShowInClass;
            parameters[13].Value = model.IsShowInSchool;
            parameters[14].Value = model.TeacherCheck;
            parameters[15].Value = model.TeacherCheckUser;
            parameters[16].Value = model.TeacherCheckDate;
            parameters[17].Value = model.SchoolCheck;
            parameters[18].Value = model.SchoolCheckUser;
            parameters[19].Value = model.SchoolCheckDate;
            parameters[20].Value = model.AgreeCount;
            parameters[21].Value = model.DisagreeCount;
            parameters[22].Value = model.CreateTime;

            DbHelperSQL.RunProcedure("UP_DA_XS_StudentWorks_Update", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {
            int rowsAffected = 0;
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            DbHelperSQL.RunProcedure("UP_DA_XS_StudentWorks_Delete", parameters, out rowsAffected);
            if (rowsAffected > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from DA_XS_StudentWorks ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_XS_StudentWorks GetModel(Guid ID)
        {
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            ECB.PC.Model.DA_XS_StudentWorks model = new ECB.PC.Model.DA_XS_StudentWorks();
            DataSet ds = DbHelperSQL.RunProcedure("UP_DA_XS_StudentWorks_GetModel", parameters, "ds");
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.DA_XS_StudentWorks DataRowToModel(DataRow row)
        {
            ECB.PC.Model.DA_XS_StudentWorks model = new ECB.PC.Model.DA_XS_StudentWorks();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["SchoolColumnId"] != null && row["SchoolColumnId"].ToString() != "")
                {
                    model.SchoolColumnId = int.Parse(row["SchoolColumnId"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
                if (row["TermId"] != null && row["TermId"].ToString() != "")
                {
                    model.TermId = new Guid(row["TermId"].ToString());
                }
                if (row["GradeId"] != null && row["GradeId"].ToString() != "")
                {
                    model.GradeId = new Guid(row["GradeId"].ToString());
                }
                if (row["ClassId"] != null && row["ClassId"].ToString() != "")
                {
                    model.ClassId = new Guid(row["ClassId"].ToString());
                }
                if (row["StudentId"] != null && row["StudentId"].ToString() != "")
                {
                    model.StudentId = new Guid(row["StudentId"].ToString());
                }
                if (row["StudentName"] != null)
                {
                    model.StudentName = row["StudentName"].ToString();
                }
                if (row["WorkName"] != null)
                {
                    model.WorkName = row["WorkName"].ToString();
                }
                if (row["Description"] != null)
                {
                    model.Description = row["Description"].ToString();
                }
                if (row["TeacherComment"] != null)
                {
                    model.TeacherComment = row["TeacherComment"].ToString();
                }
                if (row["IsShowInClass"] != null && row["IsShowInClass"].ToString() != "")
                {
                    if ((row["IsShowInClass"].ToString() == "1") || (row["IsShowInClass"].ToString().ToLower() == "true"))
                    {
                        model.IsShowInClass = true;
                    }
                    else
                    {
                        model.IsShowInClass = false;
                    }
                }
                if (row["IsShowInSchool"] != null && row["IsShowInSchool"].ToString() != "")
                {
                    if ((row["IsShowInSchool"].ToString() == "1") || (row["IsShowInSchool"].ToString().ToLower() == "true"))
                    {
                        model.IsShowInSchool = true;
                    }
                    else
                    {
                        model.IsShowInSchool = false;
                    }
                }
                if (row["TeacherCheck"] != null && row["TeacherCheck"].ToString() != "")
                {
                    if ((row["TeacherCheck"].ToString() == "1") || (row["TeacherCheck"].ToString().ToLower() == "true"))
                    {
                        model.TeacherCheck = true;
                    }
                    else
                    {
                        model.TeacherCheck = false;
                    }
                }
                if (row["TeacherCheckUser"] != null && row["TeacherCheckUser"].ToString() != "")
                {
                    model.TeacherCheckUser = new Guid(row["TeacherCheckUser"].ToString());
                }
                if (row["TeacherCheckDate"] != null && row["TeacherCheckDate"].ToString() != "")
                {
                    model.TeacherCheckDate = DateTime.Parse(row["TeacherCheckDate"].ToString());
                }
                if (row["SchoolCheck"] != null && row["SchoolCheck"].ToString() != "")
                {
                    if ((row["SchoolCheck"].ToString() == "1") || (row["SchoolCheck"].ToString().ToLower() == "true"))
                    {
                        model.SchoolCheck = true;
                    }
                    else
                    {
                        model.SchoolCheck = false;
                    }
                }
                if (row["SchoolCheckUser"] != null && row["SchoolCheckUser"].ToString() != "")
                {
                    model.SchoolCheckUser = new Guid(row["SchoolCheckUser"].ToString());
                }
                if (row["SchoolCheckDate"] != null && row["SchoolCheckDate"].ToString() != "")
                {
                    model.SchoolCheckDate = DateTime.Parse(row["SchoolCheckDate"].ToString());
                }
                if (row["AgreeCount"] != null && row["AgreeCount"].ToString() != "")
                {
                    model.AgreeCount = int.Parse(row["AgreeCount"].ToString());
                }
                if (row["DisagreeCount"] != null && row["DisagreeCount"].ToString() != "")
                {
                    model.DisagreeCount = int.Parse(row["DisagreeCount"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,SchoolColumnId,SchoolColumnPath,SchoolYear,TermId,GradeId,ClassId,StudentId,StudentName,WorkName,Description,TeacherComment,IsShowInClass,IsShowInSchool,TeacherCheck,TeacherCheckUser,TeacherCheckDate,SchoolCheck,SchoolCheckUser,SchoolCheckDate,AgreeCount,DisagreeCount,CreateTime ");
            strSql.Append(" FROM DA_XS_StudentWorks ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,SchoolColumnId,SchoolColumnPath,SchoolYear,TermId,GradeId,ClassId,StudentId,StudentName,WorkName,Description,TeacherComment,IsShowInClass,IsShowInSchool,TeacherCheck,TeacherCheckUser,TeacherCheckDate,SchoolCheck,SchoolCheckUser,SchoolCheckDate,AgreeCount,DisagreeCount,CreateTime ");
            strSql.Append(" FROM DA_XS_StudentWorks ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM DA_XS_StudentWorks ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from DA_XS_StudentWorks T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取数据列表且格式化时间
        /// </summary>
        /// <param name="strWhere"></param>
        /// <param name="strTime"></param>
        /// <returns></returns>
        public DataSet GetListAndChanegTime(string strWhere,string strTime)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append("FROM DA_XS_StudentWorks");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        #endregion  Method
        #region  MethodEx
       
        #endregion  MethodEx
    }
}

