﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_ToDayInHistory
	/// </summary>
	public partial class ecb_ToDayInHistory
	{
		public ecb_ToDayInHistory()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_ToDayInHistory");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_ToDayInHistory model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_ToDayInHistory(");
			strSql.Append("ID,Title,Content,ImgUrl,ZhaiYao,ToDay,RecordDate,RecordUserId,ColumnId,ColumnPath,IsPass,IsPassBy,IsPassDate)");
			strSql.Append(" values (");
			strSql.Append("@ID,@Title,@Content,@ImgUrl,@ZhaiYao,@ToDay,@RecordDate,@RecordUserId,@ColumnId,@ColumnPath,@IsPass,@IsPassBy,@IsPassDate)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Title", SqlDbType.NVarChar,50),
					new SqlParameter("@Content", SqlDbType.NVarChar,-1),
					new SqlParameter("@ImgUrl", SqlDbType.NVarChar,255),
					new SqlParameter("@ZhaiYao", SqlDbType.NVarChar,255),
					new SqlParameter("@ToDay", SqlDbType.Date,3),
					new SqlParameter("@RecordDate", SqlDbType.DateTime),
					new SqlParameter("@RecordUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.Title;
			parameters[2].Value = model.Content;
			parameters[3].Value = model.ImgUrl;
			parameters[4].Value = model.ZhaiYao;
			parameters[5].Value = model.ToDay;
			parameters[6].Value = model.RecordDate;
            parameters[7].Value = model.RecordUserId ;
			parameters[8].Value = model.ColumnId;
			parameters[9].Value = model.ColumnPath;
            parameters[10].Value = model.IsPass;
            parameters[11].Value = model.IsPassBy;
            parameters[12].Value = model.IsPassDate;

            int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_ToDayInHistory model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_ToDayInHistory set ");
			strSql.Append("Title=@Title,");
			strSql.Append("Content=@Content,");
			strSql.Append("ImgUrl=@ImgUrl,");
			strSql.Append("ZhaiYao=@ZhaiYao,");
			strSql.Append("ToDay=@ToDay,");
			strSql.Append("RecordDate=@RecordDate,");
			strSql.Append("RecordUserId=@RecordUserId,");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("IsPass=@IsPass,");
            strSql.Append("IsPassBy=@IsPassBy,");
            strSql.Append("IsPassDate=@IsPassDate");
            strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@Title", SqlDbType.NVarChar,50),
					new SqlParameter("@Content", SqlDbType.NVarChar,-1),
					new SqlParameter("@ImgUrl", SqlDbType.NVarChar,255),
					new SqlParameter("@ZhaiYao", SqlDbType.NVarChar,255),
					new SqlParameter("@ToDay", SqlDbType.Date,3),
					new SqlParameter("@RecordDate", SqlDbType.DateTime),
					new SqlParameter("@RecordUserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@IsPass", SqlDbType.Int,4),
                    new SqlParameter("@IsPassBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@IsPassDate", SqlDbType.DateTime),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.Title;
			parameters[1].Value = model.Content;
			parameters[2].Value = model.ImgUrl;
			parameters[3].Value = model.ZhaiYao;
			parameters[4].Value = model.ToDay;
			parameters[5].Value = model.RecordDate;
			parameters[6].Value = model.RecordUserId;
			parameters[7].Value = model.ColumnId;
			parameters[8].Value = model.ColumnPath;
            parameters[9].Value = model.IsPass;
            parameters[10].Value = model.IsPassBy;
            parameters[11].Value = model.IsPassDate;
            parameters[12].Value = model.ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_ToDayInHistory ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_ToDayInHistory ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_ToDayInHistory GetModel(Guid ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,Title,Content,ImgUrl,ZhaiYao,ToDay,RecordDate,RecordUserId,ColumnId,ColumnPath,IsPass,IsPassBy,IsPassDate from ecb_ToDayInHistory ");
			strSql.Append(" where ID=@ID ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = ID;

			ECB.PC.Model.ecb_ToDayInHistory model=new ECB.PC.Model.ecb_ToDayInHistory();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_ToDayInHistory DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_ToDayInHistory model=new ECB.PC.Model.ecb_ToDayInHistory();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["Title"]!=null)
				{
					model.Title=row["Title"].ToString();
				}
				if(row["Content"]!=null)
				{
					model.Content=row["Content"].ToString();
				}
				if(row["ImgUrl"]!=null)
				{
					model.ImgUrl=row["ImgUrl"].ToString();
				}
				if(row["ZhaiYao"]!=null)
				{
					model.ZhaiYao=row["ZhaiYao"].ToString();
				}
				if(row["ToDay"]!=null && row["ToDay"].ToString()!="")
				{
					model.ToDay=DateTime.Parse(row["ToDay"].ToString());
				}
				if(row["RecordDate"]!=null && row["RecordDate"].ToString()!="")
				{
					model.RecordDate=DateTime.Parse(row["RecordDate"].ToString());
				}
				if(row["RecordUserId"]!=null && row["RecordUserId"].ToString()!="")
				{
					model.RecordUserId= new Guid(row["RecordUserId"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
                if (row["IsPass"] != null && row["IsPass"].ToString() != "")
                {
                    model.IsPass = int.Parse(row["IsPass"].ToString());
                }
                if (row["IsPassBy"] != null && row["IsPassBy"].ToString() != "")
                {
                    model.IsPassBy = new Guid(row["IsPassBy"].ToString());
                }
                if (row["IsPassDate"] != null && row["IsPassDate"].ToString() != "")
                {
                    model.IsPassDate = DateTime.Parse(row["IsPassDate"].ToString());
                }
            }
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,Title,Content,ImgUrl,ZhaiYao,ToDay,RecordDate,RecordUserId,ColumnId,ColumnPath,IsPass,IsPassBy,IsPassDate ");
			strSql.Append(" FROM ecb_ToDayInHistory ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,Title,Content,ImgUrl,ZhaiYao,ToDay,RecordDate,RecordUserId,ColumnId,ColumnPath,IsPass,IsPassBy,IsPassDate ");
			strSql.Append(" FROM ecb_ToDayInHistory ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere, string[] ConnStr=null)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_ToDayInHistory ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString(),ConnStr);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_ToDayInHistory T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_ToDayInHistory";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod

        /// <summary>
        /// 根据标题得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_ToDayInHistory GetModel(string _title,int _columnId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,Title,Content,ImgUrl,ZhaiYao,ToDay,RecordDate,RecordUserId,ColumnId,ColumnPath,IsPass,IsPassBy,IsPassDate from ecb_ToDayInHistory ");
            strSql.Append(" where Title=@Title and  ColumnId=@ColumnId");
            SqlParameter[] parameters = {
					new SqlParameter("@Title", SqlDbType.NVarChar,50),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)};
            parameters[0].Value = _title;
            parameters[1].Value = _columnId;

            ECB.PC.Model.ecb_ToDayInHistory model = new ECB.PC.Model.ecb_ToDayInHistory();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
	}
}

