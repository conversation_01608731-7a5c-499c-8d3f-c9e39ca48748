﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 校园之星类型
	/// </summary>
	[Serializable]
	public partial class ecb_SchoolStarsType
	{
		public ecb_SchoolStarsType()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _name;
		private int _orderid;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区ID
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string Name
		{
			set{ _name=value;}
			get{return _name;}
		}
		/// <summary>
		/// 排序
		/// </summary>
		public int OrderId
		{
			set{ _orderid=value;}
			get{return _orderid;}
		}
		#endregion Model

	}
}

