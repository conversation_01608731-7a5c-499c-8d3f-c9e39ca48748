﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 第三方链接配置
	/// </summary>
	[Serializable]
	public partial class ecb_custom_products
	{
		public ecb_custom_products()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _productname;
		private string _producticon;
		private string _producturl;
		private int _isneedlogin;
        private int _sort;
        /// <summary>
        /// 
        /// </summary>
        public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 配置链接名称
		/// </summary>
		public string ProductName
		{
			set{ _productname=value;}
			get{return _productname;}
		}
		/// <summary>
		/// 图标
		/// </summary>
		public string ProductIcon
		{
			set{ _producticon=value;}
			get{return _producticon;}
		}
		/// <summary>
		/// 链接路径
		/// </summary>
		public string ProductUrl
		{
			set{ _producturl=value;}
			get{return _producturl;}
		}
		/// <summary>
		/// 是否需要登录
		/// </summary>
		public int IsNeedLogin
		{
			set{ _isneedlogin=value;}
			get{return _isneedlogin;}
		}
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort
        {
            set { _sort = value; }
            get { return _sort; }
        }
        #endregion Model

    }
}

