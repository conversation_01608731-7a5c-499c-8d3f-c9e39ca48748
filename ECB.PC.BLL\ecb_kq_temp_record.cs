﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:ecb_kq_temp_record
	/// </summary>
	public partial class ecb_kq_temp_record
	{
		public ecb_kq_temp_record()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from ecb_kq_temp_record");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_kq_temp_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into ecb_kq_temp_record(");
			strSql.Append("Id,ColumnId,ColumnPath,AtteId,UserId,SignTime,Status,BrandsType,PlaceId,UserType)");
			strSql.Append(" values (");
			strSql.Append("@Id,@ColumnId,@ColumnPath,@AtteId,@UserId,@SignTime,@Status,@BrandsType,@PlaceId,@UserType)");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@AtteId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SignTime", SqlDbType.DateTime),
					new SqlParameter("@Status", SqlDbType.Int,4),
					new SqlParameter("@BrandsType", SqlDbType.Int,4),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@UserType", SqlDbType.Int,4)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.ColumnId;
			parameters[2].Value = model.ColumnPath;
			parameters[3].Value = model.AtteId;
			parameters[4].Value = model.UserId;
			parameters[5].Value = model.SignTime;
			parameters[6].Value = model.Status;
			parameters[7].Value = model.BrandsType;
			parameters[8].Value = model.PlaceId;
			parameters[9].Value = model.UserType;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.ecb_kq_temp_record model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update ecb_kq_temp_record set ");
			strSql.Append("ColumnId=@ColumnId,");
			strSql.Append("ColumnPath=@ColumnPath,");
			strSql.Append("AtteId=@AtteId,");
			strSql.Append("UserId=@UserId,");
			strSql.Append("SignTime=@SignTime,");
			strSql.Append("Status=@Status,");
			strSql.Append("BrandsType=@BrandsType,");
			strSql.Append("PlaceId=@PlaceId");
			strSql.Append("UserType=@UserType");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@AtteId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SignTime", SqlDbType.DateTime),
					new SqlParameter("@Status", SqlDbType.Int,4),
					new SqlParameter("@BrandsType", SqlDbType.Int,4),
					new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@UserType", SqlDbType.Int,4)};
			parameters[0].Value = model.ColumnId;
			parameters[1].Value = model.ColumnPath;
			parameters[2].Value = model.AtteId;
			parameters[3].Value = model.UserId;
			parameters[4].Value = model.SignTime;
			parameters[5].Value = model.Status;
			parameters[6].Value = model.BrandsType;
			parameters[7].Value = model.PlaceId;
			parameters[8].Value = model.Id;
			parameters[9].Value = model.UserType;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_kq_temp_record ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from ecb_kq_temp_record ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_kq_temp_record GetModel(Guid Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,ColumnId,ColumnPath,AtteId,UserId,SignTime,Status,BrandsType,PlaceId,UserType from ecb_kq_temp_record ");
			strSql.Append(" where Id=@Id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = Id;

			ECB.PC.Model.ecb_kq_temp_record model=new ECB.PC.Model.ecb_kq_temp_record();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.ecb_kq_temp_record DataRowToModel(DataRow row)
		{
			ECB.PC.Model.ecb_kq_temp_record model=new ECB.PC.Model.ecb_kq_temp_record();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id= new Guid(row["Id"].ToString());
				}
				if(row["ColumnId"]!=null && row["ColumnId"].ToString()!="")
				{
					model.ColumnId=int.Parse(row["ColumnId"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
				if(row["AtteId"]!=null && row["AtteId"].ToString()!="")
				{
					model.AtteId= new Guid(row["AtteId"].ToString());
				}
				if(row["UserId"]!=null && row["UserId"].ToString()!="")
				{
					model.UserId= new Guid(row["UserId"].ToString());
				}
				if(row["SignTime"]!=null && row["SignTime"].ToString()!="")
				{
					model.SignTime=DateTime.Parse(row["SignTime"].ToString());
				}
				if(row["Status"]!=null && row["Status"].ToString()!="")
				{
					model.Status=int.Parse(row["Status"].ToString());
				}
				if(row["BrandsType"]!=null && row["BrandsType"].ToString()!="")
				{
					model.BrandsType=int.Parse(row["BrandsType"].ToString());
				}
				if(row["PlaceId"]!=null && row["PlaceId"].ToString()!="")
				{
					model.PlaceId= new Guid(row["PlaceId"].ToString());
				}
				if (row["UserType"] != null && row["UserType"].ToString() != "")
				{
					model.UserType = int.Parse(row["UserType"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,ColumnId,ColumnPath,AtteId,UserId,SignTime,Status,BrandsType,PlaceId,UserType ");
			strSql.Append(" FROM ecb_kq_temp_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,ColumnId,ColumnPath,AtteId,UserId,SignTime,Status,BrandsType,PlaceId,UserType ");
			strSql.Append(" FROM ecb_kq_temp_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM ecb_kq_temp_record ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from ecb_kq_temp_record T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_kq_temp_record";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod
		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCountbyPlace(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM ecb_kq_temp_record a left join ecb_kq_temp b on a.AtteId=b.Id ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		#endregion  ExtensionMethod
	}
}

