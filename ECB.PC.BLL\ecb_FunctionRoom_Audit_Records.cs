﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:Ecb_FunctionRoom_Audit_Records
    /// </summary>
    public partial class ecb_FunctionRoom_Audit_Records
    {
        public ecb_FunctionRoom_Audit_Records()
        { }

        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_FunctionRoom_Audit_Records");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_FunctionRoom_Audit_Records model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_FunctionRoom_Audit_Records(");
            strSql.Append("Id,ColumnId,ColumnPath,ReservationId,FlowId,FlowCode,CheckResult,Reason,CheckUserId,CheckTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@ReservationId,@FlowId,@FlowCode,@CheckResult,@Reason,@CheckUserId,@CheckTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ReservationId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@CheckResult", SqlDbType.Int,4),
                    new SqlParameter("@Reason", SqlDbType.NVarChar,2000),
                    new SqlParameter("@CheckUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CheckTime", SqlDbType.DateTime)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ReservationId;
            parameters[4].Value = model.FlowId;
            parameters[5].Value = model.FlowCode;
            parameters[6].Value = model.CheckResult;
            parameters[7].Value = model.Reason;
            parameters[8].Value = model.CheckUserId;
            parameters[9].Value = model.CheckTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_FunctionRoom_Audit_Records model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_FunctionRoom_Audit_Records set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ReservationId=@ReservationId,");
            strSql.Append("FlowId=@FlowId,");
            strSql.Append("FlowCode=@FlowCode,");
            strSql.Append("CheckResult=@CheckResult,");
            strSql.Append("Reason=@Reason,");
            strSql.Append("CheckUserId=@CheckUserId,");
            strSql.Append("CheckTime=@CheckTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@ReservationId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowCode", SqlDbType.NVarChar,10),
                    new SqlParameter("@CheckResult", SqlDbType.Int,4),
                    new SqlParameter("@Reason", SqlDbType.NVarChar,2000),
                    new SqlParameter("@CheckUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CheckTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.ReservationId;
            parameters[3].Value = model.FlowId;
            parameters[4].Value = model.FlowCode;
            parameters[5].Value = model.CheckResult;
            parameters[6].Value = model.Reason;
            parameters[7].Value = model.CheckUserId;
            parameters[8].Value = model.CheckTime;
            parameters[9].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_FunctionRoom_Audit_Records ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_FunctionRoom_Audit_Records ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_FunctionRoom_Audit_Records GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,ReservationId,FlowId,FlowCode,CheckResult,Reason,CheckUserId,CheckTime from ecb_FunctionRoom_Audit_Records ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_FunctionRoom_Audit_Records model = new ECB.PC.Model.ecb_FunctionRoom_Audit_Records();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_FunctionRoom_Audit_Records DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_FunctionRoom_Audit_Records model = new ECB.PC.Model.ecb_FunctionRoom_Audit_Records();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ReservationId"] != null && row["ReservationId"].ToString() != "")
                {
                    model.ReservationId = new Guid(row["ReservationId"].ToString());
                }
                if (row["FlowId"] != null && row["FlowId"].ToString() != "")
                {
                    model.FlowId = new Guid(row["FlowId"].ToString());
                }
                if (row["FlowCode"] != null)
                {
                    model.FlowCode = row["FlowCode"].ToString();
                }
                if (row["CheckResult"] != null && row["CheckResult"].ToString() != "")
                {
                    model.CheckResult = int.Parse(row["CheckResult"].ToString());
                }
                if (row["Reason"] != null)
                {
                    model.Reason = row["Reason"].ToString();
                }
                if (row["CheckUserId"] != null && row["CheckUserId"].ToString() != "")
                {
                    model.CheckUserId = new Guid(row["CheckUserId"].ToString());
                }
                if (row["CheckTime"] != null && row["CheckTime"].ToString() != "")
                {
                    model.CheckTime = DateTime.Parse(row["CheckTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,ReservationId,FlowId,FlowCode,CheckResult,Reason,CheckUserId,CheckTime ");
            strSql.Append(" FROM ecb_FunctionRoom_Audit_Records ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,ReservationId,FlowId,FlowCode,CheckResult,Reason,CheckUserId,CheckTime ");
            strSql.Append(" FROM ecb_FunctionRoom_Audit_Records ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_FunctionRoom_Audit_Records ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_FunctionRoom_Audit_Records T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Ecb_FunctionRoom_Audit_Records";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_FunctionRoom_Audit_Records GetModelRecord(Guid ReservationId, Guid FlowId, string FlowCode)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,ReservationId,FlowId,FlowCode,CheckResult,Reason,CheckUserId,CheckTime from ecb_FunctionRoom_Audit_Records ");
            strSql.Append(" where ReservationId=@ReservationId and FlowId=@FlowId and FlowCode=@FlowCode ");
            SqlParameter[] parameters = {
                                            new SqlParameter("@ReservationId",SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FlowId", SqlDbType.UniqueIdentifier,16),
                                        new SqlParameter("@FlowCode",SqlDbType.NVarChar,10)};
            parameters[0].Value = ReservationId;
            parameters[1].Value = FlowId;
            parameters[2].Value = FlowCode;

            Model.ecb_FunctionRoom_Audit_Records model = new Model.ecb_FunctionRoom_Audit_Records();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        #endregion  ExtensionMethod
    }
}

