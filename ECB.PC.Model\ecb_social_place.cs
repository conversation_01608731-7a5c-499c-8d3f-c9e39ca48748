﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 社会考试场地表
	/// </summary>
	[Serializable]
	public partial class ecb_social_place
	{
		public ecb_social_place()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _examid;
		private string _placeno;
		private Guid _placeid;
		private string _testnumber;
        private int _orderid;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnID
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 考试ID
		/// </summary>
		public Guid ExamId
		{
			set{ _examid=value;}
			get{return _examid;}
		}
		/// <summary>
		/// 考场号
		/// </summary>
		public string PlaceNo
		{
			set{ _placeno=value;}
			get{return _placeno;}
		}
		/// <summary>
		/// 关联场地
		/// </summary>
		public Guid PlaceId
		{
			set{ _placeid=value;}
			get{return _placeid;}
		}
		/// <summary>
		/// 起止考号
		/// </summary>
		public string TestNumber
		{
			set{ _testnumber=value;}
			get{return _testnumber;}
		}
        /// <summary>
		/// 排序编号
		/// </summary>
		public int OrderId
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        #endregion Model

    }
}

