﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理预警配置
	/// </summary>
	[Serializable]
	public partial class psych_week_diary_abnormal
	{
		public psych_week_diary_abnormal()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private Guid _studentid;
		private DateTime _submittime;
		private string _classname;
		private Guid _reviewerid;
		private string _abnormalkeywords;
		private string _abnormalemotions;
		private string _weekdiarypic;
		private string _abnormaltype;
		private string _abnormaltypeext;
		private string _severitylevel;
		private string _clcs;
		private string _clcs_ext;
		private string _mark;
		private string _gjrecod;
		private Guid _creatorid;
		private DateTime _createtime;
		private Guid _lasteditby;
		private DateTime _lastedittime;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 学生id
		/// </summary>
		public Guid StudentId
		{
			set{ _studentid=value;}
			get{return _studentid;}
		}
		/// <summary>
		/// 周记提交时间
		/// </summary>
		public DateTime SubmitTime
		{
			set{ _submittime=value;}
			get{return _submittime;}
		}
		/// <summary>
		/// 记录当前所在班级
		/// </summary>
		public string ClassName
		{
			set{ _classname=value;}
			get{return _classname;}
		}
		/// <summary>
		/// 批阅人
		/// </summary>
		public Guid ReviewerId
		{
			set{ _reviewerid=value;}
			get{return _reviewerid;}
		}
		/// <summary>
		/// 异常关键词
		/// </summary>
		public string AbnormalKeywords
		{
			set{ _abnormalkeywords=value;}
			get{return _abnormalkeywords;}
		}
		/// <summary>
		/// 异常情绪
		/// </summary>
		public string AbnormalEmotions
		{
			set{ _abnormalemotions=value;}
			get{return _abnormalemotions;}
		}
		/// <summary>
		/// 周记图片 |分割
		/// </summary>
		public string WeekDiaryPic
		{
			set{ _weekdiarypic=value;}
			get{return _weekdiarypic;}
		}
		/// <summary>
		/// 问题类型 字典 多选 |分割
		/// </summary>
		public string AbnormalType
		{
			set{ _abnormaltype=value;}
			get{return _abnormaltype;}
		}
		/// <summary>
		/// 问题类型扩展
		/// </summary>
		public string AbnormalTypeExt
		{
			set{ _abnormaltypeext=value;}
			get{return _abnormaltypeext;}
		}
		/// <summary>
		/// 严重级别 字典
		/// </summary>
		public string SeverityLevel
		{
			set{ _severitylevel=value;}
			get{return _severitylevel;}
		}
		/// <summary>
		/// 处理措施 字典 多选 |分割
		/// </summary>
		public string CLCS
		{
			set{ _clcs=value;}
			get{return _clcs;}
		}
		/// <summary>
		/// 处理措施扩展
		/// </summary>
		public string CLCS_Ext
		{
			set{ _clcs_ext=value;}
			get{return _clcs_ext;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Mark
		{
			set{ _mark=value;}
			get{return _mark;}
		}
		/// <summary>
		/// 跟进记录
		/// </summary>
		public string GJRecod
		{
			set{ _gjrecod=value;}
			get{return _gjrecod;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreatorId
		{
			set{ _creatorid=value;}
			get{return _creatorid;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}