﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// ecb_time_template:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class ecb_time_template
	{
		public ecb_time_template()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _tname;
		private int _days;
		private int _earlycount;
		private int _morningcount;
		private int _afternooncount;
		private int _nightcount;
		private int _courselength;
		private int _recesslength;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnID
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 模板名称
		/// </summary>
		public string TName
		{
			set{ _tname=value;}
			get{return _tname;}
		}
		/// <summary>
		/// 上课天数
		/// </summary>
		public int Days
		{
			set{ _days=value;}
			get{return _days;}
		}
		/// <summary>
		/// 早读课节
		/// </summary>
		public int EarlyCount
		{
			set{ _earlycount=value;}
			get{return _earlycount;}
		}
		/// <summary>
		/// 上午课节
		/// </summary>
		public int MorningCount
		{
			set{ _morningcount=value;}
			get{return _morningcount;}
		}
		/// <summary>
		/// 下午课节
		/// </summary>
		public int AfternoonCount
		{
			set{ _afternooncount=value;}
			get{return _afternooncount;}
		}
		/// <summary>
		/// 晚自习课节
		/// </summary>
		public int NightCount
		{
			set{ _nightcount=value;}
			get{return _nightcount;}
		}
		/// <summary>
		/// 课程时长
		/// </summary>
		public int CourseLength
		{
			set{ _courselength=value;}
			get{return _courselength;}
		}
		/// <summary>
		/// 课间时长
		/// </summary>
		public int RecessLength
		{
			set{ _recesslength=value;}
			get{return _recesslength;}
		}
		#endregion Model

	}
}

