﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 作业信息
	/// </summary>
	[Serializable]
	public partial class JC_Homeworks_Check
	{
		public JC_Homeworks_Check()
		{}
		#region Model
		private Guid _id;
		private int? _schoolcolumnid;
		private string _schoolcolumnpath;
		private Guid _gradeid;
		private Guid _classid;
		private Guid _termid;
		private string _subjectcode;
		private string _contents;
		private DateTime? _notedate;
		private Guid _userid;
		private DateTime? _finishdate;
		private string _schoolyear;
		private string _pictures;
		private int? _isfinished;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校ID
		/// </summary>
		public int? SchoolColumnID
		{
			set{ _schoolcolumnid=value;}
			get{return _schoolcolumnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string SchoolColumnPath
		{
			set{ _schoolcolumnpath=value;}
			get{return _schoolcolumnpath;}
		}
		/// <summary>
		/// 年级代码
		/// </summary>
		public Guid GradeID
		{
			set{ _gradeid=value;}
			get{return _gradeid;}
		}
		/// <summary>
		/// 班级代码
		/// </summary>
		public Guid ClassID
		{
			set{ _classid=value;}
			get{return _classid;}
		}
		/// <summary>
		/// 学期代码
		/// </summary>
		public Guid TermID
		{
			set{ _termid=value;}
			get{return _termid;}
		}
		/// <summary>
		/// 科目
		/// </summary>
		public string SubjectCode
		{
			set{ _subjectcode=value;}
			get{return _subjectcode;}
		}
		/// <summary>
		/// 内容
		/// </summary>
		public string Contents
		{
			set{ _contents=value;}
			get{return _contents;}
		}
		/// <summary>
		/// 发布时间
		/// </summary>
		public DateTime? NoteDate
		{
			set{ _notedate=value;}
			get{return _notedate;}
		}
		/// <summary>
		/// 发布人
		/// </summary>
		public Guid UserID
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 要求完成时间
		/// </summary>
		public DateTime? FinishDate
		{
			set{ _finishdate=value;}
			get{return _finishdate;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SchoolYear
		{
			set{ _schoolyear=value;}
			get{return _schoolyear;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Pictures
		{
			set{ _pictures=value;}
			get{return _pictures;}
		}
		/// <summary>
		/// 0 未完成   1 完成
		/// </summary>
		public int? IsFinished
		{
			set{ _isfinished=value;}
			get{return _isfinished;}
		}
		#endregion Model

	}
}

