﻿
using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// face_delete_log:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class face_delete_log
	{
		public face_delete_log()
		{}
		#region Model
		private Guid _id;
		private Guid _userid;
		private DateTime? _createtime;
		public int ColumnId { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}

