﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:JC_Homeworks
    /// </summary>
    public partial class JC_Homeworks
    {
        public JC_Homeworks()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from JC_Homeworks");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.JC_Homeworks model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into JC_Homeworks(");
            strSql.Append("ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,SubjectCode,Contents,NoteDate,UserID,FinishDate,SchoolYear,Pictures,IsFinished)");
            strSql.Append(" values (");
            strSql.Append("@ID,@SchoolColumnID,@SchoolColumnPath,@GradeID,@ClassID,@TermID,@SubjectCode,@Contents,@NoteDate,@UserID,@FinishDate,@SchoolYear,@Pictures,@IsFinished)");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@GradeID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SubjectCode", SqlDbType.NVarChar,10),
					new SqlParameter("@Contents", SqlDbType.NVarChar,-1),
					new SqlParameter("@NoteDate", SqlDbType.DateTime),
					new SqlParameter("@UserID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@FinishDate", SqlDbType.DateTime),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@Pictures", SqlDbType.NVarChar,-1),
					new SqlParameter("@IsFinished", SqlDbType.Int,4)};
            parameters[0].Value = model.ID;
            parameters[1].Value = model.SchoolColumnID;
            parameters[2].Value = model.SchoolColumnPath;
            parameters[3].Value = model.GradeID;
            parameters[4].Value = model.ClassID;
            parameters[5].Value = model.TermID;
            parameters[6].Value = model.SubjectCode;
            parameters[7].Value = model.Contents;
            parameters[8].Value = model.NoteDate;
            parameters[9].Value = model.UserID;
            parameters[10].Value = model.FinishDate;
            parameters[11].Value = model.SchoolYear;
            parameters[12].Value = model.Pictures;
            parameters[13].Value = model.IsFinished;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.JC_Homeworks model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update JC_Homeworks set ");
            strSql.Append("SchoolColumnID=@SchoolColumnID,");
            strSql.Append("SchoolColumnPath=@SchoolColumnPath,");
            strSql.Append("GradeID=@GradeID,");
            strSql.Append("ClassID=@ClassID,");
            strSql.Append("TermID=@TermID,");
            strSql.Append("SubjectCode=@SubjectCode,");
            strSql.Append("Contents=@Contents,");
            strSql.Append("NoteDate=@NoteDate,");
            strSql.Append("UserID=@UserID,");
            strSql.Append("FinishDate=@FinishDate,");
            strSql.Append("SchoolYear=@SchoolYear,");
            strSql.Append("Pictures=@Pictures,");
            strSql.Append("IsFinished=@IsFinished");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@SchoolColumnID", SqlDbType.Int,4),
					new SqlParameter("@SchoolColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@GradeID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ClassID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@TermID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@SubjectCode", SqlDbType.NVarChar,10),
					new SqlParameter("@Contents", SqlDbType.NVarChar,-1),
					new SqlParameter("@NoteDate", SqlDbType.DateTime),
					new SqlParameter("@UserID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@FinishDate", SqlDbType.DateTime),
					new SqlParameter("@SchoolYear", SqlDbType.NVarChar,10),
					new SqlParameter("@Pictures", SqlDbType.NVarChar,-1),
					new SqlParameter("@IsFinished", SqlDbType.Int,4),
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.SchoolColumnID;
            parameters[1].Value = model.SchoolColumnPath;
            parameters[2].Value = model.GradeID;
            parameters[3].Value = model.ClassID;
            parameters[4].Value = model.TermID;
            parameters[5].Value = model.SubjectCode;
            parameters[6].Value = model.Contents;
            parameters[7].Value = model.NoteDate;
            parameters[8].Value = model.UserID;
            parameters[9].Value = model.FinishDate;
            parameters[10].Value = model.SchoolYear;
            parameters[11].Value = model.Pictures;
            parameters[12].Value = model.IsFinished;
            parameters[13].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Homeworks ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from JC_Homeworks ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Homeworks GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,SubjectCode,Contents,NoteDate,UserID,FinishDate,SchoolYear,Pictures,IsFinished from JC_Homeworks ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = ID;

            ECB.PC.Model.JC_Homeworks model = new ECB.PC.Model.JC_Homeworks();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.JC_Homeworks DataRowToModel(DataRow row)
        {
            ECB.PC.Model.JC_Homeworks model = new ECB.PC.Model.JC_Homeworks();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["SchoolColumnID"] != null && row["SchoolColumnID"].ToString() != "")
                {
                    model.SchoolColumnID = int.Parse(row["SchoolColumnID"].ToString());
                }
                if (row["SchoolColumnPath"] != null)
                {
                    model.SchoolColumnPath = row["SchoolColumnPath"].ToString();
                }
                if (row["GradeID"] != null && row["GradeID"].ToString() != "")
                {
                    model.GradeID = new Guid(row["GradeID"].ToString());
                }
                if (row["ClassID"] != null && row["ClassID"].ToString() != "")
                {
                    model.ClassID = new Guid(row["ClassID"].ToString());
                }
                if (row["TermID"] != null && row["TermID"].ToString() != "")
                {
                    model.TermID = new Guid(row["TermID"].ToString());
                }
                if (row["SubjectCode"] != null)
                {
                    model.SubjectCode = row["SubjectCode"].ToString();
                }
                if (row["Contents"] != null)
                {
                    model.Contents = row["Contents"].ToString();
                }
                if (row["NoteDate"] != null && row["NoteDate"].ToString() != "")
                {
                    model.NoteDate = DateTime.Parse(row["NoteDate"].ToString());
                }
                if (row["UserID"] != null && row["UserID"].ToString() != "")
                {
                    model.UserID = new Guid(row["UserID"].ToString());
                }
                if (row["FinishDate"] != null && row["FinishDate"].ToString() != "")
                {
                    model.FinishDate = DateTime.Parse(row["FinishDate"].ToString());
                }
                if (row["SchoolYear"] != null)
                {
                    model.SchoolYear = row["SchoolYear"].ToString();
                }
                if (row["Pictures"] != null)
                {
                    model.Pictures = row["Pictures"].ToString();
                }
                if (row["IsFinished"] != null && row["IsFinished"].ToString() != "")
                {
                    model.IsFinished = int.Parse(row["IsFinished"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,SubjectCode,Contents,NoteDate,UserID,FinishDate,SchoolYear,Pictures,IsFinished ");
            strSql.Append(" FROM JC_Homeworks ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" ID,SchoolColumnID,SchoolColumnPath,GradeID,ClassID,TermID,SubjectCode,Contents,NoteDate,UserID,FinishDate,SchoolYear,Pictures,IsFinished ");
            strSql.Append(" FROM JC_Homeworks ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_Homeworks ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_Homeworks T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "JC_Homeworks";
            parameters[1].Value = "ID";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

