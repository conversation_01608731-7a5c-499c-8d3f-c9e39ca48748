﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ECB.PC.Model
{
    /// <summary>
    /// ecb_news_category:新闻栏目类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class ecb_news_category
    {
        public ecb_news_category()
        { }
        #region Model
        private int? _id;
        private int? _columnid;
        private string _columnpath;
        private string _name;
        private int? _parentid;
        private string _categorypath;
        private int? _depth;
        private int _sortid;
        /// <summary>
        /// 调用别名
        /// </summary>
        public string CallName { get; set; }
        /// <summary>
        /// 主键ID
        /// </summary>
        public int? Id
        {
            set { _id = value; }
            get { return _id; }
        }
        /// <summary>
        /// 地区ID
        /// </summary>
        public int? ColumnId
        {
            set { _columnid = value; }
            get { return _columnid; }
        }
        /// <summary>
        /// 地区深度
        /// </summary>
        public string ColumnPath
        {
            set { _columnpath = value; }
            get { return _columnpath; }
        }
        /// <summary>
        /// 新闻类型名称
        /// </summary>
        public string Name
        {
            set { _name = value; }
            get { return _name; }
        }
        /// <summary>
        /// 父级Id
        /// </summary>
        public int? ParentId
        {
            set { _parentid = value; }
            get { return _parentid; }
        }
        /// <summary>
        /// 路径
        /// </summary>
        public string CategoryPath
        {
            set { _categorypath = value; }
            get { return _categorypath; }
        }
        /// <summary>
        /// 深度
        /// </summary>
        public int? Depth
        {
            set { _depth = value; }
            get { return _depth; }
        }
        /// <summary>
        /// 排序ID
        /// </summary>
        public int SortId
        {
            set { _sortid = value; }
            get { return _sortid; }
        }
        #endregion Model

    }
}