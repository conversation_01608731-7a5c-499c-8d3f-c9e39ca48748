﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_reboot_rule
    /// </summary>
    public partial class ecb_reboot_rule
    {
        public ecb_reboot_rule()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_reboot_rule");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_reboot_rule model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_reboot_rule(");
            strSql.Append("Id,ColumnId,ColumnPath,RuleName,Ids,LastEditTime,LastEditor)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@RuleName,@Ids,@LastEditTime,@LastEditor)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@RuleName", SqlDbType.NVarChar,255),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.RuleName;
            parameters[4].Value = model.Ids;
            parameters[5].Value = model.LastEditTime;
            parameters[6].Value = model.LastEditor;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_reboot_rule model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_reboot_rule set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("RuleName=@RuleName,");
            strSql.Append("Ids=@Ids,");
            strSql.Append("LastEditTime=@LastEditTime,");
            strSql.Append("LastEditor=@LastEditor");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@RuleName", SqlDbType.NVarChar,255),
                    new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.RuleName;
            parameters[3].Value = model.Ids;
            parameters[4].Value = model.LastEditTime;
            parameters[5].Value = model.LastEditor;
            parameters[6].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据 同时包括了开关机时间
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_time ");
            strSql.Append(" where RuleId=@Id  ");
            strSql.Append("delete from ecb_reboot_rule ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_reboot_rule ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_reboot_rule GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,RuleName,Ids,LastEditTime,LastEditor from ecb_reboot_rule ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            ECB.PC.Model.ecb_reboot_rule model = new ECB.PC.Model.ecb_reboot_rule();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_reboot_rule DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_reboot_rule model = new ECB.PC.Model.ecb_reboot_rule();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["RuleName"] != null)
                {
                    model.RuleName = row["RuleName"].ToString();
                }
                if (row["Ids"] != null)
                {
                    model.Ids = row["Ids"].ToString();
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
                if (row["LastEditor"] != null && row["LastEditor"].ToString() != "")
                {
                    model.LastEditor = new Guid(row["LastEditor"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,RuleName,Ids,LastEditTime,LastEditor ");
            strSql.Append(" FROM ecb_reboot_rule ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,RuleName,Ids,LastEditTime,LastEditor ");
            strSql.Append(" FROM ecb_reboot_rule ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere, string[] ConnStr =null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_reboot_rule ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString(),ConnStr);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_reboot_rule T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_reboot_rule";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 定时开关机保存
        /// </summary>
        /// <param name="app_msg"></param>
        /// <param name="receive">记录表</param>
        /// <returns></returns>
        public bool SubRule(Model.ecb_reboot_rule ecb_reboot_rule, List<Model.ecb_reboot_time> ecb_reboot_time)
        {
            int rows = 0;
            using (SqlConnection conn = new SqlConnection(DbHelperSQL.ConnMain[0]))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    StringBuilder strSql = new StringBuilder();
                    //修改-更新
                    if (ecb_reboot_rule.IsUpdate)
                    {
                        strSql.Clear();
                        strSql.Append("update ecb_reboot_rule set ");
                        strSql.Append("ColumnId=@ColumnId,");
                        strSql.Append("ColumnPath=@ColumnPath,");
                        strSql.Append("RuleName=@RuleName,");
                        strSql.Append("Ids=@Ids,");
                        strSql.Append("LastEditTime=@LastEditTime,");
                        strSql.Append("LastEditor=@LastEditor");
                        strSql.Append(" where Id=@Id ");
                        SqlParameter[] parameters = {
                        new SqlParameter("@ColumnId", SqlDbType.Int,4),
                        new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                        new SqlParameter("@RuleName", SqlDbType.NVarChar,255),
                        new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                        new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                        new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16),
                        new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
                        parameters[0].Value = ecb_reboot_rule.ColumnId;
                        parameters[1].Value = ecb_reboot_rule.ColumnPath;
                        parameters[2].Value = ecb_reboot_rule.RuleName;
                        parameters[3].Value = ecb_reboot_rule.Ids;
                        parameters[4].Value = ecb_reboot_rule.LastEditTime;
                        parameters[5].Value = ecb_reboot_rule.LastEditor;
                        parameters[6].Value = ecb_reboot_rule.Id;

                        PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
                        rows = cmd.ExecuteNonQuery();
                        cmd.Parameters.Clear();
                    }
                    else
                    {
                        strSql.Clear();
                        strSql.Append("insert into ecb_reboot_rule(");
                        strSql.Append("Id,ColumnId,ColumnPath,RuleName,Ids,LastEditTime,LastEditor)");
                        strSql.Append(" values (");
                        strSql.Append("@Id,@ColumnId,@ColumnPath,@RuleName,@Ids,@LastEditTime,@LastEditor)");
                        SqlParameter[] parameters = {
                        new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                        new SqlParameter("@ColumnId", SqlDbType.Int,4),
                        new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                        new SqlParameter("@RuleName", SqlDbType.NVarChar,255),
                        new SqlParameter("@Ids", SqlDbType.NVarChar,-1),
                        new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                        new SqlParameter("@LastEditor", SqlDbType.UniqueIdentifier,16)};
                        parameters[0].Value = ecb_reboot_rule.Id;
                        parameters[1].Value = ecb_reboot_rule.ColumnId;
                        parameters[2].Value = ecb_reboot_rule.ColumnPath;
                        parameters[3].Value = ecb_reboot_rule.RuleName;
                        parameters[4].Value = ecb_reboot_rule.Ids;
                        parameters[5].Value = ecb_reboot_rule.LastEditTime;
                        parameters[6].Value = ecb_reboot_rule.LastEditor;

                        PrepareCommand(cmd, conn, null, strSql.ToString(), parameters);
                        rows = cmd.ExecuteNonQuery();
                        cmd.Parameters.Clear();
                    }

                    if (rows > 0)
                    {
                        if (ecb_reboot_time.Count > 0)
                        {
                            foreach (Model.ecb_reboot_time item in ecb_reboot_time)
                            {
                                strSql.Clear();
                                if (item.IsUpdate)
                                {
                                    strSql.Append("update ecb_reboot_time set ");
                                    strSql.Append("RuleId=@RuleId,");
                                    strSql.Append("WeekId=@WeekId,");
                                    strSql.Append("BootTime=@BootTime,");
                                    strSql.Append("OffTime=@OffTime");
                                    strSql.Append(" where Id=@Id ");
                                    SqlParameter[] viewerParam = {
                                                new SqlParameter("@RuleId", SqlDbType.UniqueIdentifier,16),
                                                new SqlParameter("@WeekId", SqlDbType.Int,4),
                                                new SqlParameter("@BootTime", SqlDbType.DateTime),
                                                new SqlParameter("@OffTime", SqlDbType.DateTime),
                                                new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
                                    viewerParam[0].Value = item.RuleId;
                                    viewerParam[1].Value = item.WeekId;
                                    viewerParam[2].Value = item.BootTime;
                                    viewerParam[3].Value = item.OffTime;
                                    viewerParam[4].Value = item.Id;

                                    PrepareCommand(cmd, conn, null, strSql.ToString(), viewerParam);
                                    cmd.ExecuteNonQuery();
                                    cmd.Parameters.Clear();
                                }
                                else
                                {

                                    strSql.Append("insert into ecb_reboot_time(");
                                    strSql.Append("Id,RuleId,WeekId,BootTime,OffTime)");
                                    strSql.Append(" values (");
                                    strSql.Append("@Id,@RuleId,@WeekId,@BootTime,@OffTime)");
                                    SqlParameter[] viewerParam = {
                                            new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                                            new SqlParameter("@RuleId", SqlDbType.UniqueIdentifier,16),
                                            new SqlParameter("@WeekId", SqlDbType.Int,4),
                                            new SqlParameter("@BootTime", SqlDbType.DateTime),
                                            new SqlParameter("@OffTime", SqlDbType.DateTime)};
                                    viewerParam[0].Value = Guid.NewGuid();
                                    viewerParam[1].Value = item.RuleId;
                                    viewerParam[2].Value = item.WeekId;
                                    viewerParam[3].Value = item.BootTime;
                                    viewerParam[4].Value = item.OffTime;

                                    PrepareCommand(cmd, conn, null, strSql.ToString(), viewerParam);
                                    cmd.ExecuteNonQuery();
                                    cmd.Parameters.Clear();
                                }
                            }
                        }
                    }
                    tx.Commit();
                }
                catch (System.Data.SqlClient.SqlException E)
                {
                    rows = 0;
                    tx.Rollback();
                    throw new Exception(E.Message);
                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                }
            }
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        private static void PrepareCommand(SqlCommand cmd, SqlConnection conn, SqlTransaction trans, string cmdText, SqlParameter[] cmdParms)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            cmd.Connection = conn;
            cmd.CommandText = cmdText;
            if (trans != null)
                cmd.Transaction = trans;
            cmd.CommandType = CommandType.Text;//cmdType;
            if (cmdParms != null)
            {
                foreach (SqlParameter parameter in cmdParms)
                {
                    if ((parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Input) &&
                        (parameter.Value == null))
                    {
                        parameter.Value = DBNull.Value;
                    }
                    cmd.Parameters.Add(parameter);
                }
            }
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string tabName, string fileName, string strWhere,string orderName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  " + fileName);
            strSql.Append(" FROM " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            if (orderName.Trim() != "")
            {
                strSql.Append(" ORDER BY  " + orderName);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string fileName, string tabName, string strWhere, string orderby, int startIndex, int endIndex, string[] ConnStr=null)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by " + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, " + fileName + "  from " + tabName);
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString(),ConnStr);
        }
        #endregion  ExtensionMethod
    }
}

