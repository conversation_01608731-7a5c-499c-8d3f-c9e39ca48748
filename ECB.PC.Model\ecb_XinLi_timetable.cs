﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 心理室排班表
	/// </summary>
	[Serializable]
	public partial class ecb_XinLi_timetable
	{
		public ecb_XinLi_timetable()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private int _weekday;
		private int _number;
		private DateTime _starttime;
		private DateTime _endtime;
		private Guid _teacherid;
		private Guid _createuserid;
		private DateTime _createtime;
		/// <summary>
		/// 
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 周次
		/// </summary>
		public int Weekday
		{
			set{ _weekday=value;}
			get{return _weekday;}
		}
		/// <summary>
		/// 节次
		/// </summary>
		public int Number
		{
			set{ _number=value;}
			get{return _number;}
		}
		/// <summary>
		/// 开始时间
		/// </summary>
		public DateTime StartTime
		{
			set{ _starttime=value;}
			get{return _starttime;}
		}
		/// <summary>
		/// 结束时间
		/// </summary>
		public DateTime EndTime
		{
			set{ _endtime=value;}
			get{return _endtime;}
		}
		/// <summary>
		/// 教师Id
		/// </summary>
		public Guid TeacherId
		{
			set{ _teacherid=value;}
			get{return _teacherid;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}

