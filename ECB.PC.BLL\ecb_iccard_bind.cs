﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 类ecb_iccard_bind。
    /// </summary>
    [Serializable]
    public partial class ecb_iccard_bind
    {
        public ecb_iccard_bind()
        { }
        #region  Method

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string CardNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from [ecb_iccard_bind]");
            strSql.Append(" where CardNo=@CardNo ");

            SqlParameter[] parameters = {
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,500)};
            parameters[0].Value = CardNo;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.ecb_iccard_bind model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into [ecb_iccard_bind] (");
            strSql.Append("Id,ColumnId,ColumnPath,UserId,CardNo,UpdateTime,UpdateUser)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@UserId,@CardNo,@UpdateTime,@UpdateUser)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,500),
                    new SqlParameter("@UpdateTime", SqlDbType.DateTime),
                    new SqlParameter("@UpdateUser", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.Id;
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.UserId;
            parameters[4].Value = model.CardNo;
            parameters[5].Value = model.UpdateTime;
            parameters[6].Value = model.UpdateUser;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.ecb_iccard_bind model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update [ecb_iccard_bind] set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("CardNo=@CardNo,");
            strSql.Append("UpdateTime=@UpdateTime,");
            strSql.Append("UpdateUser=@UpdateUser");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,500),
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,500),
                    new SqlParameter("@UpdateTime", SqlDbType.DateTime),
                    new SqlParameter("@UpdateUser", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.UserId;
            parameters[3].Value = model.CardNo;
            parameters[4].Value = model.UpdateTime;
            parameters[5].Value = model.UpdateUser;
            parameters[6].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string CardNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from [ecb_iccard_bind] ");
            strSql.Append(" where CardNo=@CardNo ");
            SqlParameter[] parameters = {
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,500)};
            parameters[0].Value = CardNo;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid UserId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from [ecb_iccard_bind] ");
            strSql.Append(" where UserId=@UserId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = UserId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.ecb_iccard_bind GetModel(string CardNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,UserId,CardNo,UpdateTime,UpdateUser ");
            strSql.Append(" FROM [ecb_iccard_bind] ");
            strSql.Append(" where CardNo=@CardNo ");
            SqlParameter[] parameters = {
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,500)};
            parameters[0].Value = CardNo;

            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_iccard_bind DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_iccard_bind model = new Model.ecb_iccard_bind();
            if (row != null)
            {
                if (row["Id"] != null)
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["UserId"] != null)
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["CardNo"] != null)
                {
                    model.CardNo = row["CardNo"].ToString();
                }
                if (row["UpdateTime"] != null && row["UpdateTime"].ToString() != "")
                {
                    model.UpdateTime = DateTime.Parse(row["UpdateTime"].ToString());
                }
                if (row["UpdateUser"] != null)
                {
                    model.UpdateUser = new Guid(row["UpdateUser"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM [ecb_iccard_bind] ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        #endregion  Method
        #region  ExtensionMethod
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string CardNo, Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from [ecb_iccard_bind]");
            strSql.Append(" where CardNo=@CardNo and Id!=@Id");

            SqlParameter[] parameters = {
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,500),
                                        new SqlParameter("@Id", SqlDbType.UniqueIdentifier)};
            parameters[0].Value = CardNo;
            parameters[1].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 个人是否存在卡号
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public bool ExistsUser(Guid UserId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from [ecb_iccard_bind]");
            strSql.Append(" where UserId=@UserId ");

            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = UserId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取姓名
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetCName(Guid UserId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM [aspnet_Membership] ");
            strSql.Append(" where UserId=@UserId");
            SqlParameter[] parameters = {
                    new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = UserId;

            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 获得分页数据列表
        /// </summary>
        public DataSet List(string strWhere, int startIndex, int endIndex,bool isMain=false)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER ( ");
            strSql.Append(" ORDER BY shenfn,CardNo )AS Row,T.* FROM(");
            strSql.Append(" SELECT * FROM ( ");
            strSql.Append("SELECT a.ColumnId,ClassID,c.GradeId,DeptId, CardNo,UpdateTime,CName , ISNULL(ClassName, DeptName) _name,CASE WHEN c.ID IS NOT  NULL THEN '学生' ELSE '教师' END 'shenfn' FROM ecb_iccard_bind a ");
            strSql.Append(" LEFT JOIN dbo.aspnet_Membership b ON a.UserId = b.UserId ");
            strSql.Append(" LEFT JOIN dbo.JC_StudentInfos c ON a.UserId = c.ID ");
            strSql.Append(" LEFT JOIN dbo.JC_ClassInfos d ON d.ID = c.ClassID ");
            strSql.Append(" LEFT JOIN JC_TeacherDept e ON b.UserName = e.TeacherNo ");
            strSql.Append(" LEFT JOIN JC_Department f ON e.DeptId = f.ID where b.UserId is not null");
            strSql.Append(" ) A ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" )T ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1} ", startIndex, endIndex);

            return DbHelperSQL.Query(strSql.ToString(), isMain ? DbHelperSQL.ConnMain : null);
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet List(string strWhere,bool isMain)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT * FROM ( ");
            strSql.Append("SELECT a.ColumnId,ClassID,c.GradeId,DeptId, CardNo,UpdateTime,CName , ISNULL(ClassName, DeptName) _name,CASE WHEN c.ID IS NOT  NULL THEN '学生' ELSE '教师' END 'shenfn' FROM ecb_iccard_bind a ");
            strSql.Append(" LEFT JOIN dbo.aspnet_Membership b ON a.UserId = b.UserId ");
            strSql.Append(" LEFT JOIN dbo.JC_StudentInfos c ON a.UserId = c.ID ");
            strSql.Append(" LEFT JOIN dbo.JC_ClassInfos d ON d.ID = c.ClassID ");
            strSql.Append(" LEFT JOIN JC_TeacherDept e ON b.UserName = e.TeacherNo ");
            strSql.Append(" LEFT JOIN JC_Department f ON e.DeptId = f.ID where b.UserId is not null");
            strSql.Append(" ) A ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" WHERE " + strWhere);
            }

            return DbHelperSQL.Query(strSql.ToString(), isMain ? DbHelperSQL.ConnMain : null);
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet ExportList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select  CName ,CardNo,_name from (");
            strSql.Append(" SELECT * FROM ( ");
            strSql.Append("SELECT a.ColumnId,ClassID,c.GradeId,DeptId, CardNo,UpdateTime,CName , ISNULL(ClassName, DeptName) _name,CASE WHEN c.ID IS NOT  NULL THEN '学生' ELSE '教师' END 'shenfn' FROM ecb_iccard_bind a ");
            strSql.Append(" LEFT JOIN dbo.aspnet_Membership b ON a.UserId = b.UserId ");
            strSql.Append(" LEFT JOIN dbo.JC_StudentInfos c ON a.UserId = c.ID ");
            strSql.Append(" LEFT JOIN dbo.JC_ClassInfos d ON d.ID = c.ClassID ");
            strSql.Append(" LEFT JOIN JC_TeacherDept e ON b.UserName = e.TeacherNo ");
            strSql.Append(" LEFT JOIN JC_Department f ON e.DeptId = f.ID where b.UserId is not null");
            strSql.Append(" ) A ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) B order by _name,CName");
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsByUser(string CardNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from [ecb_iccard_bind]");
            strSql.Append(" where CardNo=@CardNo  and UserId in (select UserId from aspnet_Membership)");

            SqlParameter[] parameters = {
                    new SqlParameter("@CardNo", SqlDbType.NVarChar,500)};
            parameters[0].Value = CardNo;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }
        #endregion  ExtensionMethod
    }
}