﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace ECB.PC.BLL
{
    /// <summary>
    /// 数据访问类:ecb_FunctionRoom
    /// </summary>
    public partial class ecb_FunctionRoom
    {
        public ecb_FunctionRoom()
        { }

        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from ecb_FunctionRoom");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ECB.PC.Model.ecb_FunctionRoom model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into ecb_FunctionRoom(");
            strSql.Append("ID,ColumnId,ColumnPath,RoomName,RoomAddress,ImgUrl,PlaceId,CategoryCode,Status,CloseBeginTime,CloseEndTime,OrderId,IsReservation,ManageUserId,RoomDesc,OperateVideo,ManageRule,ActivityImgs)");
            strSql.Append(" values (");
            strSql.Append("@ID,@ColumnId,@ColumnPath,@RoomName,@RoomAddress,@ImgUrl,@PlaceId,@CategoryCode,@Status,@CloseBeginTime,@CloseEndTime,@OrderId,@IsReservation,@ManageUserId,@RoomDesc,@OperateVideo,@ManageRule,@ActivityImgs)");

            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@RoomName", SqlDbType.NVarChar,50),
                    new SqlParameter("@RoomAddress", SqlDbType.NVarChar,500),
                    new SqlParameter("@ImgUrl", SqlDbType.NVarChar,-1),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CategoryCode", SqlDbType.NVarChar,2),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@CloseBeginTime", SqlDbType.DateTime),
                    new SqlParameter("@CloseEndTime", SqlDbType.DateTime),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@IsReservation", SqlDbType.Int,4),
                    new SqlParameter("@ManageUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RoomDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@OperateVideo", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ManageRule", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ActivityImgs", SqlDbType.NVarChar,-1)};
            parameters[0].Value = model.ID = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.RoomName;
            parameters[4].Value = model.RoomAddress;
            parameters[5].Value = model.ImgUrl;
            parameters[6].Value = model.PlaceId;
            parameters[7].Value = model.CategoryCode;
            parameters[8].Value = model.Status;
            parameters[9].Value = model.CloseBeginTime;
            parameters[10].Value = model.CloseEndTime;
            parameters[11].Value = model.OrderId;
            parameters[12].Value = model.IsReservation;
            parameters[13].Value = model.ManageUserId;
            parameters[14].Value = model.RoomDesc;
            parameters[15].Value = model.OperateVideo;
            parameters[16].Value = model.ManageRule;
            parameters[17].Value = model.ActivityImgs;
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.ecb_FunctionRoom model, bool isAddPlace)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine("declare @SortId int;");
            strSql.AppendLine("declare @NodeDepth int;");
            strSql.Append(" insert into ecb_FunctionRoom(");
            strSql.Append("ID,ColumnId,ColumnPath,RoomName,RoomAddress,ImgUrl,PlaceId,CategoryCode,Status,CloseBeginTime,CloseEndTime,OrderId,IsReservation,ManageUserId,RoomDesc,OperateVideo,ManageRule,ActivityImgs)");
            strSql.Append(" values (");
            strSql.Append("@ID,@ColumnId,@ColumnPath,@RoomName,@RoomAddress,@ImgUrl,@PlaceId,@CategoryCode,@Status,@CloseBeginTime,@CloseEndTime,@OrderId,@IsReservation,@ManageUserId,@RoomDesc,@OperateVideo,@ManageRule,@ActivityImgs)");

            if (isAddPlace)
            {
                // 移到顶级节点，找地区下地点最大排序+1
                strSql.AppendLine($" select @SortId=isnull(max(SortID),0)+1 from ecb_place where ColumnId={model.ColumnId};");
                strSql.AppendLine(" set @NodeDepth=1;");

                strSql.Append(" insert into ecb_place(");
                strSql.Append("Id,ColumnId,ColumnPath,Code,Name,ShortName,Location,CategoryCode,MaxNum,ParentId,TypeCode,SortID,NodeDepth)");
                strSql.Append(" values (");
                strSql.Append("@PlaceId,@ColumnId,@ColumnPath,null,@RoomName,'',@RoomAddress,'02',@OrderId,'" + Guid.Empty + "','3',@SortId,@NodeDepth)");
            }

            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@RoomName", SqlDbType.NVarChar,50),
                    new SqlParameter("@RoomAddress", SqlDbType.NVarChar,500),
                    new SqlParameter("@ImgUrl", SqlDbType.NVarChar,-1),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CategoryCode", SqlDbType.NVarChar,2),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@CloseBeginTime", SqlDbType.DateTime),
                    new SqlParameter("@CloseEndTime", SqlDbType.DateTime),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@IsReservation", SqlDbType.Int,4),
                    new SqlParameter("@ManageUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RoomDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@OperateVideo", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ManageRule", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ActivityImgs", SqlDbType.NVarChar,-1)};
            parameters[0].Value = model.ID = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.RoomName;
            parameters[4].Value = model.RoomAddress;
            parameters[5].Value = model.ImgUrl;
            parameters[6].Value = model.PlaceId;
            parameters[7].Value = model.CategoryCode;
            parameters[8].Value = model.Status;
            parameters[9].Value = model.CloseBeginTime;
            parameters[10].Value = model.CloseEndTime;
            parameters[11].Value = model.OrderId;
            parameters[12].Value = model.IsReservation;
            parameters[13].Value = model.ManageUserId;
            parameters[14].Value = model.RoomDesc;
            parameters[15].Value = model.OperateVideo;
            parameters[16].Value = model.ManageRule;
            parameters[17].Value = model.ActivityImgs;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ECB.PC.Model.ecb_FunctionRoom model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_FunctionRoom set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("RoomName=@RoomName,");
            strSql.Append("RoomAddress=@RoomAddress,");
            strSql.Append("ImgUrl=@ImgUrl,");
            strSql.Append("PlaceId=@PlaceId,");
            strSql.Append("CategoryCode=@CategoryCode,");
            strSql.Append("Status=@Status,");
            strSql.Append("CloseBeginTime=@CloseBeginTime,");
            strSql.Append("CloseEndTime=@CloseEndTime,");
            strSql.Append("OrderId=@OrderId,");
            strSql.Append("IsReservation=@IsReservation,");
            strSql.Append("ManageUserId=@ManageUserId,");
            strSql.Append("RoomDesc=@RoomDesc,");
            strSql.Append("OperateVideo=@OperateVideo,");
            strSql.Append("ManageRule=@ManageRule,");
            strSql.Append("ActivityImgs=@ActivityImgs");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
                    new SqlParameter("@RoomName", SqlDbType.NVarChar,50),
                    new SqlParameter("@RoomAddress", SqlDbType.NVarChar,500),
                    new SqlParameter("@ImgUrl", SqlDbType.NVarChar,-1),
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CategoryCode", SqlDbType.NVarChar,2),
                    new SqlParameter("@Status", SqlDbType.Int,4),
                    new SqlParameter("@CloseBeginTime", SqlDbType.DateTime),
                    new SqlParameter("@CloseEndTime", SqlDbType.DateTime),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@IsReservation", SqlDbType.Int,4),
                    new SqlParameter("@ManageUserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@RoomDesc", SqlDbType.NVarChar,-1),
                    new SqlParameter("@OperateVideo", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ManageRule", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ActivityImgs", SqlDbType.NVarChar,-1),
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.RoomName;
            parameters[3].Value = model.RoomAddress;
            parameters[4].Value = model.ImgUrl;
            parameters[5].Value = model.PlaceId;
            parameters[6].Value = model.CategoryCode;
            parameters[7].Value = model.Status;
            parameters[8].Value = model.CloseBeginTime;
            parameters[9].Value = model.CloseEndTime;
            parameters[10].Value = model.OrderId;
            parameters[11].Value = model.IsReservation;
            parameters[12].Value = model.ManageUserId;
            parameters[13].Value = model.RoomDesc;
            parameters[14].Value = model.OperateVideo;
            parameters[15].Value = model.ManageRule;
            parameters[16].Value = model.ActivityImgs;
            parameters[17].Value = model.ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_FunctionRoom ");
            strSql.Append(" where ID=@ID ");
            //删除预约记录表
            strSql.Append(" delete from ecb_FunctionRoom_Reservation ");
            strSql.Append(" where FunctionRoomId=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string IDlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from ecb_FunctionRoom ");
            strSql.Append(" where ID in (" + IDlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_FunctionRoom GetModel(Guid ID)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 ID,ColumnId,ColumnPath,RoomName,RoomAddress,ImgUrl,PlaceId,CategoryCode,Status,CloseBeginTime,CloseEndTime,OrderId,IsReservation,ManageUserId,RoomDesc,OperateVideo,ManageRule,ActivityImgs from ecb_FunctionRoom ");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = ID;

            ECB.PC.Model.ecb_FunctionRoom model = new ECB.PC.Model.ecb_FunctionRoom();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_FunctionRoom DataRowToModel(DataRow row)
        {
            ECB.PC.Model.ecb_FunctionRoom model = new ECB.PC.Model.ecb_FunctionRoom();
            if (row != null)
            {
                if (row["ID"] != null && row["ID"].ToString() != "")
                {
                    model.ID = new Guid(row["ID"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["RoomName"] != null)
                {
                    model.RoomName = row["RoomName"].ToString();
                }
                if (row["RoomAddress"] != null)
                {
                    model.RoomAddress = row["RoomAddress"].ToString();
                }
                if (row["ImgUrl"] != null)
                {
                    model.ImgUrl = row["ImgUrl"].ToString();
                }
                if (row["PlaceId"] != null && row["PlaceId"].ToString() != "")
                {
                    model.PlaceId = new Guid(row["PlaceId"].ToString());
                }
                if (row["CategoryCode"] != null)
                {
                    model.CategoryCode = row["CategoryCode"].ToString();
                }
                if (row["Status"] != null && row["Status"].ToString() != "")
                {
                    model.Status = int.Parse(row["Status"].ToString());
                }
                if (row["CloseBeginTime"] != null && row["CloseBeginTime"].ToString() != "")
                {
                    model.CloseBeginTime = DateTime.Parse(row["CloseBeginTime"].ToString());
                }
                if (row["CloseEndTime"] != null && row["CloseEndTime"].ToString() != "")
                {
                    model.CloseEndTime = DateTime.Parse(row["CloseEndTime"].ToString());
                }
                if (row["OrderId"] != null && row["OrderId"].ToString() != "")
                {
                    model.OrderId = int.Parse(row["OrderId"].ToString());
                }
                if (row["IsReservation"] != null && row["IsReservation"].ToString() != "")
                {
                    model.IsReservation = int.Parse(row["IsReservation"].ToString());
                }
                if (row["ManageUserId"] != null && row["ManageUserId"].ToString() != "")
                {
                    model.ManageUserId = new Guid(row["ManageUserId"].ToString());
                }
                if (row["RoomDesc"] != null)
                {
                    model.RoomDesc = row["RoomDesc"].ToString();
                }
                if (row["OperateVideo"] != null)
                {
                    model.OperateVideo = row["OperateVideo"].ToString();
                }
                if (row["ManageRule"] != null)
                {
                    model.ManageRule = row["ManageRule"].ToString();
                }
                if (row["ActivityImgs"] != null)
                {
                    model.ActivityImgs = row["ActivityImgs"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ID,ColumnId,ColumnPath,RoomName,RoomAddress,ImgUrl,PlaceId,CategoryCode,Status,CloseBeginTime,CloseEndTime,OrderId,IsReservation,ManageUserId,RoomDesc,OperateVideo,ManageRule ,ActivityImgs");
            strSql.Append(" FROM ecb_FunctionRoom ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append("ID,ColumnId,ColumnPath,RoomName,RoomAddress,ImgUrl,PlaceId,CategoryCode,Status,CloseBeginTime,CloseEndTime,OrderId,IsReservation,ManageUserId,RoomDesc,OperateVideo,ManageRule,ActivityImgs ");
            strSql.Append(" FROM ecb_FunctionRoom ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM ecb_FunctionRoom ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from ecb_FunctionRoom T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "ecb_FunctionRoom";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ECB.PC.Model.ecb_FunctionRoom GetModelByPlace(Guid PlaceId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 * from ecb_FunctionRoom ");
            strSql.Append(" where PlaceId=@PlaceId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@PlaceId", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = PlaceId;

            ECB.PC.Model.ecb_FunctionRoom model = new ECB.PC.Model.ecb_FunctionRoom();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        public DataSet GetList(string fileName, string strWhere, string tabName, string order)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  " + fileName);
            strSql.Append(" FROM  " + tabName);
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            if (order.Trim() != "")
            {
                strSql.Append(" ORDER BY " + order);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 更改功能室状态为正常使用
        /// </summary>
        /// <param name="strwhere"></param>
        /// <returns></returns>
        public bool UpdateStatus(int ColumnID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update ecb_FunctionRoom set Status=1");
            strSql.Append(" where Status=2 and GETDATE()>CloseEndTime and ColumnId=" + ColumnID + "");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion  ExtensionMethod
    }
}

