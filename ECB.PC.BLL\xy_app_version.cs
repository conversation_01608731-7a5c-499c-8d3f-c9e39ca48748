﻿
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
namespace ECB.PC.BLL
{
	/// <summary>
	/// 数据访问类:xy_app_version
	/// </summary>
	public partial class xy_app_version
	{
		public xy_app_version()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(Guid id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from xy_app_version");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = id;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ECB.PC.Model.xy_app_version model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into xy_app_version(");
			strSql.Append("id,client,appid,version,title,note,url,releaseTime,ColumnPath)");
			strSql.Append(" values (");
			strSql.Append("@id,@client,@appid,@version,@title,@note,@url,@releaseTime,@ColumnPath)");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@client", SqlDbType.NVarChar,50),
					new SqlParameter("@appid", SqlDbType.NVarChar,50),
					new SqlParameter("@version", SqlDbType.NVarChar,50),
					new SqlParameter("@title", SqlDbType.NVarChar,50),
					new SqlParameter("@note", SqlDbType.NVarChar,-1),
					new SqlParameter("@url", SqlDbType.NVarChar,500),
					new SqlParameter("@releaseTime", SqlDbType.DateTime),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.client;
			parameters[2].Value = model.appid;
			parameters[3].Value = model.version;
			parameters[4].Value = model.title;
			parameters[5].Value = model.note;
			parameters[6].Value = model.url;
			parameters[7].Value = model.releaseTime;
			parameters[8].Value = model.ColumnPath;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ECB.PC.Model.xy_app_version model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update xy_app_version set ");
			strSql.Append("client=@client,");
			strSql.Append("appid=@appid,");
			strSql.Append("version=@version,");
			strSql.Append("title=@title,");
			strSql.Append("note=@note,");
			strSql.Append("url=@url,");
			strSql.Append("releaseTime=@releaseTime,");
			strSql.Append("ColumnPath=@ColumnPath");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@client", SqlDbType.NVarChar,50),
					new SqlParameter("@appid", SqlDbType.NVarChar,50),
					new SqlParameter("@version", SqlDbType.NVarChar,50),
					new SqlParameter("@title", SqlDbType.NVarChar,50),
					new SqlParameter("@note", SqlDbType.NVarChar,-1),
					new SqlParameter("@url", SqlDbType.NVarChar,500),
					new SqlParameter("@releaseTime", SqlDbType.DateTime),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,255),
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)};
			parameters[0].Value = model.client;
			parameters[1].Value = model.appid;
			parameters[2].Value = model.version;
			parameters[3].Value = model.title;
			parameters[4].Value = model.note;
			parameters[5].Value = model.url;
			parameters[6].Value = model.releaseTime;
			parameters[7].Value = model.ColumnPath;
			parameters[8].Value = model.id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(Guid id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from xy_app_version ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = id;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from xy_app_version ");
			strSql.Append(" where id in ("+idlist + ")  ");
			int rows=DbHelperSQL.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.xy_app_version GetModel(Guid id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 id,client,appid,version,title,note,url,releaseTime,ColumnPath from xy_app_version ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.UniqueIdentifier,16)			};
			parameters[0].Value = id;

			ECB.PC.Model.xy_app_version model=new ECB.PC.Model.xy_app_version();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ECB.PC.Model.xy_app_version DataRowToModel(DataRow row)
		{
			ECB.PC.Model.xy_app_version model=new ECB.PC.Model.xy_app_version();
			if (row != null)
			{
				if(row["id"]!=null && row["id"].ToString()!="")
				{
					model.id= new Guid(row["id"].ToString());
				}
				if(row["client"]!=null)
				{
					model.client=row["client"].ToString();
				}
				if(row["appid"]!=null)
				{
					model.appid=row["appid"].ToString();
				}
				if(row["version"]!=null)
				{
					model.version=row["version"].ToString();
				}
				if(row["title"]!=null)
				{
					model.title=row["title"].ToString();
				}
				if(row["note"]!=null)
				{
					model.note=row["note"].ToString();
				}
				if(row["url"]!=null)
				{
					model.url=row["url"].ToString();
				}
				if(row["releaseTime"]!=null && row["releaseTime"].ToString()!="")
				{
					model.releaseTime=DateTime.Parse(row["releaseTime"].ToString());
				}
				if(row["ColumnPath"]!=null)
				{
					model.ColumnPath=row["ColumnPath"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select id,client,appid,version,title,note,url,releaseTime,ColumnPath ");
			strSql.Append(" FROM xy_app_version ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" id,client,appid,version,title,note,url,releaseTime,ColumnPath ");
			strSql.Append(" FROM xy_app_version ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM xy_app_version ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.id desc");
			}
			strSql.Append(")AS Row, T.*  from xy_app_version T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "xy_app_version";
			parameters[1].Value = "id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

