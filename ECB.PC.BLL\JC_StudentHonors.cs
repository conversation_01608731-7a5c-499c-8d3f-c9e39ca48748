using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;

namespace ECB.PC.BLL
{
    /// <summary>
    /// 学生荣誉BLL类
    /// </summary>
    public partial class JC_StudentHonors
    {
        public JC_StudentHonors()
        { }

        #region 基础方法

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from JC_StudentHonors");
            strSql.Append(" where ID=@ID ");
            SqlParameter[] parameters = {
                new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = ID;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM JC_StudentHonors ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" * from JC_StudentHonors ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM JC_StudentHonors ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.ID desc");
            }
            strSql.Append(")AS Row, T.*  from JC_StudentHonors T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        #endregion

        #region 扩展方法

        /// <summary>
        /// 获取荣誉统计数据，按荣誉类别和荣誉级别分组统计，筛选掉空值和字典中不存在的数据
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <returns>统计结果DataTable</returns>
        public DataTable GetHonorStatistics(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"
                SELECT
                    h.TypeCode as HonorType,
                    h.LeveCode as HonorLevel,
                    COUNT(*) as HonorCount
                FROM JC_StudentHonors h
                INNER JOIN JC_StudentInfos s ON h.userid = s.ID
                LEFT JOIN Site_Dictionary d1 ON h.TypeCode = d1.DictValue AND d1.DictTypeId = 65
                LEFT JOIN Site_Dictionary d2 ON h.LeveCode = d2.DictValue AND d2.DictTypeId = 22
                WHERE " + strWhere + @"
                    AND d1.DictValue IS NOT NULL
                    AND d2.DictValue IS NOT NULL
                GROUP BY h.TypeCode, h.LeveCode
                ORDER BY
                    CASE WHEN ISNUMERIC(h.TypeCode) = 1 THEN CAST(h.TypeCode AS INT) ELSE 999999 END,
                    h.TypeCode,
                    CASE WHEN ISNUMERIC(h.LeveCode) = 1 THEN CAST(h.LeveCode AS INT) ELSE 999999 END,
                    h.LeveCode
            ");

            return DbHelperSQL.Query(strSql.ToString()).Tables[0];
        }

        /// <summary>
        /// 获取荣誉记录列表数据，筛选掉空值和字典中不存在的数据
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <returns>荣誉记录列表DataTable</returns>
        public DataTable GetHonorList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"
                SELECT
                    h.ID,
                    h.AwardsName,
                    h.TypeCode,
                    h.LeveCode,
                    h.AwardsDate,
                    h.ToAwardsUnit,
                    s.SName as StudentName,
                    c.CName as ClassName
                FROM JC_StudentHonors h
                INNER JOIN JC_StudentInfos s ON h.userid = s.ID
                INNER JOIN JC_ClassInfos c ON s.ClassID = c.ID
                LEFT JOIN Site_Dictionary d1 ON h.TypeCode = d1.DictValue AND d1.DictTypeId = 65
                LEFT JOIN Site_Dictionary d2 ON h.LeveCode = d2.DictValue AND d2.DictTypeId = 22
                WHERE " + strWhere + @"
                    AND h.TypeCode IS NOT NULL
                    AND h.TypeCode != ''
                    AND h.LeveCode IS NOT NULL
                    AND h.LeveCode != ''
                    AND d1.DictValue IS NOT NULL
                    AND d2.DictValue IS NOT NULL
                ORDER BY h.AwardsDate DESC, h.ID DESC
            ");

            return DbHelperSQL.Query(strSql.ToString()).Tables[0];
        }

        #endregion
    }
}
