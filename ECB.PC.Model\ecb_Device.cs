﻿using System;
namespace ECB.PC.Model
{
	/// <summary>
	/// 装置地点表
	/// </summary>
	[Serializable]
	public partial class ecb_Device
	{
		public ecb_Device()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _deviceno;
		private string _devicename;
		private string _place;
		private DateTime _createdate;
		private Guid _createuserid;
		private string _deviceurl;
		private int? _devicetype;
		private Guid _placeid;
        private string _seriano;
        /// <summary>
        /// 
        /// </summary>
        public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 设备编号
		/// </summary>
		public string DeviceNo
		{
			set{ _deviceno=value;}
			get{return _deviceno;}
		}
		/// <summary>
		/// 设备名称
		/// </summary>
		public string DeviceName
		{
			set{ _devicename=value;}
			get{return _devicename;}
		}
		/// <summary>
		/// 设备地点
		/// </summary>
		public string Place
		{
			set{ _place=value;}
			get{return _place;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreateDate
		{
			set{ _createdate=value;}
			get{return _createdate;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid CreateUserId
		{
			set{ _createuserid=value;}
			get{return _createuserid;}
		}
		/// <summary>
		/// 设备URL地址
		/// </summary>
		public string DeviceUrl
		{
			set { _deviceurl = value; }
			get { return _deviceurl; }
		}
		/// <summary>
		/// 
		/// </summary>
		public int? DeviceType
		{
			set { _devicetype = value; }
			get { return _devicetype; }
		}
		/// <summary>
		/// 
		/// </summary>
		public Guid PlaceId
		{
			set { _placeid = value; }
			get { return _placeid; }
		}
        /// <summary>
        /// 设备型号
        /// </summary>
        public string DeviceModel { get; set; }
        /// <summary>
        /// 班牌编号
        /// </summary>
        public string SeriaNo
        {
            set { _seriano = value; }
            get { return _seriano; }
        }
        #endregion Model

    }
}

